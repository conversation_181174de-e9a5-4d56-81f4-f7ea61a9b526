<?php

namespace Tests\Unit;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Review;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ReviewPurchaseValidationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $product;
    protected $vendor;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->vendor = Vendor::factory()->create();
        $this->product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'storage_conditions' => null,
            'country_of_origin' => null,
        ]);
    }

    /** @test */
    public function it_returns_true_when_user_has_purchased_product_with_delivered_order()
    {
        // Create a delivered order with the product
        $order = Order::create([
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'order_number' => 'ORD-' . time(),
            'payment_status' => 'paid',
            'fulfillment_status' => 'delivered',
            'total_amount' => 100.00,
            'status' => 'completed'
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'product_title' => $this->product->title_en,
            'quantity' => 1,
            'price' => 100.00,
            'total' => 100.00,
            'base_price' => 100.00,
            'product_snapshot' => [
                'name' => $this->product->title_en,
                'price' => 100.00
            ]
        ]);

        $hasPurchased = Review::userHasPurchasedProduct($this->user->id, $this->product->id);

        $this->assertTrue($hasPurchased);
    }

    /** @test */
    public function it_returns_true_when_user_has_purchased_product_with_shipped_order()
    {
        // Create a shipped order with the product
        $order = Order::create([
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'order_number' => 'ORD-' . time() . '-2',
            'payment_status' => 'paid',
            'fulfillment_status' => 'shipped',
            'total_amount' => 100.00,
            'status' => 'completed'
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'product_title' => $this->product->title_en,
            'quantity' => 1,
            'price' => 100.00,
            'total' => 100.00,
            'base_price' => 100.00,
            'product_snapshot' => [
                'name' => $this->product->title_en,
                'price' => 100.00
            ]
        ]);

        $hasPurchased = Review::userHasPurchasedProduct($this->user->id, $this->product->id);

        $this->assertTrue($hasPurchased);
    }

    /** @test */
    public function it_returns_false_when_user_has_not_purchased_product()
    {
        $hasPurchased = Review::userHasPurchasedProduct($this->user->id, $this->product->id);

        $this->assertFalse($hasPurchased);
    }

    /** @test */
    public function it_returns_false_when_order_is_not_paid()
    {
        // Create an unpaid order with the product
        $order = Order::create([
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'order_number' => 'ORD-' . time() . '-3',
            'payment_status' => 'pending',
            'fulfillment_status' => 'delivered',
            'total_amount' => 100.00,
            'status' => 'pending'
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'product_title' => $this->product->title_en,
            'quantity' => 1,
            'price' => 100.00,
            'total' => 100.00,
            'base_price' => 100.00,
            'product_snapshot' => [
                'name' => $this->product->title_en,
                'price' => 100.00
            ]
        ]);

        $hasPurchased = Review::userHasPurchasedProduct($this->user->id, $this->product->id);

        $this->assertFalse($hasPurchased);
    }

    /** @test */
    public function it_returns_false_when_order_is_pending_fulfillment()
    {
        // Create a paid but pending order with the product
        $order = Order::create([
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'order_number' => 'ORD-' . time() . '-4',
            'payment_status' => 'paid',
            'fulfillment_status' => 'pending',
            'total_amount' => 100.00,
            'status' => 'processing'
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'product_title' => $this->product->title_en,
            'quantity' => 1,
            'price' => 100.00,
            'total' => 100.00,
            'base_price' => 100.00,
            'product_snapshot' => [
                'name' => $this->product->title_en,
                'price' => 100.00
            ]
        ]);

        $hasPurchased = Review::userHasPurchasedProduct($this->user->id, $this->product->id);

        $this->assertFalse($hasPurchased);
    }

    /** @test */
    public function it_returns_false_when_order_is_cancelled()
    {
        // Create a cancelled order with the product
        $order = Order::create([
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'order_number' => 'ORD-' . time() . '-5',
            'payment_status' => 'paid',
            'fulfillment_status' => 'cancelled',
            'total_amount' => 100.00,
            'status' => 'cancelled'
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'product_title' => $this->product->title_en,
            'quantity' => 1,
            'price' => 100.00,
            'total' => 100.00,
            'base_price' => 100.00,
            'product_snapshot' => [
                'name' => $this->product->title_en,
                'price' => 100.00
            ]
        ]);

        $hasPurchased = Review::userHasPurchasedProduct($this->user->id, $this->product->id);

        $this->assertFalse($hasPurchased);
    }

    /** @test */
    public function it_returns_false_when_user_purchased_different_product()
    {
        $otherProduct = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'storage_conditions' => null,
            'country_of_origin' => null,
        ]);
        
        // Create a delivered order with a different product
        $order = Order::create([
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'order_number' => 'ORD-' . time() . '-6',
            'payment_status' => 'paid',
            'fulfillment_status' => 'delivered',
            'total_amount' => 100.00,
            'status' => 'completed'
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $otherProduct->id,
            'vendor_id' => $this->vendor->id,
            'product_title' => $otherProduct->title_en,
            'quantity' => 1,
            'price' => 100.00,
            'total' => 100.00,
            'base_price' => 100.00,
            'product_snapshot' => [
                'name' => $otherProduct->title_en,
                'price' => 100.00
            ]
        ]);

        $hasPurchased = Review::userHasPurchasedProduct($this->user->id, $this->product->id);

        $this->assertFalse($hasPurchased);
    }
}
