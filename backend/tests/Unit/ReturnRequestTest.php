<?php

namespace Tests\Unit;

use App\Models\Order;
use App\Models\Product;
use App\Models\ReturnRequest;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ReturnRequestTest extends TestCase
{
    use RefreshDatabase;

    public function test_return_request_can_be_created()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create(['user_id' => $user->id]);
        $product = Product::factory()->create();

        $return = ReturnRequest::create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'user_id' => $user->id,
            'type' => 'refund',
            'reason' => 'Defective product',
            'quantity' => 2,
            'unit_price' => 50.00,
            'total_amount' => 100.00,
        ]);

        $this->assertDatabaseHas('returns', [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'user_id' => $user->id,
            'type' => 'refund',
            'status' => 'pending',
        ]);

        $this->assertNotNull($return->return_number);
        $this->assertStringStartsWith('RET-', $return->return_number);
    }

    public function test_return_request_has_relationships()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create(['user_id' => $user->id]);
        $product = Product::factory()->create();

        $return = ReturnRequest::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'user_id' => $user->id,
        ]);

        $this->assertInstanceOf(Order::class, $return->order);
        $this->assertInstanceOf(Product::class, $return->product);
        $this->assertInstanceOf(User::class, $return->user);
        $this->assertEquals($order->id, $return->order->id);
        $this->assertEquals($product->id, $return->product->id);
        $this->assertEquals($user->id, $return->user->id);
    }

    public function test_return_number_is_unique()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create(['user_id' => $user->id]);
        $product = Product::factory()->create();

        $return1 = ReturnRequest::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'user_id' => $user->id,
        ]);

        $return2 = ReturnRequest::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'user_id' => $user->id,
        ]);

        $this->assertNotEquals($return1->return_number, $return2->return_number);
    }

    public function test_return_request_casts_work_correctly()
    {
        $return = ReturnRequest::factory()->create([
            'attachments' => ['image1.jpg', 'image2.jpg'],
            'quantity' => '5',
            'unit_price' => '25.50',
            'total_amount' => '127.50',
        ]);

        $this->assertIsArray($return->attachments);
        $this->assertIsInt($return->quantity);
        $this->assertEquals(5, $return->quantity);
        $this->assertEquals('25.50', $return->unit_price);
        $this->assertEquals('127.50', $return->total_amount);
    }
}