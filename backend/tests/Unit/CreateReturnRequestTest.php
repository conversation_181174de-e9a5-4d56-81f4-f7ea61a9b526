<?php

namespace Tests\Unit;

use App\Http\Requests\CreateReturnRequest;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class CreateReturnRequestTest extends TestCase
{
    public function test_validation_passes_with_valid_data()
    {
        $request = new CreateReturnRequest();
        
        $data = [
            'order_id' => 1,
            'type' => 'refund',
            'reason' => 'Product defective',
            'description' => 'Item arrived damaged',
            'items' => [
                [
                    'product_id' => 1,
                    'quantity' => 2,
                    'reason' => 'Wrong size'
                ]
            ],
            'attachments' => ['image1.jpg', 'image2.jpg']
        ];

        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->passes());
    }

    public function test_validation_fails_without_required_fields()
    {
        $request = new CreateReturnRequest();
        
        $data = [];

        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('order_id', $validator->errors()->toArray());
        $this->assertArrayHasKey('type', $validator->errors()->toArray());
        $this->assertArrayHasKey('reason', $validator->errors()->toArray());
        $this->assertArrayHasKey('items', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_invalid_type()
    {
        $request = new CreateReturnRequest();
        
        $data = [
            'order_id' => 1,
            'type' => 'invalid_type',
            'reason' => 'Test reason',
            'items' => [
                ['product_id' => 1, 'quantity' => 1]
            ]
        ];

        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('type', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_empty_items_array()
    {
        $request = new CreateReturnRequest();
        
        $data = [
            'order_id' => 1,
            'type' => 'refund',
            'reason' => 'Test reason',
            'items' => []
        ];

        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('items', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_invalid_item_structure()
    {
        $request = new CreateReturnRequest();
        
        $data = [
            'order_id' => 1,
            'type' => 'refund',
            'reason' => 'Test reason',
            'items' => [
                [
                    'product_id' => 'invalid',
                    'quantity' => 0
                ]
            ]
        ];

        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('items.0.quantity', $validator->errors()->toArray());
    }

    public function test_validation_passes_without_optional_fields()
    {
        $request = new CreateReturnRequest();
        
        $data = [
            'order_id' => 1,
            'type' => 'return',
            'reason' => 'Not needed anymore',
            'items' => [
                ['product_id' => 1, 'quantity' => 1]
            ]
        ];

        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->passes());
    }

    public function test_custom_error_messages_are_returned()
    {
        $request = new CreateReturnRequest();
        
        $data = [
            'type' => 'invalid_type',
            'items' => []
        ];

        $validator = Validator::make($data, $request->rules(), $request->messages());

        $this->assertTrue($validator->fails());
        $this->assertEquals('Order is required', $validator->errors()->first('order_id'));
        $this->assertEquals('Invalid return type', $validator->errors()->first('type'));
        $this->assertEquals('Return reason is required', $validator->errors()->first('reason'));
        $this->assertEquals('At least one item must be selected for return', $validator->errors()->first('items'));
    }
}