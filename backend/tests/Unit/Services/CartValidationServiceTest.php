<?php

namespace Tests\Unit\Services;

use App\Models\CartItem;
use App\Models\Coupon;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ShoppingCart;
use App\Models\Vendor;
use App\Services\CartValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CartValidationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected CartValidationService $validationService;
    protected ShoppingCart $cart;
    protected Vendor $vendor;
    protected Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        $this->validationService = new CartValidationService();
        
        $this->vendor = Vendor::factory()->active()->create();
        $this->product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'status' => 'active',
            'price' => 100.00,
            'stock_quantity' => 10,
            'min_cart_quantity' => 1,
            'max_cart_quantity' => 5,
            'cart_increment' => 1,
            'allow_backorder' => false,
        ]);

        $this->cart = ShoppingCart::factory()->create();
    }

    public function test_validate_add_to_cart_success(): void
    {
        $itemData = [
            'product_id' => $this->product->id,
            'quantity' => 2,
        ];

        // Should not throw any exception
        $this->validationService->validateAddToCart($this->cart, $itemData);
        $this->assertTrue(true); // If we reach here, validation passed
    }

    public function test_validate_add_to_cart_inactive_product(): void
    {
        $this->product->update(['status' => 'inactive']);

        $itemData = [
            'product_id' => $this->product->id,
            'quantity' => 2,
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Product is not available.');

        $this->validationService->validateAddToCart($this->cart, $itemData);
    }

    public function test_validate_add_to_cart_invalid_variant(): void
    {
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id + 1, // Different product
            'status' => 'active',
        ]);

        $itemData = [
            'product_id' => $this->product->id,
            'variant_id' => $variant->id,
            'quantity' => 2,
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid product variant.');

        $this->validationService->validateAddToCart($this->cart, $itemData);
    }

    public function test_validate_quantity_below_minimum(): void
    {
        $this->product->update(['min_cart_quantity' => 3]);

        $itemData = [
            'product_id' => $this->product->id,
            'quantity' => 2, // Below minimum of 3
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Minimum quantity for');

        $this->validationService->validateAddToCart($this->cart, $itemData);
    }

    public function test_validate_quantity_above_maximum(): void
    {
        $this->product->update(['max_cart_quantity' => 3]);

        $itemData = [
            'product_id' => $this->product->id,
            'quantity' => 5, // Above maximum of 3
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Maximum quantity for');

        $this->validationService->validateAddToCart($this->cart, $itemData);
    }

    public function test_validate_quantity_increment(): void
    {
        $this->product->update(['cart_increment' => 3]);

        $itemData = [
            'product_id' => $this->product->id,
            'quantity' => 4, // Not a multiple of 3
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('must be in increments of');

        $this->validationService->validateAddToCart($this->cart, $itemData);
    }

    public function test_validate_stock_availability_insufficient(): void
    {
        $this->product->update(['stock_quantity' => 2]);

        $itemData = [
            'product_id' => $this->product->id,
            'quantity' => 5, // More than available stock
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Only 2 units of');

        $this->validationService->validateAddToCart($this->cart, $itemData);
    }

    public function test_validate_stock_availability_out_of_stock(): void
    {
        $this->product->update(['stock_quantity' => 0]);

        $itemData = [
            'product_id' => $this->product->id,
            'quantity' => 1,
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('is currently out of stock');

        $this->validationService->validateAddToCart($this->cart, $itemData);
    }

    public function test_validate_stock_availability_with_backorder(): void
    {
        $this->product->update([
            'stock_quantity' => 0,
            'allow_backorder' => true,
        ]);

        $itemData = [
            'product_id' => $this->product->id,
            'quantity' => 5,
        ];

        // Should not throw exception when backorder is allowed
        $this->validationService->validateAddToCart($this->cart, $itemData);
        $this->assertTrue(true);
    }

    public function test_validate_inactive_vendor(): void
    {
        $this->vendor->update(['is_active' => false]);

        $itemData = [
            'product_id' => $this->product->id,
            'quantity' => 2,
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Vendor is currently not accepting orders');

        $this->validationService->validateAddToCart($this->cart, $itemData);
    }

    public function test_validate_quantity_update_success(): void
    {
        $cartItem = CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $this->product->id,
            'quantity' => 2,
        ]);

        // Should not throw any exception
        $this->validationService->validateQuantityUpdate($cartItem, 3);
        $this->assertTrue(true);
    }

    public function test_validate_cart_for_checkout_empty_cart(): void
    {
        $validation = $this->validationService->validateCartForCheckout($this->cart);

        $this->assertFalse($validation['is_valid']);
        $this->assertContains('Cart is empty.', $validation['errors']);
    }

    public function test_validate_cart_for_checkout_with_items(): void
    {
        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
        ]);

        $this->cart->update(['total_amount' => 200.00]);

        $validation = $this->validationService->validateCartForCheckout($this->cart);

        $this->assertTrue($validation['is_valid']);
        $this->assertEmpty($validation['errors']);
    }

    public function test_validate_cart_for_checkout_unavailable_product(): void
    {
        $this->product->update(['status' => 'inactive']);

        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
        ]);

        $this->cart->update(['total_amount' => 200.00]);

        $validation = $this->validationService->validateCartForCheckout($this->cart);

        $this->assertFalse($validation['is_valid']);
        $this->assertNotEmpty($validation['errors']);
        $this->assertStringContains('no longer available', $validation['errors'][0]);
    }

    public function test_validate_cart_for_checkout_zero_total(): void
    {
        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
        ]);

        $this->cart->update(['total_amount' => 0.00]);

        $validation = $this->validationService->validateCartForCheckout($this->cart);

        $this->assertFalse($validation['is_valid']);
        $this->assertContains('Cart total must be greater than zero.', $validation['errors']);
    }

    public function test_validate_coupon_application_success(): void
    {
        $coupon = Coupon::factory()->create([
            'code' => 'VALID10',
            'is_active' => true,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'min_order_value' => 100.00,
        ]);

        $this->cart->update(['subtotal' => 200.00]);

        // Should not throw any exception
        $this->validationService->validateCouponApplication($this->cart, 'VALID10');
        $this->assertTrue(true);
    }

    public function test_validate_coupon_application_inactive_coupon(): void
    {
        $coupon = Coupon::factory()->create([
            'code' => 'INACTIVE',
            'is_active' => false,
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Coupon is not active.');

        $this->validationService->validateCouponApplication($this->cart, 'INACTIVE');
    }

    public function test_validate_coupon_application_expired(): void
    {
        $coupon = Coupon::factory()->create([
            'code' => 'EXPIRED',
            'is_active' => true,
            'start_date' => now()->subDays(10),
            'end_date' => now()->subDay(),
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Coupon has expired.');

        $this->validationService->validateCouponApplication($this->cart, 'EXPIRED');
    }

    public function test_validate_coupon_application_below_minimum_order(): void
    {
        $coupon = Coupon::factory()->create([
            'code' => 'MIN500',
            'is_active' => true,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'min_order_value' => 500.00,
        ]);

        $this->cart->update(['subtotal' => 200.00]); // Below minimum

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Minimum order value of AED 500 required.');

        $this->validationService->validateCouponApplication($this->cart, 'MIN500');
    }

    public function test_validate_coupon_application_already_applied(): void
    {
        $coupon = Coupon::factory()->create([
            'code' => 'DUPLICATE',
            'is_active' => true,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
        ]);

        $this->cart->update([
            'subtotal' => 200.00,
            'applied_coupons' => [
                ['code' => 'DUPLICATE', 'discount_amount' => 20.00],
            ],
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Coupon is already applied.');

        $this->validationService->validateCouponApplication($this->cart, 'DUPLICATE');
    }

    public function test_validate_vendor_specific_coupon(): void
    {
        $otherVendor = Vendor::factory()->active()->create();
        
        $coupon = Coupon::factory()->create([
            'code' => 'VENDOR123',
            'is_active' => true,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'vendor_id' => $otherVendor->id,
        ]);

        // Cart has items from different vendor
        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'vendor_id' => $this->vendor->id, // Different vendor
        ]);

        $this->cart->update(['subtotal' => 200.00]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('only valid for specific vendor products');

        $this->validationService->validateCouponApplication($this->cart, 'VENDOR123');
    }
}
