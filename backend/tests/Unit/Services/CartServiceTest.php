<?php

namespace Tests\Unit\Services;

use App\Models\CartItem;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ShoppingCart;
use App\Models\User;
use App\Models\Vendor;
use App\Services\CartCalculationService;
use App\Services\CartService;
use App\Services\CartValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Tests\TestCase;

class CartServiceTest extends TestCase
{
    use RefreshDatabase;

    protected CartService $cartService;
    protected CartCalculationService $calculationService;
    protected CartValidationService $validationService;
    protected User $user;
    protected Vendor $vendor;
    protected Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        $this->calculationService = $this->createMock(CartCalculationService::class);
        $this->validationService = $this->createMock(CartValidationService::class);
        
        $this->cartService = new CartService(
            $this->calculationService,
            $this->validationService
        );

        // Create test data
        $this->user = User::factory()->create();
        $this->vendor = Vendor::factory()->active()->create();
        $this->product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'status' => 'active',
            'price' => 100.00,
            'stock_quantity' => 10,
        ]);
    }

    public function test_create_cart_for_authenticated_user(): void
    {
        Auth::login($this->user);

        $cart = $this->cartService->createCart([
            'user_id' => $this->user->id,
            'currency' => 'AED',
        ]);

        $this->assertInstanceOf(ShoppingCart::class, $cart);
        $this->assertEquals($this->user->id, $cart->user_id);
        $this->assertEquals('AED', $cart->currency);
        $this->assertEquals('active', $cart->status);
        $this->assertNotNull($cart->uuid);
    }

    public function test_create_cart_for_guest_user(): void
    {
        $sessionId = 'test-session-123';

        $cart = $this->cartService->createCart([
            'session_id' => $sessionId,
            'currency' => 'AED',
        ]);

        $this->assertInstanceOf(ShoppingCart::class, $cart);
        $this->assertNull($cart->user_id);
        $this->assertEquals($sessionId, $cart->session_id);
        $this->assertEquals('AED', $cart->currency);
        $this->assertEquals('active', $cart->status);
    }

    public function test_get_or_create_cart_for_authenticated_user(): void
    {
        Auth::login($this->user);

        // First call should create a new cart
        $cart1 = $this->cartService->getOrCreateCart();
        $this->assertInstanceOf(ShoppingCart::class, $cart1);
        $this->assertEquals($this->user->id, $cart1->user_id);

        // Second call should return the same cart
        $cart2 = $this->cartService->getOrCreateCart();
        $this->assertEquals($cart1->id, $cart2->id);
    }

    public function test_add_item_to_cart(): void
    {
        $this->validationService
            ->expects($this->once())
            ->method('validateAddToCart')
            ->willReturn(true);

        $this->calculationService
            ->expects($this->once())
            ->method('recalculateCart');

        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);

        $itemData = [
            'product_id' => $this->product->id,
            'quantity' => 2,
        ];

        $cartItem = $this->cartService->addItem($cart, $itemData);

        $this->assertInstanceOf(CartItem::class, $cartItem);
        $this->assertEquals($cart->id, $cartItem->cart_id);
        $this->assertEquals($this->product->id, $cartItem->product_id);
        $this->assertEquals($this->vendor->id, $cartItem->vendor_id);
        $this->assertEquals(2, $cartItem->quantity);
        $this->assertEquals(100.00, $cartItem->unit_price);
        $this->assertEquals(200.00, $cartItem->total_price);
    }

    public function test_add_existing_item_increases_quantity(): void
    {
        $this->validationService
            ->expects($this->once())
            ->method('validateAddToCart')
            ->willReturn(true);

        $this->validationService
            ->expects($this->once())
            ->method('validateQuantityUpdate')
            ->willReturn(true);

        $this->calculationService
            ->expects($this->exactly(2))
            ->method('recalculateCart');

        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);
        
        // Create existing cart item
        $existingItem = CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 1,
            'unit_price' => 100.00,
            'total_price' => 100.00,
        ]);

        $itemData = [
            'product_id' => $this->product->id,
            'quantity' => 2,
        ];

        $cartItem = $this->cartService->addItem($cart, $itemData);

        $this->assertEquals($existingItem->id, $cartItem->id);
        $this->assertEquals(3, $cartItem->quantity); // 1 + 2
        $this->assertEquals(300.00, $cartItem->total_price); // 3 * 100
    }

    public function test_update_item_quantity(): void
    {
        $this->validationService
            ->expects($this->once())
            ->method('validateQuantityUpdate')
            ->willReturn(true);

        $this->calculationService
            ->expects($this->once())
            ->method('recalculateCart');

        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);
        $cartItem = CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
        ]);

        $updatedItem = $this->cartService->updateItem($cartItem, ['quantity' => 5]);

        $this->assertEquals(5, $updatedItem->quantity);
        $this->assertEquals(500.00, $updatedItem->total_price);
    }

    public function test_update_item_with_zero_quantity_removes_item(): void
    {
        $this->calculationService
            ->expects($this->once())
            ->method('recalculateCart');

        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);
        $cartItem = CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
        ]);

        $this->cartService->updateItem($cartItem, ['quantity' => 0]);

        $this->assertDatabaseMissing('cart_items', ['id' => $cartItem->id]);
    }

    public function test_remove_item_from_cart(): void
    {
        $this->calculationService
            ->expects($this->once())
            ->method('recalculateCart');

        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);
        $cartItem = CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
        ]);

        $result = $this->cartService->removeItem($cartItem);

        $this->assertTrue($result);
        $this->assertDatabaseMissing('cart_items', ['id' => $cartItem->id]);
    }

    public function test_clear_cart(): void
    {
        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);
        
        // Add some items
        CartItem::factory()->count(3)->create(['cart_id' => $cart->id]);

        $result = $this->cartService->clearCart($cart);

        $this->assertTrue($result);
        $this->assertEquals(0, $cart->items()->count());
        $this->assertEquals(0, $cart->fresh()->subtotal);
        $this->assertEquals(0, $cart->fresh()->total_amount);
    }

    public function test_bulk_update_items(): void
    {
        $this->validationService
            ->method('validateQuantityUpdate')
            ->willReturn(true);

        $this->calculationService
            ->method('recalculateCart');

        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);
        
        $item1 = CartItem::factory()->create(['cart_id' => $cart->id, 'quantity' => 1]);
        $item2 = CartItem::factory()->create(['cart_id' => $cart->id, 'quantity' => 2]);
        $item3 = CartItem::factory()->create(['cart_id' => $cart->id, 'quantity' => 3]);

        $updates = [
            ['id' => $item1->id, 'quantity' => 5],
            ['id' => $item2->id, 'quantity' => 0], // This should remove the item
            ['id' => $item3->id, 'quantity' => 10],
        ];

        $updatedItems = $this->cartService->bulkUpdateItems($cart, $updates);

        $this->assertEquals(2, $updatedItems->count()); // item2 was removed
        $this->assertEquals(5, $item1->fresh()->quantity);
        $this->assertEquals(10, $item3->fresh()->quantity);
        $this->assertDatabaseMissing('cart_items', ['id' => $item2->id]);
    }

    public function test_get_vendor_groups(): void
    {
        $vendor2 = Vendor::factory()->active()->create();
        $product2 = Product::factory()->create(['vendor_id' => $vendor2->id]);

        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);
        
        // Add items from different vendors
        CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'total_price' => 200.00,
        ]);

        CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_id' => $product2->id,
            'vendor_id' => $vendor2->id,
            'quantity' => 1,
            'total_price' => 150.00,
        ]);

        $vendorGroups = $this->cartService->getVendorGroups($cart);

        $this->assertEquals(2, $vendorGroups->count());
        
        $group1 = $vendorGroups->first();
        $this->assertEquals($this->vendor->id, $group1['vendor_id']);
        $this->assertEquals(200.00, $group1['subtotal']);
        $this->assertEquals(1, $group1['items_count']);
        $this->assertEquals(2, $group1['total_quantity']);
    }

    public function test_apply_coupon(): void
    {
        $this->calculationService
            ->expects($this->once())
            ->method('applyCoupon')
            ->with($this->anything(), 'TEST10')
            ->willReturn(50.00);

        $this->calculationService
            ->expects($this->once())
            ->method('recalculateCart');

        $cart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'subtotal' => 500.00,
        ]);

        $result = $this->cartService->applyCoupon($cart, 'TEST10');

        $this->assertEquals('TEST10', $result['coupon_code']);
        $this->assertEquals(50.00, $result['discount_amount']);
        $this->assertArrayHasKey('cart_total', $result);
    }

    public function test_remove_coupon(): void
    {
        $this->calculationService
            ->expects($this->once())
            ->method('recalculateCart');

        $cart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'applied_coupons' => [
                ['code' => 'TEST10', 'discount_amount' => 50.00],
                ['code' => 'SAVE20', 'discount_amount' => 100.00],
            ],
        ]);

        $result = $this->cartService->removeCoupon($cart, 'TEST10');

        $this->assertTrue($result);
        
        $appliedCoupons = $cart->fresh()->applied_coupons;
        $this->assertEquals(1, count($appliedCoupons));
        $this->assertEquals('SAVE20', $appliedCoupons[0]['code']);
    }
}
