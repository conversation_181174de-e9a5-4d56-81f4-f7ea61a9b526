<?php

namespace Tests\Unit\Services;

use App\Models\EmailTemplate;
use App\Models\EmailTemplateVariable;
use App\Services\EmailTemplateService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EmailTemplateServiceTest extends TestCase
{
    use RefreshDatabase;

    private EmailTemplateService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(\Database\Seeders\EmailTemplateSystemSeeder::class);
        $this->service = new EmailTemplateService();
    }

    /** @test */
    public function it_renders_template_with_variables()
    {
        $template = EmailTemplate::factory()->create([
            'subject' => 'Hello {{user.name}}',
            'body_html' => '<p>Welcome {{user.name}} to {{site.name}}!</p>',
            'body_text' => 'Welcome {{user.name}} to {{site.name}}!',
        ]);

        $variables = [
            'user.name' => '<PERSON>',
            'site.name' => 'Vitamins.ae',
        ];

        $result = $this->service->renderTemplate($template, $variables);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('subject', $result);
        $this->assertArrayHasKey('body_html', $result);
        $this->assertArrayHasKey('body_text', $result);
        
        $this->assertEquals('Hello John Doe', $result['subject']);
        $this->assertEquals('<p>Welcome John Doe to Vitamins.ae!</p>', $result['body_html']);
        $this->assertEquals('Welcome John Doe to Vitamins.ae!', $result['body_text']);
    }

    /** @test */
    public function it_handles_missing_variables_with_defaults()
    {
        $template = EmailTemplate::factory()->create([
            'subject' => 'Hello {{user.name}}',
            'body_html' => '<p>Welcome to {{site.name}}!</p>',
        ]);

        // Create a variable with default value
        EmailTemplateVariable::factory()->create([
            'key' => 'site.name',
            'default_value' => 'Our Site',
            'data_type' => 'string',
        ]);

        $variables = [
            'user.name' => 'John Doe',
            // site.name is missing, should use default
        ];

        $result = $this->service->renderTemplate($template, $variables);

        $this->assertEquals('Hello John Doe', $result['subject']);
        $this->assertEquals('<p>Welcome to Our Site!</p>', $result['body_html']);
    }

    /** @test */
    public function it_handles_conditional_logic()
    {
        $template = EmailTemplate::factory()->create([
            'body_html' => '<p>{{#if user.is_premium}}Premium content{{else}}Regular content{{/if}}</p>',
        ]);

        // Test with premium user
        $variables = ['user.is_premium' => true];
        $result = $this->service->renderTemplate($template, $variables);
        $this->assertStringContainsString('Premium content', $result['body_html']);

        // Test with regular user
        $variables = ['user.is_premium' => false];
        $result = $this->service->renderTemplate($template, $variables);
        $this->assertStringContainsString('Regular content', $result['body_html']);
    }

    /** @test */
    public function it_handles_loops()
    {
        $template = EmailTemplate::factory()->create([
            'body_html' => '<ul>{{#each order.items}}<li>{{name}} - ${{price}}</li>{{/each}}</ul>',
        ]);

        $variables = [
            'order.items' => [
                ['name' => 'Vitamin C', 'price' => '25.99'],
                ['name' => 'Vitamin D', 'price' => '19.99'],
            ]
        ];

        $result = $this->service->renderTemplate($template, $variables);
        
        $this->assertStringContainsString('<li>Vitamin C - $25.99</li>', $result['body_html']);
        $this->assertStringContainsString('<li>Vitamin D - $19.99</li>', $result['body_html']);
    }

    /** @test */
    public function it_generates_preview_with_sample_data()
    {
        $template = EmailTemplate::factory()->create([
            'subject' => 'Order Confirmation {{order.number}}',
            'body_html' => '<p>Hello {{user.name}}, your order total is ${{order.total}}</p>',
        ]);

        $result = $this->service->generatePreview($template);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('subject', $result);
        $this->assertArrayHasKey('body_html', $result);
        $this->assertArrayHasKey('body_text', $result);
        
        // Should contain sample data
        $this->assertStringContainsString('ORD-', $result['subject']);
        $this->assertStringContainsString('Sample User', $result['body_html']);
        $this->assertStringContainsString('$', $result['body_html']);
    }

    /** @test */
    public function it_validates_required_variables()
    {
        $template = EmailTemplate::factory()->create([
            'subject' => 'Hello {{user.name}}',
            'variables' => ['user.name', 'user.email'],
        ]);

        // Create required variables
        EmailTemplateVariable::factory()->create([
            'key' => 'user.name',
            'is_required' => true,
        ]);
        
        EmailTemplateVariable::factory()->create([
            'key' => 'user.email',
            'is_required' => true,
        ]);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Required variables missing');

        // Missing required variables
        $this->service->renderTemplate($template, []);
    }

    /** @test */
    public function it_extracts_variables_from_template()
    {
        $template = EmailTemplate::factory()->create([
            'subject' => 'Hello {{user.name}}',
            'body_html' => '<p>Welcome {{user.name}} to {{site.name}}! Your order {{order.number}} is ready.</p>',
            'body_text' => 'Welcome {{user.name}} to {{site.name}}!',
        ]);

        $variables = $this->service->extractVariables($template);

        $this->assertIsArray($variables);
        $this->assertContains('user.name', $variables);
        $this->assertContains('site.name', $variables);
        $this->assertContains('order.number', $variables);
        $this->assertCount(3, array_unique($variables));
    }

    /** @test */
    public function it_handles_nested_object_variables()
    {
        $template = EmailTemplate::factory()->create([
            'body_html' => '<p>{{user.profile.first_name}} {{user.profile.last_name}}</p>',
        ]);

        $variables = [
            'user.profile.first_name' => 'John',
            'user.profile.last_name' => 'Doe',
        ];

        $result = $this->service->renderTemplate($template, $variables);
        
        $this->assertEquals('<p>John Doe</p>', $result['body_html']);
    }

    /** @test */
    public function it_handles_array_access_variables()
    {
        $template = EmailTemplate::factory()->create([
            'body_html' => '<p>First item: {{items.0.name}}</p>',
        ]);

        $variables = [
            'items.0.name' => 'First Product',
        ];

        $result = $this->service->renderTemplate($template, $variables);
        
        $this->assertEquals('<p>First item: First Product</p>', $result['body_html']);
    }

    /** @test */
    public function it_escapes_html_in_variables()
    {
        $template = EmailTemplate::factory()->create([
            'body_html' => '<p>{{user.comment}}</p>',
        ]);

        $variables = [
            'user.comment' => '<script>alert("xss")</script>',
        ];

        $result = $this->service->renderTemplate($template, $variables);
        
        $this->assertStringNotContainsString('<script>', $result['body_html']);
        $this->assertStringContainsString('&lt;script&gt;', $result['body_html']);
    }

    /** @test */
    public function it_handles_empty_template_content()
    {
        $template = EmailTemplate::factory()->create([
            'subject' => '',
            'body_html' => '',
            'body_text' => '',
        ]);

        $result = $this->service->renderTemplate($template, []);

        $this->assertEquals('', $result['subject']);
        $this->assertEquals('', $result['body_html']);
        $this->assertEquals('', $result['body_text']);
    }

    /** @test */
    public function it_handles_malformed_variable_syntax()
    {
        $template = EmailTemplate::factory()->create([
            'body_html' => '<p>{{incomplete.variable</p><p>{{another.var}}</p>',
        ]);

        $variables = [
            'another.var' => 'Complete Variable',
        ];

        $result = $this->service->renderTemplate($template, $variables);
        
        // Should handle complete variables and leave malformed ones as-is
        $this->assertStringContainsString('Complete Variable', $result['body_html']);
        $this->assertStringContainsString('{{incomplete.variable', $result['body_html']);
    }

    /** @test */
    public function it_generates_sample_data_for_different_variable_types()
    {
        // Create variables of different types
        EmailTemplateVariable::factory()->create([
            'key' => 'user.name',
            'data_type' => 'string',
            'category' => 'user',
        ]);
        
        EmailTemplateVariable::factory()->create([
            'key' => 'order.total',
            'data_type' => 'number',
            'category' => 'order',
        ]);
        
        EmailTemplateVariable::factory()->create([
            'key' => 'user.is_verified',
            'data_type' => 'boolean',
            'category' => 'user',
        ]);

        $sampleData = $this->service->generateAllSampleData();

        $this->assertIsArray($sampleData);
        $this->assertArrayHasKey('user.name', $sampleData);
        $this->assertArrayHasKey('order.total', $sampleData);
        $this->assertArrayHasKey('user.is_verified', $sampleData);
        
        $this->assertIsString($sampleData['user.name']);
        $this->assertIsNumeric($sampleData['order.total']);
        $this->assertIsBool($sampleData['user.is_verified']);
    }

    /** @test */
    public function it_validates_template_syntax()
    {
        $validTemplate = EmailTemplate::factory()->create([
            'body_html' => '<p>{{user.name}} - {{order.total}}</p>',
        ]);

        $invalidTemplate = EmailTemplate::factory()->create([
            'body_html' => '<p>{{user.name} - {{order.total</p>',
        ]);

        $this->assertTrue($this->service->validateTemplateSyntax($validTemplate));
        $this->assertFalse($this->service->validateTemplateSyntax($invalidTemplate));
    }

    /** @test */
    public function it_handles_special_characters_in_variables()
    {
        $template = EmailTemplate::factory()->create([
            'body_html' => '<p>Price: {{product.price}} & Tax: {{product.tax}}</p>',
        ]);

        $variables = [
            'product.price' => '$29.99',
            'product.tax' => '$2.40',
        ];

        $result = $this->service->renderTemplate($template, $variables);
        
        $this->assertStringContainsString('$29.99', $result['body_html']);
        $this->assertStringContainsString('$2.40', $result['body_html']);
    }
}
