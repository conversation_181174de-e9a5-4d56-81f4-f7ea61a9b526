<?php

namespace Tests\Unit\Services;

use App\Models\EmailTemplate;
use App\Models\EmailTemplateVariable;
use App\Services\EmailTemplateValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EmailTemplateValidationServiceTest extends TestCase
{
    use RefreshDatabase;

    private EmailTemplateValidationService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(\Database\Seeders\EmailTemplateSystemSeeder::class);
        $this->service = new EmailTemplateValidationService();
    }

    /** @test */
    public function it_validates_template_successfully()
    {
        $template = EmailTemplate::factory()->create([
            'name' => 'Test Template',
            'slug' => 'test-template-' . uniqid(),
            'subject' => 'Hello User',
            'body_html' => '<p>Welcome to our site!</p>',
            'body_text' => 'Welcome to our site!',
        ]);

        $errors = $this->service->validateTemplate($template);

        $this->assertIsArray($errors);
        $this->assertEmpty($errors);
    }

    /** @test */
    public function it_detects_missing_subject()
    {
        $template = EmailTemplate::factory()->create([
            'subject' => '',
            'body_html' => '<p>Content</p>',
        ]);

        $errors = $this->service->validateTemplate($template);

        $this->assertIsArray($errors);
        $this->assertNotEmpty($errors);
        $this->assertStringContainsString('subject', implode(' ', $errors));
    }

    /** @test */
    public function it_detects_missing_body_content()
    {
        $template = EmailTemplate::factory()->create([
            'subject' => 'Test Subject',
            'body_html' => '',
            'body_text' => '',
        ]);

        $errors = $this->service->validateTemplate($template);

        $this->assertIsArray($errors);
        $this->assertNotEmpty($errors);
        $this->assertStringContainsString('body', implode(' ', $errors));
    }

    /** @test */
    public function it_detects_invalid_html_structure()
    {
        $template = EmailTemplate::factory()->create([
            'subject' => 'Test Subject',
            'body_html' => '<p>Unclosed paragraph<div>Nested incorrectly</p></div>',
        ]);

        $errors = $this->service->validateTemplate($template);

        $this->assertIsArray($errors);
        // HTML validation might detect structural issues
    }

    /** @test */
    public function it_detects_malformed_variables()
    {
        $template = EmailTemplate::factory()->create([
            'subject' => 'Hello {{user.name',
            'body_html' => '<p>Welcome {{incomplete.var to our site!</p>',
        ]);

        $errors = $this->service->validateTemplate($template);

        $this->assertIsArray($errors);
        $this->assertNotEmpty($errors);
        $this->assertStringContainsString('variable', implode(' ', $errors));
    }

    /** @test */
    public function it_validates_variable_syntax()
    {
        $validVariables = ['{{user.name}}', '{{order.total}}', '{{site.url}}'];
        $invalidVariables = ['{{user.name', 'user.name}}', '{{}}', '{{ }}'];

        foreach ($validVariables as $variable) {
            $isValid = $this->service->validateVariableSyntax($variable);
            $this->assertTrue($isValid, "Variable $variable should be valid");
        }

        foreach ($invalidVariables as $variable) {
            $isValid = $this->service->validateVariableSyntax($variable);
            $this->assertFalse($isValid, "Variable $variable should be invalid");
        }
    }

    /** @test */
    public function it_validates_required_variables_are_present()
    {
        // Create required variables
        EmailTemplateVariable::factory()->create([
            'key' => 'user.name',
            'is_required' => true,
        ]);
        
        EmailTemplateVariable::factory()->create([
            'key' => 'user.email',
            'is_required' => true,
        ]);

        $template = EmailTemplate::factory()->create([
            'subject' => 'Hello {{user.name}}',
            'body_html' => '<p>Contact: {{user.email}}</p>',
            'variables' => ['user.name', 'user.email'],
        ]);

        $providedVariables = ['user.name' => 'John'];

        $errors = $this->service->validateRequiredVariables($template, $providedVariables);

        $this->assertIsArray($errors);
        $this->assertNotEmpty($errors);
        $this->assertStringContainsString('user.email', implode(' ', $errors));
    }

    /** @test */
    public function it_validates_variable_data_types()
    {
        EmailTemplateVariable::factory()->create([
            'key' => 'order.total',
            'data_type' => 'number',
        ]);

        $validData = ['order.total' => 29.99];
        $invalidData = ['order.total' => 'not a number'];

        $validErrors = $this->service->validateVariableTypes($validData);
        $invalidErrors = $this->service->validateVariableTypes($invalidData);

        $this->assertEmpty($validErrors);
        $this->assertNotEmpty($invalidErrors);
    }

    /** @test */
    public function it_extracts_variables_from_template_content()
    {
        $template = EmailTemplate::factory()->create([
            'subject' => 'Order {{order.number}} for {{user.name}}',
            'body_html' => '<p>Hello {{user.name}}, your order {{order.number}} total is {{order.total}}</p>',
            'body_text' => 'Hello {{user.name}}, total: {{order.total}}',
        ]);

        $variables = $this->service->extractTemplateVariables($template);

        $this->assertIsArray($variables);
        $this->assertContains('user.name', $variables);
        $this->assertContains('order.number', $variables);
        $this->assertContains('order.total', $variables);
        $this->assertCount(3, array_unique($variables));
    }

    /** @test */
    public function it_validates_html_content_structure()
    {
        $validHtml = '<html><body><p>Valid content</p></body></html>';
        $invalidHtml = '<p>Unclosed paragraph<div>Wrong nesting</p></div>';

        $validErrors = $this->service->validateHtmlStructure($validHtml);
        $invalidErrors = $this->service->validateHtmlStructure($invalidHtml);

        $this->assertIsArray($validErrors);
        $this->assertIsArray($invalidErrors);
        // HTML validation results may vary based on implementation
    }

    /** @test */
    public function it_checks_for_dangerous_html_content()
    {
        $dangerousHtml = '<script>alert("xss")</script><p>Content</p>';
        $safeHtml = '<p>Safe content with <strong>formatting</strong></p>';

        $dangerousErrors = $this->service->validateHtmlSecurity($dangerousHtml);
        $safeErrors = $this->service->validateHtmlSecurity($safeHtml);

        $this->assertNotEmpty($dangerousErrors);
        $this->assertEmpty($safeErrors);
    }

    /** @test */
    public function it_validates_template_completeness()
    {
        $completeTemplate = EmailTemplate::factory()->create([
            'name' => 'Complete Template',
            'subject' => 'Subject with {{user.name}}',
            'body_html' => '<p>HTML content with {{user.name}}</p>',
            'body_text' => 'Text content with {{user.name}}',
        ]);

        $incompleteTemplate = EmailTemplate::factory()->create([
            'name' => '',
            'subject' => '',
            'body_html' => '',
            'body_text' => '',
        ]);

        $completeErrors = $this->service->validateTemplateCompleteness($completeTemplate);
        $incompleteErrors = $this->service->validateTemplateCompleteness($incompleteTemplate);

        $this->assertEmpty($completeErrors);
        $this->assertNotEmpty($incompleteErrors);
    }

    /** @test */
    public function it_validates_variable_consistency()
    {
        $template = EmailTemplate::factory()->create([
            'subject' => 'Hello {{user.name}}',
            'body_html' => '<p>Welcome {{user.name}} and {{user.email}}</p>',
            'variables' => ['user.name'], // Missing user.email
        ]);

        $errors = $this->service->validateVariableConsistency($template);

        $this->assertIsArray($errors);
        $this->assertNotEmpty($errors);
        $this->assertStringContainsString('user.email', implode(' ', $errors));
    }

    /** @test */
    public function it_provides_comprehensive_validation_report()
    {
        $template = EmailTemplate::factory()->create([
            'name' => 'Test Template',
            'subject' => 'Hello {{user.name}}',
            'body_html' => '<p>Welcome {{user.name}} to {{site.name}}!</p>',
            'body_text' => 'Welcome {{user.name}} to {{site.name}}!',
        ]);

        $report = $this->service->getValidationReport($template);

        $this->assertIsArray($report);
        $this->assertArrayHasKey('is_valid', $report);
        $this->assertArrayHasKey('errors', $report);
        $this->assertArrayHasKey('warnings', $report);
        $this->assertArrayHasKey('variables_found', $report);
        $this->assertIsBool($report['is_valid']);
        $this->assertIsArray($report['errors']);
        $this->assertIsArray($report['warnings']);
        $this->assertIsArray($report['variables_found']);
    }

    /** @test */
    public function it_handles_empty_template_gracefully()
    {
        $emptyTemplate = EmailTemplate::factory()->create([
            'subject' => null,
            'body_html' => null,
            'body_text' => null,
        ]);

        $errors = $this->service->validateTemplate($emptyTemplate);

        $this->assertIsArray($errors);
        $this->assertNotEmpty($errors);
    }

    /** @test */
    public function it_validates_conditional_syntax()
    {
        $validConditional = '{{#if user.is_premium}}Premium content{{else}}Regular content{{/if}}';
        $invalidConditional = '{{#if user.is_premium}}Premium content{{/if'; // Missing closing brace

        $validErrors = $this->service->validateConditionalSyntax($validConditional);
        $invalidErrors = $this->service->validateConditionalSyntax($invalidConditional);

        $this->assertEmpty($validErrors);
        $this->assertNotEmpty($invalidErrors);
    }

    /** @test */
    public function it_validates_loop_syntax()
    {
        $validLoop = '{{#each order.items}}<li>{{name}} - {{price}}</li>{{/each}}';
        $invalidLoop = '{{#each order.items}}<li>{{name}} - {{price}}</li>'; // Missing closing tag

        $validErrors = $this->service->validateLoopSyntax($validLoop);
        $invalidErrors = $this->service->validateLoopSyntax($invalidLoop);

        $this->assertEmpty($validErrors);
        $this->assertNotEmpty($invalidErrors);
    }
}
