<?php

namespace Tests\Unit;

use Tests\TestCase;

class OrderResourceRouteFixTest extends TestCase
{
    /**
     * Test that the route names are correctly prefixed
     */
    public function test_route_names_are_correctly_prefixed(): void
    {
        // Test that the cancel route exists with correct prefix
        $cancelUrl = route('api.client.orders.cancel', 'test-uuid');
        $this->assertStringContainsString('/api/client/orders/test-uuid/cancel', $cancelUrl);

        // Test that the show route exists with correct prefix
        $showUrl = route('api.client.orders.show', 'test-uuid');
        $this->assertStringContainsString('/api/client/orders/test-uuid', $showUrl);
    }

    /**
     * Test that old route names no longer exist
     */
    public function test_old_route_names_do_not_exist(): void
    {
        $this->expectException(\Symfony\Component\Routing\Exception\RouteNotFoundException::class);
        $this->expectExceptionMessage('Route [client.orders.cancel] not defined');

        route('client.orders.cancel', 'test-uuid');
    }

    /**
     * Test that the route fix resolves the checkout error
     */
    public function test_route_fix_resolves_checkout_error(): void
    {
        // Test that we can generate the route without errors
        $cancelUrl = route('api.client.orders.cancel', 'test-uuid-123');
        $showUrl = route('api.client.orders.show', 'test-uuid-123');

        $this->assertNotEmpty($cancelUrl);
        $this->assertNotEmpty($showUrl);

        // Verify the URLs are correctly formatted
        $this->assertStringContainsString('api/client/orders', $cancelUrl);
        $this->assertStringContainsString('api/client/orders', $showUrl);
        $this->assertStringContainsString('cancel', $cancelUrl);
        $this->assertStringContainsString('test-uuid-123', $showUrl);
    }
}
