<?php

namespace Tests\Unit\Models;

use App\Models\CartItem;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ShoppingCart;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CartItemTest extends TestCase
{
    use RefreshDatabase;

    protected CartItem $cartItem;
    protected ShoppingCart $cart;
    protected Product $product;
    protected Vendor $vendor;

    protected function setUp(): void
    {
        parent::setUp();

        $this->vendor = Vendor::factory()->active()->create();
        $this->product = Product::factory()->active()->create([
            'vendor_id' => $this->vendor->id,
            'title_en' => 'Test Product',
            'regular_price' => 100.00,
            'offer_price' => 80.00,
        ]);

        $this->cart = ShoppingCart::factory()->create();
        
        $this->cartItem = CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'base_price' => 120.00,
            'total_price' => 200.00,
            'discount_amount' => 10.00,
            'product_snapshot' => [
                'name' => 'Test Product',
                'image' => 'test-image.jpg',
            ],
        ]);
    }

    public function test_cart_item_belongs_to_cart(): void
    {
        $this->assertInstanceOf(ShoppingCart::class, $this->cartItem->cart);
        $this->assertEquals($this->cart->id, $this->cartItem->cart->id);
    }

    public function test_cart_item_belongs_to_product(): void
    {
        $this->assertInstanceOf(Product::class, $this->cartItem->product);
        $this->assertEquals($this->product->id, $this->cartItem->product->id);
    }

    public function test_cart_item_belongs_to_vendor(): void
    {
        $this->assertInstanceOf(Vendor::class, $this->cartItem->vendor);
        $this->assertEquals($this->vendor->id, $this->cartItem->vendor->id);
    }

    public function test_cart_item_belongs_to_variant(): void
    {
        $variant = ProductVariant::factory()->create(['product_id' => $this->product->id]);
        
        $cartItemWithVariant = CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'variant_id' => $variant->id,
        ]);

        $this->assertInstanceOf(ProductVariant::class, $cartItemWithVariant->variant);
        $this->assertEquals($variant->id, $cartItemWithVariant->variant->id);
    }

    public function test_final_unit_price_accessor(): void
    {
        // unit_price: 100.00, discount_amount: 10.00, quantity: 2
        // final_unit_price = 100.00 - (10.00 / 2) = 95.00
        $this->assertEquals(95.00, $this->cartItem->final_unit_price);
    }

    public function test_savings_amount_accessor_with_promotional_price(): void
    {
        $this->cartItem->update([
            'base_price' => 120.00,
            'promotional_price' => 100.00,
            'quantity' => 2,
        ]);

        // savings = (120.00 - 100.00) * 2 = 40.00
        $this->assertEquals(40.00, $this->cartItem->savings_amount);
    }

    public function test_savings_amount_accessor_with_discount(): void
    {
        $this->cartItem->update([
            'promotional_price' => null,
            'discount_amount' => 15.00,
        ]);

        $this->assertEquals(15.00, $this->cartItem->savings_amount);
    }

    public function test_product_name_accessor_from_snapshot(): void
    {
        $this->assertEquals('Test Product', $this->cartItem->product_name);
    }

    public function test_product_name_accessor_fallback_to_product(): void
    {
        $this->cartItem->update(['product_snapshot' => []]);

        $this->assertEquals($this->product->name_en, $this->cartItem->product_name);
    }

    public function test_product_image_accessor_from_snapshot(): void
    {
        $this->assertEquals('test-image.jpg', $this->cartItem->product_image);
    }

    public function test_calculate_prices_on_creation(): void
    {
        $newItem = CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 3,
            'unit_price' => 50.00,
        ]);

        $this->assertEquals(150.00, $newItem->total_price); // 3 * 50.00
    }

    public function test_calculate_prices_on_quantity_update(): void
    {
        $this->cartItem->update(['quantity' => 5]);

        $this->assertEquals(500.00, $this->cartItem->fresh()->total_price); // 5 * 100.00
    }

    public function test_update_quantity_method(): void
    {
        $result = $this->cartItem->updateQuantity(4);

        $this->assertTrue($result);
        $this->assertEquals(4, $this->cartItem->fresh()->quantity);
        $this->assertEquals(400.00, $this->cartItem->fresh()->total_price);
    }

    public function test_update_quantity_to_zero_deletes_item(): void
    {
        $itemId = $this->cartItem->id;
        
        $result = $this->cartItem->updateQuantity(0);

        $this->assertTrue($result);
        $this->assertDatabaseMissing('cart_items', ['id' => $itemId]);
    }

    public function test_increase_quantity_method(): void
    {
        $result = $this->cartItem->increaseQuantity(3);

        $this->assertTrue($result);
        $this->assertEquals(5, $this->cartItem->fresh()->quantity); // 2 + 3
    }

    public function test_decrease_quantity_method(): void
    {
        $this->cartItem->update(['quantity' => 5]);
        
        $result = $this->cartItem->decreaseQuantity(2);

        $this->assertTrue($result);
        $this->assertEquals(3, $this->cartItem->fresh()->quantity); // 5 - 2
    }

    public function test_decrease_quantity_below_zero_deletes_item(): void
    {
        $itemId = $this->cartItem->id;
        
        $result = $this->cartItem->decreaseQuantity(5); // More than current quantity

        $this->assertTrue($result);
        $this->assertDatabaseMissing('cart_items', ['id' => $itemId]);
    }

    public function test_apply_discount_method(): void
    {
        $this->cartItem->applyDiscount(30.00);

        $this->assertEquals(30.00, $this->cartItem->fresh()->discount_amount);
    }

    public function test_apply_discount_cannot_exceed_total_price(): void
    {
        $this->cartItem->applyDiscount(300.00); // More than total_price of 200.00

        $this->assertEquals(200.00, $this->cartItem->fresh()->discount_amount);
    }

    public function test_create_product_snapshot(): void
    {
        $snapshot = $this->cartItem->createProductSnapshot();

        $this->assertArrayHasKey('id', $snapshot);
        $this->assertArrayHasKey('name', $snapshot);
        $this->assertArrayHasKey('sku', $snapshot);
        $this->assertArrayHasKey('vendor', $snapshot);
        $this->assertArrayHasKey('captured_at', $snapshot);
        
        $this->assertEquals($this->product->id, $snapshot['id']);
        $this->assertEquals($this->product->name_en, $snapshot['name']);
        $this->assertEquals($this->vendor->id, $snapshot['vendor']['id']);
    }

    public function test_create_product_snapshot_with_variant(): void
    {
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'sku' => 'VAR-123',
        ]);

        $this->cartItem->update(['variant_id' => $variant->id]);

        $snapshot = $this->cartItem->createProductSnapshot();

        $this->assertArrayHasKey('variant', $snapshot);
        $this->assertNotNull($snapshot['variant']);
        $this->assertEquals($variant->id, $snapshot['variant']['id']);
        $this->assertEquals('VAR-123', $snapshot['variant']['sku']);
    }

    public function test_is_available_method(): void
    {
        $this->assertTrue($this->cartItem->isAvailable());

        // Test with inactive product
        $this->product->update(['is_active' => false]);
        $this->assertFalse($this->cartItem->fresh()->isAvailable());
    }

    public function test_is_available_method_with_variant(): void
    {
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'is_active' => true,
        ]);

        $this->cartItem->update(['variant_id' => $variant->id]);
        $this->assertTrue($this->cartItem->isAvailable());

        // Test with inactive variant
        $variant->update(['is_active' => false]);
        $this->assertFalse($this->cartItem->fresh()->isAvailable());
    }

    public function test_has_insufficient_stock_method(): void
    {
        // Create inventory record for the product
        \App\Models\Inventory::create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'stock' => 5,
            'reserved' => 0,
        ]);

        $this->cartItem->update(['quantity' => 3]);

        $this->assertFalse($this->cartItem->hasInsufficientStock());

        $this->cartItem->update(['quantity' => 7]); // More than stock
        $this->assertTrue($this->cartItem->hasInsufficientStock());
    }

    public function test_has_insufficient_stock_method_with_variant(): void
    {
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'stock' => 3,
        ]);

        $this->cartItem->update([
            'variant_id' => $variant->id,
            'quantity' => 2,
        ]);

        $this->assertFalse($this->cartItem->hasInsufficientStock());

        $this->cartItem->update(['quantity' => 5]); // More than variant stock
        $this->assertTrue($this->cartItem->hasInsufficientStock());
    }

    public function test_casts_are_applied(): void
    {
        $this->assertIsInt($this->cartItem->quantity);
        $this->assertIsString($this->cartItem->unit_price);
        $this->assertIsString($this->cartItem->total_price);
        $this->assertIsString($this->cartItem->discount_amount);
        $this->assertIsString($this->cartItem->tax_amount);
        $this->assertIsString($this->cartItem->base_price);

        if ($this->cartItem->promotional_price) {
            $this->assertIsString($this->cartItem->promotional_price);
        }
        
        $this->assertIsArray($this->cartItem->product_snapshot);
        
        if ($this->cartItem->customizations) {
            $this->assertIsArray($this->cartItem->customizations);
        }
        
        if ($this->cartItem->metadata) {
            $this->assertIsArray($this->cartItem->metadata);
        }
        
        if ($this->cartItem->applied_discounts) {
            $this->assertIsArray($this->cartItem->applied_discounts);
        }
    }

    public function test_cart_totals_recalculated_on_item_save(): void
    {
        // Store the original updated_at timestamp
        $originalUpdatedAt = $this->cart->updated_at;

        // Add a small delay to ensure timestamp difference
        sleep(1);

        $this->cartItem->update(['quantity' => 10]);

        // The cart should be touched/updated when item is saved
        $this->assertNotEquals(
            $originalUpdatedAt->timestamp,
            $this->cart->fresh()->updated_at->timestamp
        );
    }

    public function test_cart_totals_recalculated_on_item_delete(): void
    {
        $originalUpdatedAt = $this->cart->updated_at;
        
        sleep(1); // Ensure time difference
        
        $this->cartItem->delete();
        
        // The cart should be touched/updated when item is deleted
        $this->assertNotEquals(
            $originalUpdatedAt->timestamp,
            $this->cart->fresh()->updated_at->timestamp
        );
    }
}
