<?php

namespace Tests\Unit\Models;

use App\Models\EmailTemplateVariable;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EmailTemplateVariableTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(\Database\Seeders\EmailTemplateSystemSeeder::class);
    }

    /** @test */
    public function it_has_fillable_attributes()
    {
        $fillable = [
            'name',
            'key',
            'description',
            'data_type',
            'default_value',
            'is_required',
            'category',
            'example_value',
        ];

        $variable = new EmailTemplateVariable();
        $this->assertEquals($fillable, $variable->getFillable());
    }

    /** @test */
    public function it_casts_attributes_correctly()
    {
        $variable = EmailTemplateVariable::factory()->create([
            'is_required' => '1',
        ]);

        $this->assertIsBool($variable->is_required);
        $this->assertTrue($variable->is_required);
    }

    /** @test */
    public function it_has_category_scope()
    {
        EmailTemplateVariable::factory()->category('user')->create();
        EmailTemplateVariable::factory()->category('order')->create();
        
        $userVariables = EmailTemplateVariable::category('user')->count();
        $orderVariables = EmailTemplateVariable::category('order')->count();
        
        $this->assertGreaterThan(0, $userVariables);
        $this->assertGreaterThan(0, $orderVariables);
    }

    /** @test */
    public function it_has_required_scope()
    {
        EmailTemplateVariable::factory()->required()->create();
        EmailTemplateVariable::factory()->optional()->create();
        
        $requiredVariables = EmailTemplateVariable::required()->count();
        $optionalVariables = EmailTemplateVariable::optional()->count();
        
        $this->assertGreaterThan(0, $requiredVariables);
        $this->assertGreaterThan(0, $optionalVariables);
    }

    /** @test */
    public function it_has_data_type_scope()
    {
        EmailTemplateVariable::factory()->dataType('string')->create();
        EmailTemplateVariable::factory()->dataType('number')->create();
        
        $stringVariables = EmailTemplateVariable::dataType('string')->count();
        $numberVariables = EmailTemplateVariable::dataType('number')->count();
        
        $this->assertGreaterThan(0, $stringVariables);
        $this->assertGreaterThan(0, $numberVariables);
    }

    /** @test */
    public function it_formats_key_correctly()
    {
        $variable = EmailTemplateVariable::first();
        $formattedKey = $variable->getFormattedKey();
        
        $this->assertStringStartsWith('{{', $formattedKey);
        $this->assertStringEndsWith('}}', $formattedKey);
        $this->assertStringContainsString($variable->key, $formattedKey);
    }

    /** @test */
    public function it_gets_typed_default_value()
    {
        // Test string type
        $stringVar = EmailTemplateVariable::factory()->create([
            'data_type' => 'string',
            'default_value' => 'test',
        ]);
        
        $this->assertIsString($stringVar->getTypedDefaultValue());
        $this->assertEquals('test', $stringVar->getTypedDefaultValue());
        
        // Test number type
        $numberVar = EmailTemplateVariable::factory()->create([
            'data_type' => 'number',
            'default_value' => '123',
        ]);
        
        $this->assertIsNumeric($numberVar->getTypedDefaultValue());
        $this->assertEquals(123, $numberVar->getTypedDefaultValue());
        
        // Test boolean type
        $boolVar = EmailTemplateVariable::factory()->create([
            'data_type' => 'boolean',
            'default_value' => 'true',
        ]);
        
        $this->assertIsBool($boolVar->getTypedDefaultValue());
        $this->assertTrue($boolVar->getTypedDefaultValue());
    }

    /** @test */
    public function it_can_be_created_with_valid_data()
    {
        $data = [
            'name' => 'Test Variable',
            'key' => 'test.variable',
            'description' => 'A test variable',
            'data_type' => 'string',
            'default_value' => 'default',
            'is_required' => true,
            'category' => 'test',
            'example_value' => 'example',
        ];

        $variable = EmailTemplateVariable::create($data);

        $this->assertInstanceOf(EmailTemplateVariable::class, $variable);
        $this->assertEquals('Test Variable', $variable->name);
        $this->assertEquals('test.variable', $variable->key);
        $this->assertTrue($variable->is_required);
    }

    /** @test */
    public function scopes_filter_correctly()
    {
        // Create test variables
        EmailTemplateVariable::factory()->category('user')->required()->create();
        EmailTemplateVariable::factory()->category('order')->optional()->create();
        EmailTemplateVariable::factory()->dataType('string')->create();
        EmailTemplateVariable::factory()->dataType('number')->create();
        
        // Test category scope
        $userVariables = EmailTemplateVariable::category('user')->get();
        foreach ($userVariables as $variable) {
            $this->assertEquals('user', $variable->category);
        }
        
        // Test required scope
        $requiredVariables = EmailTemplateVariable::required()->get();
        foreach ($requiredVariables as $variable) {
            $this->assertTrue($variable->is_required);
        }
        
        // Test optional scope
        $optionalVariables = EmailTemplateVariable::optional()->get();
        foreach ($optionalVariables as $variable) {
            $this->assertFalse($variable->is_required);
        }
        
        // Test data type scope
        $stringVariables = EmailTemplateVariable::dataType('string')->get();
        foreach ($stringVariables as $variable) {
            $this->assertEquals('string', $variable->data_type);
        }
    }

    /** @test */
    public function it_handles_null_default_values()
    {
        $variable = EmailTemplateVariable::factory()->create([
            'default_value' => null,
        ]);
        
        $this->assertNull($variable->getTypedDefaultValue());
    }

    /** @test */
    public function it_handles_array_data_type()
    {
        $variable = EmailTemplateVariable::factory()->create([
            'data_type' => 'array',
            'default_value' => '[]',
        ]);
        
        $typedValue = $variable->getTypedDefaultValue();
        $this->assertIsArray($typedValue);
        $this->assertEmpty($typedValue);
    }

    /** @test */
    public function it_handles_date_data_type()
    {
        $variable = EmailTemplateVariable::factory()->create([
            'data_type' => 'date',
            'default_value' => '2025-01-28',
        ]);
        
        $typedValue = $variable->getTypedDefaultValue();
        $this->assertIsString($typedValue);
        $this->assertEquals('2025-01-28', $typedValue);
    }

    /** @test */
    public function it_validates_data_types()
    {
        $validTypes = ['string', 'number', 'boolean', 'date', 'array'];
        
        foreach ($validTypes as $type) {
            $variable = EmailTemplateVariable::factory()->create(['data_type' => $type]);
            $this->assertContains($variable->data_type, $validTypes);
        }
    }

    /** @test */
    public function it_validates_categories()
    {
        $validCategories = ['user', 'order', 'vendor', 'system', 'authentication'];
        
        foreach ($validCategories as $category) {
            $variable = EmailTemplateVariable::factory()->create(['category' => $category]);
            $this->assertContains($variable->category, $validCategories);
        }
    }
}
