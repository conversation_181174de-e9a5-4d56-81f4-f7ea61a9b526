<?php

namespace Tests\Unit;

use App\Models\Blog;
use App\Models\BlogCategory;
use App\Models\BlogComment;
use App\Models\User;
use Database\Seeders\BlogSystemSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BlogSystemSeederTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_creates_blog_categories_successfully()
    {
        // Ensure we have at least one user for blog creation
        User::factory()->create();

        $seeder = new BlogSystemSeeder();
        $seeder->run();

        // Check that categories were created
        $this->assertGreaterThanOrEqual(8, BlogCategory::count());
        $this->assertLessThanOrEqual(12, BlogCategory::count());

        // Check specific categories exist
        $this->assertDatabaseHas('blog_categories', [
            'slug' => 'product-reviews',
            'status' => 'active'
        ]);

        $this->assertDatabaseHas('blog_categories', [
            'slug' => 'shopping-tips',
            'status' => 'active'
        ]);
    }

    /** @test */
    public function it_creates_blog_posts_with_correct_distribution()
    {
        // Ensure we have at least one user for blog creation
        User::factory()->create();

        $seeder = new BlogSystemSeeder();
        $seeder->run();

        // Check that blogs were created
        $totalBlogs = Blog::count();
        $this->assertGreaterThanOrEqual(50, $totalBlogs);
        $this->assertLessThanOrEqual(80, $totalBlogs);

        // Check status distribution (approximately 80% published, 20% draft)
        $publishedCount = Blog::where('status', 'published')->count();
        $draftCount = Blog::where('status', 'draft')->count();

        $publishedPercentage = ($publishedCount / $totalBlogs) * 100;
        $this->assertGreaterThan(70, $publishedPercentage); // Allow some variance
        $this->assertLessThan(90, $publishedPercentage);

        // Check that published blogs have published_at dates
        $publishedBlogsWithoutDate = Blog::where('status', 'published')
            ->whereNull('published_at')
            ->count();
        $this->assertEquals(0, $publishedBlogsWithoutDate);

        // Check that draft blogs don't have published_at dates
        $draftBlogsWithDate = Blog::where('status', 'draft')
            ->whereNotNull('published_at')
            ->count();
        $this->assertEquals(0, $draftBlogsWithDate);
    }

    /** @test */
    public function it_creates_blog_comments_with_correct_structure()
    {
        // Ensure we have users for comment creation
        User::factory()->count(5)->create();

        $seeder = new BlogSystemSeeder();
        $seeder->run();

        // Check that comments were created
        $totalComments = BlogComment::count();
        $this->assertGreaterThanOrEqual(200, $totalComments);
        $this->assertLessThanOrEqual(300, $totalComments);

        // Check approval distribution (approximately 70% approved)
        $approvedCount = BlogComment::where('is_approved', true)->count();
        $approvedPercentage = ($approvedCount / $totalComments) * 100;
        $this->assertGreaterThan(60, $approvedPercentage); // Allow some variance
        $this->assertLessThan(80, $approvedPercentage);

        // Check that replies exist (approximately 20% of comments)
        $replyCount = BlogComment::whereNotNull('parent_id')->count();
        $replyPercentage = ($replyCount / $totalComments) * 100;
        $this->assertGreaterThan(15, $replyPercentage); // Allow some variance
        $this->assertLessThan(25, $replyPercentage);

        // Check that all comments belong to published blogs
        $commentsOnDraftBlogs = BlogComment::join('blogs', 'blog_comments.blog_id', '=', 'blogs.id')
            ->where('blogs.status', 'draft')
            ->count();
        $this->assertEquals(0, $commentsOnDraftBlogs);
    }

    /** @test */
    public function it_maintains_referential_integrity()
    {
        // Ensure we have users for creation
        User::factory()->count(3)->create();

        $seeder = new BlogSystemSeeder();
        $seeder->run();

        // Check that all blogs have valid categories
        $blogsWithInvalidCategory = Blog::whereNotExists(function ($query) {
            $query->select('id')
                ->from('blog_categories')
                ->whereColumn('blog_categories.id', 'blogs.blog_category_id');
        })->count();
        $this->assertEquals(0, $blogsWithInvalidCategory);

        // Check that all blogs have valid users
        $blogsWithInvalidUser = Blog::whereNotExists(function ($query) {
            $query->select('id')
                ->from('users')
                ->whereColumn('users.id', 'blogs.user_id');
        })->count();
        $this->assertEquals(0, $blogsWithInvalidUser);

        // Check that all comments have valid blogs
        $commentsWithInvalidBlog = BlogComment::whereNotExists(function ($query) {
            $query->select('id')
                ->from('blogs')
                ->whereColumn('blogs.id', 'blog_comments.blog_id');
        })->count();
        $this->assertEquals(0, $commentsWithInvalidBlog);

        // Check that all comments have valid users
        $commentsWithInvalidUser = BlogComment::whereNotExists(function ($query) {
            $query->select('id')
                ->from('users')
                ->whereColumn('users.id', 'blog_comments.user_id');
        })->count();
        $this->assertEquals(0, $commentsWithInvalidUser);

        // Check that all reply comments have valid parent comments
        $repliesWithInvalidParent = BlogComment::whereNotNull('parent_id')
            ->whereNotExists(function ($query) {
                $query->select('id')
                    ->from('blog_comments as parent_comments')
                    ->whereColumn('parent_comments.id', 'blog_comments.parent_id');
            })->count();
        $this->assertEquals(0, $repliesWithInvalidParent);
    }

    /** @test */
    public function it_can_be_run_multiple_times_safely()
    {
        // Ensure we have users for creation
        User::factory()->count(2)->create();

        $seeder = new BlogSystemSeeder();

        // Run seeder first time
        $seeder->run();
        $firstRunCategoryCount = BlogCategory::count();
        $firstRunBlogCount = Blog::count();
        $firstRunCommentCount = BlogComment::count();

        // Run seeder second time (should clear and recreate in development)
        $seeder->run();
        $secondRunCategoryCount = BlogCategory::count();
        $secondRunBlogCount = Blog::count();
        $secondRunCommentCount = BlogComment::count();

        // In development environment, data should be recreated
        if (app()->environment(['local', 'development'])) {
            $this->assertEquals($firstRunCategoryCount, $secondRunCategoryCount);
            $this->assertGreaterThanOrEqual(50, $secondRunBlogCount);
            $this->assertGreaterThanOrEqual(200, $secondRunCommentCount);
        }
    }

    /** @test */
    public function it_creates_multilingual_content()
    {
        // Ensure we have users for creation
        User::factory()->create();

        $seeder = new BlogSystemSeeder();
        $seeder->run();

        // Check that categories have both English and Arabic titles
        $categoriesWithoutEnglish = BlogCategory::whereNull('title_en')->count();
        $categoriesWithoutArabic = BlogCategory::whereNull('title_ar')->count();

        $this->assertEquals(0, $categoriesWithoutEnglish);
        $this->assertEquals(0, $categoriesWithoutArabic);

        // Check that blogs have both English and Arabic content
        $blogsWithoutEnglishTitle = Blog::whereNull('title_en')->count();
        $blogsWithoutArabicTitle = Blog::whereNull('title_ar')->count();
        $blogsWithoutEnglishContent = Blog::whereNull('content_en')->count();
        $blogsWithoutArabicContent = Blog::whereNull('content_ar')->count();

        $this->assertEquals(0, $blogsWithoutEnglishTitle);
        $this->assertEquals(0, $blogsWithoutArabicTitle);
        $this->assertEquals(0, $blogsWithoutEnglishContent);
        $this->assertEquals(0, $blogsWithoutArabicContent);
    }

    /** @test */
    public function it_creates_seo_friendly_content()
    {
        // Ensure we have users for creation
        User::factory()->create();

        $seeder = new BlogSystemSeeder();
        $seeder->run();

        // Check that all blogs have SEO fields
        $blogsWithoutSlug = Blog::whereNull('slug')->count();
        $blogsWithoutMetaTitle = Blog::whereNull('meta_title')->count();
        $blogsWithoutMetaDescription = Blog::whereNull('meta_description')->count();
        $blogsWithoutKeywords = Blog::whereNull('keywords')->count();

        $this->assertEquals(0, $blogsWithoutSlug);
        $this->assertEquals(0, $blogsWithoutMetaTitle);
        $this->assertEquals(0, $blogsWithoutMetaDescription);
        $this->assertEquals(0, $blogsWithoutKeywords);

        // Check that slugs are unique
        $totalBlogs = Blog::count();
        $uniqueSlugs = Blog::distinct('slug')->count();
        $this->assertEquals($totalBlogs, $uniqueSlugs);
    }
}
