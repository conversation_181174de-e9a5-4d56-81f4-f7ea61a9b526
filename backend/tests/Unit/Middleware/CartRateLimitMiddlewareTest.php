<?php

namespace Tests\Unit\Middleware;

use App\Http\Middleware\CartRateLimitMiddleware;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class CartRateLimitMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    protected CartRateLimitMiddleware $middleware;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->middleware = new CartRateLimitMiddleware();
        $this->user = User::factory()->create();
        
        // Clear cache before each test
        Cache::flush();
    }

    /** @test */
    public function it_allows_requests_within_rate_limit()
    {
        $request = Request::create('/api/client/cart', 'POST');
        $request->setUserResolver(fn() => $this->user);
        
        $response = $this->middleware->handle($request, function ($req) {
            return response()->json(['success' => true]);
        });
        
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertArrayHasKey('X-RateLimit-Limit', $response->headers->all());
        $this->assertArrayHasKey('X-RateLimit-Remaining', $response->headers->all());
    }

    /** @test */
    public function it_blocks_requests_exceeding_rate_limit()
    {
        $request = Request::create('/api/client/cart', 'POST');
        $request->setUserResolver(fn() => $this->user);
        
        // Simulate exceeding rate limit by setting cache
        $cacheKey = "cart_rate_limit:cart.create:user:{$this->user->id}";
        Cache::put($cacheKey, 10, 60); // Set to limit
        
        $response = $this->middleware->handle($request, function ($req) {
            return response()->json(['success' => true]);
        });
        
        $this->assertEquals(Response::HTTP_TOO_MANY_REQUESTS, $response->getStatusCode());
        
        $data = json_decode($response->getContent(), true);
        $this->assertFalse($data['success']);
        $this->assertEquals('Rate limit exceeded. Please try again later.', $data['message']);
        $this->assertArrayHasKey('retry_after', $data['data']);
    }

    /** @test */
    public function it_determines_correct_operation_type_for_cart_creation()
    {
        $request = Request::create('/api/client/cart', 'POST');
        
        $operationType = $this->invokeMethod($this->middleware, 'determineOperationType', [$request, null]);
        
        $this->assertEquals('cart.create', $operationType);
    }

    /** @test */
    public function it_determines_correct_operation_type_for_add_item()
    {
        $request = Request::create('/api/client/cart/123/items', 'POST');
        
        $operationType = $this->invokeMethod($this->middleware, 'determineOperationType', [$request, null]);
        
        $this->assertEquals('cart.add_item', $operationType);
    }

    /** @test */
    public function it_determines_correct_operation_type_for_update_item()
    {
        $request = Request::create('/api/client/cart/123/items/456', 'PUT');
        
        $operationType = $this->invokeMethod($this->middleware, 'determineOperationType', [$request, null]);
        
        $this->assertEquals('cart.update_item', $operationType);
    }

    /** @test */
    public function it_determines_correct_operation_type_for_remove_item()
    {
        $request = Request::create('/api/client/cart/123/items/456', 'DELETE');
        
        $operationType = $this->invokeMethod($this->middleware, 'determineOperationType', [$request, null]);
        
        $this->assertEquals('cart.remove_item', $operationType);
    }

    /** @test */
    public function it_determines_correct_operation_type_for_bulk_update()
    {
        $request = Request::create('/api/client/cart/123/items/bulk', 'POST');
        
        $operationType = $this->invokeMethod($this->middleware, 'determineOperationType', [$request, null]);
        
        $this->assertEquals('cart.bulk_update', $operationType);
    }

    /** @test */
    public function it_determines_correct_operation_type_for_apply_coupon()
    {
        $request = Request::create('/api/client/cart/123/apply-coupon', 'POST');
        
        $operationType = $this->invokeMethod($this->middleware, 'determineOperationType', [$request, null]);
        
        $this->assertEquals('cart.apply_coupon', $operationType);
    }

    /** @test */
    public function it_determines_correct_operation_type_for_cart_validation()
    {
        $request = Request::create('/api/client/cart/123/validate', 'POST');
        
        $operationType = $this->invokeMethod($this->middleware, 'determineOperationType', [$request, null]);
        
        $this->assertEquals('cart.validate', $operationType);
    }

    /** @test */
    public function it_determines_correct_operation_type_for_inventory_reservation()
    {
        $request = Request::create('/api/client/cart/123/reserve', 'POST');
        
        $operationType = $this->invokeMethod($this->middleware, 'determineOperationType', [$request, null]);
        
        $this->assertEquals('cart.reserve', $operationType);
    }

    /** @test */
    public function it_determines_correct_operation_type_for_cart_recovery()
    {
        $request = Request::create('/api/client/cart-recovery/send-reminder', 'POST');
        
        $operationType = $this->invokeMethod($this->middleware, 'determineOperationType', [$request, null]);
        
        $this->assertEquals('cart.recovery', $operationType);
    }

    /** @test */
    public function it_uses_default_operation_type_for_unknown_paths()
    {
        $request = Request::create('/api/client/unknown-path', 'GET');
        
        $operationType = $this->invokeMethod($this->middleware, 'determineOperationType', [$request, null]);
        
        $this->assertEquals('default', $operationType);
    }

    /** @test */
    public function it_generates_different_cache_keys_for_authenticated_and_guest_users()
    {
        // Authenticated user
        $request1 = Request::create('/api/client/cart', 'POST');
        $request1->setUserResolver(fn() => $this->user);
        
        $cacheKey1 = $this->invokeMethod($this->middleware, 'generateCacheKey', [$request1, 'cart.create']);
        
        // Guest user
        $request2 = Request::create('/api/client/cart', 'POST');
        $request2->setUserResolver(fn() => null);
        $request2->server->set('REMOTE_ADDR', '***********');
        
        $cacheKey2 = $this->invokeMethod($this->middleware, 'generateCacheKey', [$request2, 'cart.create']);
        
        $this->assertNotEquals($cacheKey1, $cacheKey2);
        $this->assertStringContains("user:{$this->user->id}", $cacheKey1);
        $this->assertStringContains('ip:***********', $cacheKey2);
    }

    /** @test */
    public function it_increments_counter_correctly()
    {
        $cacheKey = 'test_rate_limit_key';
        
        // First increment
        $this->invokeMethod($this->middleware, 'incrementCounter', [$cacheKey, 60]);
        $this->assertEquals(1, Cache::get($cacheKey));
        
        // Second increment
        $this->invokeMethod($this->middleware, 'incrementCounter', [$cacheKey, 60]);
        $this->assertEquals(2, Cache::get($cacheKey));
    }

    /** @test */
    public function it_adds_correct_rate_limit_headers()
    {
        $response = response()->json(['test' => true]);
        $config = ['limit' => 10, 'window' => 60];
        $currentCount = 3;
        
        $updatedResponse = $this->invokeMethod(
            $this->middleware, 
            'addRateLimitHeaders', 
            [$response, $config, $currentCount]
        );
        
        $this->assertEquals('10', $updatedResponse->headers->get('X-RateLimit-Limit'));
        $this->assertEquals('7', $updatedResponse->headers->get('X-RateLimit-Remaining')); // 10 - 3
        $this->assertEquals('60', $updatedResponse->headers->get('X-RateLimit-Window'));
    }

    /** @test */
    public function it_adds_retry_after_header_when_limit_reached()
    {
        $response = response()->json(['test' => true]);
        $config = ['limit' => 10, 'window' => 60];
        $currentCount = 10; // At limit
        
        $updatedResponse = $this->invokeMethod(
            $this->middleware, 
            'addRateLimitHeaders', 
            [$response, $config, $currentCount]
        );
        
        $this->assertEquals('0', $updatedResponse->headers->get('X-RateLimit-Remaining'));
        $this->assertEquals('60', $updatedResponse->headers->get('Retry-After'));
    }

    /** @test */
    public function it_can_get_rate_limit_status()
    {
        $request = Request::create('/api/client/cart', 'POST');
        $request->setUserResolver(fn() => $this->user);
        
        $status = CartRateLimitMiddleware::getRateLimitStatus($request, 'cart.create');
        
        $this->assertEquals('cart.create', $status['operation']);
        $this->assertEquals(10, $status['limit']); // Default limit for cart.create
        $this->assertEquals(0, $status['current']);
        $this->assertEquals(10, $status['remaining']);
        $this->assertEquals(60, $status['window_seconds']);
        $this->assertArrayHasKey('reset_at', $status);
    }

    /** @test */
    public function it_can_check_if_operation_is_rate_limited()
    {
        $request = Request::create('/api/client/cart', 'POST');
        $request->setUserResolver(fn() => $this->user);
        
        // Initially not rate limited
        $isLimited = CartRateLimitMiddleware::isRateLimited($request, 'cart.create');
        $this->assertFalse($isLimited);
        
        // Simulate hitting the limit
        $cacheKey = "cart_rate_limit:cart.create:user:{$this->user->id}";
        Cache::put($cacheKey, 10, 60); // Set to limit
        
        $isLimited = CartRateLimitMiddleware::isRateLimited($request, 'cart.create');
        $this->assertTrue($isLimited);
    }

    /** @test */
    public function it_can_clear_rate_limit()
    {
        $request = Request::create('/api/client/cart', 'POST');
        $request->setUserResolver(fn() => $this->user);
        
        // Set some rate limit data
        $cacheKey = "cart_rate_limit:cart.create:user:{$this->user->id}";
        Cache::put($cacheKey, 5, 60);
        
        // Clear rate limit
        $result = CartRateLimitMiddleware::clearRateLimit($request, 'cart.create');
        
        $this->assertTrue($result);
        $this->assertNull(Cache::get($cacheKey));
    }

    /** @test */
    public function it_can_get_all_rate_limit_statuses()
    {
        $request = Request::create('/api/client/cart', 'POST');
        $request->setUserResolver(fn() => $this->user);
        
        $statuses = CartRateLimitMiddleware::getAllRateLimitStatuses($request);
        
        $this->assertIsArray($statuses);
        $this->assertArrayHasKey('cart.create', $statuses);
        $this->assertArrayHasKey('cart.add_item', $statuses);
        $this->assertArrayHasKey('cart.apply_coupon', $statuses);
        $this->assertArrayNotHasKey('default', $statuses); // Should exclude default
    }

    /** @test */
    public function it_can_get_rate_limit_configuration()
    {
        $config = CartRateLimitMiddleware::getRateLimitConfig();
        
        $this->assertIsArray($config);
        $this->assertArrayHasKey('cart.create', $config);
        $this->assertArrayHasKey('cart.add_item', $config);
        $this->assertArrayHasKey('default', $config);
        
        // Check structure
        $this->assertArrayHasKey('limit', $config['cart.create']);
        $this->assertArrayHasKey('window', $config['cart.create']);
    }

    /**
     * Helper method to invoke private/protected methods for testing.
     */
    protected function invokeMethod($object, $methodName, array $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        
        return $method->invokeArgs($object, $parameters);
    }
}
