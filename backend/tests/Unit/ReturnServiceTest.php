<?php

namespace Tests\Unit;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\ReturnRequest;
use App\Models\User;
use App\Services\ReturnService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Tests\TestCase;

class ReturnServiceTest extends TestCase
{
    use RefreshDatabase;

    private ReturnService $returnService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->returnService = new ReturnService();
    }

    public function test_create_return_creates_individual_records_for_each_product()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create(['user_id' => $user->id]);
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();
        
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product1->id,
            'price' => 50.00,
            'quantity' => 5,
        ]);
        
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product2->id,
            'price' => 75.00,
            'quantity' => 3,
        ]);

        Auth::shouldReceive('id')->andReturn($user->id);

        $request = new Request([
            'order_id' => $order->id,
            'type' => 'refund',
            'reason' => 'Not satisfied',
            'description' => 'Products not as expected',
            'items' => [
                ['product_id' => $product1->id, 'quantity' => 2],
                ['product_id' => $product2->id, 'quantity' => 1, 'reason' => 'Damaged'],
            ],
        ]);

        $returns = $this->returnService->createReturn($request);

        $this->assertCount(2, $returns);
        $this->assertEquals($product1->id, $returns[0]->product_id);
        $this->assertEquals($product2->id, $returns[1]->product_id);
        $this->assertEquals(100.00, $returns[0]->total_amount); // 50 * 2
        $this->assertEquals(75.00, $returns[1]->total_amount);  // 75 * 1
        $this->assertEquals('Damaged', $returns[1]->reason);
    }

    public function test_get_user_returns_filters_by_user()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $order = Order::factory()->create();
        $product = Product::factory()->create();

        ReturnRequest::factory()->create(['user_id' => $user1->id]);
        ReturnRequest::factory()->create(['user_id' => $user2->id]);

        Auth::shouldReceive('id')->andReturn($user1->id);

        $request = new Request();
        $returns = $this->returnService->getUserReturns($request);

        $this->assertCount(1, $returns);
        $this->assertEquals($user1->id, $returns->first()->user_id);
    }

    public function test_get_user_returns_filters_by_status()
    {
        $user = User::factory()->create();
        
        ReturnRequest::factory()->create(['user_id' => $user->id, 'status' => 'pending']);
        ReturnRequest::factory()->create(['user_id' => $user->id, 'status' => 'approved']);

        Auth::shouldReceive('id')->andReturn($user->id);

        $request = new Request(['status' => 'pending']);
        $returns = $this->returnService->getUserReturns($request);

        $this->assertCount(1, $returns);
        $this->assertEquals('pending', $returns->first()->status);
    }

    public function test_cancel_return_updates_status()
    {
        $user = User::factory()->create();
        $return = ReturnRequest::factory()->create([
            'user_id' => $user->id,
            'status' => 'pending',
        ]);

        Auth::shouldReceive('id')->andReturn($user->id);

        $updatedReturn = $this->returnService->cancelReturn($return->id);

        $this->assertEquals('cancelled', $updatedReturn->status);
    }

    public function test_admin_update_return_sets_approval_data()
    {
        $admin = User::factory()->create();
        $return = ReturnRequest::factory()->create(['status' => 'pending']);

        Auth::shouldReceive('id')->andReturn($admin->id);

        $request = new Request([
            'status' => 'approved',
            'admin_notes' => 'Approved for refund',
        ]);

        $updatedReturn = $this->returnService->adminUpdateReturn($request, $return->id);

        $this->assertEquals('approved', $updatedReturn->status);
        $this->assertEquals('Approved for refund', $updatedReturn->admin_notes);
        $this->assertEquals($admin->id, $updatedReturn->approved_by);
        $this->assertNotNull($updatedReturn->approved_at);
    }

    public function test_get_return_statistics_returns_correct_counts()
    {
        ReturnRequest::factory()->create(['status' => 'pending']);
        ReturnRequest::factory()->create(['status' => 'pending']);
        ReturnRequest::factory()->create(['status' => 'approved']);
        ReturnRequest::factory()->create(['status' => 'completed']);
        ReturnRequest::factory()->create(['status' => 'rejected']);

        $stats = $this->returnService->getReturnStatistics();

        $this->assertEquals(5, $stats['total']);
        $this->assertEquals(2, $stats['pending']);
        $this->assertEquals(1, $stats['approved']);
        $this->assertEquals(1, $stats['completed']);
        $this->assertEquals(1, $stats['rejected']);
    }
}