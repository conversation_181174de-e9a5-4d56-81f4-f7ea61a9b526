<?php

namespace Tests\Unit;

use App\Models\Customer;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CustomerHasActiveTierFixTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that Customer model has canAccessMemberPricing method and not hasActiveTier
     */
    public function test_customer_has_can_access_member_pricing_method(): void
    {
        $user = User::factory()->create(['is_active' => true]);
        $customer = Customer::factory()->create([
            'user_id' => $user->id,
            'is_member_pricing_enabled' => true,
        ]);

        // Test that canAccessMemberPricing method exists and works
        $this->assertTrue(method_exists($customer, 'canAccessMemberPricing'));
        $this->assertTrue($customer->canAccessMemberPricing());

        // Test that hasActiveTier method does NOT exist
        $this->assertFalse(method_exists($customer, 'hasActiveTier'));
    }

    /**
     * Test that canAccessMemberPricing returns false when user is inactive
     */
    public function test_can_access_member_pricing_returns_false_when_user_inactive(): void
    {
        $user = User::factory()->create(['is_active' => false]);
        $customer = Customer::factory()->create([
            'user_id' => $user->id,
            'is_member_pricing_enabled' => true,
        ]);

        $this->assertFalse($customer->canAccessMemberPricing());
    }

    /**
     * Test that canAccessMemberPricing returns false when member pricing is disabled
     */
    public function test_can_access_member_pricing_returns_false_when_member_pricing_disabled(): void
    {
        $user = User::factory()->create(['is_active' => true]);
        $customer = Customer::factory()->create([
            'user_id' => $user->id,
            'is_member_pricing_enabled' => false,
        ]);

        $this->assertFalse($customer->canAccessMemberPricing());
    }

    /**
     * Test that the fix prevents hasActiveTier method calls from causing errors
     */
    public function test_no_has_active_tier_method_calls_in_services(): void
    {
        // Read the service files and check they don't contain hasActiveTier calls
        $orderCalculationService = file_get_contents(app_path('Services/OrderCalculationService.php'));
        $orderPricingService = file_get_contents(app_path('Services/OrderPricingService.php'));
        $convertCartRequest = file_get_contents(app_path('Http/Requests/Order/ConvertCartToOrderRequest.php'));

        // Assert that hasActiveTier is not called in these files
        $this->assertStringNotContainsString('hasActiveTier()', $orderCalculationService);
        $this->assertStringNotContainsString('hasActiveTier()', $orderPricingService);
        $this->assertStringNotContainsString('hasActiveTier()', $convertCartRequest);

        // Assert that canAccessMemberPricing is used instead
        $this->assertStringContainsString('canAccessMemberPricing()', $orderCalculationService);
        $this->assertStringContainsString('canAccessMemberPricing()', $orderPricingService);
        $this->assertStringContainsString('canAccessMemberPricing()', $convertCartRequest);
    }
}
