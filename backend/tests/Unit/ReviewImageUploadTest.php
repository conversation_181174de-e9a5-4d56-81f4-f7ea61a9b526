<?php

namespace Tests\Unit;

use App\Models\Review;
use App\Models\ReviewAttachment;
use App\Models\User;
use App\Models\Product;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ReviewImageUploadTest extends TestCase
{
    use RefreshDatabase;

    private $user;
    private $product;
    private $vendor;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data using factories
        $this->user = User::factory()->create();
        $this->vendor = Vendor::factory()->create();

        // Use the factory but override specific fields to avoid foreign key issues
        $this->product = Product::factory()->create([
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'is_active' => true,
            'is_approved' => true,
            'status' => 'submitted',
            'storage_conditions' => null, // Set to null to avoid integer constraint
            'country_of_origin' => null, // Set to null to avoid integer constraint
        ]);
    }

    public function test_review_attachment_model_relationships()
    {
        $review = $this->createReview();

        $attachment = ReviewAttachment::create([
            'review_id' => $review->id,
            'file_path' => 'test/image.jpg',
            'file_url' => 'https://example.com/test/image.jpg',
            'original_name' => 'test-image.jpg',
            'file_type' => 'jpg',
            'mime_type' => 'image/jpeg',
            'file_size' => 1024,
            'width' => 800,
            'height' => 600,
            'sort_order' => 0,
            'is_primary' => true,
        ]);

        // Test relationship
        $this->assertEquals($review->id, $attachment->review->id);
        $this->assertEquals(1, $review->attachments()->count());
        $this->assertEquals($attachment->id, $review->attachments->first()->id);
    }

    public function test_review_attachment_model_attributes()
    {
        $review = $this->createReview();

        $attachment = ReviewAttachment::create([
            'review_id' => $review->id,
            'file_path' => 'test/image.jpg',
            'file_url' => 'https://example.com/test/image.jpg',
            'original_name' => 'test-image.jpg',
            'file_type' => 'jpg',
            'mime_type' => 'image/jpeg',
            'file_size' => 1024, // 1KB
            'width' => 800,
            'height' => 600,
            'sort_order' => 0,
            'is_primary' => true,
        ]);

        // Test computed attributes
        $this->assertEquals('https://example.com/test/image.jpg', $attachment->full_url);
        $this->assertEquals('https://example.com/test/image.jpg', $attachment->thumbnail_url);
        $this->assertEquals('1024 B', $attachment->formatted_file_size);
    }

    public function test_review_attachment_scopes()
    {
        $review = $this->createReview();

        // Create multiple attachments
        ReviewAttachment::create([
            'review_id' => $review->id,
            'file_path' => 'test/image1.jpg',
            'file_url' => 'https://example.com/test/image1.jpg',
            'original_name' => 'image1.jpg',
            'file_type' => 'jpg',
            'mime_type' => 'image/jpeg',
            'file_size' => 1024,
            'sort_order' => 1,
            'is_primary' => true,
        ]);

        ReviewAttachment::create([
            'review_id' => $review->id,
            'file_path' => 'test/image2.jpg',
            'file_url' => 'https://example.com/test/image2.jpg',
            'original_name' => 'image2.jpg',
            'file_type' => 'jpg',
            'mime_type' => 'image/jpeg',
            'file_size' => 1024,
            'sort_order' => 0,
            'is_primary' => false,
        ]);

        // Test ordered scope
        $orderedAttachments = ReviewAttachment::where('review_id', $review->id)->ordered()->get();
        $this->assertEquals(0, $orderedAttachments->first()->sort_order);
        $this->assertEquals(1, $orderedAttachments->last()->sort_order);

        // Test primary scope
        $primaryAttachment = ReviewAttachment::where('review_id', $review->id)->primary()->first();
        $this->assertTrue($primaryAttachment->is_primary);
    }

    public function test_review_model_attachment_relationships()
    {
        $review = $this->createReview();

        // Create attachments
        $primaryAttachment = ReviewAttachment::create([
            'review_id' => $review->id,
            'file_path' => 'test/primary.jpg',
            'file_url' => 'https://example.com/test/primary.jpg',
            'original_name' => 'primary.jpg',
            'file_type' => 'jpg',
            'mime_type' => 'image/jpeg',
            'file_size' => 1024,
            'sort_order' => 0,
            'is_primary' => true,
        ]);

        ReviewAttachment::create([
            'review_id' => $review->id,
            'file_path' => 'test/secondary.jpg',
            'file_url' => 'https://example.com/test/secondary.jpg',
            'original_name' => 'secondary.jpg',
            'file_type' => 'jpg',
            'mime_type' => 'image/jpeg',
            'file_size' => 1024,
            'sort_order' => 1,
            'is_primary' => false,
        ]);

        // Test attachments relationship
        $this->assertEquals(2, $review->attachments()->count());

        // Test primary attachment relationship
        $this->assertEquals($primaryAttachment->id, $review->primaryAttachment->id);
        $this->assertTrue($review->primaryAttachment->is_primary);
    }

    private function createReview($overrides = [])
    {
        return Review::create(array_merge([
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'rating' => 4,
            'comment' => 'Great product with images!',
            'is_approved' => true,
            'is_visible' => true,
        ], $overrides));
    }
}
