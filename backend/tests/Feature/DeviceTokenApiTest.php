<?php

namespace Tests\Feature;

use App\Models\DeviceToken;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Passport\Passport;
use Tests\TestCase;

class DeviceTokenApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_verified' => true,
            'is_active' => true,
        ]);
    }

    public function test_can_register_device_token(): void
    {
        Passport::actingAs($this->user);

        $deviceTokenData = [
            'device_token' => 'test-device-token-123',
            'platform' => 'ios',
            'os_id' => 'ios-device-id-456',
        ];

        $response = $this->postJson('/api/devices/register', $deviceTokenData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'platform',
                    'registered_at',
                ]
            ]);

        $this->assertDatabaseHas('device_tokens', [
            'user_id' => $this->user->id,
            'device_token' => 'test-device-token-123',
            'platform' => 'ios',
            'os_id' => 'ios-device-id-456',
        ]);
    }

    public function test_can_update_existing_device_token(): void
    {
        Passport::actingAs($this->user);

        // Create initial device token
        $deviceToken = DeviceToken::create([
            'user_id' => $this->user->id,
            'device_token' => 'existing-token',
            'platform' => 'android',
            'os_id' => 'old-os-id',
        ]);

        // Update with same device token but different user/platform
        $updateData = [
            'device_token' => 'existing-token',
            'platform' => 'ios',
            'os_id' => 'new-os-id',
        ];

        $response = $this->postJson('/api/devices/register', $updateData);

        $response->assertStatus(200);

        // Should update the existing record
        $deviceToken->refresh();
        $this->assertEquals($this->user->id, $deviceToken->user_id);
        $this->assertEquals('ios', $deviceToken->platform);
        $this->assertEquals('new-os-id', $deviceToken->os_id);
    }

    public function test_device_token_registration_validation(): void
    {
        Passport::actingAs($this->user);

        // Test missing device_token
        $response = $this->postJson('/api/devices/register', [
            'platform' => 'ios',
        ]);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['device_token']);

        // Test missing platform
        $response = $this->postJson('/api/devices/register', [
            'device_token' => 'test-token',
        ]);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['platform']);

        // Test invalid platform
        $response = $this->postJson('/api/devices/register', [
            'device_token' => 'test-token',
            'platform' => 'invalid-platform',
        ]);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['platform']);
    }

    public function test_can_unregister_device_token(): void
    {
        Passport::actingAs($this->user);

        // Create device token
        DeviceToken::create([
            'user_id' => $this->user->id,
            'device_token' => 'token-to-delete',
            'platform' => 'web',
        ]);

        $response = $this->deleteJson('/api/devices/unregister', [
            'device_token' => 'token-to-delete',
        ]);

        $response->assertStatus(200);

        $this->assertDatabaseMissing('device_tokens', [
            'device_token' => 'token-to-delete',
        ]);
    }

    public function test_cannot_unregister_nonexistent_device_token(): void
    {
        Passport::actingAs($this->user);

        $response = $this->deleteJson('/api/devices/unregister', [
            'device_token' => 'nonexistent-token',
        ]);

        $response->assertStatus(404);
    }

    public function test_cannot_unregister_other_users_device_token(): void
    {
        $otherUser = User::factory()->create();

        // Create device token for other user
        DeviceToken::create([
            'user_id' => $otherUser->id,
            'device_token' => 'other-user-token',
            'platform' => 'ios',
        ]);

        Passport::actingAs($this->user);

        $response = $this->deleteJson('/api/devices/unregister', [
            'device_token' => 'other-user-token',
        ]);

        $response->assertStatus(404);

        // Token should still exist
        $this->assertDatabaseHas('device_tokens', [
            'device_token' => 'other-user-token',
        ]);
    }

    public function test_can_get_user_device_tokens(): void
    {
        Passport::actingAs($this->user);

        // Create multiple device tokens
        DeviceToken::create([
            'user_id' => $this->user->id,
            'device_token' => 'ios-token',
            'platform' => 'ios',
            'os_id' => 'ios-id',
        ]);

        DeviceToken::create([
            'user_id' => $this->user->id,
            'device_token' => 'android-token',
            'platform' => 'android',
            'os_id' => 'android-id',
        ]);

        // Create token for other user (should not appear)
        $otherUser = User::factory()->create();
        DeviceToken::create([
            'user_id' => $otherUser->id,
            'device_token' => 'other-token',
            'platform' => 'web',
        ]);

        $response = $this->getJson('/api/devices');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'platform',
                        'os_id',
                        'created_at',
                        'updated_at',
                    ]
                ]
            ]);

        $this->assertCount(2, $response->json('data'));
    }

    public function test_device_token_endpoints_require_authentication(): void
    {
        $response = $this->postJson('/api/devices/register', [
            'device_token' => 'test-token',
            'platform' => 'ios',
        ]);
        $response->assertStatus(401);

        $response = $this->deleteJson('/api/devices/unregister', [
            'device_token' => 'test-token',
        ]);
        $response->assertStatus(401);

        $response = $this->getJson('/api/devices');
        $response->assertStatus(401);
    }

    public function test_device_token_rate_limiting(): void
    {
        Passport::actingAs($this->user);

        // Make multiple requests to test rate limiting
        for ($i = 0; $i < 12; $i++) {
            $response = $this->postJson('/api/devices/register', [
                'device_token' => "test-token-{$i}",
                'platform' => 'web',
            ]);

            if ($i < 10) {
                $response->assertStatus(200);
            } else {
                // Should be rate limited after 10 requests
                $response->assertStatus(429);
            }
        }
    }
}
