<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Wishlist;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Passport\Passport;
use Tests\TestCase;

class WishlistTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private User $user;
    private User $otherUser;
    private Product $product;
    private ProductVariant $variant;
    private Vendor $vendor;
    private Category $category;
    private Category $subCategory;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->user = User::factory()->create();
        $this->otherUser = User::factory()->create();

        // Create test vendor
        $this->vendor = Vendor::factory()->create();

        // Create test category
        $this->category = Category::factory()->create();

        // Create test subcategory
        $this->subCategory = Category::factory()->create([
            'parent_id' => $this->category->id,
            'type' => 'sub'
        ]);

        // Create test product with minimal required fields
        $this->product = Product::create([
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'category_id' => $this->category->id,
            'sub_category_id' => $this->subCategory->id,
            'title_en' => 'Test Product',
            'title_ar' => 'منتج تجريبي',
            'regular_price' => 100.00,
            'offer_price' => 80.00,
            'is_active' => true,
            'is_approved' => true,
            'status' => 'pending',
            'uuid' => \Illuminate\Support\Str::uuid(),
            // Set dropdown fields to null or valid integer values
            'storage_conditions' => null,
            'country_of_origin' => null,
            'is_returnable' => null,
            'warranty' => null,
        ]);

        // Create test product variant
        $this->variant = ProductVariant::create([
            'product_id' => $this->product->id,
            'regular_price' => 120.00,
            'offer_price' => 90.00,
            'is_active' => true,
            'system_sku' => 'TEST-SKU-' . uniqid(),
        ]);
    }

    public function test_user_can_view_their_wishlist(): void
    {
        Passport::actingAs($this->user);

        // Create wishlist items for the user
        for ($i = 0; $i < 3; $i++) {
            Wishlist::create([
                'user_id' => $this->user->id,
                'product_id' => $this->product->id,
                'vendor_id' => $this->vendor->id,
            ]);
        }

        // Create wishlist item for another user (should not be visible)
        Wishlist::create([
            'user_id' => $this->otherUser->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
        ]);

        $response = $this->getJson('/api/client/wishlist');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
                'message'
            ]);

        // Should only see own wishlist items
        $this->assertCount(3, $response->json('data.data') ?? $response->json('data'));
    }

    public function test_user_can_add_item_to_wishlist(): void
    {
        Passport::actingAs($this->user);

        $response = $this->postJson('/api/client/wishlist', [
            'product_id' => $this->product->id,
            'note' => 'Want to buy this later',
        ]);

        $response->assertStatus(201)
            ->assertJson([
                'status' => true,
                'message' => 'Item added to wishlist successfully!'
            ]);

        $this->assertDatabaseHas('wishlists', [
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'note' => 'Want to buy this later',
        ]);
    }

    public function test_user_can_add_product_variant_to_wishlist(): void
    {
        Passport::actingAs($this->user);

        $response = $this->postJson('/api/client/wishlist', [
            'product_id' => $this->product->id,
            'product_variant_id' => $this->variant->id,
            'note' => 'Specific variant I want',
        ]);

        $response->assertStatus(201);

        $this->assertDatabaseHas('wishlists', [
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'product_variant_id' => $this->variant->id,
        ]);
    }

    public function test_user_cannot_add_duplicate_item_to_wishlist(): void
    {
        Passport::actingAs($this->user);

        // First addition should succeed
        $this->postJson('/api/client/wishlist', [
            'product_id' => $this->product->id,
        ])->assertStatus(201);

        // Second addition should fail
        $response = $this->postJson('/api/client/wishlist', [
            'product_id' => $this->product->id,
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'status' => false,
                'errors' => 'Item already exists in wishlist'
            ]);
    }

    public function test_user_can_view_specific_wishlist_item(): void
    {
        Passport::actingAs($this->user);

        $wishlist = Wishlist::create([
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
        ]);

        $response = $this->getJson("/api/client/wishlist/{$wishlist->id}");

        $response->assertStatus(200)
            ->assertJson([
                'status' => true,
                'data' => [
                    'id' => $wishlist->id,
                    'user_id' => $this->user->id,
                    'product_id' => $this->product->id,
                ]
            ]);
    }

    public function test_user_cannot_view_other_users_wishlist_item(): void
    {
        Passport::actingAs($this->user);

        $otherWishlist = Wishlist::create([
            'user_id' => $this->otherUser->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
        ]);

        $response = $this->getJson("/api/client/wishlist/{$otherWishlist->id}");

        $response->assertStatus(500); // Should fail with model not found
    }

    public function test_user_can_remove_item_from_wishlist(): void
    {
        Passport::actingAs($this->user);

        $wishlist = Wishlist::create([
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
        ]);

        $response = $this->deleteJson("/api/client/wishlist/{$wishlist->id}");

        $response->assertStatus(200)
            ->assertJson([
                'status' => true,
                'message' => 'Item removed from wishlist successfully!'
            ]);

        $this->assertDatabaseMissing('wishlists', [
            'id' => $wishlist->id,
        ]);
    }

    public function test_user_can_bulk_delete_wishlist_items(): void
    {
        Passport::actingAs($this->user);

        $wishlists = collect();
        for ($i = 0; $i < 3; $i++) {
            $wishlists->push(Wishlist::create([
                'user_id' => $this->user->id,
                'product_id' => $this->product->id,
                'vendor_id' => $this->vendor->id,
            ]));
        }

        $ids = $wishlists->pluck('id')->toArray();

        $response = $this->postJson('/api/client/wishlist/bulk-delete', [
            'wishlist_ids' => $ids,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'status' => true,
                'message' => 'Items removed from wishlist successfully!'
            ]);

        foreach ($ids as $id) {
            $this->assertDatabaseMissing('wishlists', ['id' => $id]);
        }
    }

    public function test_validation_errors_for_invalid_data(): void
    {
        Passport::actingAs($this->user);

        // Test missing product_id
        $response = $this->postJson('/api/client/wishlist', [
            'note' => 'Test note',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['product_id']);

        // Test invalid product_id
        $response = $this->postJson('/api/client/wishlist', [
            'product_id' => 99999,
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['product_id']);

        // Test invalid product_variant_id
        $response = $this->postJson('/api/client/wishlist', [
            'product_id' => $this->product->id,
            'product_variant_id' => 99999,
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['product_variant_id']);
    }

    public function test_unauthenticated_user_cannot_access_wishlist(): void
    {
        $response = $this->getJson('/api/client/wishlist');
        $response->assertStatus(401);

        $response = $this->postJson('/api/client/wishlist', [
            'product_id' => $this->product->id,
        ]);
        $response->assertStatus(401);
    }
}
