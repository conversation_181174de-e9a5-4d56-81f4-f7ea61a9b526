<?php

namespace Tests\Feature\Checkout;

use App\Models\Customer;
use App\Models\PaymentMethod;
use App\Models\Product;
use App\Models\ShoppingCart;
use App\Models\User;
use App\Models\UserAddress;
use App\Models\UserCard;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Laravel\Passport\Passport;
use Tests\TestCase;

class CheckoutApiErrorTest extends TestCase
{
    use DatabaseTransactions;

    protected User $user;
    protected Customer $customer;
    protected ?Vendor $vendor;
    protected ?Product $product;
    protected ShoppingCart $cart;
    protected UserAddress $shippingAddress;
    protected UserCard $userCard;
    protected ?PaymentMethod $paymentMethod;

    protected function setUp(): void
    {
        parent::setUp();

        // Use existing seeded data instead of creating new data
        $this->vendor = Vendor::first();
        $this->product = Product::first();
        $this->paymentMethod = PaymentMethod::first();

        // Skip test if required data is not available
        if (!$this->vendor || !$this->product || !$this->paymentMethod) {
            $this->markTestSkipped('Required seeded data not available');
        }

        // Ensure product has inventory
        if (!$this->product->inventory) {
            $this->product->inventory()->create([
                'vendor_id' => $this->vendor->id,
                'stock' => 100,
                'reserved' => 0,
                'stock_status' => 'in_stock',
                'is_active' => true,
            ]);
        }

        // Create test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Test User',
            'is_active' => true,
        ]);

        // Create customer
        $this->customer = Customer::create([
            'user_id' => $this->user->id,
            'is_member_pricing_enabled' => true,
            'gender' => 'male',
            'loyalty_points' => 0,
            'preferred_language' => 'en',
            'preferred_currency' => 'AED',
            'kyc_verified' => false,
            'loyalty_points_awarded' => false,
            'newsletter_consent' => false,
            'is_vrps' => false,
        ]);

        // Create shopping cart
        $this->cart = ShoppingCart::create([
            'user_id' => $this->user->id,
            'uuid' => \Illuminate\Support\Str::uuid(),
            'status' => 'active',
            'currency' => 'AED',
            'subtotal' => 100.00,
            'total_amount' => 100.00,
            'expires_at' => now()->addDays(7),
        ]);

        // Add item to cart
        $this->cart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 1,
            'unit_price' => 100.00,
            'total_price' => 100.00,
        ]);

        // Create shipping address
        $this->shippingAddress = UserAddress::create([
            'user_id' => $this->user->id,
            'type' => 'shipping',
            'is_default' => true,
            'first_name' => 'Test',
            'last_name' => 'User',
            'phone' => '+************',
            'address_line_1' => 'Test Address',
            'city' => 'Dubai',
            'state' => 'Dubai',
            'postal_code' => '12345',
            'country' => 'AE',
        ]);

        // Create user card
        $this->userCard = UserCard::create([
            'user_id' => $this->user->id,
            'payment_method_id' => $this->paymentMethod->id,
            'card_holder_name' => 'Test User',
            'card_number_last_four' => '1234',
            'card_type' => 'visa',
            'expiry_month' => '12',
            'expiry_year' => '2025',
            'is_active' => true,
        ]);

        // Authenticate user
        Passport::actingAs($this->user);
    }

    /**
     * Test that the hasActiveTier() method error has been fixed during checkout
     */
    public function test_checkout_process_no_longer_has_active_tier_error(): void
    {
        $checkoutData = [
            'payment_method_id' => $this->paymentMethod->id,
            'shipping_address_id' => $this->shippingAddress->id,
            'user_card_id' => $this->userCard->id,
            'use_shipping_for_billing' => true,
            'terms_accepted' => true,
            'customer_note' => '',
        ];

        $response = $this->postJson("/api/client/checkout/process/{$this->cart->uuid}", $checkoutData);

        // After the fix, this should NOT contain hasActiveTier error
        $responseData = $response->json();

        // Check that the error is NOT related to hasActiveTier method
        $this->assertStringNotContainsString(
            'hasActiveTier',
            $responseData['errors'] ?? $responseData['message'] ?? ''
        );

        // The response might still fail for other reasons (validation, etc.)
        // but it should not be due to the hasActiveTier method error
        $this->assertTrue(true, 'Test passed - no hasActiveTier error found');
    }

    /**
     * Test checkout validation works correctly
     */
    public function test_checkout_validation_works(): void
    {
        $response = $this->postJson("/api/client/checkout/validate/{$this->cart->uuid}", [
            'payment_method_id' => $this->paymentMethod->id,
            'shipping_address_id' => $this->shippingAddress->id,
            'user_card_id' => $this->userCard->id,
            'use_shipping_for_billing' => true,
            'terms_accepted' => true,
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'is_valid',
                    'errors',
                    'cart_validation',
                ],
            ]);
    }
}
