<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Category;
use App\Models\Brand;
use App\Models\ProductClass;
use App\Models\Fulfilment;
use App\Models\SupportCategory;
use App\Models\SupportTopic;
use App\Models\Warehouse;
use App\Models\Dropdown;
use App\Models\DropdownOption;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;

class DropdownDataTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        
        // Assign admin role if using Spatie permissions
        if (class_exists(\Spatie\Permission\Models\Role::class)) {
            $adminRole = \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'admin']);
            $this->user->assignRole($adminRole);
        }
    }

    /** @test */
    public function it_can_fetch_consolidated_dropdown_data_without_authentication()
    {
        // Create test data
        $this->createTestData();

        // Test the endpoint without authentication (public endpoint)
        $response = $this->getJson('/api/general/dropdown-data');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'categories',
                        'brands',
                        'dropdowns',
                        'product_classes',
                        'fulfilments',
                        'support_categories',
                        'support_topics',
                        'warehouses',
                        'meta' => [
                            'cached_at',
                            'cache_duration',
                            'errors',
                            'has_errors'
                        ]
                    ]
                ]);

        $data = $response->json('data');
        
        // Verify data structure
        $this->assertIsArray($data['categories']);
        $this->assertIsArray($data['brands']);
        $this->assertIsArray($data['dropdowns']);
        $this->assertIsArray($data['product_classes']);
        $this->assertIsArray($data['fulfilments']);
        $this->assertIsArray($data['support_categories']);
        $this->assertIsArray($data['support_topics']);
        $this->assertIsArray($data['warehouses']);
        
        // Verify meta information
        $this->assertArrayHasKey('cached_at', $data['meta']);
        $this->assertArrayHasKey('cache_duration', $data['meta']);
        $this->assertArrayHasKey('errors', $data['meta']);
        $this->assertArrayHasKey('has_errors', $data['meta']);
    }

    /** @test */
    public function it_handles_database_errors_gracefully()
    {
        // Clear cache to ensure fresh request
        Cache::forget('admin_dropdown_data');

        // Test with empty database (should not fail)
        $response = $this->getJson('/api/general/dropdown-data');

        $response->assertStatus(200);
        
        $data = $response->json('data');
        
        // Should return empty arrays, not fail
        $this->assertIsArray($data['categories']);
        $this->assertIsArray($data['brands']);
        $this->assertIsArray($data['dropdowns']);
        
        // Meta should indicate no errors for empty data
        $this->assertFalse($data['meta']['has_errors']);
    }

    /** @test */
    public function it_filters_data_by_correct_status_columns()
    {
        // Create test data with different statuses
        $activeCategory = Category::factory()->create([
            'name_en' => 'Active Category',
            'status' => 'active'
        ]);
        
        $inactiveCategory = Category::factory()->create([
            'name_en' => 'Inactive Category',
            'status' => 'inactive'
        ]);

        $activeBrand = Brand::factory()->create([
            'name_en' => 'Active Brand',
            'is_active' => true,
            'status' => 'approved'
        ]);
        
        $inactiveBrand = Brand::factory()->create([
            'name_en' => 'Inactive Brand',
            'is_active' => false,
            'status' => 'pending'
        ]);

        $response = $this->getJson('/api/general/dropdown-data');
        $response->assertStatus(200);
        
        $data = $response->json('data');
        
        // Should only include active/approved items
        $categoryNames = collect($data['categories'])->flatten(1)->pluck('name_en')->toArray();
        $this->assertContains('Active Category', $categoryNames);
        $this->assertNotContains('Inactive Category', $categoryNames);
        
        $brandNames = collect($data['brands'])->pluck('name_en')->toArray();
        $this->assertContains('Active Brand', $brandNames);
        $this->assertNotContains('Inactive Brand', $brandNames);
    }

    /** @test */
    public function it_caches_dropdown_data()
    {
        $this->createTestData();
        
        // Clear cache
        Cache::forget('admin_dropdown_data');
        
        // First request should cache the data
        $response1 = $this->getJson('/api/general/dropdown-data');
        $response1->assertStatus(200);
        
        // Verify cache exists
        $this->assertTrue(Cache::has('admin_dropdown_data'));
        
        // Second request should use cached data
        $response2 = $this->getJson('/api/general/dropdown-data');
        $response2->assertStatus(200);
        
        // Both responses should be identical
        $this->assertEquals($response1->json('data'), $response2->json('data'));
    }

    /** @test */
    public function it_handles_missing_columns_gracefully()
    {
        // This test ensures the service handles potential column mismatches
        // by using try-catch blocks in the service
        
        $response = $this->getJson('/api/general/dropdown-data');
        $response->assertStatus(200);
        
        $data = $response->json('data');
        
        // Even if some queries fail, the endpoint should still return a response
        $this->assertArrayHasKey('meta', $data);
        $this->assertArrayHasKey('errors', $data['meta']);
        $this->assertArrayHasKey('has_errors', $data['meta']);
    }

    private function createTestData()
    {
        // Create categories
        $parentCategory = Category::factory()->create([
            'name_en' => 'Parent Category',
            'name_ar' => 'فئة الوالدين',
            'parent_id' => null,
            'status' => 'active'
        ]);
        
        Category::factory()->create([
            'name_en' => 'Child Category',
            'name_ar' => 'فئة الطفل',
            'parent_id' => $parentCategory->id,
            'status' => 'active'
        ]);

        // Create brands
        Brand::factory()->create([
            'name_en' => 'Test Brand',
            'name_ar' => 'علامة تجارية تجريبية',
            'is_active' => true,
            'status' => 'approved'
        ]);

        // Create product classes
        ProductClass::factory()->create([
            'name_en' => 'Test Class',
            'name_ar' => 'فئة تجريبية',
            'category_id' => $parentCategory->id,
            'status' => 'active'
        ]);

        // Create fulfilments
        Fulfilment::factory()->create([
            'name_en' => 'Test Fulfilment',
            'name_ar' => 'تنفيذ تجريبي',
            'is_active' => true
        ]);

        // Create support categories
        $supportCategory = SupportCategory::factory()->create([
            'user_id' => $this->user->id,
            'name_en' => 'Test Support Category',
            'name_ar' => 'فئة دعم تجريبية',
            'status' => 'active'
        ]);

        // Create support topics
        SupportTopic::factory()->create([
            'user_id' => $this->user->id,
            'category_id' => $supportCategory->id,
            'name_en' => 'Test Support Topic',
            'name_ar' => 'موضوع دعم تجريبي',
            'status' => 'active'
        ]);

        // Create warehouses
        Warehouse::factory()->create([
            'name_en' => 'Test Warehouse',
            'name_ar' => 'مستودع تجريبي',
            'location' => 'Test Location',
            'status' => 'active',
            'is_active' => true
        ]);

        // Create dropdowns
        $dropdown = Dropdown::factory()->create([
            'name_en' => 'Test Dropdown',
            'name_ar' => 'قائمة منسدلة تجريبية',
            'slug' => 'test-dropdown'
        ]);

        DropdownOption::factory()->create([
            'dropdown_id' => $dropdown->id,
            'value_en' => 'Test Option',
            'value_ar' => 'خيار تجريبي'
        ]);
    }
}
