<?php

namespace Tests\Feature;

use App\Jobs\SendAbandonedCartReminderJob;
use App\Models\AbandonedCart;
use App\Models\CartItem;
use App\Models\Product;
use App\Models\ShoppingCart;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class CartRecoveryApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected ShoppingCart $cart;
    protected Product $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);
        
        $vendor = Vendor::factory()->create(['is_active' => true]);
        $this->product = Product::factory()->create([
            'vendor_id' => $vendor->id,
            'is_active' => true,
            'name' => 'Test Product',
            'price' => 100.00,
        ]);
        
        $this->cart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
            'subtotal' => 200.00,
            'total_amount' => 210.00,
        ]);
        
        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $this->product->id,
            'vendor_id' => $vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
        ]);
    }

    /** @test */
    public function it_can_send_cart_recovery_reminder()
    {
        Queue::fake();

        $response = $this->postJson('/api/client/cart-recovery/send-reminder', [
            'cart_id' => $this->cart->uuid,
            'email' => '<EMAIL>',
            'discount_percentage' => 10,
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'abandoned_cart_id',
                    'recovery_token',
                    'reminder_scheduled',
                ],
            ]);

        // Check abandoned cart record created
        $this->assertDatabaseHas('abandoned_carts', [
            'cart_id' => $this->cart->id,
            'user_id' => $this->user->id,
            'email' => '<EMAIL>',
            'status' => 'pending',
        ]);

        // Check reminder job queued
        Queue::assertPushed(SendAbandonedCartReminderJob::class);
    }

    /** @test */
    public function it_validates_required_fields_for_reminder()
    {
        $response = $this->postJson('/api/client/cart-recovery/send-reminder', [
            // Missing required fields
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cart_id', 'email']);
    }

    /** @test */
    public function it_validates_email_format_for_reminder()
    {
        $response = $this->postJson('/api/client/cart-recovery/send-reminder', [
            'cart_id' => $this->cart->uuid,
            'email' => 'invalid-email',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    /** @test */
    public function it_rejects_empty_cart_for_reminder()
    {
        // Clear cart items
        $this->cart->items()->delete();

        $response = $this->postJson('/api/client/cart-recovery/send-reminder', [
            'cart_id' => $this->cart->uuid,
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
            ->assertJsonPath('success', false)
            ->assertJsonStructure([
                'success',
                'message',
                'error',
            ]);
    }

    /** @test */
    public function it_can_recover_cart_with_valid_token()
    {
        $abandonedCart = AbandonedCart::factory()->create([
            'cart_id' => $this->cart->id,
            'user_id' => $this->user->id,
            'recovery_token' => 'valid-token-123',
            'status' => 'pending',
            'recovery_incentive' => [
                'type' => 'discount',
                'discount_percentage' => 15,
            ],
        ]);

        $response = $this->getJson('/api/client/cart-recovery/recover/valid-token-123');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'cart' => [
                        'id',
                        'uuid',
                        'items',
                        'subtotal',
                        'total_amount',
                    ],
                    'incentive' => [
                        'type',
                        'discount_percentage',
                    ],
                ],
            ]);

        // Check abandoned cart marked as recovered
        $this->assertDatabaseHas('abandoned_carts', [
            'id' => $abandonedCart->id,
            'status' => 'recovered',
        ]);

        $this->assertNotNull($abandonedCart->fresh()->recovered_at);
    }

    /** @test */
    public function it_rejects_invalid_recovery_token()
    {
        $response = $this->getJson('/api/client/cart-recovery/recover/invalid-token');

        $response->assertStatus(404)
            ->assertJsonPath('success', false)
            ->assertJsonPath('error', 'INVALID_RECOVERY_TOKEN');
    }

    /** @test */
    public function it_rejects_already_recovered_cart()
    {
        $abandonedCart = AbandonedCart::factory()->create([
            'cart_id' => $this->cart->id,
            'user_id' => $this->user->id,
            'recovery_token' => 'valid-token-123',
            'status' => 'recovered',
            'recovered_at' => now(),
        ]);

        $response = $this->getJson('/api/client/cart-recovery/recover/valid-token-123');

        $response->assertStatus(409)
            ->assertJsonPath('success', false)
            ->assertJsonPath('error', 'CART_ALREADY_RECOVERED');
    }

    /** @test */
    public function it_can_convert_abandoned_cart()
    {
        $abandonedCart = AbandonedCart::factory()->create([
            'cart_id' => $this->cart->id,
            'user_id' => $this->user->id,
            'recovery_token' => 'valid-token-123',
            'status' => 'recovered',
        ]);

        $response = $this->postJson('/api/client/cart-recovery/convert/valid-token-123', [
            'order_id' => 'ORD-12345',
            'order_total' => 210.00,
            'payment_method' => 'credit_card',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'conversion_confirmed',
                    'order_details',
                ],
            ]);

        // Check abandoned cart marked as converted
        $this->assertDatabaseHas('abandoned_carts', [
            'id' => $abandonedCart->id,
            'status' => 'converted',
        ]);

        $this->assertNotNull($abandonedCart->fresh()->converted_at);
        $this->assertEquals('ORD-12345', $abandonedCart->fresh()->conversion_data['order_id']);
    }

    /** @test */
    public function it_validates_required_fields_for_conversion()
    {
        $abandonedCart = AbandonedCart::factory()->create([
            'recovery_token' => 'valid-token-123',
            'status' => 'recovered',
        ]);

        $response = $this->postJson('/api/client/cart-recovery/convert/valid-token-123', [
            // Missing required fields
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['order_id', 'order_total']);
    }

    /** @test */
    public function it_can_get_recovery_statistics()
    {
        // Create test data for statistics
        AbandonedCart::factory()->create([
            'status' => 'pending',
            'created_at' => now()->subHours(2),
        ]);

        AbandonedCart::factory()->create([
            'status' => 'recovered',
            'created_at' => now()->subHours(1),
            'recovered_at' => now()->subMinutes(30),
        ]);

        AbandonedCart::factory()->create([
            'status' => 'converted',
            'created_at' => now()->subHours(3),
            'converted_at' => now()->subMinutes(15),
        ]);

        $response = $this->getJson('/api/client/cart-recovery/statistics?period=24h');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'period',
                    'start_date',
                    'end_date',
                    'totals' => [
                        'total_abandoned',
                        'recovered',
                        'converted',
                        'pending',
                    ],
                    'rates' => [
                        'recovery_rate',
                        'conversion_rate',
                    ],
                    'revenue_impact' => [
                        'recovered_value',
                        'converted_value',
                    ],
                ],
            ]);
    }

    /** @test */
    public function it_handles_cart_recovery_with_expired_items()
    {
        // Deactivate product to simulate expired/unavailable item
        $this->product->update(['is_active' => false]);

        $abandonedCart = AbandonedCart::factory()->create([
            'cart_id' => $this->cart->id,
            'user_id' => $this->user->id,
            'recovery_token' => 'valid-token-123',
            'status' => 'pending',
        ]);

        $response = $this->getJson('/api/client/cart-recovery/recover/valid-token-123');

        $response->assertStatus(200)
            ->assertJsonPath('data.cart_valid', false)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'cart',
                    'cart_valid',
                    'validation_issues',
                ],
            ]);
    }

    /** @test */
    public function it_prevents_duplicate_reminder_within_time_limit()
    {
        // Create existing abandoned cart with recent reminder
        AbandonedCart::factory()->create([
            'cart_id' => $this->cart->id,
            'user_id' => $this->user->id,
            'email' => '<EMAIL>',
            'last_reminder_sent_at' => now()->subHours(1), // Recent reminder
            'reminder_count' => 1,
        ]);

        $response = $this->postJson('/api/client/cart-recovery/send-reminder', [
            'cart_id' => $this->cart->uuid,
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
            ->assertJsonPath('success', false)
            ->assertJsonStructure([
                'success',
                'message',
                'error',
            ]);
    }

    /** @test */
    public function it_respects_maximum_reminder_limit()
    {
        // Create abandoned cart with maximum reminders sent
        AbandonedCart::factory()->create([
            'cart_id' => $this->cart->id,
            'user_id' => $this->user->id,
            'email' => '<EMAIL>',
            'reminder_count' => 3, // Maximum limit
            'last_reminder_sent_at' => now()->subDays(2),
        ]);

        $response = $this->postJson('/api/client/cart-recovery/send-reminder', [
            'cart_id' => $this->cart->uuid,
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
            ->assertJsonPath('success', false);
    }

    /** @test */
    public function it_includes_discount_in_recovery_response()
    {
        $abandonedCart = AbandonedCart::factory()->create([
            'cart_id' => $this->cart->id,
            'user_id' => $this->user->id,
            'recovery_token' => 'valid-token-123',
            'status' => 'pending',
            'recovery_incentive' => [
                'type' => 'discount',
                'discount_percentage' => 20,
                'discount_code' => 'COMEBACK20',
            ],
        ]);

        $response = $this->getJson('/api/client/cart-recovery/recover/valid-token-123');

        $response->assertStatus(200)
            ->assertJsonPath('data.incentive.type', 'discount')
            ->assertJsonPath('data.incentive.discount_percentage', 20)
            ->assertJsonPath('data.incentive.discount_code', 'COMEBACK20');
    }

    /** @test */
    public function it_tracks_recovery_analytics()
    {
        $abandonedCart = AbandonedCart::factory()->create([
            'cart_id' => $this->cart->id,
            'user_id' => $this->user->id,
            'recovery_token' => 'valid-token-123',
            'status' => 'pending',
        ]);

        // Recover cart
        $this->getJson('/api/client/cart-recovery/recover/valid-token-123');

        // Convert cart
        $this->postJson('/api/client/cart-recovery/convert/valid-token-123', [
            'order_id' => 'ORD-12345',
            'order_total' => 210.00,
        ]);

        $abandonedCart = $abandonedCart->fresh();

        $this->assertNotNull($abandonedCart->recovered_at);
        $this->assertNotNull($abandonedCart->converted_at);
        $this->assertEquals('converted', $abandonedCart->status);
        $this->assertIsArray($abandonedCart->conversion_data);
        $this->assertEquals('ORD-12345', $abandonedCart->conversion_data['order_id']);
        $this->assertEquals(210.00, $abandonedCart->conversion_data['order_total']);
    }
}
