<?php

namespace Tests\Feature;

use App\Models\Review;
use App\Models\ReviewAttachment;
use App\Models\User;
use App\Models\Product;
use App\Models\Vendor;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Foundation\Testing\RefreshDatabase;

use Laravel\Passport\Passport;
use Tests\TestCase;

class ReviewImageUploadApiTest extends TestCase
{
    use RefreshDatabase;

    private $user;
    private $product;
    private $vendor;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data using factories
        $this->user = User::factory()->create();
        $this->vendor = Vendor::factory()->create();

        // Use the factory but override specific fields to avoid foreign key issues
        $this->product = Product::factory()->create([
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'is_active' => true,
            'is_approved' => true,
            'status' => 'submitted',
            'storage_conditions' => null, // Set to null to avoid integer constraint
            'country_of_origin' => null, // Set to null to avoid integer constraint
        ]);


    }

    public function test_user_cannot_submit_review_with_images_if_not_purchased()
    {
        // Authenticate user (but don't create an order)
        Passport::actingAs($this->user);

        $imageUrls = ['https://example.com/images/review.jpg'];

        $response = $this->postJson('/api/client/reviews', [
            'product_id' => $this->product->id,
            'rating' => 5,
            'comment' => 'Great product!',
            'images' => $imageUrls,
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['images']);
        $response->assertJsonFragment([
            'images' => ['Only verified buyers who have purchased this product can upload images with their reviews']
        ]);
    }

    public function test_user_can_submit_review_without_images_if_not_purchased()
    {
        // Authenticate user (but don't create an order)
        Passport::actingAs($this->user);

        $response = $this->postJson('/api/client/reviews', [
            'product_id' => $this->product->id,
            'rating' => 5,
            'comment' => 'Great product!',
        ]);

        $response->assertStatus(201);

        // Verify review was created without images
        $review = Review::where('user_id', $this->user->id)->first();
        $this->assertEquals(0, $review->attachments()->count());
    }

    public function test_image_upload_validation_rules()
    {
        $this->createCompletedOrder();
        Passport::actingAs($this->user);

        // Test maximum number of images (6 images, limit is 5)
        $images = [];
        for ($i = 1; $i <= 6; $i++) {
            $images[] = "https://example.com/images/image{$i}.jpg";
        }

        $response = $this->postJson('/api/client/reviews', [
            'product_id' => $this->product->id,
            'rating' => 5,
            'comment' => 'Too many images',
            'images' => $images,
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['images']);
    }

    public function test_image_file_type_validation()
    {
        $this->createCompletedOrder();
        Passport::actingAs($this->user);

        // Test invalid URL format
        $invalidUrl = 'not-a-valid-url';

        $response = $this->postJson('/api/client/reviews', [
            'product_id' => $this->product->id,
            'rating' => 5,
            'comment' => 'Invalid URL format',
            'images' => [$invalidUrl],
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['images.0']);
    }

    public function test_unauthenticated_user_cannot_submit_review_with_images()
    {
        $imageUrls = ['https://example.com/images/review.jpg'];

        $response = $this->postJson('/api/client/reviews', [
            'product_id' => $this->product->id,
            'rating' => 5,
            'comment' => 'Great product!',
            'images' => $imageUrls,
        ]);

        $response->assertStatus(401);
    }

    private function createCompletedOrder()
    {
        $order = Order::create([
            'user_id' => $this->user->id,
            'order_number' => 'ORD-' . time(),
            'total_amount' => 100.00,
            'payment_status' => 'paid',
            'fulfillment_status' => 'delivered',
            'status' => 'completed',
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'product_title' => $this->product->title_en,
            'quantity' => 1,
            'price' => 100.00,
            'total' => 100.00,
            'base_price' => 100.00,
            'product_snapshot' => [
                'name' => $this->product->title_en,
                'price' => 100.00
            ]
        ]);

        return $order;
    }
}
