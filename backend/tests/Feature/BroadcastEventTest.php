<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Vendor;
use App\Notifications\GenericRealtimeNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class BroadcastEventTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected User $vendorUser;
    protected User $adminUser;
    protected Vendor $vendor;

    protected function setUp(): void
    {
        parent::setUp();

        // Create regular user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_verified' => true,
            'is_active' => true,
        ]);

        // Create vendor
        $this->vendor = Vendor::factory()->create();

        // Create vendor user
        $this->vendorUser = User::factory()->create([
            'email' => '<EMAIL>',
            'vendor_id' => $this->vendor->id,
            'is_verified' => true,
            'is_active' => true,
        ]);
        $this->vendorUser->assignRole('vendor');

        // Create admin user
        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'is_verified' => true,
            'is_active' => true,
        ]);
        $this->adminUser->assignRole('admin');
    }

    public function test_notification_is_stored_in_database(): void
    {
        $notification = new GenericRealtimeNotification(
            'Test Order Update',
            'Your order has been shipped',
            'order',
            ['order_id' => 123],
            '/orders/123',
            'user',
            $this->user->id
        );

        $this->user->notify($notification);

        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->user->id,
            'notifiable_type' => User::class,
            'type' => GenericRealtimeNotification::class,
        ]);

        $storedNotification = $this->user->notifications()->first();
        $data = $storedNotification->data;

        $this->assertEquals('Test Order Update', $data['title']);
        $this->assertEquals('Your order has been shipped', $data['body']);
        $this->assertEquals('order', $data['category']);
        $this->assertEquals(['order_id' => 123], $data['meta']);
        $this->assertEquals('/orders/123', $data['action_url']);
    }

    public function test_notification_broadcasts_to_correct_channel(): void
    {
        Event::fake();

        $notification = new GenericRealtimeNotification(
            'Test Notification',
            'Test body',
            'system',
            [],
            null,
            'user',
            $this->user->id
        );

        $this->user->notify($notification);

        // Check that the notification was queued for broadcasting
        Event::assertDispatched(\Illuminate\Notifications\Events\NotificationSent::class);
    }

    public function test_user_scope_notification_uses_correct_channel(): void
    {
        $notification = new GenericRealtimeNotification(
            'User Notification',
            'This is for a specific user',
            'system',
            [],
            null,
            'user',
            $this->user->id
        );

        $channels = $notification->broadcastOn();
        $this->assertEquals(["private-user.{$this->user->id}"], $channels);
    }

    public function test_vendor_scope_notification_uses_correct_channel(): void
    {
        $notification = new GenericRealtimeNotification(
            'Vendor Notification',
            'This is for a specific vendor',
            'order',
            [],
            null,
            'vendor',
            $this->vendor->id
        );

        $channels = $notification->broadcastOn();
        $this->assertEquals(["private-vendor.{$this->vendor->id}"], $channels);
    }

    public function test_admin_scope_notification_uses_correct_channel(): void
    {
        $notification = new GenericRealtimeNotification(
            'Admin Notification',
            'This is for admins',
            'system',
            [],
            null,
            'admin',
            0
        );

        $channels = $notification->broadcastOn();
        $this->assertEquals(['private-admin.global'], $channels);
    }

    public function test_order_scope_notification_uses_correct_channel(): void
    {
        $orderId = 123;
        $notification = new GenericRealtimeNotification(
            'Order Notification',
            'Order status update',
            'order',
            [],
            null,
            'order',
            $orderId
        );

        $channels = $notification->broadcastOn();
        $this->assertEquals(["private-order.{$orderId}"], $channels);
    }

    public function test_notification_broadcast_event_name(): void
    {
        $notification = new GenericRealtimeNotification(
            'Test Notification',
            'Test body',
            'system'
        );

        $this->assertEquals('notification.created', $notification->broadcastAs());
    }

    public function test_notification_broadcast_payload(): void
    {
        $notification = new GenericRealtimeNotification(
            'Test Title',
            'Test Body',
            'order',
            ['test' => 'meta'],
            '/test-url'
        );

        // Set a mock ID for testing
        $notification->id = 'test-uuid-123';

        $broadcastMessage = $notification->toBroadcast($this->user);
        $payload = $broadcastMessage->data;

        $this->assertEquals('test-uuid-123', $payload['id']);
        $this->assertEquals('Test Title', $payload['title']);
        $this->assertEquals('Test Body', $payload['body']);
        $this->assertEquals('order', $payload['category']);
        $this->assertArrayHasKey('created_at', $payload);
    }

    public function test_notification_implements_should_queue(): void
    {
        $notification = new GenericRealtimeNotification(
            'Test Notification',
            'Test body',
            'system'
        );

        $this->assertInstanceOf(\Illuminate\Contracts\Queue\ShouldQueue::class, $notification);
    }

    public function test_notification_implements_should_broadcast_now(): void
    {
        $notification = new GenericRealtimeNotification(
            'Test Notification',
            'Test body',
            'system'
        );

        $this->assertInstanceOf(\Illuminate\Contracts\Broadcasting\ShouldBroadcastNow::class, $notification);
    }

    public function test_notification_uses_correct_delivery_channels(): void
    {
        $notification = new GenericRealtimeNotification(
            'Test Notification',
            'Test body',
            'system'
        );

        $channels = $notification->via($this->user);
        $this->assertEquals(['database', 'broadcast'], $channels);
    }

    public function test_notification_database_payload(): void
    {
        $notification = new GenericRealtimeNotification(
            'Database Test',
            'Database body',
            'promotion',
            ['promo_id' => 456],
            '/promotions/456'
        );

        $payload = $notification->toArray($this->user);

        $this->assertEquals('Database Test', $payload['title']);
        $this->assertEquals('Database body', $payload['body']);
        $this->assertEquals('promotion', $payload['category']);
        $this->assertEquals(['promo_id' => 456], $payload['meta']);
        $this->assertEquals('/promotions/456', $payload['action_url']);
    }

    public function test_multiple_notifications_to_same_user(): void
    {
        $notification1 = new GenericRealtimeNotification(
            'First Notification',
            'First body',
            'order'
        );

        $notification2 = new GenericRealtimeNotification(
            'Second Notification',
            'Second body',
            'system'
        );

        $this->user->notify($notification1);
        $this->user->notify($notification2);

        $this->assertEquals(2, $this->user->notifications()->count());
        $this->assertEquals(2, $this->user->unreadNotifications()->count());
    }
}
