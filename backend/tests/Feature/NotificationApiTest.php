<?php

namespace Tests\Feature;

use App\Models\User;
use App\Notifications\GenericRealtimeNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Notification;
use Laravel\Passport\Passport;
use Tests\TestCase;

class NotificationApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_verified' => true,
            'is_active' => true,
        ]);
    }

    public function test_can_get_notifications_list(): void
    {
        Passport::actingAs($this->user);

        // Create some test notifications
        $this->user->notify(new GenericRealtimeNotification(
            'Test Order Update',
            'Your order has been shipped',
            'order',
            ['order_id' => 123],
            '/orders/123'
        ));

        $this->user->notify(new GenericRealtimeNotification(
            'System Maintenance',
            'Scheduled maintenance tonight',
            'system'
        ));

        $response = $this->getJson('/api/notifications');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'title',
                            'body',
                            'meta',
                            'category',
                            'action_url',
                            'read_at',
                            'created_at',
                        ]
                    ],
                    'current_page',
                    'last_page',
                    'per_page',
                    'total',
                ]
            ]);

        $this->assertEquals(2, $response->json('data.total'));
    }

    public function test_can_filter_notifications_by_category(): void
    {
        Passport::actingAs($this->user);

        // Create notifications with different categories
        $this->user->notify(new GenericRealtimeNotification(
            'Order Update',
            'Your order has been shipped',
            'order'
        ));

        $this->user->notify(new GenericRealtimeNotification(
            'System Update',
            'System maintenance completed',
            'system'
        ));

        $response = $this->getJson('/api/notifications?category=order');

        $response->assertStatus(200);
        $this->assertEquals(1, $response->json('data.total'));
        $this->assertEquals('order', $response->json('data.data.0.category'));
    }

    public function test_can_filter_unread_notifications(): void
    {
        Passport::actingAs($this->user);

        // Create notifications
        $this->user->notify(new GenericRealtimeNotification(
            'Unread Notification',
            'This should appear in unread filter',
            'system'
        ));

        $notification = $this->user->notifications()->first();
        $notification->markAsRead(); // Mark one as read

        $this->user->notify(new GenericRealtimeNotification(
            'Another Unread',
            'This should also appear',
            'order'
        ));

        $response = $this->getJson('/api/notifications?unread_only=true');

        $response->assertStatus(200);
        $this->assertEquals(1, $response->json('data.total'));
    }

    public function test_can_get_unread_count(): void
    {
        Passport::actingAs($this->user);

        // Create some notifications
        $this->user->notify(new GenericRealtimeNotification('Test 1', 'Body 1', 'system'));
        $this->user->notify(new GenericRealtimeNotification('Test 2', 'Body 2', 'order'));
        $this->user->notify(new GenericRealtimeNotification('Test 3', 'Body 3', 'promotion'));

        $response = $this->getJson('/api/notifications/unread-count');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'count'
                ]
            ]);

        $this->assertEquals(3, $response->json('data.count'));
    }

    public function test_can_mark_notification_as_read(): void
    {
        Passport::actingAs($this->user);

        $this->user->notify(new GenericRealtimeNotification(
            'Test Notification',
            'Test body',
            'system'
        ));

        $notification = $this->user->notifications()->first();
        $this->assertNull($notification->read_at);

        $response = $this->postJson("/api/notifications/{$notification->id}/read");

        $response->assertStatus(200);

        $notification->refresh();
        $this->assertNotNull($notification->read_at);
    }

    public function test_can_mark_all_notifications_as_read(): void
    {
        Passport::actingAs($this->user);

        // Create multiple notifications
        $this->user->notify(new GenericRealtimeNotification('Test 1', 'Body 1', 'system'));
        $this->user->notify(new GenericRealtimeNotification('Test 2', 'Body 2', 'order'));

        $response = $this->postJson('/api/notifications/read-all');

        $response->assertStatus(200);

        $unreadCount = $this->user->unreadNotifications()->count();
        $this->assertEquals(0, $unreadCount);
    }

    public function test_can_delete_notification(): void
    {
        Passport::actingAs($this->user);

        $this->user->notify(new GenericRealtimeNotification(
            'Test Notification',
            'Test body',
            'system'
        ));

        $notification = $this->user->notifications()->first();

        $response = $this->deleteJson("/api/notifications/{$notification->id}");

        $response->assertStatus(200);

        $this->assertDatabaseMissing('notifications', [
            'id' => $notification->id
        ]);
    }

    public function test_cannot_access_notifications_without_authentication(): void
    {
        $response = $this->getJson('/api/notifications');
        $response->assertStatus(401);

        $response = $this->getJson('/api/notifications/unread-count');
        $response->assertStatus(401);

        $response = $this->postJson('/api/notifications/read-all');
        $response->assertStatus(401);
    }

    public function test_cannot_access_other_users_notifications(): void
    {
        $otherUser = User::factory()->create();

        Passport::actingAs($otherUser);

        $this->user->notify(new GenericRealtimeNotification(
            'Private Notification',
            'This should not be accessible',
            'system'
        ));

        $notification = $this->user->notifications()->first();

        $response = $this->postJson("/api/notifications/{$notification->id}/read");
        $response->assertStatus(404);

        $response = $this->deleteJson("/api/notifications/{$notification->id}");
        $response->assertStatus(404);
    }
}
