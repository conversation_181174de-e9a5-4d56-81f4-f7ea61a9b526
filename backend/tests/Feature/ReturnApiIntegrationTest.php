<?php

namespace Tests\Feature;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\ReturnRequest;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Passport\Passport;
use Tests\TestCase;

class ReturnApiIntegrationTest extends TestCase
{
    use RefreshDatabase;

    public function test_complete_return_workflow()
    {
        // Setup
        $user = User::factory()->create();
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        
        $order = Order::factory()->create(['user_id' => $user->id]);
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();
        
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product1->id,
            'price' => 100.00,
            'quantity' => 2,
        ]);
        
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product2->id,
            'price' => 50.00,
            'quantity' => 1,
        ]);

        // Step 1: User creates return request
        Passport::actingAs($user);
        
        $createResponse = $this->postJson('/api/returns', [
            'order_id' => $order->id,
            'type' => 'refund',
            'reason' => 'Product defective',
            'description' => 'Items not working properly',
            'items' => [
                ['product_id' => $product1->id, 'quantity' => 1],
                ['product_id' => $product2->id, 'quantity' => 1, 'reason' => 'Wrong color'],
            ],
        ]);

        $createResponse->assertStatus(201);
        $returns = $createResponse->json('data');
        $this->assertCount(2, $returns);

        // Step 2: User views their returns
        $listResponse = $this->getJson('/api/returns');
        $listResponse->assertStatus(200)
                    ->assertJsonCount(2, 'data');

        // Step 3: User views specific return details
        $returnId = $returns[0]['id'];
        $detailResponse = $this->getJson("/api/returns/{$returnId}");
        $detailResponse->assertStatus(200)
                      ->assertJsonStructure([
                          'data' => ['order', 'product', 'user']
                      ]);

        // Step 4: Admin views all returns
        Passport::actingAs($admin);
        
        $adminListResponse = $this->getJson('/api/admin/returns');
        $adminListResponse->assertStatus(200)
                         ->assertJsonCount(2, 'data');

        // Step 5: Admin approves return
        $approveResponse = $this->putJson("/api/admin/returns/{$returnId}", [
            'status' => 'approved',
            'admin_notes' => 'Approved for refund processing',
        ]);

        $approveResponse->assertStatus(200);
        
        $this->assertDatabaseHas('returns', [
            'id' => $returnId,
            'status' => 'approved',
            'approved_by' => $admin->id,
        ]);

        // Step 6: Admin completes return
        $completeResponse = $this->putJson("/api/admin/returns/{$returnId}", [
            'status' => 'completed',
            'admin_notes' => 'Refund processed successfully',
        ]);

        $completeResponse->assertStatus(200);
        
        $this->assertDatabaseHas('returns', [
            'id' => $returnId,
            'status' => 'completed',
        ]);

        // Step 7: Check statistics
        $statsResponse = $this->getJson('/api/admin/returns/statistics');
        $statsResponse->assertStatus(200)
                     ->assertJson([
                         'data' => [
                             'total' => 2,
                             'completed' => 1,
                             'pending' => 1,
                         ]
                     ]);
    }

    public function test_return_filtering_and_search()
    {
        $user = User::factory()->create();
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        // Create returns with different statuses
        ReturnRequest::factory()->create([
            'user_id' => $user->id,
            'status' => 'pending',
            'reason' => 'Defective product',
        ]);
        
        ReturnRequest::factory()->create([
            'user_id' => $user->id,
            'status' => 'approved',
            'reason' => 'Wrong size',
        ]);

        // Test user filtering by status
        Passport::actingAs($user);
        
        $pendingResponse = $this->getJson('/api/returns?status=pending');
        $pendingResponse->assertStatus(200)
                       ->assertJsonCount(1, 'data');

        $approvedResponse = $this->getJson('/api/returns?status=approved');
        $approvedResponse->assertStatus(200)
                        ->assertJsonCount(1, 'data');

        // Test admin search functionality
        Passport::actingAs($admin);
        
        $searchResponse = $this->getJson('/api/admin/returns?search=defective');
        $searchResponse->assertStatus(200)
                      ->assertJsonCount(1, 'data');
    }

    public function test_return_authorization_and_security()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $return = ReturnRequest::factory()->create(['user_id' => $user1->id]);

        // Test user cannot access other user's returns
        Passport::actingAs($user2);
        
        $response = $this->getJson("/api/returns/{$return->id}");
        $response->assertStatus(500); // Should fail

        // Test unauthenticated access is blocked
        $response = $this->getJson('/api/returns');
        $response->assertStatus(401);

        // Test admin can access all returns
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        
        Passport::actingAs($admin);
        
        $response = $this->getJson("/api/admin/returns/{$return->id}");
        $response->assertStatus(200);
    }
}