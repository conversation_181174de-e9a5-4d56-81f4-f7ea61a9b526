<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Passport\Passport;
use Tests\TestCase;

class BroadcastAuthTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected User $vendorUser;
    protected User $adminUser;
    protected Vendor $vendor;

    protected function setUp(): void
    {
        parent::setUp();

        // Create regular user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_verified' => true,
            'is_active' => true,
        ]);

        // Create vendor
        $this->vendor = Vendor::factory()->create();

        // Create vendor user
        $this->vendorUser = User::factory()->create([
            'email' => '<EMAIL>',
            'vendor_id' => $this->vendor->id,
            'is_verified' => true,
            'is_active' => true,
        ]);
        $this->vendorUser->assignRole('vendor');

        // Create admin user
        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'is_verified' => true,
            'is_active' => true,
        ]);
        $this->adminUser->assignRole('admin');
    }

    public function test_can_authenticate_to_user_channel(): void
    {
        Passport::actingAs($this->user);

        $response = $this->postJson('/broadcasting/auth', [
            'channel_name' => "private-user.{$this->user->id}",
            'socket_id' => 'test-socket-id',
        ]);

        $response->assertStatus(200);
    }

    public function test_cannot_authenticate_to_other_user_channel(): void
    {
        $otherUser = User::factory()->create();
        Passport::actingAs($this->user);

        $response = $this->postJson('/broadcasting/auth', [
            'channel_name' => "private-user.{$otherUser->id}",
            'socket_id' => 'test-socket-id',
        ]);

        $response->assertStatus(403);
    }

    public function test_vendor_can_authenticate_to_own_vendor_channel(): void
    {
        Passport::actingAs($this->vendorUser);

        $response = $this->postJson('/broadcasting/auth', [
            'channel_name' => "private-vendor.{$this->vendor->id}",
            'socket_id' => 'test-socket-id',
        ]);

        $response->assertStatus(200);
    }

    public function test_vendor_cannot_authenticate_to_other_vendor_channel(): void
    {
        $otherVendor = Vendor::factory()->create();
        Passport::actingAs($this->vendorUser);

        $response = $this->postJson('/broadcasting/auth', [
            'channel_name' => "private-vendor.{$otherVendor->id}",
            'socket_id' => 'test-socket-id',
        ]);

        $response->assertStatus(403);
    }

    public function test_admin_can_authenticate_to_vendor_channels(): void
    {
        Passport::actingAs($this->adminUser);

        $response = $this->postJson('/broadcasting/auth', [
            'channel_name' => "private-vendor.{$this->vendor->id}",
            'socket_id' => 'test-socket-id',
        ]);

        $response->assertStatus(200);
    }

    public function test_admin_can_authenticate_to_admin_channel(): void
    {
        Passport::actingAs($this->adminUser);

        $response = $this->postJson('/broadcasting/auth', [
            'channel_name' => 'private-admin.global',
            'socket_id' => 'test-socket-id',
        ]);

        $response->assertStatus(200);
    }

    public function test_regular_user_cannot_authenticate_to_admin_channel(): void
    {
        Passport::actingAs($this->user);

        $response = $this->postJson('/broadcasting/auth', [
            'channel_name' => 'private-admin.global',
            'socket_id' => 'test-socket-id',
        ]);

        $response->assertStatus(403);
    }

    public function test_vendor_cannot_authenticate_to_admin_channel(): void
    {
        Passport::actingAs($this->vendorUser);

        $response = $this->postJson('/broadcasting/auth', [
            'channel_name' => 'private-admin.global',
            'socket_id' => 'test-socket-id',
        ]);

        $response->assertStatus(403);
    }

    public function test_broadcasting_auth_requires_authentication(): void
    {
        $response = $this->postJson('/broadcasting/auth', [
            'channel_name' => "private-user.{$this->user->id}",
            'socket_id' => 'test-socket-id',
        ]);

        $response->assertStatus(401);
    }

    public function test_broadcasting_auth_requires_valid_channel_name(): void
    {
        Passport::actingAs($this->user);

        $response = $this->postJson('/broadcasting/auth', [
            'channel_name' => 'invalid-channel-name',
            'socket_id' => 'test-socket-id',
        ]);

        $response->assertStatus(403);
    }

    public function test_broadcasting_auth_requires_socket_id(): void
    {
        Passport::actingAs($this->user);

        $response = $this->postJson('/broadcasting/auth', [
            'channel_name' => "private-user.{$this->user->id}",
        ]);

        $response->assertStatus(422);
    }

    public function test_jwt_token_in_authorization_header_works(): void
    {
        $token = $this->user->createToken('test-token')->accessToken;

        $response = $this->postJson('/broadcasting/auth', [
            'channel_name' => "private-user.{$this->user->id}",
            'socket_id' => 'test-socket-id',
        ], [
            'Authorization' => "Bearer {$token}",
        ]);

        $response->assertStatus(200);
    }

    public function test_invalid_jwt_token_fails_authentication(): void
    {
        $response = $this->postJson('/broadcasting/auth', [
            'channel_name' => "private-user.{$this->user->id}",
            'socket_id' => 'test-socket-id',
        ], [
            'Authorization' => 'Bearer invalid-token',
        ]);

        $response->assertStatus(401);
    }

    public function test_expired_jwt_token_fails_authentication(): void
    {
        // This would require manipulating token expiration
        // For now, we'll test with a malformed token
        $response = $this->postJson('/broadcasting/auth', [
            'channel_name' => "private-user.{$this->user->id}",
            'socket_id' => 'test-socket-id',
        ], [
            'Authorization' => 'Bearer expired.token.here',
        ]);

        $response->assertStatus(401);
    }
}
