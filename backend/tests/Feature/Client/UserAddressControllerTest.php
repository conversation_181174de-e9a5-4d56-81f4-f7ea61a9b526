<?php

namespace Tests\Feature\Client;

use App\Models\User;
use App\Models\UserAddress;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Passport\Passport;
use Tests\TestCase;

class UserAddressControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        Passport::actingAs($this->user);
    }

    public function test_list_user_addresses(): void
    {
        // Create some addresses for the user
        UserAddress::factory()->count(3)->create([
            'user_id' => $this->user->id,
        ]);

        $response = $this->getJson('/api/client/user-addresses');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    '*' => [
                        'id',
                        'type',
                        'first_name',
                        'last_name',
                        'company',
                        'address_line_1',
                        'address_line_2',
                        'city',
                        'state',
                        'postal_code',
                        'country',
                        'phone',
                        'email',
                        'is_default',
                        'created_at',
                        'updated_at',
                    ],
                ],
                'message',
            ]);

        $this->assertCount(3, $response->json('data'));
    }

    public function test_create_user_address(): void
    {
        $addressData = [
            'type' => 'shipping',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'company' => 'Test Company',
            'address_line_1' => '123 Test Street',
            'address_line_2' => 'Apartment 4B',
            'city' => 'Dubai',
            'state' => 'Dubai',
            'postal_code' => '12345',
            'country' => 'AE',
            'phone' => '+971501234567',
            'email' => '<EMAIL>',
            'is_default' => true,
        ];

        $response = $this->postJson('/api/client/user-addresses', $addressData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'type',
                    'first_name',
                    'last_name',
                    'company',
                    'address_line_1',
                    'address_line_2',
                    'city',
                    'state',
                    'postal_code',
                    'country',
                    'phone',
                    'email',
                    'is_default',
                ],
            ]);

        $this->assertDatabaseHas('user_addresses', [
            'user_id' => $this->user->id,
            'type' => 'shipping',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'address_line_1' => '123 Test Street',
            'city' => 'Dubai',
            'country' => 'AE',
            'is_default' => true,
        ]);
    }

    public function test_create_address_validation_errors(): void
    {
        $invalidData = [
            'type' => 'invalid_type',
            'first_name' => '',
            'last_name' => '',
            'address_line_1' => '',
            'city' => '',
            'country' => 'INVALID',
            'phone' => '123',
            'email' => 'invalid-email',
        ];

        $response = $this->postJson('/api/client/user-addresses', $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'type',
                'first_name',
                'last_name',
                'address_line_1',
                'city',
                'country',
                'phone',
                'email',
            ]);
    }

    public function test_show_user_address(): void
    {
        $address = UserAddress::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $response = $this->getJson("/api/client/user-addresses/{$address->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'id',
                    'type',
                    'first_name',
                    'last_name',
                    'address_line_1',
                    'city',
                    'country',
                    'is_default',
                ],
                'message',
            ])
            ->assertJson([
                'data' => [
                    'id' => $address->id,
                    'first_name' => $address->first_name,
                    'last_name' => $address->last_name,
                ],
            ]);
    }

    public function test_show_address_unauthorized(): void
    {
        $otherUser = User::factory()->create();
        $address = UserAddress::factory()->create([
            'user_id' => $otherUser->id,
        ]);

        $response = $this->getJson("/api/client/user-addresses/{$address->id}");

        $response->assertStatus(403);
    }

    public function test_update_user_address(): void
    {
        $address = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'first_name' => 'Original',
            'last_name' => 'Name',
        ]);

        $updateData = [
            'first_name' => 'Updated',
            'last_name' => 'Name',
            'address_line_1' => 'Updated Address',
            'city' => 'Abu Dhabi',
            'is_default' => true,
        ];

        $response = $this->putJson("/api/client/user-addresses/{$address->id}", $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'first_name',
                    'last_name',
                    'address_line_1',
                    'city',
                    'is_default',
                ],
            ]);

        $this->assertDatabaseHas('user_addresses', [
            'id' => $address->id,
            'first_name' => 'Updated',
            'address_line_1' => 'Updated Address',
            'city' => 'Abu Dhabi',
            'is_default' => true,
        ]);
    }

    public function test_update_address_unauthorized(): void
    {
        $otherUser = User::factory()->create();
        $address = UserAddress::factory()->create([
            'user_id' => $otherUser->id,
        ]);

        $updateData = [
            'first_name' => 'Hacker',
        ];

        $response = $this->putJson("/api/client/user-addresses/{$address->id}", $updateData);

        $response->assertStatus(403);
    }

    public function test_delete_user_address(): void
    {
        $address = UserAddress::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $response = $this->deleteJson("/api/client/user-addresses/{$address->id}");

        $response->assertStatus(200)
            ->assertJson([
                'status' => true,
                'message' => 'Address deleted successfully',
            ]);

        $this->assertDatabaseMissing('user_addresses', [
            'id' => $address->id,
        ]);
    }

    public function test_delete_address_unauthorized(): void
    {
        $otherUser = User::factory()->create();
        $address = UserAddress::factory()->create([
            'user_id' => $otherUser->id,
        ]);

        $response = $this->deleteJson("/api/client/user-addresses/{$address->id}");

        $response->assertStatus(403);
    }

    public function test_set_default_address_unsets_others(): void
    {
        // Create multiple addresses, one already default
        $defaultAddress = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'is_default' => true,
        ]);

        $newAddress = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'is_default' => false,
        ]);

        // Update new address to be default
        $updateData = [
            'first_name' => $newAddress->first_name,
            'last_name' => $newAddress->last_name,
            'address_line_1' => $newAddress->address_line_1,
            'city' => $newAddress->city,
            'country' => $newAddress->country,
            'is_default' => true,
        ];

        $response = $this->putJson("/api/client/user-addresses/{$newAddress->id}", $updateData);

        $response->assertStatus(200);

        // Check that old default is no longer default
        $this->assertDatabaseHas('user_addresses', [
            'id' => $defaultAddress->id,
            'is_default' => false,
        ]);

        // Check that new address is now default
        $this->assertDatabaseHas('user_addresses', [
            'id' => $newAddress->id,
            'is_default' => true,
        ]);
    }

    public function test_create_default_address_unsets_others(): void
    {
        // Create existing default address
        $existingDefault = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'is_default' => true,
        ]);

        $newAddressData = [
            'type' => 'billing',
            'first_name' => 'New',
            'last_name' => 'Default',
            'address_line_1' => '456 New Street',
            'city' => 'Sharjah',
            'country' => 'AE',
            'is_default' => true,
        ];

        $response = $this->postJson('/api/client/user-addresses', $newAddressData);

        $response->assertStatus(201);

        // Check that old default is no longer default
        $this->assertDatabaseHas('user_addresses', [
            'id' => $existingDefault->id,
            'is_default' => false,
        ]);

        // Check that new address is default
        $this->assertDatabaseHas('user_addresses', [
            'user_id' => $this->user->id,
            'first_name' => 'New',
            'is_default' => true,
        ]);
    }

    public function test_address_type_validation(): void
    {
        $addressData = [
            'type' => 'both', // Valid type
            'first_name' => 'Test',
            'last_name' => 'User',
            'address_line_1' => '123 Test St',
            'city' => 'Dubai',
            'country' => 'AE',
        ];

        $response = $this->postJson('/api/client/user-addresses', $addressData);

        $response->assertStatus(201);

        $this->assertDatabaseHas('user_addresses', [
            'type' => 'both',
        ]);
    }

    public function test_unauthenticated_access_denied(): void
    {
        Passport::actingAs(null);

        $response = $this->getJson('/api/client/user-addresses');
        $response->assertStatus(401);

        $response = $this->postJson('/api/client/user-addresses', []);
        $response->assertStatus(401);
    }

    public function test_address_not_found(): void
    {
        $response = $this->getJson('/api/client/user-addresses/99999');

        $response->assertStatus(404);
    }
}
