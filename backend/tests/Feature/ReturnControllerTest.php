<?php

namespace Tests\Feature;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\ReturnRequest;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Passport\Passport;
use Tests\TestCase;

class ReturnControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_create_return_request()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create(['user_id' => $user->id]);
        $product = Product::factory()->create();
        
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'price' => 100.00,
            'quantity' => 2,
        ]);

        Passport::actingAs($user);

        $response = $this->postJson('/api/returns', [
            'order_id' => $order->id,
            'type' => 'refund',
            'reason' => 'Product defective',
            'description' => 'Item arrived damaged',
            'items' => [
                ['product_id' => $product->id, 'quantity' => 1],
            ],
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        '*' => [
                            'id',
                            'return_number',
                            'order_id',
                            'product_id',
                            'user_id',
                            'type',
                            'status',
                            'reason',
                            'quantity',
                            'total_amount',
                        ]
                    ]
                ]);

        $this->assertDatabaseHas('returns', [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'user_id' => $user->id,
            'type' => 'refund',
            'status' => 'pending',
        ]);
    }

    public function test_user_can_view_their_returns()
    {
        $user = User::factory()->create();
        $otherUser = User::factory()->create();
        
        ReturnRequest::factory()->create(['user_id' => $user->id]);
        ReturnRequest::factory()->create(['user_id' => $otherUser->id]);

        Passport::actingAs($user);

        $response = $this->getJson('/api/returns');

        $response->assertStatus(200)
                ->assertJsonCount(1, 'data');
    }

    public function test_user_can_view_return_details()
    {
        $user = User::factory()->create();
        $return = ReturnRequest::factory()->create(['user_id' => $user->id]);

        Passport::actingAs($user);

        $response = $this->getJson("/api/returns/{$return->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'return_number',
                        'order',
                        'product',
                        'user',
                    ]
                ]);
    }

    public function test_user_can_cancel_pending_return()
    {
        $user = User::factory()->create();
        $return = ReturnRequest::factory()->create([
            'user_id' => $user->id,
            'status' => 'pending',
        ]);

        Passport::actingAs($user);

        $response = $this->patchJson("/api/returns/{$return->id}/cancel");

        $response->assertStatus(200);
        
        $this->assertDatabaseHas('returns', [
            'id' => $return->id,
            'status' => 'cancelled',
        ]);
    }

    public function test_user_cannot_view_other_users_returns()
    {
        $user = User::factory()->create();
        $otherUser = User::factory()->create();
        $return = ReturnRequest::factory()->create(['user_id' => $otherUser->id]);

        Passport::actingAs($user);

        $response = $this->getJson("/api/returns/{$return->id}");

        $response->assertStatus(500); // Should fail to find the return
    }

    public function test_create_return_validation_fails_with_invalid_data()
    {
        $user = User::factory()->create();

        Passport::actingAs($user);

        $response = $this->postJson('/api/returns', [
            'order_id' => 999, // Non-existent order
            'type' => 'invalid_type',
            'items' => [],
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['order_id', 'type', 'reason', 'items']);
    }

    public function test_admin_can_view_all_returns()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        
        ReturnRequest::factory()->count(3)->create();

        Passport::actingAs($admin);

        $response = $this->getJson('/api/admin/returns');

        $response->assertStatus(200)
                ->assertJsonCount(3, 'data');
    }

    public function test_admin_can_update_return_status()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        $return = ReturnRequest::factory()->create(['status' => 'pending']);

        Passport::actingAs($admin);

        $response = $this->putJson("/api/admin/returns/{$return->id}", [
            'status' => 'approved',
            'admin_notes' => 'Approved for processing',
        ]);

        $response->assertStatus(200);
        
        $this->assertDatabaseHas('returns', [
            'id' => $return->id,
            'status' => 'approved',
            'admin_notes' => 'Approved for processing',
            'approved_by' => $admin->id,
        ]);
    }

    public function test_admin_can_view_return_statistics()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        
        ReturnRequest::factory()->create(['status' => 'pending']);
        ReturnRequest::factory()->create(['status' => 'approved']);

        Passport::actingAs($admin);

        $response = $this->getJson('/api/admin/returns/statistics');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'total',
                        'pending',
                        'approved',
                        'completed',
                        'rejected',
                    ]
                ]);
    }
}