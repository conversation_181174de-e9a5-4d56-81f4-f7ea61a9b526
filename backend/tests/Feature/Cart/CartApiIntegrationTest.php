<?php

namespace Tests\Feature\Cart;

use Tests\TestCase;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Product;
use App\Models\ShoppingCart;
use App\Models\CartItem;
use App\Models\Inventory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class CartApiIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected Vendor $vendor1;
    protected Vendor $vendor2;
    protected Product $product1;
    protected Product $product2;
    protected Product $product3;
    protected string $cartUuid;
    protected string $cartToken;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test vendors
        $this->vendor1 = Vendor::factory()->active()->create([
            'name_tl_en' => 'Test Vendor One',
            'vendor_display_name_en' => 'Vendor One Store',
            'min_order_value' => 50.00,
            'free_shipping_threshold' => 200.00,
        ]);

        $this->vendor2 = Vendor::factory()->active()->create([
            'name_tl_en' => 'Test Vendor Two', 
            'vendor_display_name_en' => 'Vendor Two Store',
            'min_order_value' => 75.00,
            'free_shipping_threshold' => 150.00,
        ]);

        // Create test products with minimal required fields to avoid schema issues
        $this->product1 = $this->createMinimalProduct($this->vendor1, 'Test Product 1', 100.00, 90.00);
        $this->product2 = $this->createMinimalProduct($this->vendor1, 'Test Product 2', 75.00);
        $this->product3 = $this->createMinimalProduct($this->vendor2, 'Test Product 3', 120.00);

        // Create inventory for products
        foreach ([$this->product1, $this->product2, $this->product3] as $product) {
            Inventory::create([
                'product_id' => $product->id,
                'vendor_id' => $product->vendor_id,
                'stock' => 100,
                'reserved' => 0,
                'stock_status' => 'in_stock',
                'is_active' => true,
            ]);
        }

        $this->user = User::factory()->create();
    }

    public function test_complete_cart_api_workflow_with_new_vendors_structure()
    {
        echo "\n=== COMPREHENSIVE CART API TEST WITH NEW VENDORS STRUCTURE ===\n";

        // Step 1: Create a new cart
        echo "\n1. Creating new cart...\n";
        $createResponse = $this->postJson('/api/client/cart', [
            'currency' => 'AED',
            'notes' => 'Integration test cart',
        ]);

        $createResponse->assertStatus(201);
        $this->cartUuid = $createResponse->json('data.uuid');
        $this->cartToken = $createResponse->json('data.cart_token');
        
        echo "✓ Cart created: {$this->cartUuid}\n";
        echo "✓ Cart token: {$this->cartToken}\n";

        // Verify initial cart structure
        $this->assertCartStructure($createResponse->json('data'), 'CREATE');

        // Step 2: Add items from different vendors
        echo "\n2. Adding items to cart...\n";
        
        // Add item from vendor 1
        $addItem1Response = $this->postJson("/api/client/cart/{$this->cartUuid}/items", [
            'product_id' => $this->product1->id,
            'quantity' => 2,
            'cart_token' => $this->cartToken,
        ]);
        $addItem1Response->assertStatus(201);
        echo "✓ Added Product 1 (Vendor 1): 2x {$this->product1->title_en}\n";
        $this->assertCartStructure($addItem1Response->json('data'), 'ADD_ITEM_1');

        // Add another item from vendor 1
        $addItem2Response = $this->postJson("/api/client/cart/{$this->cartUuid}/items", [
            'product_id' => $this->product2->id,
            'quantity' => 1,
            'cart_token' => $this->cartToken,
        ]);
        $addItem2Response->assertStatus(201);
        echo "✓ Added Product 2 (Vendor 1): 1x {$this->product2->title_en}\n";
        $this->assertCartStructure($addItem2Response->json('data'), 'ADD_ITEM_2');

        // Add item from vendor 2
        $addItem3Response = $this->postJson("/api/client/cart/{$this->cartUuid}/items", [
            'product_id' => $this->product3->id,
            'quantity' => 1,
            'cart_token' => $this->cartToken,
        ]);
        $addItem3Response->assertStatus(201);
        echo "✓ Added Product 3 (Vendor 2): 1x {$this->product3->title_en}\n";
        $this->assertCartStructure($addItem3Response->json('data'), 'ADD_ITEM_3');

        // Step 3: Get cart and verify structure
        echo "\n3. Getting cart data...\n";
        $getResponse = $this->getJson("/api/client/cart/{$this->cartUuid}?cart_token={$this->cartToken}");
        $getResponse->assertStatus(200);
        echo "✓ Retrieved cart successfully\n";
        $this->assertCartStructure($getResponse->json('data'), 'GET_CART');

        // Step 4: Update item quantity
        echo "\n4. Updating item quantity...\n";
        $cartData = $getResponse->json('data');
        $firstVendor = $cartData['vendors'][0];
        $firstItem = $firstVendor['items'][0];
        
        $updateResponse = $this->putJson("/api/client/cart/{$this->cartUuid}/items/{$firstItem['id']}", [
            'quantity' => 3,
            'cart_token' => $this->cartToken,
        ]);
        $updateResponse->assertStatus(200);
        echo "✓ Updated item quantity to 3\n";
        $this->assertCartStructure($updateResponse->json('data'), 'UPDATE_ITEM');

        // Step 5: Remove an item
        echo "\n5. Removing an item...\n";
        $cartData = $updateResponse->json('data');
        $firstVendor = $cartData['vendors'][0];
        $secondItem = $firstVendor['items'][1];
        
        $removeResponse = $this->deleteJson("/api/client/cart/{$this->cartUuid}/items/{$secondItem['id']}", [
            'cart_token' => $this->cartToken,
        ]);
        $removeResponse->assertStatus(200);
        echo "✓ Removed item successfully\n";
        $this->assertCartStructure($removeResponse->json('data'), 'REMOVE_ITEM');

        echo "\n=== ALL CART API OPERATIONS COMPLETED SUCCESSFULLY ===\n";
        echo "✅ New vendors structure working correctly across all endpoints!\n\n";
    }

    private function assertCartStructure(array $cartData, string $operation): void
    {
        echo "  → Validating structure for {$operation}...\n";
        
        // Basic cart structure
        $this->assertArrayHasKey('id', $cartData);
        $this->assertArrayHasKey('uuid', $cartData);
        $this->assertArrayHasKey('currency', $cartData);
        
        // New vendors structure (not vendor_groups)
        $this->assertArrayHasKey('vendors', $cartData, 'Cart should have vendors key');
        $this->assertArrayNotHasKey('vendor_groups', $cartData, 'Cart should NOT have old vendor_groups key');
        
        if (empty($cartData['vendors'])) {
            echo "    ✓ Empty cart - no vendors\n";
            return;
        }
        
        $this->assertIsArray($cartData['vendors']);
        echo "    ✓ Found " . count($cartData['vendors']) . " vendor(s)\n";
        
        foreach ($cartData['vendors'] as $index => $vendor) {
            echo "    → Validating vendor " . ($index + 1) . "...\n";
            
            // Vendor should have data at top level (not nested under 'vendor' key)
            $this->assertArrayHasKey('id', $vendor, 'Vendor should have id at top level');
            $this->assertArrayHasKey('name', $vendor, 'Vendor should have name at top level');
            $this->assertArrayHasKey('display_name', $vendor, 'Vendor should have display_name at top level');
            $this->assertArrayHasKey('items', $vendor, 'Vendor should have items array');
            
            // Should NOT have redundant fields
            $this->assertArrayNotHasKey('vendor_id', $vendor, 'Should not have redundant vendor_id field');
            $this->assertArrayNotHasKey('vendor', $vendor, 'Should not have nested vendor object');
            
            echo "      ✓ Vendor {$vendor['id']}: {$vendor['name']} with " . count($vendor['items']) . " item(s)\n";
            
            // Validate items
            $this->assertIsArray($vendor['items']);
            foreach ($vendor['items'] as $itemIndex => $item) {
                // Items should NOT contain vendor data (it's at vendor level now)
                $this->assertArrayNotHasKey('vendor', $item, 'Items should not contain redundant vendor data');
                
                // Items should still have their core data
                $this->assertArrayHasKey('id', $item);
                $this->assertArrayHasKey('product_id', $item);
                $this->assertArrayHasKey('quantity', $item);
                $this->assertArrayHasKey('unit_price', $item);
                $this->assertArrayHasKey('total_price', $item);
                
                echo "        ✓ Item " . ($itemIndex + 1) . ": Product {$item['product_id']}, Qty: {$item['quantity']}\n";
            }
        }
        
        echo "    ✅ Structure validation passed for {$operation}\n";
    }

    private function createMinimalProduct(Vendor $vendor, string $title, float $regularPrice, ?float $offerPrice = null): Product
    {
        // Create product with minimal required fields to avoid schema issues
        return Product::create([
            'user_id' => $this->user->id,
            'vendor_id' => $vendor->id,
            'category_id' => 1, // Assuming category 1 exists
            'sub_category_id' => 1, // Assuming subcategory 1 exists
            'brand_id' => 1, // Assuming brand 1 exists
            'vendor_sku' => 'TEST-' . uniqid(),
            'system_sku' => 'SKU-' . uniqid(),
            'title_en' => $title,
            'title_ar' => $title,
            'short_name' => substr($title, 0, 50),
            'short_description_en' => 'Test product description',
            'short_description_ar' => 'وصف المنتج التجريبي',
            'description_en' => 'Detailed test product description',
            'description_ar' => 'وصف مفصل للمنتج التجريبي',
            'regular_price' => $regularPrice,
            'offer_price' => $offerPrice ?? $regularPrice,
            'vat_tax' => '5%',
            'is_active' => true,
            'is_approved' => true,
            'status' => 'active',
            'uuid' => (string) \Illuminate\Support\Str::uuid(),
            'slug' => \Illuminate\Support\Str::slug($title),
            'net_weight' => 100,
            'package_length' => 10,
            'package_width' => 10,
            'package_height' => 10,
            'package_weight' => 0.5,
            'approx_commission' => 10.0,
            'is_variant' => false,
            'is_vegan' => false,
            'is_vegetarian' => false,
            'is_halal' => true,
        ]);
    }
}
