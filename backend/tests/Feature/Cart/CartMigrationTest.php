<?php

namespace Tests\Feature\Cart;

use App\Models\CartSession;
use App\Models\Product;
use App\Models\ShoppingCart;
use App\Models\User;
use App\Models\Vendor;
use App\Services\CartService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CartMigrationTest extends TestCase
{
    use RefreshDatabase;

    protected CartService $cartService;
    protected User $user;
    protected Vendor $vendor;
    protected Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        $this->cartService = app(CartService::class);
        $this->user = User::factory()->create();
        $this->vendor = Vendor::factory()->active()->create();
        $this->product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'status' => 'active',
            'price' => 100.00,
        ]);
    }

    public function test_migrate_guest_cart_session_to_user(): void
    {
        $sessionId = 'guest-session-123';
        
        // Create guest cart session
        $cartSession = CartSession::create([
            'session_id' => $sessionId,
            'cart_data' => [
                'items' => [
                    [
                        'product_id' => $this->product->id,
                        'vendor_id' => $this->vendor->id,
                        'quantity' => 2,
                        'unit_price' => 100.00,
                        'total_price' => 200.00,
                        'product_snapshot' => [
                            'name' => $this->product->name_en,
                        ],
                    ],
                ],
            ],
            'expires_at' => now()->addDays(7),
            'is_migrated' => false,
        ]);

        $migratedCart = $this->cartService->migrateGuestCart($this->user, $sessionId);

        $this->assertNotNull($migratedCart);
        $this->assertEquals($this->user->id, $migratedCart->user_id);
        $this->assertEquals(1, $migratedCart->items()->count());
        
        $item = $migratedCart->items()->first();
        $this->assertEquals($this->product->id, $item->product_id);
        $this->assertEquals(2, $item->quantity);
        
        // Session should be marked as migrated
        $this->assertTrue($cartSession->fresh()->is_migrated);
        $this->assertEquals($this->user->id, $cartSession->fresh()->migrated_to_user_id);
    }

    public function test_migrate_guest_shopping_cart_to_user(): void
    {
        $sessionId = 'guest-cart-456';
        
        // Create guest shopping cart
        $guestCart = ShoppingCart::factory()->create([
            'session_id' => $sessionId,
            'user_id' => null,
            'status' => 'active',
            'currency' => 'AED',
        ]);

        $guestCart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 3,
            'unit_price' => 100.00,
            'total_price' => 300.00,
            'product_snapshot' => [],
        ]);

        $migratedCart = $this->cartService->migrateGuestCart($this->user, $sessionId, 'replace');

        $this->assertNotNull($migratedCart);
        $this->assertEquals($this->user->id, $migratedCart->user_id);
        $this->assertEquals(1, $migratedCart->items()->count());
        
        $item = $migratedCart->items()->first();
        $this->assertEquals(3, $item->quantity);
        
        // Guest cart should be marked as converted
        $this->assertEquals('converted', $guestCart->fresh()->status);
    }

    public function test_migrate_guest_cart_merge_strategy(): void
    {
        $sessionId = 'guest-merge-789';
        
        // Create existing user cart
        $userCart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
        ]);

        $userCart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 1,
            'unit_price' => 100.00,
            'total_price' => 100.00,
            'product_snapshot' => [],
        ]);

        // Create guest cart with same product
        $guestCart = ShoppingCart::factory()->create([
            'session_id' => $sessionId,
            'user_id' => null,
            'status' => 'active',
        ]);

        $guestCart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
            'product_snapshot' => [],
        ]);

        $migratedCart = $this->cartService->migrateGuestCart($this->user, $sessionId, 'merge');

        $this->assertNotNull($migratedCart);
        $this->assertEquals($userCart->id, $migratedCart->id);
        
        // Quantities should be merged
        $item = $migratedCart->items()->first();
        $this->assertEquals(3, $item->quantity); // 1 + 2
        $this->assertEquals(300.00, $item->total_price);
    }

    public function test_migrate_guest_cart_replace_strategy(): void
    {
        $sessionId = 'guest-replace-101';
        
        // Create existing user cart with items
        $userCart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
        ]);

        $userCart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 5,
            'unit_price' => 100.00,
            'total_price' => 500.00,
            'product_snapshot' => [],
        ]);

        // Create guest cart
        $guestCart = ShoppingCart::factory()->create([
            'session_id' => $sessionId,
            'user_id' => null,
            'status' => 'active',
        ]);

        $guestCart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
            'product_snapshot' => [],
        ]);

        $migratedCart = $this->cartService->migrateGuestCart($this->user, $sessionId, 'replace');

        $this->assertNotNull($migratedCart);
        $this->assertEquals($userCart->id, $migratedCart->id);
        
        // User cart should be replaced with guest cart items
        $item = $migratedCart->items()->first();
        $this->assertEquals(2, $item->quantity); // Guest cart quantity
    }

    public function test_migrate_empty_guest_cart_returns_null(): void
    {
        $sessionId = 'empty-guest-cart';
        
        $emptyGuestCart = ShoppingCart::factory()->create([
            'session_id' => $sessionId,
            'user_id' => null,
            'status' => 'active',
        ]);

        $result = $this->cartService->migrateGuestCart($this->user, $sessionId);

        $this->assertNull($result);
    }

    public function test_migrate_nonexistent_guest_cart_returns_null(): void
    {
        $result = $this->cartService->migrateGuestCart($this->user, 'nonexistent-session');

        $this->assertNull($result);
    }

    public function test_migrate_guest_cart_with_different_products(): void
    {
        $sessionId = 'different-products-123';
        
        $product2 = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'status' => 'active',
            'price' => 150.00,
        ]);

        // Create user cart with product1
        $userCart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
        ]);

        $userCart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 1,
            'unit_price' => 100.00,
            'total_price' => 100.00,
            'product_snapshot' => [],
        ]);

        // Create guest cart with product2
        $guestCart = ShoppingCart::factory()->create([
            'session_id' => $sessionId,
            'user_id' => null,
            'status' => 'active',
        ]);

        $guestCart->items()->create([
            'product_id' => $product2->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 150.00,
            'total_price' => 300.00,
            'product_snapshot' => [],
        ]);

        $migratedCart = $this->cartService->migrateGuestCart($this->user, $sessionId, 'merge');

        $this->assertNotNull($migratedCart);
        $this->assertEquals(2, $migratedCart->items()->count());
        
        // Both products should be in the cart
        $productIds = $migratedCart->items()->pluck('product_id')->toArray();
        $this->assertContains($this->product->id, $productIds);
        $this->assertContains($product2->id, $productIds);
    }

    public function test_migrate_guest_cart_with_variants(): void
    {
        $sessionId = 'variants-test-456';
        
        $variant = \App\Models\ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'price' => 120.00,
        ]);

        // Create guest cart session with variant
        $cartSession = CartSession::create([
            'session_id' => $sessionId,
            'cart_data' => [
                'items' => [
                    [
                        'product_id' => $this->product->id,
                        'variant_id' => $variant->id,
                        'vendor_id' => $this->vendor->id,
                        'quantity' => 1,
                        'unit_price' => 120.00,
                        'total_price' => 120.00,
                        'product_snapshot' => [],
                    ],
                ],
            ],
            'expires_at' => now()->addDays(7),
            'is_migrated' => false,
        ]);

        $migratedCart = $this->cartService->migrateGuestCart($this->user, $sessionId);

        $this->assertNotNull($migratedCart);
        $item = $migratedCart->items()->first();
        $this->assertEquals($variant->id, $item->variant_id);
        $this->assertEquals(120.00, $item->unit_price);
    }

    public function test_migrate_expired_guest_cart_session(): void
    {
        $sessionId = 'expired-session-789';
        
        $expiredSession = CartSession::create([
            'session_id' => $sessionId,
            'cart_data' => [
                'items' => [
                    [
                        'product_id' => $this->product->id,
                        'vendor_id' => $this->vendor->id,
                        'quantity' => 1,
                        'unit_price' => 100.00,
                        'total_price' => 100.00,
                        'product_snapshot' => [],
                    ],
                ],
            ],
            'expires_at' => now()->subDay(), // Expired
            'is_migrated' => false,
        ]);

        $result = $this->cartService->migrateGuestCart($this->user, $sessionId);

        // Should still migrate expired sessions
        $this->assertNotNull($result);
        $this->assertTrue($expiredSession->fresh()->is_migrated);
    }

    public function test_migrate_already_migrated_session(): void
    {
        $sessionId = 'already-migrated-101';
        
        $migratedSession = CartSession::create([
            'session_id' => $sessionId,
            'cart_data' => ['items' => []],
            'expires_at' => now()->addDays(7),
            'is_migrated' => true, // Already migrated
            'migrated_to_user_id' => $this->user->id,
        ]);

        $result = $this->cartService->migrateGuestCart($this->user, $sessionId);

        $this->assertNull($result);
    }

    public function test_migration_preserves_cart_metadata(): void
    {
        $sessionId = 'metadata-test-202';
        
        $guestCart = ShoppingCart::factory()->create([
            'session_id' => $sessionId,
            'user_id' => null,
            'status' => 'active',
            'currency' => 'USD',
            'notes' => 'Guest cart notes',
            'metadata' => ['source' => 'mobile_app'],
        ]);

        $guestCart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 1,
            'unit_price' => 100.00,
            'total_price' => 100.00,
            'product_snapshot' => [],
        ]);

        $migratedCart = $this->cartService->migrateGuestCart($this->user, $sessionId);

        $this->assertNotNull($migratedCart);
        $this->assertEquals('USD', $migratedCart->currency);
        // Notes and metadata would be preserved in a full implementation
    }
}
