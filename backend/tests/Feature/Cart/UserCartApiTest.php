<?php

namespace Tests\Feature\Cart;

use App\Models\CartSession;
use App\Models\Product;
use App\Models\ShoppingCart;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Passport\Passport;
use Tests\TestCase;

class UserCartApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Vendor $vendor;
    protected Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->vendor = Vendor::factory()->active()->create();
        $this->product = Product::factory()->active()->create([
            'vendor_id' => $this->vendor->id,
            'regular_price' => 100.00,
            'offer_price' => 80.00,
        ]);

        Passport::actingAs($this->user);
    }

    public function test_get_current_cart_creates_new_cart(): void
    {
        $response = $this->getJson('/api/client/my-cart');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'uuid',
                    'user_id',
                    'currency',
                    'status',
                    'subtotal',
                    'total_amount',
                    'items',
                ],
            ]);

        $this->assertEquals($this->user->id, $response->json('data.user_id'));
        $this->assertDatabaseHas('shopping_carts', [
            'user_id' => $this->user->id,
            'status' => 'active',
        ]);
    }

    public function test_get_current_cart_returns_existing_cart(): void
    {
        $existingCart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
        ]);

        $response = $this->getJson('/api/client/my-cart');

        $response->assertStatus(200);
        $this->assertEquals($existingCart->uuid, $response->json('data.uuid'));
    }

    public function test_migrate_guest_cart_from_session(): void
    {
        $sessionId = 'guest-session-123';
        
        // Create guest cart session
        $cartSession = CartSession::factory()->create([
            'session_id' => $sessionId,
            'cart_data' => [
                'items' => [
                    [
                        'product_id' => $this->product->id,
                        'vendor_id' => $this->vendor->id,
                        'quantity' => 2,
                        'unit_price' => 100.00,
                        'total_price' => 200.00,
                        'product_snapshot' => [],
                    ],
                ],
            ],
            'is_migrated' => false,
        ]);

        $response = $this->postJson('/api/client/my-cart/migrate', [
            'guest_session_id' => $sessionId,
            'merge_strategy' => 'replace',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'uuid',
                    'user_id',
                    'items',
                ],
            ]);

        $this->assertEquals($this->user->id, $response->json('data.user_id'));
        $this->assertTrue($cartSession->fresh()->is_migrated);
    }

    public function test_migrate_guest_cart_from_shopping_cart(): void
    {
        $sessionId = 'guest-session-456';
        
        // Create guest shopping cart
        $guestCart = ShoppingCart::factory()->create([
            'session_id' => $sessionId,
            'user_id' => null,
            'status' => 'active',
        ]);

        $guestCart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 3,
            'unit_price' => 100.00,
            'total_price' => 300.00,
            'product_snapshot' => [],
        ]);

        $response = $this->postJson('/api/client/my-cart/migrate', [
            'guest_session_id' => $sessionId,
            'merge_strategy' => 'replace',
        ]);

        $response->assertStatus(200);

        $this->assertEquals($this->user->id, $response->json('data.user_id'));
        $this->assertEquals('converted', $guestCart->fresh()->status);
        
        // Check that items were migrated
        $userCart = ShoppingCart::where('user_id', $this->user->id)
            ->where('status', 'active')
            ->first();
        
        $this->assertNotNull($userCart);
        $this->assertEquals(1, $userCart->items()->count());
    }

    public function test_migrate_guest_cart_merge_strategy(): void
    {
        $sessionId = 'guest-session-789';
        
        // Create existing user cart
        $userCart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
        ]);

        $userCart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 1,
            'unit_price' => 100.00,
            'total_price' => 100.00,
            'product_snapshot' => [],
        ]);

        // Create guest cart with same product
        $guestCart = ShoppingCart::factory()->create([
            'session_id' => $sessionId,
            'user_id' => null,
            'status' => 'active',
        ]);

        $guestCart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
            'product_snapshot' => [],
        ]);

        $response = $this->postJson('/api/client/my-cart/migrate', [
            'guest_session_id' => $sessionId,
            'merge_strategy' => 'merge',
        ]);

        $response->assertStatus(200);

        // Check that quantities were merged
        $mergedItem = $userCart->fresh()->items()->first();
        $this->assertEquals(3, $mergedItem->quantity); // 1 + 2
    }

    public function test_migrate_nonexistent_guest_cart(): void
    {
        $response = $this->postJson('/api/client/my-cart/migrate', [
            'guest_session_id' => 'nonexistent-session',
        ]);

        $response->assertStatus(404);
    }

    public function test_get_cart_history(): void
    {
        // Create some historical carts
        ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'abandoned',
        ]);

        ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'converted',
        ]);

        $response = $this->getJson('/api/client/my-cart/history');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'uuid',
                        'status',
                        'subtotal',
                        'total_amount',
                        'created_at',
                    ],
                ],
            ]);

        $this->assertCount(2, $response->json('data'));
    }

    public function test_get_cart_history_with_status_filter(): void
    {
        ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'abandoned',
        ]);

        ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'converted',
        ]);

        $response = $this->getJson('/api/client/my-cart/history?status=abandoned');

        $response->assertStatus(200);
        
        $carts = $response->json('data');
        $this->assertCount(1, $carts);
        $this->assertEquals('abandoned', $carts[0]['status']);
    }

    public function test_save_items_for_later(): void
    {
        $cart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
        ]);

        $item1 = $cart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
            'product_snapshot' => [],
        ]);

        $item2 = $cart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 1,
            'unit_price' => 100.00,
            'total_price' => 100.00,
            'product_snapshot' => [],
        ]);

        $response = $this->postJson('/api/client/my-cart/save-for-later', [
            'cart_item_ids' => [$item1->id],
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'saved_items_count',
                    'saved_items',
                    'cart',
                ],
            ]);

        $this->assertEquals(1, $response->json('data.saved_items_count'));
        
        // Item should be removed from cart
        $this->assertDatabaseMissing('cart_items', ['id' => $item1->id]);
        $this->assertDatabaseHas('cart_items', ['id' => $item2->id]);
    }

    public function test_get_saved_items(): void
    {
        $response = $this->getJson('/api/client/my-cart/saved-items');

        $response->assertStatus(200);
        // This would return wishlist items in a real implementation
        $this->assertEquals([], $response->json('data'));
    }

    public function test_get_cart_statistics(): void
    {
        // Create some carts for statistics
        ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'abandoned',
        ]);

        ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'converted',
            'total_amount' => 500.00,
        ]);

        $activeCart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
            'total_amount' => 200.00,
        ]);

        $activeCart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
            'product_snapshot' => [],
        ]);

        $response = $this->getJson('/api/client/my-cart/statistics');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'total_carts_created',
                    'abandoned_carts_count',
                    'converted_carts_count',
                    'current_cart_value',
                    'average_cart_value',
                    'total_items_in_current_cart',
                ],
            ]);

        $stats = $response->json('data');
        $this->assertEquals(3, $stats['total_carts_created']);
        $this->assertEquals(1, $stats['abandoned_carts_count']);
        $this->assertEquals(1, $stats['converted_carts_count']);
        $this->assertEquals(200.00, $stats['current_cart_value']);
        $this->assertEquals(2, $stats['total_items_in_current_cart']);
    }

    public function test_clear_current_cart(): void
    {
        $cart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
        ]);

        $cart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
            'product_snapshot' => [],
        ]);

        $response = $this->deleteJson('/api/client/my-cart/clear');

        $response->assertStatus(200);

        $this->assertEquals(0, $cart->fresh()->items()->count());
        $this->assertEquals(0, $cart->fresh()->subtotal);
    }

    public function test_clear_current_cart_when_no_cart_exists(): void
    {
        $response = $this->deleteJson('/api/client/my-cart/clear');

        $response->assertStatus(404);
    }

    public function test_unauthenticated_access_denied(): void
    {
        Passport::actingAs(null);

        $response = $this->getJson('/api/client/my-cart');

        $response->assertStatus(401);
    }

    public function test_migration_validation_errors(): void
    {
        $response = $this->postJson('/api/client/my-cart/migrate', [
            'guest_session_id' => '', // Empty session ID
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['guest_session_id']);
    }

    public function test_save_for_later_validation_errors(): void
    {
        $response = $this->postJson('/api/client/my-cart/save-for-later', [
            'cart_item_ids' => [], // Empty array
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cart_item_ids']);
    }

    public function test_save_for_later_with_invalid_item_ids(): void
    {
        $response = $this->postJson('/api/client/my-cart/save-for-later', [
            'cart_item_ids' => [999999], // Non-existent item ID
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cart_item_ids.0']);
    }
}
