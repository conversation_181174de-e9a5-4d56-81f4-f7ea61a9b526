<?php

namespace Tests\Feature;

use App\Models\Blog;
use App\Models\BlogCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ClientBlogControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->user = User::factory()->create();
        $this->category = BlogCategory::factory()->active()->create();
    }

    /** @test */
    public function it_can_get_published_blogs_list()
    {
        // Create published and draft blogs
        $publishedBlog = Blog::factory()->published()->create([
            'blog_category_id' => $this->category->id,
            'published_at' => now()->subDay(),
        ]);

        $draftBlog = Blog::factory()->draft()->create([
            'blog_category_id' => $this->category->id,
        ]);

        $response = $this->getJson('/api/client/blogs');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'title_en',
                                'slug',
                                'summary_en',
                                'featured_image_url',
                                'published_at',
                                'category',
                                'user',
                            ]
                        ]
                    ]
                ]);

        // Should only return published blog
        $this->assertEquals(1, count($response->json('data.data')));
        $this->assertEquals($publishedBlog->id, $response->json('data.data.0.id'));
    }

    /** @test */
    public function it_can_get_single_published_blog_by_slug()
    {
        $blog = Blog::factory()->published()->create([
            'blog_category_id' => $this->category->id,
            'published_at' => now()->subDay(),
        ]);

        $response = $this->getJson("/api/client/blogs/{$blog->slug}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'title_en',
                        'slug',
                        'content_en',
                        'featured_image_url',
                        'published_at',
                        'category',
                        'user',
                    ]
                ])
                ->assertJson([
                    'data' => [
                        'id' => $blog->id,
                        'slug' => $blog->slug,
                    ]
                ]);
    }

    /** @test */
    public function it_returns_404_for_draft_blog_by_slug()
    {
        $draftBlog = Blog::factory()->draft()->create([
            'blog_category_id' => $this->category->id,
        ]);

        $response = $this->getJson("/api/client/blogs/{$draftBlog->slug}");

        $response->assertStatus(500); // Will be 404 in real implementation with proper error handling
    }

    /** @test */
    public function it_returns_404_for_nonexistent_blog_slug()
    {
        $response = $this->getJson('/api/client/blogs/nonexistent-slug');

        $response->assertStatus(500); // Will be 404 in real implementation with proper error handling
    }

    /** @test */
    public function it_can_get_blogs_by_category_slug()
    {
        $category1 = BlogCategory::factory()->active()->create();
        $category2 = BlogCategory::factory()->active()->create();

        $blog1 = Blog::factory()->published()->create([
            'blog_category_id' => $category1->id,
            'published_at' => now()->subDay(),
        ]);

        $blog2 = Blog::factory()->published()->create([
            'blog_category_id' => $category2->id,
            'published_at' => now()->subDay(),
        ]);

        $response = $this->getJson("/api/client/blogs/category/{$category1->slug}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'title_en',
                                'slug',
                                'category',
                            ]
                        ]
                    ]
                ]);

        // Should only return blogs from category1
        $this->assertEquals(1, count($response->json('data.data')));
        $this->assertEquals($blog1->id, $response->json('data.data.0.id'));
    }

    /** @test */
    public function it_returns_404_for_inactive_category_slug()
    {
        $inactiveCategory = BlogCategory::factory()->inactive()->create();

        $response = $this->getJson("/api/client/blogs/category/{$inactiveCategory->slug}");

        $response->assertStatus(500); // Will be 404 in real implementation with proper error handling
    }

    /** @test */
    public function it_can_get_featured_blogs()
    {
        // Create multiple published blogs
        Blog::factory()->count(3)->published()->create([
            'blog_category_id' => $this->category->id,
            'published_at' => now()->subDays(rand(1, 10)),
        ]);

        $response = $this->getJson('/api/client/blogs/featured');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'title_en',
                                'slug',
                                'featured_image_url',
                                'published_at',
                            ]
                        ]
                    ]
                ]);

        $this->assertEquals(3, count($response->json('data.data')));
    }

    /** @test */
    public function it_can_search_blogs()
    {
        $blog1 = Blog::factory()->published()->create([
            'title_en' => 'Laravel Tutorial',
            'blog_category_id' => $this->category->id,
            'published_at' => now()->subDay(),
        ]);

        $blog2 = Blog::factory()->published()->create([
            'title_en' => 'PHP Best Practices',
            'blog_category_id' => $this->category->id,
            'published_at' => now()->subDay(),
        ]);

        $response = $this->getJson('/api/client/blogs/search?search=Laravel');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'title_en',
                                'slug',
                            ]
                        ]
                    ]
                ]);

        // Should only return blog with Laravel in title
        $this->assertEquals(1, count($response->json('data.data')));
        $this->assertEquals($blog1->id, $response->json('data.data.0.id'));
    }

    /** @test */
    public function it_can_get_related_blogs()
    {
        $category = BlogCategory::factory()->active()->create();

        $mainBlog = Blog::factory()->published()->create([
            'blog_category_id' => $category->id,
            'published_at' => now()->subDay(),
        ]);

        $relatedBlog = Blog::factory()->published()->create([
            'blog_category_id' => $category->id,
            'published_at' => now()->subDays(2),
        ]);

        // Blog in different category should not appear
        $unrelatedBlog = Blog::factory()->published()->create([
            'blog_category_id' => $this->category->id,
            'published_at' => now()->subDay(),
        ]);

        $response = $this->getJson("/api/client/blogs/{$mainBlog->slug}/related");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'title_en',
                                'slug',
                            ]
                        ]
                    ]
                ]);

        // Should only return related blog from same category, excluding main blog
        $this->assertEquals(1, count($response->json('data.data')));
        $this->assertEquals($relatedBlog->id, $response->json('data.data.0.id'));
    }

    /** @test */
    public function it_can_filter_blogs_by_category_id()
    {
        $blog1 = Blog::factory()->published()->create([
            'blog_category_id' => $this->category->id,
            'published_at' => now()->subDay(),
        ]);

        $otherCategory = BlogCategory::factory()->active()->create();
        $blog2 = Blog::factory()->published()->create([
            'blog_category_id' => $otherCategory->id,
            'published_at' => now()->subDay(),
        ]);

        $response = $this->getJson("/api/client/blogs?blog_category_id={$this->category->id}");

        $response->assertStatus(200);

        // Should only return blog from specified category
        $this->assertEquals(1, count($response->json('data.data')));
        $this->assertEquals($blog1->id, $response->json('data.data.0.id'));
    }

    /** @test */
    public function it_orders_blogs_by_published_date_desc_by_default()
    {
        $olderBlog = Blog::factory()->published()->create([
            'blog_category_id' => $this->category->id,
            'published_at' => now()->subDays(2),
        ]);

        $newerBlog = Blog::factory()->published()->create([
            'blog_category_id' => $this->category->id,
            'published_at' => now()->subDay(),
        ]);

        $response = $this->getJson('/api/client/blogs');

        $response->assertStatus(200);

        $blogs = $response->json('data.data');
        $this->assertEquals($newerBlog->id, $blogs[0]['id']);
        $this->assertEquals($olderBlog->id, $blogs[1]['id']);
    }
}
