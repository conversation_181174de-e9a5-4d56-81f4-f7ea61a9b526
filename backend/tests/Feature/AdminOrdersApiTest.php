<?php

namespace Tests\Feature;

use App\Models\Order;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class AdminOrdersApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $customer;
    protected $vendor;
    protected $paymentMethod;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles for both web and api guards
        Role::create(['name' => 'admin', 'guard_name' => 'web']);
        Role::create(['name' => 'customer', 'guard_name' => 'web']);
        Role::create(['name' => 'admin', 'guard_name' => 'api']);
        Role::create(['name' => 'customer', 'guard_name' => 'api']);

        // Create test users
        $this->admin = User::factory()->create();
        $this->admin->assignRole('admin');

        $this->customer = User::factory()->create();
        $this->customer->assignRole('customer');
        
        // Create test vendor
        $this->vendor = Vendor::factory()->create();
        
        // Create test payment method
        $this->paymentMethod = PaymentMethod::create([
            'name_en' => 'Credit Card',
            'name_ar' => 'بطاقة ائتمان',
            'slug' => 'credit-card',
            'icon' => 'credit-card.png',
            'description_en' => 'Pay with credit or debit card',
            'description_ar' => 'ادفع بالبطاقة الائتمانية أو بطاقة الخصم',
            'status' => 'active',
        ]);
    }

    /** @test */
    public function admin_can_list_orders_with_payment_method_relationship()
    {
        // Create test orders with payment method
        $orders = Order::factory()->count(3)->create([
            'user_id' => $this->customer->id,
            'vendor_id' => $this->vendor->id,
            'payment_method_id' => $this->paymentMethod->id,
            'total' => 100.00,
            'currency' => 'AED',
            'payment_status' => 'paid',
            'fulfillment_status' => 'processing',
        ]);

        // Make API request as admin
        $response = $this->actingAs($this->admin, 'sanctum')
            ->getJson('/api/admin/orders');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'uuid',
                            'order_number',
                            'customer',
                            'vendor',
                            'pricing',
                            'status',
                            'payment_method',
                            'payment_method_name',
                            'payment_method_display',
                            'payment_method_object' => [
                                'id',
                                'name_en',
                                'name_ar',
                                'slug',
                                'icon',
                                'icon_url',
                                'status',
                            ],
                        ]
                    ]
                ]
            ]);

        // Verify payment method data is correctly included
        $orderData = $response->json('data.data.0');
        $this->assertEquals('card', $orderData['payment_method']); // backward compatibility
        $this->assertEquals('Credit Card', $orderData['payment_method_name']);
        $this->assertEquals('Credit Card', $orderData['payment_method_display']);
        $this->assertEquals($this->paymentMethod->id, $orderData['payment_method_object']['id']);
        $this->assertEquals('Credit Card', $orderData['payment_method_object']['name_en']);
        $this->assertEquals('credit-card', $orderData['payment_method_object']['slug']);
    }

    /** @test */
    public function admin_can_view_single_order_with_payment_method_relationship()
    {
        // Create test order with payment method
        $order = Order::factory()->create([
            'user_id' => $this->customer->id,
            'vendor_id' => $this->vendor->id,
            'payment_method_id' => $this->paymentMethod->id,
            'total' => 150.00,
            'currency' => 'AED',
            'payment_status' => 'paid',
            'fulfillment_status' => 'delivered',
        ]);

        // Make API request as admin
        $response = $this->actingAs($this->admin, 'sanctum')
            ->getJson("/api/admin/orders/{$order->uuid}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'uuid',
                    'order_number',
                    'customer',
                    'vendor',
                    'pricing',
                    'status',
                    'details' => [
                        'payment_method',
                        'payment_method_name',
                    ],
                    'payment_method_object' => [
                        'id',
                        'name_en',
                        'name_ar',
                        'slug',
                        'icon',
                        'icon_url',
                        'description_en',
                        'description_ar',
                        'status',
                    ],
                ]
            ]);

        // Verify payment method data is correctly included
        $orderData = $response->json('data');
        $this->assertEquals('card', $orderData['details']['payment_method']); // backward compatibility
        $this->assertEquals('Credit Card', $orderData['details']['payment_method_name']);
        $this->assertEquals($this->paymentMethod->id, $orderData['payment_method_object']['id']);
        $this->assertEquals('Credit Card', $orderData['payment_method_object']['name_en']);
        $this->assertEquals('بطاقة ائتمان', $orderData['payment_method_object']['name_ar']);
        $this->assertEquals('credit-card', $orderData['payment_method_object']['slug']);
    }

    /** @test */
    public function admin_can_filter_orders_by_payment_method_id()
    {
        // Create another payment method
        $codPaymentMethod = PaymentMethod::create([
            'name_en' => 'Cash on Delivery',
            'name_ar' => 'الدفع عند الاستلام',
            'slug' => 'cash-on-delivery',
            'status' => 'active',
        ]);

        // Create orders with different payment methods
        Order::factory()->create([
            'user_id' => $this->customer->id,
            'payment_method_id' => $this->paymentMethod->id,
        ]);
        
        Order::factory()->create([
            'user_id' => $this->customer->id,
            'payment_method_id' => $codPaymentMethod->id,
        ]);

        // Filter by payment method ID
        $response = $this->actingAs($this->admin, 'sanctum')
            ->getJson("/api/admin/orders?payment_method={$this->paymentMethod->id}");

        $response->assertStatus(200);
        
        $orders = $response->json('data.data');
        $this->assertCount(1, $orders);
        $this->assertEquals($this->paymentMethod->id, $orders[0]['payment_method_object']['id']);
    }

    /** @test */
    public function admin_can_filter_orders_by_payment_method_slug()
    {
        // Create another payment method
        $codPaymentMethod = PaymentMethod::create([
            'name_en' => 'Cash on Delivery',
            'name_ar' => 'الدفع عند الاستلام',
            'slug' => 'cash-on-delivery',
            'status' => 'active',
        ]);

        // Create orders with different payment methods
        Order::factory()->create([
            'user_id' => $this->customer->id,
            'payment_method_id' => $this->paymentMethod->id,
        ]);
        
        Order::factory()->create([
            'user_id' => $this->customer->id,
            'payment_method_id' => $codPaymentMethod->id,
        ]);

        // Filter by payment method slug
        $response = $this->actingAs($this->admin, 'sanctum')
            ->getJson('/api/admin/orders?payment_method=cash-on-delivery');

        $response->assertStatus(200);
        
        $orders = $response->json('data.data');
        $this->assertCount(1, $orders);
        $this->assertEquals('cash-on-delivery', $orders[0]['payment_method_object']['slug']);
    }

    /** @test */
    public function orders_without_payment_method_handle_gracefully()
    {
        // Create order without payment method
        $order = Order::factory()->create([
            'user_id' => $this->customer->id,
            'payment_method_id' => null,
        ]);

        // Make API request as admin
        $response = $this->actingAs($this->admin, 'sanctum')
            ->getJson('/api/admin/orders');

        $response->assertStatus(200);
        
        $orderData = $response->json('data.data.0');
        $this->assertNull($orderData['payment_method']);
        $this->assertNull($orderData['payment_method_name']);
        $this->assertArrayNotHasKey('payment_method_object', $orderData);
    }
}
