<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Passport\Passport;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class ProductClassCreationBugTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected Category $mainCategory;
    protected Category $subCategory;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin role for API guard
        Role::create(['name' => 'admin', 'guard_name' => 'api']);

        // Create admin user
        $this->admin = User::factory()->create();
        $this->admin->assignRole('admin');

        // Create test categories matching the failing request
        $this->mainCategory = Category::factory()->create([
            'id' => 27,
            'name_en' => 'Beauty',
            'type' => 'main',
            'parent_id' => null,
        ]);

        $this->subCategory = Category::factory()->create([
            'id' => 34,
            'name_en' => 'Bath & Home',
            'type' => 'sub',
            'parent_id' => 27,
        ]);

        // Authenticate as admin
        Passport::actingAs($this->admin);
    }

    public function test_product_class_creation_with_valid_sub_category_id()
    {
        $payload = [
            'name_en' => 'TEST',
            'name_ar' => 'TEST',
            'code' => 'VSC',
            'category_id' => '27',
            'sub_category_id' => '34',
            'parent_id' => null,
            'is_popular' => false,
            'status' => 'active'
        ];

        $response = $this->postJson('/api/admin/product-classes', $payload);

        // Should succeed now that validation rule is fixed
        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name_en',
                    'name_ar',
                    'code',
                    'category_id',
                    'sub_category_id',
                    'status'
                ]
            ]);

        // Verify the product class was created in database
        $this->assertDatabaseHas('product_classes', [
            'name_en' => 'TEST',
            'code' => 'VSC',
            'category_id' => 27,
            'sub_category_id' => 34,
            'status' => 'active'
        ]);
    }

    public function test_product_class_creation_with_invalid_sub_category_id()
    {
        $payload = [
            'name_en' => 'TEST',
            'name_ar' => 'TEST',
            'code' => 'VSC2',
            'category_id' => '27',
            'sub_category_id' => '999', // Non-existent category
            'parent_id' => null,
            'is_popular' => false,
            'status' => 'active'
        ];

        $response = $this->postJson('/api/admin/product-classes', $payload);

        // Should fail with proper validation error
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sub_category_id']);
    }

    public function test_product_class_creation_without_sub_category_id()
    {
        $payload = [
            'name_en' => 'TEST WITHOUT SUB',
            'name_ar' => 'TEST WITHOUT SUB',
            'code' => 'VSC3',
            'category_id' => '27',
            'sub_category_id' => null, // Nullable field
            'parent_id' => null,
            'is_popular' => false,
            'status' => 'active'
        ];

        $response = $this->postJson('/api/admin/product-classes', $payload);

        // Should succeed since sub_category_id is nullable
        $response->assertStatus(201);

        // Verify the product class was created in database
        $this->assertDatabaseHas('product_classes', [
            'name_en' => 'TEST WITHOUT SUB',
            'code' => 'VSC3',
            'category_id' => 27,
            'sub_category_id' => null,
            'status' => 'active'
        ]);
    }
}
