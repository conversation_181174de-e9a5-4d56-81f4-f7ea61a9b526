<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Product;
use App\Models\Vendor;
use App\Models\Wishlist;
use App\Models\ShoppingCart;
use App\Models\CartItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Laravel\Passport\Passport;

class WishlistToCartTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $vendor;
    protected $products;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'is_verified' => true,
            'is_active' => true,
        ]);

        // Create test vendor
        $this->vendor = Vendor::factory()->create([
            'is_active' => true,
            'approval_status' => 'Approved',
        ]);

        // Create test categories
        $category = \App\Models\Category::factory()->create();
        $subCategory = \App\Models\Category::factory()->create();

        // Create test warehouse manually
        $warehouse = \App\Models\Warehouse::create([
            'name_en' => 'Test Warehouse',
            'name_ar' => 'مستودع تجريبي',
            'code' => 'TEST-WH-001',
            'address' => 'Test Address',
            'is_active' => true,
            'is_global' => true,
            'status' => 'active',
        ]);

        // Create test products manually to avoid factory issues
        $this->products = collect();
        for ($i = 1; $i <= 4; $i++) {
            $product = Product::create([
                'user_id' => $this->user->id,
                'vendor_id' => $this->vendor->id,
                'category_id' => $category->id,
                'sub_category_id' => $subCategory->id,
                'title_en' => "Test Product {$i}",
                'title_ar' => "منتج تجريبي {$i}",
                'regular_price' => 100.00,
                'offer_price' => 75.00,
                'is_active' => true,
                'is_approved' => true,
                'status' => 'submitted',
                'uuid' => \Illuminate\Support\Str::uuid(),
                'slug' => "test-product-{$i}",
                'vendor_sku' => "VND-{$i}",
                'system_sku' => "SKU-{$i}",
                'allow_backorder' => true,  // Allow backorders
                // Set dropdown fields to null to avoid validation issues
                'storage_conditions' => null,
                'country_of_origin' => null,
                'is_returnable' => null,
                'warranty' => null,
            ]);

            // Create inventory record to ensure product is available
            \App\Models\Inventory::create([
                'product_id' => $product->id,
                'vendor_id' => $this->vendor->id,
                'warehouse_id' => $warehouse->id,
                'stock' => 100,
                'reserved' => 0,
                'threshold' => 10,
                'is_active' => true,
            ]);

            $this->products->push($product);
        }

        // Authenticate user for API requests
        Passport::actingAs($this->user);
    }

    /** @test */
    public function it_can_move_single_wishlist_item_to_cart()
    {
        // Create a wishlist item
        $wishlist = Wishlist::create([
            'user_id' => $this->user->id,
            'product_id' => $this->products->first()->id,
            'vendor_id' => $this->vendor->id,
            'note' => 'Test wishlist item',
        ]);

        // Make API request to move item to cart
        $response = $this->postJson("/api/client/wishlist/{$wishlist->id}/move-to-cart");

        // Assert response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'id',
                    'cart_id',
                    'product_id',
                    'vendor_id',
                    'quantity',
                    'unit_price',
                    'total_price',
                ],
                'message',
            ]);

        // Assert wishlist item was removed
        $this->assertDatabaseMissing('wishlists', ['id' => $wishlist->id]);

        // Assert cart item was created
        $this->assertDatabaseHas('cart_items', [
            'product_id' => $this->products->first()->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 1,
        ]);

        // Assert cart was created for user
        $this->assertDatabaseHas('shopping_carts', [
            'user_id' => $this->user->id,
            'status' => 'active',
        ]);
    }

    /** @test */
    public function it_can_move_multiple_wishlist_items_to_cart()
    {
        // Create multiple wishlist items
        $wishlists = collect();
        foreach ($this->products->take(3) as $product) {
            $wishlists->push(Wishlist::create([
                'user_id' => $this->user->id,
                'product_id' => $product->id,
                'vendor_id' => $this->vendor->id,
                'note' => "Test item for {$product->title_en}",
            ]));
        }

        $wishlistIds = $wishlists->pluck('id')->toArray();

        // Make API request to bulk move items to cart
        $response = $this->postJson('/api/client/wishlist/bulk-move-to-cart', [
            'wishlist_ids' => $wishlistIds,
        ]);

        // Assert response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'moved' => [
                        '*' => [
                            'wishlist_id',
                            'cart_item_id',
                        ],
                    ],
                    'failed',
                ],
                'message',
            ]);

        $responseData = $response->json('data');
        
        // Assert all items were moved successfully
        $this->assertCount(3, $responseData['moved']);
        $this->assertCount(0, $responseData['failed']);

        // Assert all wishlist items were removed
        foreach ($wishlistIds as $wishlistId) {
            $this->assertDatabaseMissing('wishlists', ['id' => $wishlistId]);
        }

        // Assert all cart items were created
        foreach ($this->products->take(3) as $product) {
            $this->assertDatabaseHas('cart_items', [
                'product_id' => $product->id,
                'vendor_id' => $this->vendor->id,
                'quantity' => 1,
            ]);
        }
    }

    /** @test */
    public function it_handles_non_existent_wishlist_item()
    {
        $response = $this->postJson('/api/client/wishlist/999/move-to-cart');

        $response->assertStatus(422);
    }

    /** @test */
    public function it_handles_unauthorized_access_to_other_users_wishlist()
    {
        // Create another user and their wishlist item
        $otherUser = User::factory()->create();
        $wishlist = Wishlist::create([
            'user_id' => $otherUser->id,
            'product_id' => $this->products->first()->id,
            'vendor_id' => $this->vendor->id,
            'note' => 'Other user\'s item',
        ]);

        // Try to move other user's wishlist item
        $response = $this->postJson("/api/client/wishlist/{$wishlist->id}/move-to-cart");

        $response->assertStatus(422);
        
        // Assert wishlist item still exists
        $this->assertDatabaseHas('wishlists', ['id' => $wishlist->id]);
    }

    /** @test */
    public function it_adds_to_existing_cart_if_user_already_has_one()
    {
        // Create an existing cart with an item
        $existingCart = ShoppingCart::create([
            'user_id' => $this->user->id,
            'status' => 'active',
            'expires_at' => now()->addDays(30),
        ]);

        $existingCartItem = CartItem::create([
            'cart_id' => $existingCart->id,
            'product_id' => $this->products->get(1)->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 1,
            'unit_price' => 75.00,
            'base_price' => 100.00,
            'total_price' => 75.00,
            'product_snapshot' => [],
        ]);

        // Create a wishlist item
        $wishlist = Wishlist::create([
            'user_id' => $this->user->id,
            'product_id' => $this->products->first()->id,
            'vendor_id' => $this->vendor->id,
            'note' => 'Test wishlist item',
        ]);

        // Move wishlist item to cart
        $response = $this->postJson("/api/client/wishlist/{$wishlist->id}/move-to-cart");

        $response->assertStatus(200);

        // Assert the same cart was used (not a new one created)
        $cartData = $response->json('data.cart');
        $this->assertEquals($existingCart->id, $cartData['id']);

        // Assert cart now has 2 items
        $this->assertEquals(2, $existingCart->fresh()->items()->count());
    }

    /** @test */
    public function it_handles_inactive_products_gracefully()
    {
        // Create an inactive product manually
        $category = \App\Models\Category::factory()->create();
        $subCategory = \App\Models\Category::factory()->create();

        $inactiveProduct = Product::create([
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'category_id' => $category->id,
            'sub_category_id' => $subCategory->id,
            'title_en' => 'Inactive Test Product',
            'title_ar' => 'منتج تجريبي غير نشط',
            'regular_price' => 100.00,
            'offer_price' => 75.00,
            'is_active' => false, // Make it inactive
            'is_approved' => true,
            'status' => 'submitted',
            'uuid' => \Illuminate\Support\Str::uuid(),
            'slug' => 'inactive-test-product',
            'vendor_sku' => 'VND-INACTIVE',
            'system_sku' => 'SKU-INACTIVE',
            'allow_backorder' => true,
            'storage_conditions' => null,
            'country_of_origin' => null,
            'is_returnable' => null,
            'warranty' => null,
        ]);

        // Create wishlist item with inactive product
        $wishlist = Wishlist::create([
            'user_id' => $this->user->id,
            'product_id' => $inactiveProduct->id,
            'vendor_id' => $this->vendor->id,
            'note' => 'Test with inactive product',
        ]);

        // Try to move to cart
        $response = $this->postJson("/api/client/wishlist/{$wishlist->id}/move-to-cart");

        // Should return 422 because product is not available
        $response->assertStatus(422);
        $response->assertJsonFragment(['errors' => 'Product is no longer available']);
    }

    /** @test */
    public function it_requires_authentication()
    {
        // Remove authentication
        $this->app['auth']->forgetGuards();

        $response = $this->postJson('/api/client/wishlist/1/move-to-cart');

        $response->assertStatus(401);
    }
}
