<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\Product;
use App\Models\ReturnRequest;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ReturnRequestFactory extends Factory
{
    protected $model = ReturnRequest::class;

    public function definition(): array
    {
        return [
            'return_number' => 'RET-' . strtoupper($this->faker->unique()->bothify('??##??##')),
            'order_id' => Order::factory(),
            'product_id' => Product::factory(),
            'user_id' => User::factory(),
            'type' => $this->faker->randomElement(['return', 'refund', 'exchange']),
            'status' => $this->faker->randomElement(['pending', 'approved', 'rejected', 'processing', 'completed', 'cancelled']),
            'reason' => $this->faker->sentence(),
            'description' => $this->faker->optional()->paragraph(),
            'quantity' => $this->faker->numberBetween(1, 5),
            'unit_price' => $this->faker->randomFloat(2, 10, 500),
            'total_amount' => function (array $attributes) {
                return $attributes['quantity'] * $attributes['unit_price'];
            },
            'attachments' => $this->faker->optional()->randomElements(['image1.jpg', 'image2.jpg', 'receipt.pdf'], 2),
            'requested_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'approved_at' => $this->faker->optional()->dateTimeBetween('-20 days', 'now'),
            'completed_at' => $this->faker->optional()->dateTimeBetween('-10 days', 'now'),
            'approved_by' => $this->faker->optional()->randomElement([1, 2, 3]),
            'admin_notes' => $this->faker->optional()->sentence(),
        ];
    }

    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'approved_at' => null,
            'completed_at' => null,
            'approved_by' => null,
        ]);
    }

    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'approved',
            'approved_at' => $this->faker->dateTimeBetween('-10 days', 'now'),
            'approved_by' => User::factory(),
        ]);
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'approved_at' => $this->faker->dateTimeBetween('-15 days', '-10 days'),
            'completed_at' => $this->faker->dateTimeBetween('-10 days', 'now'),
            'approved_by' => User::factory(),
        ]);
    }
}