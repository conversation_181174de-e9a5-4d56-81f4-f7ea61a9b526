<?php

namespace Database\Factories;

use App\Models\EmailTemplate;
use App\Models\EmailTemplateCategory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmailTemplate>
 */
class EmailTemplateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = EmailTemplate::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(3, true);

        return [
            'uuid' => Str::uuid(),
            'name' => $name,
            'slug' => Str::slug($name),
            'subject' => $this->faker->sentence(),
            'body_html' => $this->getHtmlTemplate(),
            'body_text' => $this->getTextTemplate(),
            'category_id' => EmailTemplateCategory::factory(),
            'language' => 'en',
            'is_active' => $this->faker->boolean(80),
            'is_default' => $this->faker->boolean(20),
            'variables' => ['user.name', 'site.name'],
            'metadata' => ['type' => 'test', 'priority' => 'medium'],
        ];
    }

    /**
     * Get a sample HTML template.
     */
    private function getHtmlTemplate(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{subject}}</title>
</head>
<body>
    <h1>Hello {{user.name}}</h1>
    <p>' . $this->faker->paragraph() . '</p>
    <p>Best regards,<br>{{site.name}} Team</p>
</body>
</html>';
    }

    /**
     * Get a sample text template.
     */
    private function getTextTemplate(): string
    {
        return 'Hello {{user.name}},

' . $this->faker->paragraph() . '

Best regards,
{{site.name}} Team';
    }

    /**
     * Indicate that the template is active.
     */
    public function active(): static
    {
        return $this->state(fn () => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the template is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn () => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the template is a default template.
     */
    public function default(): static
    {
        return $this->state(fn () => [
            'is_default' => true,
        ]);
    }
}
