<?php

namespace Database\Factories;

use App\Models\EmailTemplateVariable;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmailTemplateVariable>
 */
class EmailTemplateVariableFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = EmailTemplateVariable::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(2, true);
        $key = strtolower(str_replace(' ', '.', $name));

        return [
            'name' => $name,
            'key' => $key,
            'description' => $this->faker->sentence(),
            'data_type' => $this->faker->randomElement(['string', 'number', 'boolean', 'date', 'array']),
            'default_value' => $this->faker->optional()->word(),
            'is_required' => $this->faker->boolean(30),
            'category' => $this->faker->randomElement(['user', 'order', 'vendor', 'system', 'authentication']),
            'example_value' => $this->faker->word(),
        ];
    }

    /**
     * Indicate that the variable is required.
     */
    public function required(): static
    {
        return $this->state(fn () => [
            'is_required' => true,
        ]);
    }

    /**
     * Indicate that the variable is optional.
     */
    public function optional(): static
    {
        return $this->state(fn () => [
            'is_required' => false,
        ]);
    }

    /**
     * Set the variable category.
     */
    public function category(string $category): static
    {
        return $this->state(fn () => [
            'category' => $category,
        ]);
    }

    /**
     * Set the variable data type.
     */
    public function dataType(string $type): static
    {
        return $this->state(fn () => [
            'data_type' => $type,
        ]);
    }
}
