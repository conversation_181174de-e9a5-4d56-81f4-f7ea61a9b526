<?php

namespace Database\Factories;

use App\Models\ProductVariant;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductVariantFactory extends Factory
{
    protected $model = ProductVariant::class;

    public function definition(): array
    {
        return [
            'product_id' => Product::factory(),
            'regular_price' => $this->faker->randomFloat(2, 10, 1000),
            'offer_price' => $this->faker->optional(0.3)->randomFloat(2, 5, 500),
            'vat_tax' => $this->faker->optional()->randomElement(['5%', '0%']),
            'discount_start_date' => $this->faker->optional()->dateTimeBetween('now', '+1 month'),
            'discount_end_date' => $this->faker->optional()->dateTimeBetween('+1 month', '+3 months'),
            'stock' => $this->faker->numberBetween(0, 100),
            'sku' => $this->faker->optional()->bothify('???-####-??'),
            'system_sku' => $this->faker->unique()->bothify('SYS-???-######'),
            'barcode' => $this->faker->optional()->ean13(),
            'weight' => $this->faker->optional()->randomFloat(2, 0.1, 50),
            'length' => $this->faker->optional()->randomFloat(2, 1, 100),
            'width' => $this->faker->optional()->randomFloat(2, 1, 100),
            'height' => $this->faker->optional()->randomFloat(2, 1, 100),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
        ];
    }

    /**
     * Indicate that the variant is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the variant is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the variant is out of stock.
     */
    public function outOfStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
            'stock' => 0,
        ]);
    }

    /**
     * Set specific stock quantity.
     */
    public function withStock(int $quantity): static
    {
        return $this->state(fn (array $attributes) => [
            'stock' => $quantity,
        ]);
    }

    /**
     * Set promotional price.
     */
    public function onSale(float $offerPrice = null): static
    {
        return $this->state(function (array $attributes) use ($offerPrice) {
            $regularPrice = $attributes['regular_price'] ?? 100;
            return [
                'offer_price' => $offerPrice ?? ($regularPrice * 0.8), // 20% off by default
                'discount_start_date' => now(),
                'discount_end_date' => now()->addMonth(),
            ];
        });
    }
}
