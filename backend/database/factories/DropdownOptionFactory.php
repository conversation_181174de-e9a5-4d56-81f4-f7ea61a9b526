<?php

namespace Database\Factories;

use App\Models\DropdownOption;
use App\Models\Dropdown;
use Illuminate\Database\Eloquent\Factories\Factory;

class DropdownOptionFactory extends Factory
{
    protected $model = DropdownOption::class;

    public function definition(): array
    {
        $valueEn = $this->faker->word();
        
        return [
            'dropdown_id' => Dropdown::factory(),
            'value_en' => $valueEn,
            'value_ar' => $valueEn, // Simplified for testing
            'sort_order' => $this->faker->numberBetween(0, 100),
        ];
    }

    public function forDropdown(Dropdown $dropdown)
    {
        return $this->state(function (array $attributes) use ($dropdown) {
            return [
                'dropdown_id' => $dropdown->id,
            ];
        });
    }
}
