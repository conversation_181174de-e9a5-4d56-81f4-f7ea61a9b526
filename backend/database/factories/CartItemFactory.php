<?php

namespace Database\Factories;

use App\Models\CartItem;
use App\Models\ShoppingCart;
use App\Models\Product;
use App\Models\Vendor;
use App\Models\ProductVariant;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CartItem>
 */
class CartItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CartItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $quantity = $this->faker->numberBetween(1, 5);
        $unitPrice = $this->faker->randomFloat(2, 10, 500);
        $totalPrice = $unitPrice * $quantity;
        $discountAmount = $this->faker->randomFloat(2, 0, $totalPrice * 0.2);
        $taxAmount = $this->faker->randomFloat(2, 0, $totalPrice * 0.1);
        $basePrice = $unitPrice;
        $promotionalPrice = $this->faker->optional(0.3)->randomFloat(2, $basePrice * 0.7, $basePrice * 0.9);

        return [
            'cart_id' => ShoppingCart::factory(),
            'product_id' => Product::factory(),
            'vendor_id' => Vendor::factory(),
            'variant_id' => null, // Will be set if needed
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'total_price' => $totalPrice,
            'discount_amount' => $discountAmount,
            'tax_amount' => $taxAmount,
            'product_snapshot' => [
                'title' => $this->faker->words(3, true),
                'sku' => $this->faker->bothify('SKU-####'),
                'price' => $unitPrice,
                'image' => $this->faker->imageUrl(300, 300, 'products'),
                'description' => $this->faker->sentence(),
            ],
            'customizations' => null,
            'special_instructions' => $this->faker->optional()->sentence(),
            'metadata' => null,
            'base_price' => $basePrice,
            'promotional_price' => $promotionalPrice,
            'applied_discounts' => null,
        ];
    }

    /**
     * Indicate that the cart item has a variant.
     */
    public function withVariant(): static
    {
        return $this->state(fn (array $attributes) => [
            'variant_id' => ProductVariant::factory(),
        ]);
    }

    /**
     * Indicate that the cart item has customizations.
     */
    public function withCustomizations(): static
    {
        return $this->state(fn (array $attributes) => [
            'customizations' => [
                'color' => $this->faker->colorName(),
                'size' => $this->faker->randomElement(['S', 'M', 'L', 'XL']),
                'engraving' => $this->faker->optional()->words(3, true),
            ],
        ]);
    }

    /**
     * Indicate that the cart item has applied discounts.
     */
    public function withDiscounts(): static
    {
        return $this->state(fn (array $attributes) => [
            'applied_discounts' => [
                [
                    'type' => 'percentage',
                    'value' => 10,
                    'amount' => $attributes['total_price'] * 0.1,
                    'description' => 'Member discount',
                ],
            ],
            'discount_amount' => $attributes['total_price'] * 0.1,
        ]);
    }

    /**
     * Indicate that the cart item is on promotion.
     */
    public function onPromotion(): static
    {
        return $this->state(fn (array $attributes) => [
            'promotional_price' => $attributes['base_price'] * 0.8,
            'unit_price' => $attributes['base_price'] * 0.8,
            'total_price' => $attributes['base_price'] * 0.8 * $attributes['quantity'],
        ]);
    }
}
