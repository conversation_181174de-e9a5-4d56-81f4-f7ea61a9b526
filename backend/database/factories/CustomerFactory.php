<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    protected $model = Customer::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $customerTypes = ['individual', 'business', 'wholesale', 'retail'];
        $genders = ['male', 'female', 'other'];
        $languages = ['en', 'ar'];
        $currencies = ['AED', 'USD', 'EUR'];
        $kycDocTypes = ['passport', 'emirates_id', 'driving_license', 'national_id', 'student_id'];
        $occupations = [
            'Engineer', 'Doctor', 'Teacher', 'Manager', 'Developer', 'Designer',
            'Consultant', 'Analyst', 'Sales Representative', 'Marketing Specialist',
            'Accountant', 'Lawyer', 'Nurse', 'Pharmacist', 'Architect'
        ];
        $designations = [
            'Senior Manager', 'Team Lead', 'Specialist', 'Executive', 'Director',
            'Coordinator', 'Supervisor', 'Assistant', 'Associate', 'Principal'
        ];
        $companies = [
            'Emirates NBD', 'ADNOC', 'Etisalat', 'Dubai Municipality', 'DEWA',
            'Mashreq Bank', 'FAB Bank', 'Careem', 'Talabat', 'Noon',
            'Amazon', 'Microsoft', 'Google', 'Apple', 'Samsung'
        ];

        return [
            'gender' => fake()->randomElement($genders),
            'date_of_birth' => fake()->dateTimeBetween('-65 years', '-18 years')->format('Y-m-d'),
            'loyalty_points' => fake()->numberBetween(0, 5000),
            'customer_type' => fake()->randomElement($customerTypes),
            'preferred_language' => fake()->randomElement($languages),
            'preferred_currency' => fake()->randomElement($currencies),
            'kyc_document_type' => fake()->randomElement($kycDocTypes),
            'kyc_file' => fake()->boolean(30) ? 'kyc_documents/' . fake()->uuid() . '.pdf' : null,
            'kyc_verified' => fake()->boolean(70),
            'referral_code' => fake()->boolean(20) ? strtoupper(fake()->lexify('REF???')) : null,
            'referred_by' => fake()->boolean(15) ? strtoupper(fake()->lexify('REF???')) : null,
            'loyalty_points_awarded' => fake()->boolean(60),
            'occupation' => fake()->randomElement($occupations),
            'designation' => fake()->randomElement($designations),
            'company_name' => fake()->randomElement($companies),
            'newsletter_consent' => fake()->boolean(75),
            'is_vrps' => fake()->boolean(10),
            'is_member_pricing_enabled' => fake()->boolean(90), // 90% of customers have member pricing enabled
            // Cart-related fields from migration
            'cart_abandonment_count' => fake()->numberBetween(0, 10),
            'last_cart_activity' => fake()->boolean(60) ? fake()->dateTimeBetween('-30 days', 'now') : null,
            'lifetime_cart_value' => fake()->randomFloat(2, 0, 75000),
        ];
    }

    /**
     * Create a customer with high spending (potential VIP)
     */
    public function highSpender(): static
    {
        return $this->state(fn (array $attributes) => [
            'loyalty_points' => fake()->numberBetween(2000, 10000),
            'kyc_verified' => true,
            'customer_type' => fake()->randomElement(['business', 'wholesale']),
            'is_vrps' => fake()->boolean(30),
            'is_member_pricing_enabled' => true, // High spenders always get member pricing
        ]);
    }

    /**
     * Create a new customer with minimal activity
     */
    public function newCustomer(): static
    {
        return $this->state(fn (array $attributes) => [
            'loyalty_points' => fake()->numberBetween(0, 100),
            'kyc_verified' => fake()->boolean(30),
            'customer_type' => 'individual',
            'is_vrps' => false,
            'is_member_pricing_enabled' => true, // New customers get member pricing by default
        ]);
    }

    /**
     * Create a business customer
     */
    public function business(): static
    {
        return $this->state(fn (array $attributes) => [
            'customer_type' => 'business',
            'kyc_verified' => true,
            'company_name' => fake()->company(),
            'designation' => fake()->randomElement(['CEO', 'CFO', 'Manager', 'Director', 'Owner']),
            'is_member_pricing_enabled' => true, // Business customers get member pricing
        ]);
    }

    /**
     * Create an inactive customer
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'last_cart_activity' => fake()->dateTimeBetween('-2 years', '-6 months'),
            'cart_abandonment_count' => fake()->numberBetween(3, 15),
            'is_member_pricing_enabled' => fake()->boolean(50), // 50% of inactive customers have member pricing disabled
        ]);
    }
}
