<?php

namespace Database\Factories;

use App\Models\ProductClass;
use App\Models\Category;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class ProductClassFactory extends Factory
{
    protected $model = ProductClass::class;

    public function definition(): array
    {
        $nameEn = $this->faker->words(2, true);
        
        return [
            'user_id' => User::factory(),
            'name_en' => $nameEn,
            'name_ar' => $nameEn, // Simplified for testing
            'category_id' => Category::factory(),
            'sub_category_id' => null,
            'status' => 'active',
            'parent_id' => null,
            'slug' => Str::slug($nameEn) . '-' . $this->faker->unique()->numberBetween(1, 1000),
        ];
    }

    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'inactive',
            ];
        });
    }

    public function withCategory(Category $category)
    {
        return $this->state(function (array $attributes) use ($category) {
            return [
                'category_id' => $category->id,
            ];
        });
    }

    public function withSubCategory(Category $subCategory)
    {
        return $this->state(function (array $attributes) use ($subCategory) {
            return [
                'sub_category_id' => $subCategory->id,
            ];
        });
    }
}
