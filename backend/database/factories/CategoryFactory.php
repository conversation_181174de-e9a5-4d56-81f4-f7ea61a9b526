<?php

namespace Database\Factories;

use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class CategoryFactory extends Factory
{
    protected $model = Category::class;

    public function definition(): array
    {
        $nameEn = $this->faker->words(2, true);
        
        return [
            'name_en' => $nameEn,
            'name_ar' => $nameEn, // Simplified for testing
            'type' => $this->faker->randomElement(['main', 'sub']),
            'parent_id' => null,
            'ordering_number' => $this->faker->numberBetween(1, 100),
            'icon' => null,
            'meta_title' => $this->faker->sentence(),
            'meta_description' => $this->faker->paragraph(),
            'slug' => Str::slug($nameEn) . '-' . $this->faker->unique()->numberBetween(1, 1000),
            'status' => 'active',
        ];
    }

    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'inactive',
            ];
        });
    }

    public function withParent(Category $parent)
    {
        return $this->state(function (array $attributes) use ($parent) {
            return [
                'parent_id' => $parent->id,
                'type' => 'sub',
            ];
        });
    }
}
