<?php

namespace Database\Factories;

use App\Models\Blog;
use App\Models\BlogCategory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Blog>
 */
class BlogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Blog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->sentence(6, true);

        return [
            'blog_category_id' => BlogCategory::factory(),
            'user_id' => User::factory(),
            'title_en' => $title,
            'title_ar' => $title, // For testing, using same title
            'slug' => $this->faker->unique()->slug(3),
            'summary_en' => $this->faker->paragraph(2),
            'summary_ar' => $this->faker->paragraph(2),
            'content_en' => $this->faker->paragraphs(5, true),
            'content_ar' => $this->faker->paragraphs(5, true),
            'meta_title' => $this->faker->sentence(4),
            'meta_description' => $this->faker->sentence(8),
            'keywords' => implode(', ', $this->faker->words(5)),
            'featured_image' => 'blog-images/' . $this->faker->uuid() . '.jpg',
            'status' => $this->faker->randomElement(['draft', 'published']),
            'published_at' => $this->faker->optional(0.8)->dateTimeBetween('-1 year', 'now'),
        ];
    }

    /**
     * Indicate that the blog is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'published',
            'published_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
        ]);
    }

    /**
     * Indicate that the blog is a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'published_at' => null,
        ]);
    }
}
