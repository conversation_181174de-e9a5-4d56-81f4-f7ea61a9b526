<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_status_histories', function (Blueprint $table) {
            $table->id();
            
            // Relationships
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // Who made the change
            
            // Status tracking
            $table->string('from_status')->nullable(); // Previous status
            $table->string('to_status'); // New status
            $table->string('status_type')->default('fulfillment'); // fulfillment, payment, etc.
            
            // Change details
            $table->text('reason')->nullable(); // Reason for status change
            $table->text('notes')->nullable(); // Additional notes
            $table->json('metadata')->nullable(); // Additional data
            
            // Tracking information
            $table->string('changed_by_type')->nullable(); // admin, vendor, system, customer
            $table->timestamp('changed_at')->useCurrent();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['order_id', 'changed_at']);
            $table->index(['to_status', 'changed_at']);
            $table->index('changed_by_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_status_histories');
    }
};
