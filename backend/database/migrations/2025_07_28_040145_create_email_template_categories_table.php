<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_template_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('Category display name');
            $table->string('slug')->unique()->comment('URL-friendly category identifier');
            $table->text('description')->nullable()->comment('Category description');
            $table->string('icon', 100)->nullable()->comment('Icon class or image path');
            $table->integer('sort_order')->default(0)->comment('Display order');
            $table->boolean('is_active')->default(true)->comment('Category status');
            $table->timestamps();

            // Indexes for performance
            $table->index('slug');
            $table->index('is_active');
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_template_categories');
    }
};
