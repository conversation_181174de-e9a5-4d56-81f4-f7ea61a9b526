<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cart_items', function (Blueprint $table) {
            // Add computed columns that were being selected in queries
            $table->decimal('final_unit_price', 10, 2)->nullable()->after('promotional_price');
            $table->decimal('savings_amount', 10, 2)->nullable()->after('final_unit_price');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cart_items', function (Blueprint $table) {
            $table->dropColumn(['final_unit_price', 'savings_amount']);
        });
    }
};
