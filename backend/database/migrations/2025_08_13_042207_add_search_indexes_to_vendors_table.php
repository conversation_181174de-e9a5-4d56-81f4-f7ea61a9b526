<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            // Add indexes for search performance
            $table->index(['is_active', 'approval_status'], 'vendors_active_approved_idx');
            $table->index('vendor_display_name_en', 'vendors_display_name_en_idx');
            $table->index('name_tl_en', 'vendors_name_tl_en_idx');
            $table->index('vendor_display_name_ar', 'vendors_display_name_ar_idx');
            $table->index('name_tl_ar', 'vendors_name_tl_ar_idx');

            // Composite index for common search patterns
            $table->index(['is_active', 'approval_status', 'vendor_display_name_en'], 'vendors_search_composite_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            // Drop indexes
            $table->dropIndex('vendors_active_approved_idx');
            $table->dropIndex('vendors_display_name_en_idx');
            $table->dropIndex('vendors_name_tl_en_idx');
            $table->dropIndex('vendors_display_name_ar_idx');
            $table->dropIndex('vendors_name_tl_ar_idx');
            $table->dropIndex('vendors_search_composite_idx');
        });
    }
};
