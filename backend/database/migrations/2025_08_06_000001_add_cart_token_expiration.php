<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shopping_carts', function (Blueprint $table) {
            $table->timestamp('cart_token_expires_at')->nullable()->after('cart_token');
            $table->integer('token_rotation_count')->default(0)->after('cart_token_expires_at');
            $table->timestamp('last_token_rotation')->nullable()->after('token_rotation_count');
            
            // Add index for token cleanup
            $table->index(['cart_token_expires_at', 'cart_token']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shopping_carts', function (Blueprint $table) {
            $table->dropIndex(['cart_token_expires_at', 'cart_token']);
            $table->dropColumn([
                'cart_token_expires_at', 
                'token_rotation_count', 
                'last_token_rotation'
            ]);
        });
    }
};
