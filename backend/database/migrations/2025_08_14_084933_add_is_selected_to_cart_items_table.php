<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cart_items', function (Blueprint $table) {
            // Add is_selected field with default value true
            // This ensures all existing items are selected by default
            // and new items are selected when added to cart
            $table->boolean('is_selected')->default(true)->after('metadata');

            // Add index for performance when filtering selected items
            $table->index(['cart_id', 'is_selected']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cart_items', function (Blueprint $table) {
            // Drop the index first
            $table->dropIndex(['cart_id', 'is_selected']);

            // Drop the is_selected column
            $table->dropColumn('is_selected');
        });
    }
};
