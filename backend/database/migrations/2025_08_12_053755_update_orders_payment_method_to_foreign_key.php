<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Replace payment_method enum with payment_method_id foreign key
     * to enable database-driven payment methods with localization support.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Add payment_method_id foreign key to reference payment_methods table
            $table->foreignId('payment_method_id')->nullable()->after('payment_method')->constrained('payment_methods')->onDelete('set null');

            // Remove the old payment_method enum field (replaced by foreign key)
            $table->dropColumn('payment_method');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Restore the old payment_method enum field
            $table->enum('payment_method', ['cod', 'card', 'wallet', 'bank'])->nullable()->after('fulfillment_status');

            // Remove the payment_method_id foreign key
            $table->dropForeign(['payment_method_id']);
            $table->dropColumn('payment_method_id');
        });
    }
};
