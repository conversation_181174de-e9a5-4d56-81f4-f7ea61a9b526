<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cart_items', function (Blueprint $table) {
            $table->id();
            
            // Relationships
            $table->foreignId('cart_id')->constrained('shopping_carts')->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('vendor_id')->constrained()->onDelete('cascade');
            $table->foreignId('variant_id')->nullable()->constrained('product_variants')->onDelete('cascade');
            
            // Item details
            $table->integer('quantity')->default(1);
            $table->decimal('unit_price', 10, 2);
            $table->decimal('total_price', 10, 2);
            $table->decimal('discount_amount', 10, 2)->default(0.00);
            $table->decimal('tax_amount', 10, 2)->default(0.00);
            
            // Product snapshot for historical accuracy
            $table->json('product_snapshot');
            
            // Item-specific metadata
            $table->json('customizations')->nullable(); // For customizable products
            $table->text('special_instructions')->nullable();
            $table->json('metadata')->nullable();
            
            // Pricing breakdown
            $table->decimal('base_price', 10, 2); // Original product price
            $table->decimal('promotional_price', 10, 2)->nullable(); // If on promotion
            $table->json('applied_discounts')->nullable(); // Item-specific discounts
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['cart_id', 'product_id']);
            $table->index(['vendor_id', 'cart_id']);
            $table->index('product_id');
            
            // Unique constraint to prevent duplicate items in same cart
            $table->unique(['cart_id', 'product_id', 'variant_id'], 'unique_cart_product_variant');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cart_items');
    }
};
