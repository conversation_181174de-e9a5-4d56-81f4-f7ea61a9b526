<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_template_variables', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('Human-readable variable name');
            $table->string('key')->unique()->comment('Variable key used in templates (e.g., user.name)');
            $table->text('description')->nullable()->comment('Variable description and usage');
            $table->enum('data_type', ['string', 'number', 'date', 'boolean', 'object', 'array'])
                  ->default('string')->comment('Expected data type');
            $table->text('default_value')->nullable()->comment('Default value if variable is missing');
            $table->boolean('is_required')->default(false)->comment('Whether variable is required');
            $table->string('category', 100)->nullable()->comment('Variable category (user, order, vendor, etc.)');
            $table->text('example_value')->nullable()->comment('Example value for documentation');
            $table->timestamps();

            // Indexes for performance
            $table->index('key');
            $table->index('category');
            $table->index('is_required');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_template_variables');
    }
};
