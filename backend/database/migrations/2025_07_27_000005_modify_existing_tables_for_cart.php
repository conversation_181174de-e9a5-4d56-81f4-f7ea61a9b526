<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add cart-related fields to products table
        Schema::table('products', function (Blueprint $table) {
            $table->integer('max_cart_quantity')->nullable()->after('status');
            $table->integer('min_cart_quantity')->default(1)->after('max_cart_quantity');
            $table->integer('cart_increment')->default(1)->after('min_cart_quantity');
            $table->boolean('allow_backorder')->default(false)->after('cart_increment');
        });

        // Add cart tracking to customers table
        Schema::table('customers', function (Blueprint $table) {
            $table->integer('cart_abandonment_count')->default(0)->after('is_vrps');
            $table->timestamp('last_cart_activity')->nullable()->after('cart_abandonment_count');
            $table->decimal('lifetime_cart_value', 12, 2)->default(0.00)->after('last_cart_activity');
        });

        // Update abandoned_carts table structure
        Schema::table('abandoned_carts', function (Blueprint $table) {
            $table->foreignId('cart_id')->nullable()->after('id')->constrained('shopping_carts')->onDelete('set null');
            $table->string('recovery_token')->nullable()->after('reminder_sent_at');
            $table->timestamp('recovery_expires_at')->nullable()->after('recovery_token');
            $table->integer('recovery_attempts')->default(0)->after('recovery_expires_at');
            $table->enum('recovery_status', ['pending', 'sent', 'clicked', 'recovered', 'expired'])->default('pending')->after('recovery_attempts');
        });

        // Add cart-related fields to vendors table for vendor-specific cart rules
        Schema::table('vendors', function (Blueprint $table) {
            $table->decimal('min_order_value', 10, 2)->nullable()->after('is_active');
            $table->decimal('free_shipping_threshold', 10, 2)->nullable()->after('min_order_value');
            $table->boolean('allow_partial_orders')->default(true)->after('free_shipping_threshold');
            $table->json('cart_rules')->nullable()->after('allow_partial_orders');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn(['max_cart_quantity', 'min_cart_quantity', 'cart_increment', 'allow_backorder']);
        });

        Schema::table('customers', function (Blueprint $table) {
            $table->dropColumn(['cart_abandonment_count', 'last_cart_activity', 'lifetime_cart_value']);
        });

        Schema::table('abandoned_carts', function (Blueprint $table) {
            $table->dropForeign(['cart_id']);
            $table->dropColumn(['cart_id', 'recovery_token', 'recovery_expires_at', 'recovery_attempts', 'recovery_status']);
        });

        Schema::table('vendors', function (Blueprint $table) {
            $table->dropColumn(['min_order_value', 'free_shipping_threshold', 'allow_partial_orders', 'cart_rules']);
        });
    }
};
