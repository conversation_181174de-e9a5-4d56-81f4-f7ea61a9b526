<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Remove the conflicting is_returnable field from product_fulfillments table.
     * The return policy is now handled exclusively through the products.is_returnable field
     * which references dropdown_options.id for standardized return policy options.
     */
    public function up(): void
    {
        Schema::table('product_fulfillments', function (Blueprint $table) {
            $table->dropColumn('is_returnable');
        });
    }

    /**
     * Reverse the migrations.
     * 
     * Restore the is_returnable field if needed for rollback.
     */
    public function down(): void
    {
        Schema::table('product_fulfillments', function (Blueprint $table) {
            $table->boolean('is_returnable')->default(true)->after('mode_id');
        });
    }
};
