<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_templates', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique()->comment('Unique identifier for external references');
            $table->string('name')->comment('Template display name');
            $table->string('slug')->unique()->comment('URL-friendly template identifier');
            $table->string('subject', 500)->comment('Email subject line template');
            $table->longText('body_html')->comment('HTML email body template');
            $table->longText('body_text')->nullable()->comment('Plain text email body template');

            // Category and language
            $table->unsignedBigInteger('category_id')->nullable();
            $table->string('language', 5)->default('en')->comment('Template language code (ISO 639-1)');

            // Status and configuration
            $table->boolean('is_active')->default(true)->comment('Template status');
            $table->boolean('is_default')->default(false)->comment('Default template for category');

            // Metadata and variables
            $table->json('variables')->nullable()->comment('Available template variables');
            $table->json('metadata')->nullable()->comment('Additional template metadata');

            // Audit fields
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();

            $table->timestamps();

            // Indexes for performance
            $table->index('slug');
            $table->index('category_id');
            $table->index('is_active');
            $table->index('language');
            $table->index('is_default');

            // Foreign key constraints
            $table->foreign('category_id')->references('id')->on('email_template_categories')->onDelete('set null');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_templates');
    }
};
