<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EmailTemplateCategorySeeder extends Seeder
{
    /**
     * Run the email template category seeds.
     */
    public function run(): void
    {
        $this->command->info('📂 Seeding Email Template Categories...');

        $categories = [
            [
                'name' => 'Authentication',
                'slug' => 'authentication',
                'description' => 'Email templates for user authentication, verification, password resets, and security-related communications',
                'icon' => 'shield-check',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Orders',
                'slug' => 'orders',
                'description' => 'Email templates for order confirmations, updates, shipping notifications, and order-related communications',
                'icon' => 'shopping-cart',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'Notifications',
                'slug' => 'notifications',
                'description' => 'Email templates for system notifications, alerts, reminders, and general user communications',
                'icon' => 'bell',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'name' => 'Marketing',
                'slug' => 'marketing',
                'description' => 'Email templates for promotional campaigns, newsletters, product announcements, and marketing communications',
                'icon' => 'megaphone',
                'sort_order' => 4,
                'is_active' => true,
            ],
            [
                'name' => 'System',
                'slug' => 'system',
                'description' => 'Email templates for system-generated messages, maintenance notifications, and administrative communications',
                'icon' => 'cog',
                'sort_order' => 5,
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            DB::table('email_template_categories')->updateOrInsert(
                ['slug' => $category['slug']],
                array_merge($category, [
                    'created_at' => now(),
                    'updated_at' => now(),
                ])
            );
        }

        $this->command->info('✅ Email Template Categories seeded successfully!');
    }
}
