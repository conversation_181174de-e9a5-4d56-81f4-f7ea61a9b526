<?php

namespace Database\Seeders;

use App\Models\PaymentMethod;
use Illuminate\Database\Seeder;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentMethods = [
            [
                'name_en' => 'Cash on Delivery',
                'name_ar' => 'الدفع عند الاستلام',
                'slug' => 'cod',
                'description_en' => 'Pay when your order is delivered to your doorstep',
                'description_ar' => 'ادفع عند توصيل طلبك إلى باب منزلك',
                'icon' => 'payment-icons/cod.svg',
                'status' => 'active',
            ],
            [
                'name_en' => 'Credit/Debit Card',
                'name_ar' => 'بطاقة ائتمان/خصم',
                'slug' => 'card',
                'description_en' => 'Pay securely using your credit or debit card',
                'description_ar' => 'ادفع بأمان باستخدام بطاقة الائتمان أو الخصم الخاصة بك',
                'icon' => 'payment-icons/card.svg',
                'status' => 'active',
            ],
            [
                'name_en' => 'Digital Wallet',
                'name_ar' => 'المحفظة الرقمية',
                'slug' => 'wallet',
                'description_en' => 'Pay using your digital wallet balance',
                'description_ar' => 'ادفع باستخدام رصيد محفظتك الرقمية',
                'icon' => 'payment-icons/wallet.svg',
                'status' => 'active',
            ],
            [
                'name_en' => 'Bank Transfer',
                'name_ar' => 'تحويل بنكي',
                'slug' => 'bank',
                'description_en' => 'Pay via direct bank transfer',
                'description_ar' => 'ادفع عن طريق التحويل البنكي المباشر',
                'icon' => 'payment-icons/bank.svg',
                'status' => 'active',
            ],
            [
                'name_en' => 'Apple Pay',
                'name_ar' => 'آبل باي',
                'slug' => 'apple-pay',
                'description_en' => 'Pay quickly and securely with Apple Pay',
                'description_ar' => 'ادفع بسرعة وأمان باستخدام آبل باي',
                'icon' => 'payment-icons/apple-pay.svg',
                'status' => 'active',
            ],
            [
                'name_en' => 'Google Pay',
                'name_ar' => 'جوجل باي',
                'slug' => 'google-pay',
                'description_en' => 'Pay quickly and securely with Google Pay',
                'description_ar' => 'ادفع بسرعة وأمان باستخدام جوجل باي',
                'icon' => 'payment-icons/google-pay.svg',
                'status' => 'active',
            ],
            [
                'name_en' => 'PayPal',
                'name_ar' => 'باي بال',
                'slug' => 'paypal',
                'description_en' => 'Pay securely with your PayPal account',
                'description_ar' => 'ادفع بأمان باستخدام حساب باي بال الخاص بك',
                'icon' => 'payment-icons/paypal.svg',
                'status' => 'active',
            ],
        ];

        foreach ($paymentMethods as $method) {
            PaymentMethod::updateOrCreate(
                ['slug' => $method['slug']],
                $method
            );
        }

        $this->command->info('Payment methods seeded successfully!');
    }
}
