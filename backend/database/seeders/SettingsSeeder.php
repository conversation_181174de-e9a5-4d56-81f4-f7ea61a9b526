<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // System & Backup Settings
            [
                'key' => 'system.auto_backup_enabled',
                'value' => true,
                'type' => 'boolean',
                'category' => 'system_backup',
                'display_name' => 'Auto Database Backup',
                'description' => 'Enable automatic database backups',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'system.backup_frequency',
                'value' => 'daily',
                'type' => 'string',
                'category' => 'system_backup',
                'display_name' => 'Backup Frequency',
                'description' => 'How often to perform automatic backups',
                'is_public' => false,
                'options' => ['hourly', 'daily', 'weekly', 'monthly'],
                'sort_order' => 2,
            ],
            [
                'key' => 'system.cache_enabled',
                'value' => true,
                'type' => 'boolean',
                'category' => 'system_backup',
                'display_name' => 'Cache Management',
                'description' => 'Enable application caching',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'system.cache_ttl',
                'value' => 3600,
                'type' => 'integer',
                'category' => 'system_backup',
                'display_name' => 'Cache TTL (seconds)',
                'description' => 'Default cache time-to-live in seconds',
                'is_public' => false,
                'sort_order' => 4,
            ],

            // Payment & Financial Settings
            [
                'key' => 'payment.stripe_enabled',
                'value' => false,
                'type' => 'boolean',
                'category' => 'payment_financial',
                'display_name' => 'Stripe Payment Gateway',
                'description' => 'Enable Stripe payment processing',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'payment.stripe_public_key',
                'value' => '',
                'type' => 'string',
                'category' => 'payment_financial',
                'display_name' => 'Stripe Public Key',
                'description' => 'Stripe publishable key',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'payment.paypal_enabled',
                'value' => false,
                'type' => 'boolean',
                'category' => 'payment_financial',
                'display_name' => 'PayPal Payment Gateway',
                'description' => 'Enable PayPal payment processing',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'payment.default_currency',
                'value' => 'USD',
                'type' => 'string',
                'category' => 'payment_financial',
                'display_name' => 'Default Currency',
                'description' => 'Default platform currency',
                'is_public' => true,
                'options' => ['USD', 'EUR', 'GBP', 'SAR', 'AED'],
                'sort_order' => 4,
            ],
            [
                'key' => 'payment.tax_rate',
                'value' => 0.15,
                'type' => 'float',
                'category' => 'payment_financial',
                'display_name' => 'Default Tax Rate',
                'description' => 'Default tax rate (as decimal)',
                'is_public' => true,
                'sort_order' => 5,
            ],
            [
                'key' => 'payment.currency_conversion_enabled',
                'value' => false,
                'type' => 'boolean',
                'category' => 'payment_financial',
                'display_name' => 'Currency Conversion',
                'description' => 'Enable automatic currency conversion',
                'is_public' => true,
                'sort_order' => 6,
            ],

            // Communication Settings
            [
                'key' => 'communication.smtp_enabled',
                'value' => false,
                'type' => 'boolean',
                'category' => 'communication',
                'display_name' => 'SMTP Email',
                'description' => 'Enable SMTP email sending',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'communication.smtp_host',
                'value' => '',
                'type' => 'string',
                'category' => 'communication',
                'display_name' => 'SMTP Host',
                'description' => 'SMTP server hostname',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'communication.smtp_port',
                'value' => 587,
                'type' => 'integer',
                'category' => 'communication',
                'display_name' => 'SMTP Port',
                'description' => 'SMTP server port',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'communication.sms_enabled',
                'value' => false,
                'type' => 'boolean',
                'category' => 'communication',
                'display_name' => 'SMS Gateway',
                'description' => 'Enable SMS notifications',
                'is_public' => false,
                'sort_order' => 4,
            ],
            [
                'key' => 'communication.notification_preferences',
                'value' => ['email' => true, 'sms' => false, 'push' => true],
                'type' => 'json',
                'category' => 'communication',
                'display_name' => 'Notification Preferences',
                'description' => 'Default notification channel preferences',
                'is_public' => false,
                'sort_order' => 5,
            ],

            // Homepage Management Settings
            [
                'key' => 'homepage.featured_categories',
                'value' => [],
                'type' => 'array',
                'category' => 'homepage_management',
                'display_name' => 'Featured Categories',
                'description' => 'Categories to display on homepage',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'homepage.hot_deals_enabled',
                'value' => true,
                'type' => 'boolean',
                'category' => 'homepage_management',
                'display_name' => 'Hot Deals Section',
                'description' => 'Show hot deals on homepage',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'homepage.promotional_banners',
                'value' => [],
                'type' => 'json',
                'category' => 'homepage_management',
                'display_name' => 'Promotional Banners',
                'description' => 'Homepage promotional banner configuration',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'homepage.app_download_links',
                'value' => ['ios' => '', 'android' => ''],
                'type' => 'json',
                'category' => 'homepage_management',
                'display_name' => 'App Download Links',
                'description' => 'Mobile app download links',
                'is_public' => true,
                'sort_order' => 4,
            ],
            [
                'key' => 'homepage.review_system_enabled',
                'value' => true,
                'type' => 'boolean',
                'category' => 'homepage_management',
                'display_name' => 'Review System Toggle',
                'description' => 'Enable/disable product review system',
                'is_public' => true,
                'sort_order' => 5,
            ],

            // Branding & UI Settings
            [
                'key' => 'branding.site_logo',
                'value' => '',
                'type' => 'string',
                'category' => 'branding_ui',
                'display_name' => 'Site Logo',
                'description' => 'Main site logo URL',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'branding.site_name',
                'value' => 'UAE eCommerce Platform',
                'type' => 'string',
                'category' => 'branding_ui',
                'display_name' => 'Site Name',
                'description' => 'Main site name/title',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'branding.support_contact',
                'value' => '',
                'type' => 'string',
                'category' => 'branding_ui',
                'display_name' => 'Support Contact',
                'description' => 'Customer support contact information',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'branding.support_email',
                'value' => '',
                'type' => 'string',
                'category' => 'branding_ui',
                'display_name' => 'Support Email',
                'description' => 'Customer support email address',
                'is_public' => true,
                'sort_order' => 4,
            ],
            [
                'key' => 'branding.footer_address',
                'value' => '',
                'type' => 'text',
                'category' => 'branding_ui',
                'display_name' => 'Footer Address',
                'description' => 'Company address for footer',
                'is_public' => true,
                'sort_order' => 5,
            ],
            [
                'key' => 'branding.social_links',
                'value' => ['facebook' => '', 'twitter' => '', 'instagram' => '', 'linkedin' => ''],
                'type' => 'json',
                'category' => 'branding_ui',
                'display_name' => 'Social Media Links',
                'description' => 'Social media profile links',
                'is_public' => true,
                'sort_order' => 6,
            ],
            [
                'key' => 'branding.payment_icons',
                'value' => ['visa', 'mastercard', 'paypal'],
                'type' => 'array',
                'category' => 'branding_ui',
                'display_name' => 'Payment Icons',
                'description' => 'Payment method icons to display',
                'is_public' => true,
                'sort_order' => 7,
            ],
            [
                'key' => 'branding.partner_logos',
                'value' => [],
                'type' => 'array',
                'category' => 'branding_ui',
                'display_name' => 'Partner Logos',
                'description' => 'Partner/sponsor logos',
                'is_public' => true,
                'sort_order' => 8,
            ],

            // Security & Performance Settings
            [
                'key' => 'security.rate_limiting_enabled',
                'value' => true,
                'type' => 'boolean',
                'category' => 'security_performance',
                'display_name' => 'Rate Limiting',
                'description' => 'Enable API rate limiting',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'security.max_requests_per_minute',
                'value' => 60,
                'type' => 'integer',
                'category' => 'security_performance',
                'display_name' => 'Max Requests Per Minute',
                'description' => 'Maximum API requests per minute per user',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'security.session_timeout',
                'value' => 120,
                'type' => 'integer',
                'category' => 'security_performance',
                'display_name' => 'Session Timeout (minutes)',
                'description' => 'User session timeout in minutes',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'security.api_throttling_enabled',
                'value' => true,
                'type' => 'boolean',
                'category' => 'security_performance',
                'display_name' => 'API Throttling',
                'description' => 'Enable API request throttling',
                'is_public' => false,
                'sort_order' => 4,
            ],

            // Vendor Management Settings
            [
                'key' => 'vendor.commission_rate',
                'value' => 0.10,
                'type' => 'float',
                'category' => 'vendor_management',
                'display_name' => 'Default Commission Rate',
                'description' => 'Default commission rate for vendors (as decimal)',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'vendor.auto_approval_enabled',
                'value' => false,
                'type' => 'boolean',
                'category' => 'vendor_management',
                'display_name' => 'Auto Vendor Approval',
                'description' => 'Automatically approve new vendor registrations',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'vendor.product_approval_required',
                'value' => true,
                'type' => 'boolean',
                'category' => 'vendor_management',
                'display_name' => 'Product Approval Required',
                'description' => 'Require admin approval for vendor products',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'vendor.minimum_payout_amount',
                'value' => 100.00,
                'type' => 'float',
                'category' => 'vendor_management',
                'display_name' => 'Minimum Payout Amount',
                'description' => 'Minimum amount for vendor payouts',
                'is_public' => false,
                'sort_order' => 4,
            ],

            // Order Management Settings
            [
                'key' => 'order.auto_confirm_enabled',
                'value' => false,
                'type' => 'boolean',
                'category' => 'order_management',
                'display_name' => 'Auto Order Confirmation',
                'description' => 'Automatically confirm orders after payment',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'order.cancellation_window_hours',
                'value' => 24,
                'type' => 'integer',
                'category' => 'order_management',
                'display_name' => 'Cancellation Window (hours)',
                'description' => 'Hours within which orders can be cancelled',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'order.default_status',
                'value' => 'pending',
                'type' => 'string',
                'category' => 'order_management',
                'display_name' => 'Default Order Status',
                'description' => 'Default status for new orders',
                'is_public' => false,
                'options' => ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'],
                'sort_order' => 3,
            ],
            [
                'key' => 'order.inventory_hold_minutes',
                'value' => 15,
                'type' => 'integer',
                'category' => 'order_management',
                'display_name' => 'Inventory Hold Time (minutes)',
                'description' => 'Minutes to hold inventory for unpaid orders',
                'is_public' => false,
                'sort_order' => 4,
            ],

            // Customer Management Settings
            [
                'key' => 'customer.email_verification_required',
                'value' => true,
                'type' => 'boolean',
                'category' => 'customer_management',
                'display_name' => 'Email Verification Required',
                'description' => 'Require email verification for new customers',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'customer.phone_verification_required',
                'value' => false,
                'type' => 'boolean',
                'category' => 'customer_management',
                'display_name' => 'Phone Verification Required',
                'description' => 'Require phone verification for new customers',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'customer.guest_checkout_enabled',
                'value' => true,
                'type' => 'boolean',
                'category' => 'customer_management',
                'display_name' => 'Guest Checkout',
                'description' => 'Allow customers to checkout without registration',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'customer.loyalty_program_enabled',
                'value' => false,
                'type' => 'boolean',
                'category' => 'customer_management',
                'display_name' => 'Loyalty Program',
                'description' => 'Enable customer loyalty program',
                'is_public' => true,
                'sort_order' => 4,
            ],

            // SEO Settings
            [
                'key' => 'seo.site_title',
                'value' => 'UAE eCommerce Platform - Your Online Shopping Destination',
                'type' => 'string',
                'category' => 'seo_settings',
                'display_name' => 'Site Title',
                'description' => 'Default site title for SEO',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'seo.meta_description',
                'value' => 'Discover amazing products from trusted vendors on UAE\'s premier eCommerce platform.',
                'type' => 'text',
                'category' => 'seo_settings',
                'display_name' => 'Meta Description',
                'description' => 'Default meta description for SEO',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'seo.meta_keywords',
                'value' => 'ecommerce, online shopping, UAE, marketplace, vendors',
                'type' => 'string',
                'category' => 'seo_settings',
                'display_name' => 'Meta Keywords',
                'description' => 'Default meta keywords for SEO',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'seo.sitemap_enabled',
                'value' => true,
                'type' => 'boolean',
                'category' => 'seo_settings',
                'display_name' => 'Sitemap Generation',
                'description' => 'Enable automatic sitemap generation',
                'is_public' => false,
                'sort_order' => 4,
            ],
            [
                'key' => 'seo.robots_txt_enabled',
                'value' => true,
                'type' => 'boolean',
                'category' => 'seo_settings',
                'display_name' => 'Robots.txt',
                'description' => 'Enable robots.txt file generation',
                'is_public' => false,
                'sort_order' => 5,
            ],
        ];

        foreach ($settings as $setting) {
            // Convert array/json values to JSON strings for storage
            if (in_array($setting['type'], ['json', 'array']) && is_array($setting['value'])) {
                $setting['value'] = json_encode($setting['value']);
            }

            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
