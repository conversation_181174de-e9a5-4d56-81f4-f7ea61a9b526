<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EmailTemplateVariableSeeder extends Seeder
{
    /**
     * Run the email template variable seeds.
     */
    public function run(): void
    {
        $this->command->info('🔧 Seeding Email Template Variables...');

        $variables = [
            // User variables (4 variables)
            [
                'name' => 'User ID',
                'key' => 'user.id',
                'description' => 'Unique identifier for the user account',
                'data_type' => 'number',
                'category' => 'user',
                'is_required' => false,
                'default_value' => null,
                'example_value' => '12345',
            ],
            [
                'name' => 'User Name',
                'key' => 'user.name',
                'description' => 'Full name of the user',
                'data_type' => 'string',
                'category' => 'user',
                'is_required' => false,
                'default_value' => 'Valued Customer',
                'example_value' => '<PERSON>',
            ],
            [
                'name' => 'User Email',
                'key' => 'user.email',
                'description' => 'Email address of the user',
                'data_type' => 'string',
                'category' => 'user',
                'is_required' => false,
                'default_value' => null,
                'example_value' => '<EMAIL>',
            ],
            [
                'name' => 'User Phone',
                'key' => 'user.phone',
                'description' => 'Phone number of the user',
                'data_type' => 'string',
                'category' => 'user',
                'is_required' => false,
                'default_value' => null,
                'example_value' => '+971501234567',
            ],

            // Order variables (4 variables)
            [
                'name' => 'Order Number',
                'key' => 'order.order_number',
                'description' => 'Unique order identifier',
                'data_type' => 'string',
                'category' => 'order',
                'is_required' => true,
                'default_value' => null,
                'example_value' => 'VIT-2025-0001',
            ],
            [
                'name' => 'Order Total',
                'key' => 'order.total',
                'description' => 'Total order amount including taxes and fees',
                'data_type' => 'number',
                'category' => 'order',
                'is_required' => true,
                'default_value' => '0.00',
                'example_value' => '299.99',
            ],
            [
                'name' => 'Order Currency',
                'key' => 'order.currency',
                'description' => 'Currency code for the order',
                'data_type' => 'string',
                'category' => 'order',
                'is_required' => true,
                'default_value' => 'AED',
                'example_value' => 'AED',
            ],
            [
                'name' => 'Order Status',
                'key' => 'order.status',
                'description' => 'Current status of the order',
                'data_type' => 'string',
                'category' => 'order',
                'is_required' => true,
                'default_value' => 'pending',
                'example_value' => 'confirmed',
            ],

            // Vendor variables (3 variables)
            [
                'name' => 'Vendor Name',
                'key' => 'vendor.name',
                'description' => 'Business name of the vendor',
                'data_type' => 'string',
                'category' => 'vendor',
                'is_required' => true,
                'default_value' => null,
                'example_value' => 'Health Plus Supplements',
            ],
            [
                'name' => 'Vendor Contact Name',
                'key' => 'vendor.contact_name',
                'description' => 'Primary contact person name for the vendor',
                'data_type' => 'string',
                'category' => 'vendor',
                'is_required' => true,
                'default_value' => null,
                'example_value' => 'Mohammed Al-Zahra',
            ],
            [
                'name' => 'Vendor Email',
                'key' => 'vendor.email',
                'description' => 'Contact email address for the vendor',
                'data_type' => 'string',
                'category' => 'vendor',
                'is_required' => true,
                'default_value' => null,
                'example_value' => '<EMAIL>',
            ],

            // Site variables (3 variables)
            [
                'name' => 'Site Name',
                'key' => 'site.name',
                'description' => 'Name of the website/platform',
                'data_type' => 'string',
                'category' => 'site',
                'is_required' => false,
                'default_value' => 'Vitamins.ae',
                'example_value' => 'Vitamins.ae',
            ],
            [
                'name' => 'Site URL',
                'key' => 'site.url',
                'description' => 'Base URL of the website',
                'data_type' => 'string',
                'category' => 'site',
                'is_required' => false,
                'default_value' => 'https://vitamins.ae',
                'example_value' => 'https://vitamins.ae',
            ],
            [
                'name' => 'Support Email',
                'key' => 'site.support_email',
                'description' => 'Customer support email address',
                'data_type' => 'string',
                'category' => 'site',
                'is_required' => false,
                'default_value' => '<EMAIL>',
                'example_value' => '<EMAIL>',
            ],

            // Auth variables (2 variables)
            [
                'name' => 'OTP Code',
                'key' => 'auth.otp_code',
                'description' => 'One-time password for verification',
                'data_type' => 'string',
                'category' => 'auth',
                'is_required' => true,
                'default_value' => null,
                'example_value' => '123456',
            ],
            [
                'name' => 'Reset Token URL',
                'key' => 'auth.reset_url',
                'description' => 'Password reset URL with token',
                'data_type' => 'string',
                'category' => 'auth',
                'is_required' => true,
                'default_value' => null,
                'example_value' => 'https://vitamins.ae/reset-password?token=abc123xyz789',
            ],
        ];

        foreach ($variables as $variable) {
            DB::table('email_template_variables')->updateOrInsert(
                ['key' => $variable['key']],
                array_merge($variable, [
                    'created_at' => now(),
                    'updated_at' => now(),
                ])
            );
        }

        $this->command->info('✅ Email Template Variables seeded successfully! (16 variables created)');
    }
}
