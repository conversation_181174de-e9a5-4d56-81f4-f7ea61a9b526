<?php

namespace Database\Seeders;

use App\Models\Review;
use App\Models\User;
use App\Models\Product;
use App\Models\Vendor;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReviewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating test reviews...');

        DB::transaction(function () {
            // Get existing data
            $customers = User::whereHas('roles', function ($query) {
                $query->where('name', 'customer');
            })->get();
            
            $products = Product::with('vendor')->get();
            $vendors = Vendor::all();
            $completedOrders = Order::where('fulfillment_status', 'delivered')
                ->with(['items.product', 'user'])
                ->get();

            if ($customers->isEmpty()) {
                $this->command->warn('No customers found. Please run CustomerSeeder first.');
                return;
            }

            if ($products->isEmpty()) {
                $this->command->warn('No products found. Please run ProductSeeder first.');
                return;
            }

            if ($completedOrders->isEmpty()) {
                $this->command->warn('No completed orders found. Please run OrderSeeder first.');
                return;
            }

            // Create reviews based on completed orders
            $this->createOrderBasedReviews($completedOrders, 40);
            
            // Create additional product reviews (without order reference)
            $this->createStandaloneProductReviews($customers, $products, 30);
            
            // Create vendor reviews
            $this->createVendorReviews($customers, $vendors, 15);
        });

        $this->command->info('Review seeding completed successfully!');
        $this->command->info('Created ' . Review::count() . ' reviews.');
        $this->command->info('Approved: ' . Review::where('is_approved', true)->count());
        $this->command->info('Pending: ' . Review::where('is_approved', false)->count());
    }

    /**
     * Create reviews based on completed orders
     */
    private function createOrderBasedReviews($completedOrders, $targetCount): void
    {
        $this->command->info('Creating order-based reviews...');
        
        $createdCount = 0;
        $processedOrders = collect();

        foreach ($completedOrders as $order) {
            if ($createdCount >= $targetCount) break;

            // Skip if we already processed this order
            if ($processedOrders->contains($order->id)) continue;
            
            // 70% chance to create a review for this order
            if (rand(1, 100) > 70) continue;

            // Get a random product from this order
            $orderItem = $order->items->random();
            
            // Check if review already exists for this user-product combination
            if (Review::where('user_id', $order->user_id)
                ->where('product_id', $orderItem->product_id)
                ->exists()) {
                continue;
            }

            $reviewData = $this->generateProductReviewData(
                $order->user,
                $orderItem->product,
                $order
            );

            Review::create($reviewData);
            $processedOrders->push($order->id);
            $createdCount++;
        }
    }

    /**
     * Create standalone product reviews
     */
    private function createStandaloneProductReviews($customers, $products, $targetCount): void
    {
        $this->command->info('Creating standalone product reviews...');
        
        $createdCount = 0;
        $attempts = 0;
        $maxAttempts = $targetCount * 3; // Prevent infinite loop

        while ($createdCount < $targetCount && $attempts < $maxAttempts) {
            $attempts++;
            
            $customer = $customers->random();
            $product = $products->random();

            // Check if review already exists
            if (Review::where('user_id', $customer->id)
                ->where('product_id', $product->id)
                ->exists()) {
                continue;
            }

            $reviewData = $this->generateProductReviewData($customer, $product);
            Review::create($reviewData);
            $createdCount++;
        }
    }

    /**
     * Create vendor reviews
     */
    private function createVendorReviews($customers, $vendors, $targetCount): void
    {
        $this->command->info('Creating vendor reviews...');
        
        $createdCount = 0;
        $attempts = 0;
        $maxAttempts = $targetCount * 3;

        while ($createdCount < $targetCount && $attempts < $maxAttempts) {
            $attempts++;
            
            $customer = $customers->random();
            $vendor = $vendors->random();

            // Check if review already exists
            if (Review::where('user_id', $customer->id)
                ->where('vendor_id', $vendor->id)
                ->exists()) {
                continue;
            }

            $reviewData = $this->generateVendorReviewData($customer, $vendor);
            Review::create($reviewData);
            $createdCount++;
        }
    }

    /**
     * Generate product review data
     */
    private function generateProductReviewData($user, $product, $order = null): array
    {
        $rating = $this->generateRealisticRating();
        $isApproved = $this->shouldBeApproved();
        $createdAt = $this->generateReviewDate($order);

        return [
            'user_id' => $user->id,
            'product_id' => $product->id,
            'vendor_id' => null,
            'order_id' => $order?->id,
            'rating' => $rating,
            'comment' => $this->generateProductComment($rating, $product),
            'is_approved' => $isApproved,
            'is_visible' => true,
            'created_at' => $createdAt,
            'updated_at' => $createdAt,
        ];
    }

    /**
     * Generate vendor review data
     */
    private function generateVendorReviewData($user, $vendor): array
    {
        $rating = $this->generateRealisticRating();
        $isApproved = $this->shouldBeApproved();
        $createdAt = $this->generateReviewDate();

        return [
            'user_id' => $user->id,
            'product_id' => null,
            'vendor_id' => $vendor->id,
            'order_id' => null,
            'rating' => $rating,
            'comment' => $this->generateVendorComment($rating, $vendor),
            'is_approved' => $isApproved,
            'is_visible' => true,
            'created_at' => $createdAt,
            'updated_at' => $createdAt,
        ];
    }

    /**
     * Generate realistic rating distribution (more 4-5 stars)
     */
    private function generateRealisticRating(): int
    {
        $weights = [
            1 => 5,   // 5% chance for 1 star
            2 => 8,   // 8% chance for 2 stars
            3 => 15,  // 15% chance for 3 stars
            4 => 35,  // 35% chance for 4 stars
            5 => 37,  // 37% chance for 5 stars
        ];

        $random = rand(1, 100);
        $cumulative = 0;

        foreach ($weights as $rating => $weight) {
            $cumulative += $weight;
            if ($random <= $cumulative) {
                return $rating;
            }
        }

        return 5; // Fallback
    }

    /**
     * Determine if review should be approved (80% approval rate)
     */
    private function shouldBeApproved(): bool
    {
        return rand(1, 100) <= 80;
    }

    /**
     * Generate review date (within last 6 months, after order if provided)
     */
    private function generateReviewDate($order = null): Carbon
    {
        if ($order) {
            // Review created 1-30 days after order delivery
            $orderDate = Carbon::parse($order->updated_at);
            return $orderDate->copy()->addDays(rand(1, 30));
        }

        // Random date within last 6 months
        return Carbon::now()->subDays(rand(1, 180));
    }

    /**
     * Generate product review comments
     */
    private function generateProductComment($rating, $product): string
    {
        $positiveComments = [
            "Excellent product! Exactly what I was looking for. Fast delivery and great packaging.",
            "Amazing quality and very effective. I've been using it for a month and can see great results.",
            "Perfect! This product exceeded my expectations. Will definitely order again.",
            "Great value for money. The product works as described and arrived quickly.",
            "Outstanding quality! I'm very satisfied with this purchase. Highly recommended.",
            "Fantastic product! Easy to use and very effective. Customer service was also excellent.",
            "Love this product! It's become part of my daily routine. Great quality and packaging.",
            "Excellent choice! The product is authentic and works perfectly. Fast shipping too.",
            "منتج ممتاز! جودة عالية وتسليم سريع. أنصح به بشدة.",
            "رائع جداً! المنتج فعال ووصل في الوقت المحدد. سأطلبه مرة أخرى.",
        ];

        $neutralComments = [
            "Good product overall. Does what it's supposed to do. Delivery was on time.",
            "Decent quality for the price. Nothing exceptional but gets the job done.",
            "Average product. It works but I expected a bit more for this price range.",
            "It's okay. The product is fine but the packaging could be better.",
            "Good enough. Does what it says on the label. Delivery was standard.",
            "منتج جيد بشكل عام. يؤدي الغرض المطلوب منه.",
            "مقبول. جودة مناسبة للسعر ولكن يمكن تحسين التغليف.",
        ];

        $negativeComments = [
            "Not what I expected. The product didn't work as advertised. Disappointed.",
            "Poor quality for the price. Had issues with the product after a few days.",
            "Delivery was delayed and the product quality is below average.",
            "Not satisfied with this purchase. The product doesn't match the description.",
            "Expected better quality. The product broke after minimal use.",
            "غير راضي عن المنتج. الجودة أقل من المتوقع.",
            "المنتج لم يلبي توقعاتي. التسليم كان متأخراً أيضاً.",
        ];

        if ($rating >= 4) {
            return collect($positiveComments)->random();
        } elseif ($rating == 3) {
            return collect($neutralComments)->random();
        } else {
            return collect($negativeComments)->random();
        }
    }

    /**
     * Generate vendor review comments
     */
    private function generateVendorComment($rating, $vendor): string
    {
        $positiveComments = [
            "Excellent vendor! Fast shipping, great customer service, and authentic products.",
            "Amazing experience with this vendor. Professional service and quick responses.",
            "Perfect vendor! Always delivers on time and products are exactly as described.",
            "Outstanding service! This vendor goes above and beyond. Highly recommended.",
            "Great vendor to work with. Reliable, professional, and high-quality products.",
            "بائع ممتاز! خدمة عملاء رائعة وتسليم سريع. أنصح بالتعامل معه.",
            "تجربة رائعة مع هذا البائع. منتجات أصلية وخدمة احترافية.",
        ];

        $neutralComments = [
            "Good vendor overall. Products are fine and delivery is usually on time.",
            "Decent service. Nothing exceptional but reliable for basic needs.",
            "Average vendor. Gets the job done but could improve communication.",
            "Okay experience. Products are as expected and delivery is standard.",
            "بائع جيد بشكل عام. المنتجات مناسبة والتسليم في الوقت المحدد.",
        ];

        $negativeComments = [
            "Poor service from this vendor. Delayed delivery and unresponsive customer service.",
            "Not satisfied with this vendor. Products were not as described.",
            "Bad experience. Vendor was unprofessional and delivery was very late.",
            "Disappointed with the service. Would not recommend this vendor.",
            "تجربة سيئة مع هذا البائع. التسليم متأخر وخدمة العملاء ضعيفة.",
        ];

        if ($rating >= 4) {
            return collect($positiveComments)->random();
        } elseif ($rating == 3) {
            return collect($neutralComments)->random();
        } else {
            return collect($negativeComments)->random();
        }
    }
}
