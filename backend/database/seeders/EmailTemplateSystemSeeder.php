<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class EmailTemplateSystemSeeder extends Seeder
{
    /**
     * Run the email template system seeders.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting Email Template System seeding...');

        // Seed in the correct order due to foreign key dependencies
        $this->call([
            EmailTemplateCategorySeeder::class,
            EmailTemplateVariableSeeder::class,
            EmailTemplateSeeder::class,
        ]);

        $this->command->info('✅ Email Template System seeding completed successfully!');
    }
}