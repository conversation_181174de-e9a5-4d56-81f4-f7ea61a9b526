<?php

require_once 'vendor/autoload.php';

use Illuminate\Http\Request;
use App\Http\Controllers\Client\CartController;
use App\Http\Requests\Cart\AddToCartRequest;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// Get a product and create a cart
$product = App\Models\Product::first();
$cart = App\Models\ShoppingCart::create([
    'uuid' => (string) Str::uuid(),
    'session_id' => 'api-test-session',
    'currency' => 'AED',
    'status' => 'active',
    'expires_at' => now()->addDays(7)
]);

echo "Testing Cart API\n";
echo "================\n";
echo "Cart UUID: {$cart->uuid}\n";
echo "Product: {$product->title_en}\n";
echo "Available Stock: {$product->inventory->available_stock}\n\n";

function testAddItem($cart, $product, $quantity, $testName) {
    echo "{$testName}\n";
    try {
        $controller = new CartController(
            app(App\Services\CartService::class),
            app(App\Services\CartValidationService::class),
            app(App\Services\InventoryReservationService::class)
        );
        
        // Create a mock request
        $request = new AddToCartRequest();
        $request->merge([
            'product_id' => $product->id,
            'quantity' => $quantity
        ]);
        
        $response = $controller->addItem($cart->uuid, $request);
        $responseData = json_decode($response->getContent(), true);
        
        echo "Response: " . $response->getContent() . "\n";
        
        if (isset($responseData['success']) && $responseData['success']) {
            echo "✓ Success: {$responseData['message']}\n";
        } else {
            echo "✗ Failed: " . (isset($responseData['message']) ? $responseData['message'] : 'Unknown error') . "\n";
            if (isset($responseData['data']['validation_errors'])) {
                foreach ($responseData['data']['validation_errors'] as $field => $errors) {
                    foreach ($errors as $error) {
                        echo "  - {$field}: {$error}\n";
                    }
                }
            }
        }
    } catch (Exception $e) {
        echo "✗ Exception: {$e->getMessage()}\n";
    }
    echo "\n";
}

// Test 1: Add item with valid quantity
testAddItem($cart, $product, 2, "Test 1: Adding 2 items to cart");

// Test 2: Add item with quantity exceeding stock (set stock to 1 first)
$product->inventory->update(['stock' => 1]);
testAddItem($cart, $product, 5, "Test 2: Adding item with quantity exceeding stock");

// Test 3: Add item when completely out of stock
$product->inventory->update(['stock' => 0]);
testAddItem($cart, $product, 1, "Test 3: Adding item when completely out of stock");

// Restore inventory
$product->inventory->update(['stock' => 100]);

echo "Test completed!\n";