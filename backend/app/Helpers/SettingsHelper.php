<?php

namespace App\Helpers;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class SettingsHelper
{
    /**
     * Get a setting value by key with optional default
     */
    public static function get(string $key, $default = null)
    {
        return Setting::getValue($key, $default);
    }

    /**
     * Set a setting value by key
     */
    public static function set(string $key, $value): bool
    {
        return Setting::setValue($key, $value);
    }

    /**
     * Get all settings for a specific category
     */
    public static function getByCategory(string $category): array
    {
        $settings = Cache::remember("settings.category.{$category}", config('settings.cache.ttl', 3600), function () use ($category) {
            return Setting::category($category)->active()->get();
        });

        return $settings->pluck('typed_value', 'key')->toArray();
    }

    /**
     * Get all public settings
     */
    public static function getPublic(): array
    {
        $settings = Cache::remember('settings.public', config('settings.cache.ttl', 3600), function () {
            return Setting::public()->active()->get();
        });

        return $settings->pluck('typed_value', 'key')->toArray();
    }

    /**
     * Get public settings for a specific category
     */
    public static function getPublicByCategory(string $category): array
    {
        $settings = Cache::remember("settings.public.category.{$category}", config('settings.cache.ttl', 3600), function () use ($category) {
            return Setting::public()->category($category)->active()->get();
        });

        return $settings->pluck('typed_value', 'key')->toArray();
    }

    /**
     * Clear cache for a specific setting or all settings
     */
    public static function clearCache(string $key = null): void
    {
        if ($key) {
            Cache::forget("setting.{$key}");
            
            // Clear category cache if we can determine the category
            $setting = Setting::where('key', $key)->first();
            if ($setting && $setting->category) {
                Cache::forget("settings.category.{$setting->category}");
                Cache::forget("settings.public.category.{$setting->category}");
            }
        } else {
            // Clear all settings cache
            Cache::forget('settings.all');
            Cache::forget('settings.public');
            
            // Clear category caches
            $categories = config('settings.categories', []);
            foreach (array_keys($categories) as $category) {
                Cache::forget("settings.category.{$category}");
                Cache::forget("settings.public.category.{$category}");
            }
        }
    }

    /**
     * Get multiple settings by keys
     */
    public static function getMultiple(array $keys): array
    {
        $results = [];
        foreach ($keys as $key) {
            $results[$key] = static::get($key);
        }
        return $results;
    }

    /**
     * Set multiple settings at once
     */
    public static function setMultiple(array $settings): array
    {
        $results = [];
        foreach ($settings as $key => $value) {
            $results[$key] = static::set($key, $value);
        }
        return $results;
    }

    /**
     * Check if a setting exists
     */
    public static function exists(string $key): bool
    {
        return Setting::where('key', $key)->exists();
    }

    /**
     * Get setting with type information
     */
    public static function getWithType(string $key): ?array
    {
        $setting = Setting::where('key', $key)->where('is_active', true)->first();
        
        if (!$setting) {
            return null;
        }

        return [
            'key' => $setting->key,
            'value' => $setting->typed_value,
            'type' => $setting->type,
            'display_name' => $setting->display_name,
            'description' => $setting->description,
            'is_public' => $setting->is_public,
        ];
    }

    /**
     * Get all settings grouped by category
     */
    public static function getAllGrouped(): array
    {
        $settings = Cache::remember('settings.all.grouped', config('settings.cache.ttl', 3600), function () {
            return Setting::active()->orderBy('category')->orderBy('sort_order')->get();
        });

        return $settings->groupBy('category')->map(function ($categorySettings) {
            return $categorySettings->pluck('typed_value', 'key')->toArray();
        })->toArray();
    }

    /**
     * Get settings for frontend configuration
     */
    public static function getFrontendConfig(): array
    {
        $publicKeys = config('settings.public_settings', []);
        return static::getMultiple($publicKeys);
    }

    /**
     * Validate setting value against its rules
     */
    public static function validateValue(string $key, $value): bool
    {
        $setting = Setting::where('key', $key)->first();
        
        if (!$setting || !$setting->validation_rules) {
            return true;
        }

        $validator = \Illuminate\Support\Facades\Validator::make(
            ['value' => $value],
            ['value' => $setting->validation_rules]
        );

        return !$validator->fails();
    }

    /**
     * Get setting categories configuration
     */
    public static function getCategories(): array
    {
        return config('settings.categories', []);
    }

    /**
     * Get field types configuration
     */
    public static function getFieldTypes(): array
    {
        return config('settings.field_types', []);
    }

    /**
     * Check if setting is environment specific
     */
    public static function isEnvironmentSpecific(string $key): bool
    {
        $environmentSpecific = config('settings.environment_specific', []);
        return in_array($key, $environmentSpecific);
    }

    /**
     * Check if setting is public
     */
    public static function isPublic(string $key): bool
    {
        $publicSettings = config('settings.public_settings', []);
        return in_array($key, $publicSettings);
    }

    /**
     * Get default value for a setting type
     */
    public static function getDefaultForType(string $type)
    {
        return match ($type) {
            'boolean' => false,
            'integer' => 0,
            'float' => 0.0,
            'json', 'array' => [],
            default => '',
        };
    }

    /**
     * Format setting value for display
     */
    public static function formatForDisplay(string $key): string
    {
        $setting = static::getWithType($key);
        
        if (!$setting) {
            return '';
        }

        return match ($setting['type']) {
            'boolean' => $setting['value'] ? 'Yes' : 'No',
            'json', 'array' => json_encode($setting['value'], JSON_PRETTY_PRINT),
            default => (string) $setting['value'],
        };
    }
}
