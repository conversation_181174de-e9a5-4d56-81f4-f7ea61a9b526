<?php

namespace App\Jobs;

use App\Mail\AbandonedCartReminderMail;
use App\Models\AbandonedCart;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendAbandonedCartReminderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected AbandonedCart $abandonedCart;
    protected array $options;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 120;

    /**
     * Create a new job instance.
     */
    public function __construct(AbandonedCart $abandonedCart, array $options = [])
    {
        $this->abandonedCart = $abandonedCart;
        $this->options = $options;
        
        // Set queue based on priority
        $this->onQueue($this->determineQueue());
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Verify abandoned cart is still valid for reminder
            if (!$this->isValidForReminder()) {
                Log::info('Abandoned cart reminder skipped - not valid', [
                    'abandoned_cart_id' => $this->abandonedCart->id,
                    'cart_id' => $this->abandonedCart->cart_id,
                ]);
                return;
            }

            // Load fresh data with relationships
            $this->abandonedCart->load(['cart.items.product', 'cart.items.vendor', 'user']);

            // Send the email
            Mail::to($this->abandonedCart->customer_email)
                ->send(new AbandonedCartReminderMail($this->abandonedCart, $this->options));

            // Update reminder tracking
            $this->updateReminderTracking();

            // Schedule follow-up reminder if configured
            $this->scheduleFollowUpReminder();

            Log::info('Abandoned cart reminder sent successfully', [
                'abandoned_cart_id' => $this->abandonedCart->id,
                'cart_id' => $this->abandonedCart->cart_id,
                'customer_email' => $this->abandonedCart->customer_email,
                'reminder_count' => $this->abandonedCart->reminder_count + 1,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send abandoned cart reminder', [
                'abandoned_cart_id' => $this->abandonedCart->id,
                'cart_id' => $this->abandonedCart->cart_id,
                'customer_email' => $this->abandonedCart->customer_email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Re-throw to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Abandoned cart reminder job failed permanently', [
            'abandoned_cart_id' => $this->abandonedCart->id,
            'cart_id' => $this->abandonedCart->cart_id,
            'customer_email' => $this->abandonedCart->customer_email,
            'attempts' => $this->attempts(),
            'error' => $exception->getMessage(),
        ]);

        // Mark as failed in database for tracking
        $this->abandonedCart->update([
            'last_reminder_failed_at' => now(),
            'last_reminder_error' => $exception->getMessage(),
        ]);
    }

    /**
     * Check if abandoned cart is still valid for reminder.
     */
    protected function isValidForReminder(): bool
    {
        // Refresh the model to get latest data
        $this->abandonedCart->refresh();

        // Check if already recovered or converted
        if ($this->abandonedCart->is_recovered || $this->abandonedCart->is_converted) {
            return false;
        }

        // Check if recovery token expired
        if ($this->abandonedCart->is_recovery_expired) {
            return false;
        }

        // Check if cart still exists and has items
        $cart = $this->abandonedCart->cart;
        if (!$cart || $cart->items()->count() === 0) {
            return false;
        }

        // Check if cart was converted to order
        if ($cart->status === 'converted') {
            return false;
        }

        // Check reminder frequency limits
        if (!$this->abandonedCart->canSendReminder()) {
            return false;
        }

        return true;
    }

    /**
     * Update reminder tracking information.
     */
    protected function updateReminderTracking(): void
    {
        $this->abandonedCart->increment('reminder_count');
        $this->abandonedCart->update([
            'last_reminder_sent_at' => now(),
            'last_reminder_type' => $this->options['reminder_type'] ?? 'standard',
            'last_reminder_failed_at' => null,
            'last_reminder_error' => null,
        ]);
    }

    /**
     * Schedule follow-up reminder if configured.
     */
    protected function scheduleFollowUpReminder(): void
    {
        $followUpConfig = config('cart.follow_up_reminders', []);
        
        if (empty($followUpConfig)) {
            return;
        }

        $currentCount = $this->abandonedCart->reminder_count;
        
        // Check if there's a follow-up configured for this reminder count
        foreach ($followUpConfig as $config) {
            if ($config['after_reminder'] === $currentCount && $currentCount < config('cart.max_recovery_reminders', 3)) {
                $delay = $config['delay_hours'] ?? 24;
                
                static::dispatch($this->abandonedCart, [
                    'reminder_type' => 'follow_up',
                    'sequence_number' => $currentCount + 1,
                ])->delay(now()->addHours($delay));
                
                Log::info('Follow-up reminder scheduled', [
                    'abandoned_cart_id' => $this->abandonedCart->id,
                    'delay_hours' => $delay,
                    'sequence_number' => $currentCount + 1,
                ]);
                
                break;
            }
        }
    }

    /**
     * Determine which queue to use based on priority.
     */
    protected function determineQueue(): string
    {
        // High value carts get priority queue
        if ($this->abandonedCart->cart_value >= config('cart.high_value_threshold', 500)) {
            return 'high-priority-emails';
        }

        // Recent abandonment gets priority
        if ($this->abandonedCart->abandoned_at && 
            $this->abandonedCart->abandoned_at->diffInHours(now()) <= 2) {
            return 'priority-emails';
        }

        return 'emails';
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'abandoned-cart',
            'email',
            'cart:' . $this->abandonedCart->cart_id,
            'customer:' . $this->abandonedCart->customer_email,
            'reminder:' . ($this->abandonedCart->reminder_count + 1),
        ];
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [30, 120, 300]; // 30 seconds, 2 minutes, 5 minutes
    }

    /**
     * Determine if the job should be retried based on the exception.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addHours(2); // Stop retrying after 2 hours
    }
}
