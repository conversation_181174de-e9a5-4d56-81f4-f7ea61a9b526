<?php

namespace App\Services;

use App\Models\Popup;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class PopupService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Popup::query();

        // Select specific columns
        $query->select(['*']);
        // Filtering
        $filters = ['status' => '=']; // Define the filters you want to apply
        $this->applyFilters($query, $request, $filters);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['title_en', 'title_ar', 'content_en', 'content_ar']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->preparePopupData($request);

        return Popup::create($data);
    }

    private function preparePopupData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new Popup())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'popup')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'popup');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'popup');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            // $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): Popup
    {
        return Popup::findOrFail($id);
    }

    public function update($request, int $id)
    {
        $popup = Popup::findOrFail($id);
        $updateData = $this->preparePopupData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $popup->update($updateData);

        return $popup;
    }

    public function destroy(int $id): bool
    {
        $popup = Popup::findOrFail($id);
        return $popup->delete();
    }

    public function getPopupsByType(string $type)
    {
        $popups = Popup::where('type', $type)->first();
        if (!$popups) {
            throw new \Exception('No popups found for the specified type');
        }

        return $popups;
    }
}