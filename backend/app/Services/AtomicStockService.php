<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\CartReservation;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class AtomicStockService
{
    /**
     * Check and reserve stock atomically to prevent overselling.
     */
    public function checkAndReserveStock(int $productId, ?int $variantId, int $quantity): bool
    {
        return DB::transaction(function () use ($productId, $variantId, $quantity) {
            // Lock the product/variant row
            if ($variantId) {
                $item = ProductVariant::lockForUpdate()->find($variantId);
                $stockField = 'stock_quantity';
            } else {
                $item = Product::lockForUpdate()->find($productId);
                $stockField = 'stock_quantity';
            }

            if (!$item) {
                return false;
            }

            // Get current reserved quantity
            $reservedQuantity = CartReservation::active()
                ->forProduct($productId, $variantId)
                ->sum('quantity_reserved');

            // Calculate available stock
            $availableStock = $item->{$stockField} - $reservedQuantity;

            // Check if we have enough stock
            if ($availableStock < $quantity) {
                return false;
            }

            // Create reservation
            CartReservation::create([
                'product_id' => $productId,
                'variant_id' => $variantId,
                'quantity_reserved' => $quantity,
                'reserved_until' => now()->addMinutes(15),
                'status' => 'active',
            ]);

            return true;
        });
    }

    /**
     * Get available stock with caching for performance.
     */
    public function getAvailableStock(int $productId, ?int $variantId = null): int
    {
        $cacheKey = "stock.{$productId}." . ($variantId ?? 'null');
        
        return Cache::remember($cacheKey, 60, function () use ($productId, $variantId) {
            if ($variantId) {
                $item = ProductVariant::find($variantId);
            } else {
                $item = Product::find($productId);
            }

            if (!$item) {
                return 0;
            }

            $reservedQuantity = CartReservation::active()
                ->forProduct($productId, $variantId)
                ->sum('quantity_reserved');

            return max(0, $item->stock_quantity - $reservedQuantity);
        });
    }

    /**
     * Invalidate stock cache when inventory changes.
     */
    public function invalidateStockCache(int $productId, ?int $variantId = null): void
    {
        $cacheKey = "stock.{$productId}." . ($variantId ?? 'null');
        Cache::forget($cacheKey);
    }
}
