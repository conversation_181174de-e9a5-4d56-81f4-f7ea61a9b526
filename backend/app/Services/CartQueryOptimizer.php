<?php

namespace App\Services;

use App\Models\ShoppingCart;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;

class CartQueryOptimizer
{
    /**
     * Get cart with optimized relationships for display.
     */
    public function getCartForDisplay(string $cartId): ?ShoppingCart
    {
        return ShoppingCart::where('uuid', $cartId)
            ->with([
                'items' => function ($query) {
                    $query->select([
                        'id', 'cart_id', 'product_id', 'vendor_id', 'variant_id',
                        'quantity', 'unit_price', 'total_price', 'base_price', 'promotional_price',
                        'final_unit_price', 'savings_amount', 'discount_amount', 'tax_amount',
                        'product_snapshot', 'customizations', 'special_instructions', 'metadata',
                        'applied_discounts', 'created_at', 'updated_at'
                    ]);
                },
                'items.product:id,name_en,name_ar,sku,featured_image,status,is_active,stock_quantity,min_cart_quantity,max_cart_quantity',
                'items.vendor:id,name_tl_en,vendor_display_name_en,approval_status,is_active,min_order_value,free_shipping_threshold',
                'items.variant:id,product_id,name_en,sku,stock_quantity,is_active'
            ])
            ->first();
    }

    /**
     * Get cart with minimal data for calculations.
     */
    public function getCartForCalculation(string $cartId): ?ShoppingCart
    {
        return ShoppingCart::where('uuid', $cartId)
            ->with([
                'items:id,cart_id,vendor_id,quantity,unit_price,total_price,discount_amount,tax_amount',
                'items.vendor:id,free_shipping_threshold,min_order_value'
            ])
            ->first();
    }

    /**
     * Get vendor groups with optimized queries.
     */
    public function getVendorGroupsOptimized(ShoppingCart $cart): array
    {
        $cacheKey = "cart_vendor_groups:{$cart->id}:{$cart->updated_at->timestamp}";
        
        return Cache::remember($cacheKey, 300, function () use ($cart) {
            // Single query to get all vendor data
            $vendorData = \DB::table('cart_items as ci')
                ->join('vendors as v', 'ci.vendor_id', '=', 'v.id')
                ->where('ci.cart_id', $cart->id)
                ->select([
                    'v.id as vendor_id',
                    'v.name_tl_en as vendor_name',
                    'v.vendor_display_name_en as vendor_display_name',
                    'v.min_order_value',
                    'v.free_shipping_threshold',
                    'v.approval_status',
                    'v.is_active',
                    \DB::raw('COUNT(ci.id) as items_count'),
                    \DB::raw('SUM(ci.quantity) as total_quantity'),
                    \DB::raw('SUM(ci.total_price) as subtotal'),
                    \DB::raw('SUM(ci.discount_amount) as total_discount')
                ])
                ->groupBy([
                    'v.id', 'v.name_tl_en', 'v.vendor_display_name_en',
                    'v.min_order_value', 'v.free_shipping_threshold',
                    'v.approval_status', 'v.is_active'
                ])
                ->get();

            return $vendorData->map(function ($vendor) {
                return [
                    'vendor_id' => $vendor->vendor_id,
                    'vendor_name' => $vendor->vendor_name,
                    'vendor_display_name' => $vendor->vendor_display_name,
                    'items_count' => $vendor->items_count,
                    'total_quantity' => $vendor->total_quantity,
                    'subtotal' => (float) $vendor->subtotal,
                    'total_discount' => (float) $vendor->total_discount,
                    'min_order_value' => (float) $vendor->min_order_value,
                    'free_shipping_threshold' => (float) $vendor->free_shipping_threshold,
                    'min_order_met' => !$vendor->min_order_value || $vendor->subtotal >= $vendor->min_order_value,
                    'qualifies_for_free_shipping' => $vendor->free_shipping_threshold && 
                        $vendor->subtotal >= $vendor->free_shipping_threshold,
                    'is_active' => (bool) $vendor->is_active,
                    'approval_status' => $vendor->approval_status,
                ];
            })->toArray();
        });
    }

    /**
     * Preload cart relationships for bulk operations.
     */
    public function preloadCartRelationships(Builder $query): Builder
    {
        return $query->with([
            'items' => function ($query) {
                $query->select([
                    'id', 'cart_id', 'product_id', 'vendor_id', 'variant_id',
                    'quantity', 'unit_price', 'total_price', 'base_price', 'promotional_price',
                    'final_unit_price', 'savings_amount', 'discount_amount', 'tax_amount'
                ]);
            },
            'items.product:id,name_en,status,is_active,stock_quantity',
            'items.vendor:id,name_tl_en,approval_status,is_active'
        ]);
    }

    /**
     * Get cart statistics with optimized queries.
     */
    public function getCartStatistics(int $userId): array
    {
        $cacheKey = "user_cart_stats:{$userId}";
        
        return Cache::remember($cacheKey, 600, function () use ($userId) {
            $stats = \DB::table('shopping_carts as sc')
                ->leftJoin('cart_items as ci', 'sc.id', '=', 'ci.cart_id')
                ->where('sc.user_id', $userId)
                ->select([
                    \DB::raw('COUNT(DISTINCT sc.id) as total_carts'),
                    \DB::raw('COUNT(DISTINCT CASE WHEN sc.status = "active" THEN sc.id END) as active_carts'),
                    \DB::raw('COUNT(DISTINCT CASE WHEN sc.status = "abandoned" THEN sc.id END) as abandoned_carts'),
                    \DB::raw('COUNT(DISTINCT CASE WHEN sc.status = "converted" THEN sc.id END) as converted_carts'),
                    \DB::raw('COALESCE(SUM(CASE WHEN sc.status = "active" THEN ci.total_price END), 0) as active_cart_value'),
                    \DB::raw('COALESCE(SUM(CASE WHEN sc.status = "active" THEN ci.quantity END), 0) as active_items_count')
                ])
                ->first();

            return [
                'total_carts' => (int) $stats->total_carts,
                'active_carts' => (int) $stats->active_carts,
                'abandoned_carts' => (int) $stats->abandoned_carts,
                'converted_carts' => (int) $stats->converted_carts,
                'active_cart_value' => (float) $stats->active_cart_value,
                'active_items_count' => (int) $stats->active_items_count,
            ];
        });
    }

    /**
     * Invalidate cart-related caches.
     */
    public function invalidateCartCaches(ShoppingCart $cart): void
    {
        $patterns = [
            "cart_vendor_groups:{$cart->id}:*",
            "user_cart_stats:{$cart->user_id}",
            "cart_calculations:{$cart->id}:*"
        ];

        foreach ($patterns as $pattern) {
            if (str_contains($pattern, '*')) {
                // For patterns with wildcards, we'd need Redis SCAN or similar
                // For now, we'll use a simple approach
                $baseKey = str_replace(':*', '', $pattern);
                Cache::forget($baseKey);
            } else {
                Cache::forget($pattern);
            }
        }
    }

    /**
     * Batch load multiple carts efficiently.
     */
    public function batchLoadCarts(array $cartIds): array
    {
        return ShoppingCart::whereIn('uuid', $cartIds)
            ->with([
                'items:id,cart_id,product_id,vendor_id,quantity,unit_price,total_price,base_price,promotional_price,final_unit_price,savings_amount,discount_amount,tax_amount',
                'items.product:id,name_en,status,is_active',
                'items.vendor:id,name_tl_en,approval_status,is_active'
            ])
            ->get()
            ->keyBy('uuid')
            ->toArray();
    }
}
