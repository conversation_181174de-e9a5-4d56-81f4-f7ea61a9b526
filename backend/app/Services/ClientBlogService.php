<?php

namespace App\Services;

use App\Models\Blog;
use App\Models\BlogCategory;
use App\Models\User;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class ClientBlogService
{
    use HelperTrait;

    /**
     * Get comprehensive blog data for three-column layout
     */
    public function index($request): array
    {
        // Check if this is a request for comprehensive layout data
        if ($request->input('layout') === 'comprehensive' || !$request->has('page')) {
            return $this->getComprehensiveBlogData($request);
        }

        // Default behavior for paginated requests
        return $this->getPaginatedBlogs($request);
    }

    /**
     * Get comprehensive blog data for landing page
     */
    private function getComprehensiveBlogData($request): array
    {
        return [
            'latest_articles' => $this->getLatestArticles(9),
            'popular_articles' => $this->getPopularArticles(9),
            'featured_authors' => $this->getFeaturedAuthors(6),
            'categories' => $this->getCategoriesWithCounts(),
            'blog_stats' => $this->getBlogStats(),
        ];
    }

    /**
     * Get paginated blogs (original behavior)
     */
    private function getPaginatedBlogs($request): Collection|LengthAwarePaginator|array
    {
        $query = Blog::query();

        // Only published blogs
        $query->where('status', 'published')
              ->where('published_at', '<=', now());

        // Include relationships
        $query->with(['category', 'user']);

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Filtering
        $filters = [
            'blog_category_id' => '=',
        ];
        $this->applyFilters($query, $request, $filters);

        // Searching
        $searchKeys = ['title_en', 'title_ar', 'summary_en', 'summary_ar'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Default ordering by published date
        if (!$request->has('sort')) {
            $query->orderBy('published_at', 'desc');
        }

        // Get the results and format them
        $results = $this->paginateOrGet($query, $request);

        // If it's a paginated array structure (from paginateOrGet when pagination=true)
        if (is_array($results) && isset($results['data'])) {
            $formattedData = collect($results['data'])->map(function ($blog) {
                return $this->formatBlogForResponse($blog);
            })->toArray();

            $results['data'] = $formattedData;
            return $results;
        }

        // If it's a paginated result, format the data items
        if ($results instanceof LengthAwarePaginator) {
            $formattedData = $results->getCollection()->map(function ($blog) {
                return $this->formatBlogForResponse($blog);
            });
            $results->setCollection($formattedData);
            return $results;
        }

        // If it's a collection, format each item
        if ($results instanceof Collection) {
            return $results->map(function ($blog) {
                return $this->formatBlogForResponse($blog);
            });
        }

        // If it's an array, format each item
        return collect($results)->map(function ($blog) {
            return $this->formatBlogForResponse($blog);
        })->toArray();
    }

    /**
     * Get a single published blog by slug with comprehensive data
     */
    public function show(string $slug, $request = null): array
    {
        $blog = Blog::with(['category', 'user'])
            ->withCount('approvedComments')
            ->where('slug', $slug)
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->firstOrFail();

        // Increment view count
        $blog->increment('view_count');

        // Get related articles count from request or default to 3
        $relatedLimit = $request ? $request->input('related_limit', 3) : 3;
        $relatedArticles = $this->getRelatedArticles($blog, $relatedLimit);

        return [
            'article' => $this->formatBlogForDetailResponse($blog),
            'related_articles' => $relatedArticles,
            'author_info' => $this->getAuthorInfo($blog->user),
            'category_info' => $this->getCategoryInfo($blog->category),
        ];
    }

    /**
     * Get blogs by category slug
     */
    public function getByCategory(string $categorySlug, $request)
    {
        // Find the category
        $category = BlogCategory::where('slug', $categorySlug)
            ->where('status', 'active')
            ->firstOrFail();

        $query = Blog::query();

        // Only published blogs in this category
        $query->where('status', 'published')
              ->where('published_at', '<=', now())
              ->where('blog_category_id', $category->id);

        // Include relationships and ensure valid relationships exist
        $query->with(['category', 'user'])
              ->whereHas('category', function($q) {
                  $q->where('status', 'active');
              });

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['title_en', 'title_ar', 'summary_en', 'summary_ar'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Default ordering by published date
        if (!$request->has('sort')) {
            $query->orderBy('published_at', 'desc');
        }

        // Get the results and format them
        $results = $this->paginateOrGet($query, $request);

        // If it's a paginated array structure (from paginateOrGet when pagination=true)
        if (is_array($results) && isset($results['data'])) {
            $formattedData = collect($results['data'])->map(function ($blog) {
                return $this->formatBlogForResponse($blog);
            })->toArray();

            $results['data'] = $formattedData;
            return $results;
        }

        // If it's a paginated result, format the data items
        if ($results instanceof LengthAwarePaginator) {
            $formattedData = $results->getCollection()->map(function ($blog) {
                return $this->formatBlogForResponse($blog);
            });
            $results->setCollection($formattedData);
            return $results;
        }

        // If it's a collection, format each item
        if ($results instanceof Collection) {
            return $results->map(function ($blog) {
                return $this->formatBlogForResponse($blog);
            });
        }

        // If it's an array, format each item
        return collect($results)->map(function ($blog) {
            try {
                return $this->formatBlogForResponse($blog);
            } catch (\Exception $e) {
                \Log::error('Error formatting blog for response', [
                    'blog_id' => $blog ? ($blog->id ?? 'unknown') : 'null',
                    'error' => $e->getMessage()
                ]);
                return null;
            }
        })->filter()->toArray(); // Remove null entries
    }

    /**
     * Get featured blogs (enhanced with better logic)
     */
    public function getFeatured($request): array
    {
        // Check if this is a request for formatted response
        if ($request->input('format') === 'structured') {
            return $this->getFeaturedStructured($request->input('limit', 6));
        }

        $query = Blog::query();

        // Only published blogs
        $query->where('status', 'published')
              ->where('published_at', '<=', now());

        // Include relationships
        $query->with(['category', 'user']);
        $query->withCount('approvedComments');

        // Select specific columns
        $query->select(['*']);

        // Enhanced featured logic: high view count OR recent articles
        $query->where(function($q) {
            $q->where('view_count', '>', 10) // Popular articles (lowered threshold)
              ->orWhere('published_at', '>=', now()->subDays(30)); // Recent articles (extended period)
        });

        $query->orderBy('view_count', 'desc')
              ->orderBy('published_at', 'desc');

        // Limit to featured count if specified
        if ($request->has('limit')) {
            $query->limit($request->input('limit', 5));
        }

        // Get the results and format them
        $results = $this->paginateOrGet($query, $request);

        // If it's a paginated result, format the data items
        if ($results instanceof LengthAwarePaginator) {
            $formattedData = $results->getCollection()->map(function ($blog) {
                try {
                    return $this->formatBlogForResponse($blog);
                } catch (\Exception $e) {
                    \Log::error('Error formatting blog for response', [
                        'blog_id' => $blog ? ($blog->id ?? 'unknown') : 'null',
                        'error' => $e->getMessage()
                    ]);
                    return null;
                }
            })->filter(); // Remove null entries
            $results->setCollection($formattedData);
            return $results;
        }

        // If it's a collection, format each item
        if ($results instanceof Collection) {
            return $results->map(function ($blog) {
                try {
                    return $this->formatBlogForResponse($blog);
                } catch (\Exception $e) {
                    \Log::error('Error formatting blog for response', [
                        'blog_id' => $blog ? ($blog->id ?? 'unknown') : 'null',
                        'error' => $e->getMessage()
                    ]);
                    return null;
                }
            })->filter(); // Remove null entries
        }

        // If it's an array, format each item
        return collect($results)->map(function ($blog) {
            try {
                return $this->formatBlogForResponse($blog);
            } catch (\Exception $e) {
                \Log::error('Error formatting blog for response', [
                    'blog_id' => $blog ? ($blog->id ?? 'unknown') : 'null',
                    'error' => $e->getMessage()
                ]);
                return null;
            }
        })->filter()->toArray(); // Remove null entries
    }

    /**
     * Get featured blogs in structured format
     */
    private function getFeaturedStructured(int $limit = 6): array
    {
        return Blog::with(['category', 'user'])
            ->withCount('approvedComments')
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->where(function($q) {
                $q->where('view_count', '>', 10)
                  ->orWhere('published_at', '>=', now()->subDays(30));
            })
            ->orderBy('view_count', 'desc')
            ->orderBy('published_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($blog) {
                return $this->formatBlogForResponse($blog);
            })->toArray();
    }

    /**
     * Search blogs
     */
    public function search($request)
    {
        $query = Blog::query();

        // Only published blogs
        $query->where('status', 'published')
              ->where('published_at', '<=', now());

        // Include relationships
        $query->with(['category', 'user']);

        // Select specific columns
        $query->select(['*']);

        // Searching
        $searchKeys = ['title_en', 'title_ar', 'summary_en', 'summary_ar', 'content_en', 'content_ar'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Filtering by category if provided
        $filters = [
            'blog_category_id' => '=',
        ];
        $this->applyFilters($query, $request, $filters);

        // Sorting
        $this->applySorting($query, $request);

        // Default ordering by relevance (published date for now)
        if (!$request->has('sort')) {
            $query->orderBy('published_at', 'desc');
        }

        // Get the results and format them
        $results = $this->paginateOrGet($query, $request);

        // If it's a paginated array structure (from paginateOrGet when pagination=true)
        if (is_array($results) && isset($results['data'])) {
            $formattedData = collect($results['data'])->map(function ($blog) {
                return $this->formatBlogForResponse($blog);
            })->toArray();

            $results['data'] = $formattedData;
            return $results;
        }

        // If it's a paginated result, format the data items
        if ($results instanceof LengthAwarePaginator) {
            $formattedData = $results->getCollection()->map(function ($blog) {
                return $this->formatBlogForResponse($blog);
            });
            $results->setCollection($formattedData);
            return $results;
        }

        // If it's a collection, format each item
        if ($results instanceof Collection) {
            return $results->map(function ($blog) {
                return $this->formatBlogForResponse($blog);
            });
        }

        // If it's an array, format each item
        return collect($results)->map(function ($blog) {
            return $this->formatBlogForResponse($blog);
        })->toArray();
    }

    /**
     * Get related blogs based on category
     *
     * @deprecated This method is deprecated. Related articles are now included
     *             in the main show() method response. Use show() method instead.
     */
    public function getRelated(string $slug, $request): Collection|LengthAwarePaginator|array
    {
        // For backward compatibility, find the current blog and return related articles
        $currentBlog = Blog::where('slug', $slug)
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->firstOrFail();

        $limit = $request->has('limit') ? $request->input('limit', 5) : 5;
        $relatedArticles = $this->getRelatedArticles($currentBlog, $limit);

        // Return in the expected paginated format for backward compatibility
        if ($request->has('page')) {
            // If pagination is requested, return the structured format
            return [
                'data' => $relatedArticles,
                'current_page' => 1,
                'per_page' => $limit,
                'total_items' => count($relatedArticles),
                'total_pages' => 1
            ];
        }

        return $relatedArticles;
    }

    /**
     * Get latest articles for "What's New" section
     */
    private function getLatestArticles(int $limit = 9): array
    {
        return Blog::with(['category', 'user'])
            ->withCount('approvedComments')
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->orderBy('published_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($blog) {
                return $this->formatBlogForResponse($blog);
            })->toArray();
    }

    /**
     * Get popular articles based on view count
     */
    private function getPopularArticles(int $limit = 9): array
    {
        return Blog::with(['category', 'user'])
            ->withCount('approvedComments')
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->orderBy('view_count', 'desc')
            ->orderBy('published_at', 'desc') // Secondary sort for articles with same view count
            ->limit($limit)
            ->get()
            ->map(function ($blog) {
                return $this->formatBlogForResponse($blog);
            })->toArray();
    }

    /**
     * Get featured authors with their article counts
     */
    private function getFeaturedAuthors(int $limit = 6): array
    {
        return User::select(['id', 'name', 'email', 'avatar'])
            ->withCount(['blogs as article_count' => function ($query) {
                $query->where('status', 'published')
                      ->where('published_at', '<=', now());
            }])
            ->whereHas('blogs', function ($query) {
                $query->where('status', 'published')
                      ->where('published_at', '<=', now());
            })
            ->having('article_count', '>', 0)
            ->orderBy('article_count', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'avatar' => $user->avatar_url, // This will use the avatar_url accessor
                    'article_count' => $user->article_count,
                    'bio' => null, // Can be added later if user profile is extended
                ];
            })->toArray();
    }

    /**
     * Get categories with article counts for sidebar navigation
     */
    private function getCategoriesWithCounts(): array
    {
        return BlogCategory::select(['id', 'title_en', 'title_ar', 'slug'])
            ->withCount(['blogs as article_count' => function ($query) {
                $query->where('status', 'published')
                      ->where('published_at', '<=', now());
            }])
            ->where('status', 'active')
            ->having('article_count', '>', 0)
            ->orderBy('article_count', 'desc')
            ->get()
            ->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->title_en,
                    'slug' => $category->slug,
                    'article_count' => $category->article_count,
                    'description' => null, // Can be added if description field exists
                ];
            })->toArray();
    }

    /**
     * Get blog statistics for sidebar display
     */
    private function getBlogStats(): array
    {
        $totalArticles = Blog::where('status', 'published')
            ->where('published_at', '<=', now())
            ->count();

        $totalCategories = BlogCategory::where('status', 'active')
            ->whereHas('blogs', function ($query) {
                $query->where('status', 'published')
                      ->where('published_at', '<=', now());
            })
            ->count();

        $totalAuthors = User::whereHas('blogs', function ($query) {
                $query->where('status', 'published')
                      ->where('published_at', '<=', now());
            })
            ->count();

        return [
            'total_articles' => $totalArticles,
            'total_categories' => $totalCategories,
            'total_authors' => $totalAuthors,
        ];
    }

    /**
     * Format blog data for consistent response structure
     */
    private function formatBlogForResponse($blog): array
    {
        // Ensure we have a valid blog object
        if (!$blog || !isset($blog->id)) {
            throw new \Exception('Invalid blog object provided to formatBlogForResponse');
        }

        // Load relationships if not already loaded
        if (!$blog->relationLoaded('category')) {
            $blog->load('category');
        }
        if (!$blog->relationLoaded('user')) {
            $blog->load('user');
        }

        return [
            'id' => $blog->id,
            'slug' => $blog->slug ?? '',
            'title' => $blog->title_en ?? '',
            'excerpt' => $blog->summary_en ?? '',
            'cover_image' => $blog->featured_image_url ?? null,
            'category' => ($blog->category && isset($blog->category->id)) ? [
                'id' => $blog->category->id,
                'name' => $blog->category->title_en ?? '',
                'slug' => $blog->category->slug ?? '',
            ] : null,
            'author' => ($blog->user && isset($blog->user->id)) ? [
                'id' => $blog->user->id,
                'name' => $blog->user->name ?? '',
                'avatar' => $blog->user->avatar_url ?? null,
            ] : null,
            'published_at' => $blog->published_at?->format('Y-m-d'),
            'read_time' => $blog->read_time ?? 5, // Default to 5 minutes if not set
            'view_count' => $blog->view_count ?? 0,
            'comments_count' => $blog->approved_comments_count ?? 0,
        ];
    }

    /**
     * Get categories with article counts (standalone endpoint)
     */
    public function getCategories($request): array
    {
        return $this->getCategoriesWithCounts();
    }

    /**
     * Get popular blogs (standalone endpoint)
     */
    public function getPopular($request): array
    {
        $limit = $request->input('limit', 12);
        return $this->getPopularArticles($limit);
    }

    /**
     * Get blog statistics (standalone endpoint)
     */
    public function getStats($request): array
    {
        return $this->getBlogStats();
    }

    /**
     * Get featured authors (standalone endpoint)
     */
    public function getAuthors($request): array
    {
        $limit = $request->input('limit', 12);
        return $this->getFeaturedAuthors($limit);
    }

    /**
     * Format blog data for detailed single blog response
     */
    private function formatBlogForDetailResponse($blog): array
    {
        return [
            'id' => $blog->id,
            'slug' => $blog->slug,
            'title' => $blog->title_en,
            'title_ar' => $blog->title_ar,
            'excerpt' => $blog->summary_en,
            'excerpt_ar' => $blog->summary_ar,
            'content' => $blog->content_en,
            'content_ar' => $blog->content_ar,
            'cover_image' => $blog->featured_image_url,
            'meta_title' => $blog->meta_title,
            'meta_description' => $blog->meta_description,
            'keywords' => $blog->keywords,
            'category' => $blog->category ? [
                'id' => $blog->category->id,
                'name' => $blog->category->title_en ?? '',
                'name_ar' => $blog->category->title_ar ?? '',
                'slug' => $blog->category->slug ?? '',
            ] : null,
            'author' => $blog->user ? [
                'id' => $blog->user->id,
                'name' => $blog->user->name ?? '',
                'email' => $blog->user->email ?? '',
                'avatar' => $blog->user->avatar_url ?? null,
            ] : null,
            'published_at' => $blog->published_at?->format('Y-m-d'),
            'published_at_formatted' => $blog->published_at?->format('F j, Y'),
            'read_time' => $blog->read_time ?? 5,
            'view_count' => $blog->view_count ?? 0,
            'comments_count' => $blog->approved_comments_count ?? 0,
            'created_at' => $blog->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $blog->updated_at?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Get related articles from the same category
     */
    private function getRelatedArticles($currentBlog, int $limit = 3): array
    {
        return Blog::with(['category', 'user'])
            ->withCount('approvedComments')
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->where('blog_category_id', $currentBlog->blog_category_id)
            ->where('id', '!=', $currentBlog->id)
            ->orderBy('published_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($blog) {
                return $this->formatBlogForResponse($blog);
            })->toArray();
    }

    /**
     * Get comprehensive author information
     */
    private function getAuthorInfo($user): array
    {
        if (!$user) {
            return [];
        }

        $articleCount = Blog::where('user_id', $user->id)
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->count();

        return [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'avatar' => $user->avatar_url,
            'article_count' => $articleCount,
            'bio' => null, // Can be added later if user profile is extended
            'social_links' => [], // Can be added later if needed
        ];
    }

    /**
     * Get comprehensive category information
     */
    private function getCategoryInfo($category): array
    {
        if (!$category) {
            return [];
        }

        $articleCount = Blog::where('blog_category_id', $category->id)
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->count();

        return [
            'id' => $category->id,
            'name' => $category->title_en,
            'name_ar' => $category->title_ar,
            'slug' => $category->slug,
            'article_count' => $articleCount,
            'description' => null, // Can be added if description field exists
        ];
    }
}
