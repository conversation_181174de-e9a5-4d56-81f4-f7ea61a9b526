<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Customer;

class SimpleMemberPricingService
{
    /**
     * Calculate member pricing for a product - simplified version
     */
    public function calculateMemberPrice(Product $product, Customer $customer, ?int $variantId = null): ?array
    {
        // Simple check: is customer a member?
        if (!$customer->canAccessMemberPricing()) {
            return null;
        }

        $variant = $variantId ? ProductVariant::find($variantId) : null;
        
        // Get base price
        $basePrice = $variant?->price ?? $product->regular_price;
        
        // Get member price - simple hierarchy
        $memberPrice = $this->getMemberPrice($product, $variant);
        
        // If no member price is set, return null (no member benefit)
        if (!$memberPrice || $memberPrice >= $basePrice) {
            return null;
        }

        $savingsAmount = $basePrice - $memberPrice;
        $savingsPercentage = ($savingsAmount / $basePrice) * 100;

        return [
            'base_price' => $basePrice,
            'member_price' => $memberPrice,
            'savings_amount' => $savingsAmount,
            'savings_percentage' => round($savingsPercentage, 2),
            'is_member_price' => true,
        ];
    }

    /**
     * Get member price for product/variant
     */
    protected function getMemberPrice(Product $product, ?ProductVariant $variant = null): ?float
    {
        // Check if product has member pricing enabled
        if (!$product->enable_member_pricing) {
            return null;
        }

        // For variants, use variant price if available, otherwise fall back to product member price
        if ($variant) {
            // If variant has its own member pricing logic, implement here
            // For now, fall back to product member price
            return $product->member_price;
        }

        return $product->member_price;
    }

    /**
     * Calculate member pricing for multiple products
     */
    public function calculateBulkMemberPricing(array $products, Customer $customer): array
    {
        $results = [];

        foreach ($products as $productData) {
            $product = $productData['product'];
            $quantity = $productData['quantity'] ?? 1;
            $variantId = $productData['variant_id'] ?? null;

            $memberPricing = $this->calculateMemberPrice($product, $customer, $variantId);

            $results[] = [
                'product_id' => $product->id,
                'variant_id' => $variantId,
                'quantity' => $quantity,
                'member_pricing' => $memberPricing,
                'total_savings' => $memberPricing ? $memberPricing['savings_amount'] * $quantity : 0,
            ];
        }

        return $results;
    }

    /**
     * Get member pricing preview for customer
     */
    public function getMemberPricingPreview(Customer $customer, array $productIds): array
    {
        $products = Product::whereIn('id', $productIds)
                          ->where('enable_member_pricing', true)
                          ->get();
        $preview = [];

        foreach ($products as $product) {
            $memberPricing = $this->calculateMemberPrice($product, $customer);
            
            $preview[] = [
                'product_id' => $product->id,
                'product_name' => $product->title_en,
                'regular_price' => $product->regular_price,
                'member_pricing' => $memberPricing,
                'has_member_benefit' => $memberPricing !== null,
            ];
        }

        return [
            'customer_is_member' => $customer->canAccessMemberPricing(),
            'products' => $preview,
            'total_potential_savings' => array_sum(array_column($preview, 'member_pricing.savings_amount')),
        ];
    }

    /**
     * Check if customer qualifies for member pricing
     */
    public function customerQualifiesForMemberPricing(Customer $customer): bool
    {
        return $customer->canAccessMemberPricing();
    }

    /**
     * Get the best available price for a customer
     */
    public function getBestPrice(Product $product, Customer $customer, ?int $variantId = null): array
    {
        $variant = $variantId ? ProductVariant::find($variantId) : null;
        $basePrice = $variant?->price ?? $product->regular_price;
        $offerPrice = $variant?->offer_price ?? $product->offer_price;
        
        // Start with base price
        $bestPrice = $basePrice;
        $appliedType = 'regular';

        // Check offer price
        if ($offerPrice && $offerPrice < $bestPrice) {
            $bestPrice = $offerPrice;
            $appliedType = 'promotional';
        }

        // Check member price
        $memberPricing = $this->calculateMemberPrice($product, $customer, $variantId);
        if ($memberPricing && $memberPricing['member_price'] < $bestPrice) {
            $bestPrice = $memberPricing['member_price'];
            $appliedType = 'member';
        }

        return [
            'base_price' => $basePrice,
            'offer_price' => $offerPrice,
            'member_price' => $memberPricing['member_price'] ?? null,
            'final_price' => $bestPrice,
            'applied_type' => $appliedType,
            'savings_amount' => $basePrice - $bestPrice,
            'is_member_pricing' => $appliedType === 'member',
        ];
    }
}
