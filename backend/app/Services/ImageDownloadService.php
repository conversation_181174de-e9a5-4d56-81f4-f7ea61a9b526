<?php

namespace App\Services;

use Aws\S3\S3Client;
use Aws\Exception\AwsException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Exception;

class ImageDownloadService
{
    private S3Client $s3Client;
    private string $bucket;
    private string $region;

    public function __construct()
    {
        $this->bucket = config('filesystems.disks.s3.bucket', env('AWS_BUCKET'));
        $this->region = config('filesystems.disks.s3.region', env('AWS_DEFAULT_REGION'));
        
        $this->s3Client = new S3Client([
            'version' => 'latest',
            'region' => $this->region,
            'credentials' => [
                'key' => config('filesystems.disks.s3.key', env('AWS_ACCESS_KEY_ID')),
                'secret' => config('filesystems.disks.s3.secret', env('AWS_SECRET_ACCESS_KEY')),
            ],
        ]);
    }

    /**
     * Download image from URL and upload to S3 with retry logic
     *
     * @param string $imageUrl URL of the image to download
     * @param string $path S3 path prefix (e.g., 'blog-images')
     * @param string|null $filename Optional custom filename
     * @param int $maxRetries Maximum number of retries
     * @return array|null Array with 'path' and 'url' keys or null on failure
     */
    public function downloadAndUploadToS3(string $imageUrl, string $path = 'blog-images', ?string $filename = null, int $maxRetries = 3): ?array
    {
        $attempt = 0;

        while ($attempt < $maxRetries) {
            try {
                Log::info('Starting image download and S3 upload', [
                    'image_url' => $imageUrl,
                    'path' => $path,
                    'filename' => $filename,
                    'attempt' => $attempt + 1
                ]);

                // Download the image
                $imageData = $this->downloadImage($imageUrl);
                if (!$imageData) {
                    throw new Exception('Failed to download image data');
                }

                // Generate filename if not provided
                if (!$filename) {
                    $filename = $this->generateFilename($imageUrl);
                }

                // Ensure path has trailing slash
                $path = rtrim($path, '/') . '/';
                $s3Key = $path . $filename;

                // Upload to S3
                $result = $this->uploadToS3($imageData, $s3Key);

                if ($result) {
                    Log::info('Image successfully uploaded to S3', [
                        'image_url' => $imageUrl,
                        's3_key' => $s3Key,
                        'attempt' => $attempt + 1
                    ]);

                    return [
                        'path' => $s3Key,
                        'url' => config('filesystems.disks.s3.url') . '/' . $s3Key,
                        'original_url' => $imageUrl
                    ];
                }

                throw new Exception('S3 upload failed');

            } catch (Exception $e) {
                $attempt++;

                Log::warning('Image download/upload attempt failed', [
                    'image_url' => $imageUrl,
                    'attempt' => $attempt,
                    'max_retries' => $maxRetries,
                    'error' => $e->getMessage()
                ]);

                if ($attempt >= $maxRetries) {
                    Log::error('Image download and upload failed after all retries', [
                        'image_url' => $imageUrl,
                        'attempts' => $attempt,
                        'error' => $e->getMessage()
                    ]);
                    return null;
                }

                // Exponential backoff: wait 2^attempt seconds
                sleep(pow(2, $attempt - 1));
            }
        }

        return null;
    }

    /**
     * Download image from URL with optimization
     *
     * @param string $imageUrl URL of the image
     * @return string|null Image binary data or null on failure
     */
    private function downloadImage(string $imageUrl): ?string
    {
        try {
            $response = Http::timeout(30)
                ->withHeaders([
                    'User-Agent' => 'Mozilla/5.0 (compatible; BlogSeeder/1.0)',
                ])
                ->get($imageUrl);

            if ($response->successful()) {
                $contentType = $response->header('Content-Type');
                
                // Validate content type
                if (!$this->isValidImageType($contentType)) {
                    Log::warning('Invalid image content type', [
                        'url' => $imageUrl,
                        'content_type' => $contentType
                    ]);
                    return null;
                }

                $imageData = $response->body();
                
                // Validate image size (max 10MB)
                if (strlen($imageData) > 10 * 1024 * 1024) {
                    Log::warning('Image too large', [
                        'url' => $imageUrl,
                        'size' => strlen($imageData)
                    ]);
                    return null;
                }

                return $imageData;
            }

            Log::warning('Failed to download image', [
                'url' => $imageUrl,
                'status' => $response->status()
            ]);

            return null;
        } catch (Exception $e) {
            Log::error('Image download error', [
                'url' => $imageUrl,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Upload image data to S3
     *
     * @param string $imageData Binary image data
     * @param string $s3Key S3 object key
     * @return bool True on success, false on failure
     */
    private function uploadToS3(string $imageData, string $s3Key): bool
    {
        try {
            $this->s3Client->putObject([
                'Bucket' => $this->bucket,
                'Key' => $s3Key,
                'Body' => $imageData,
                'ContentType' => $this->getContentTypeFromData($imageData),
                'CacheControl' => 'max-age=31536000', // 1 year cache
                'Metadata' => [
                    'uploaded_by' => 'blog_seeder',
                    'uploaded_at' => now()->toISOString(),
                ]
            ]);

            return true;
        } catch (AwsException $e) {
            Log::error('S3 upload failed', [
                's3_key' => $s3Key,
                'aws_error' => $e->getAwsErrorMessage(),
                'aws_code' => $e->getAwsErrorCode()
            ]);
            return false;
        } catch (Exception $e) {
            Log::error('S3 upload error', [
                's3_key' => $s3Key,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Generate a unique filename for the image
     *
     * @param string $imageUrl Original image URL
     * @return string Generated filename
     */
    private function generateFilename(string $imageUrl): string
    {
        // Extract extension from URL or default to jpg
        $extension = $this->getExtensionFromUrl($imageUrl);
        
        // Generate unique filename
        $uuid = Str::uuid();
        $timestamp = now()->format('Y-m-d');
        
        return "blog-{$timestamp}-{$uuid}.{$extension}";
    }

    /**
     * Extract file extension from URL
     *
     * @param string $url Image URL
     * @return string File extension
     */
    private function getExtensionFromUrl(string $url): string
    {
        $path = parse_url($url, PHP_URL_PATH);
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        
        // Validate and normalize extension
        $validExtensions = ['jpg', 'jpeg', 'png', 'webp'];
        $extension = strtolower($extension);
        
        if (!in_array($extension, $validExtensions)) {
            return 'jpg'; // Default fallback
        }
        
        return $extension;
    }

    /**
     * Validate image content type
     *
     * @param string|null $contentType HTTP Content-Type header
     * @return bool True if valid image type
     */
    private function isValidImageType(?string $contentType): bool
    {
        if (!$contentType) {
            return false;
        }

        $validTypes = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/webp'
        ];

        return in_array(strtolower($contentType), $validTypes);
    }

    /**
     * Detect content type from image data
     *
     * @param string $imageData Binary image data
     * @return string Content type
     */
    private function getContentTypeFromData(string $imageData): string
    {
        // Check image signature
        $signatures = [
            "\xFF\xD8\xFF" => 'image/jpeg',
            "\x89\x50\x4E\x47" => 'image/png',
            "RIFF" => 'image/webp', // WebP starts with RIFF
        ];

        foreach ($signatures as $signature => $contentType) {
            if (strpos($imageData, $signature) === 0) {
                return $contentType;
            }
        }

        // Default fallback
        return 'image/jpeg';
    }

    /**
     * Check if S3 service is properly configured
     *
     * @return bool True if configured, false otherwise
     */
    public function isConfigured(): bool
    {
        return !empty($this->bucket) && !empty($this->region);
    }
}
