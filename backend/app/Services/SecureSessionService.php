<?php

namespace App\Services;

use App\Models\ShoppingCart;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class SecureSessionService
{
    /**
     * Regenerate session ID to prevent session fixation.
     */
    public function regenerateSession(Request $request, bool $deleteOld = true): string
    {
        $oldSessionId = session()->getId();
        
        // Regenerate session ID
        session()->regenerate($deleteOld);
        
        $newSessionId = session()->getId();
        
        Log::info('Session regenerated', [
            'old_session_id' => substr($oldSessionId, 0, 8) . '...',
            'new_session_id' => substr($newSessionId, 0, 8) . '...',
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);
        
        return $newSessionId;
    }

    /**
     * Migrate cart to new session after regeneration.
     */
    public function migrateCartToNewSession(string $oldSessionId, string $newSessionId): void
    {
        $carts = ShoppingCart::where('session_id', $oldSessionId)
            ->where('status', 'active')
            ->whereNull('user_id')
            ->get();

        foreach ($carts as $cart) {
            $cart->update(['session_id' => $newSessionId]);
            
            Log::info('Cart migrated to new session', [
                'cart_id' => $cart->id,
                'old_session' => substr($oldSessionId, 0, 8) . '...',
                'new_session' => substr($newSessionId, 0, 8) . '...',
            ]);
        }
    }

    /**
     * Secure session initialization for cart operations.
     */
    public function initializeSecureSession(Request $request): array
    {
        $oldSessionId = session()->getId();
        
        // Check for suspicious session activity
        if ($this->detectSuspiciousActivity($request)) {
            Log::warning('Suspicious session activity detected', [
                'session_id' => substr($oldSessionId, 0, 8) . '...',
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);
            
            // Force session regeneration
            $newSessionId = $this->regenerateSession($request);
            $this->migrateCartToNewSession($oldSessionId, $newSessionId);
            
            return [
                'regenerated' => true,
                'old_session_id' => $oldSessionId,
                'new_session_id' => $newSessionId,
                'reason' => 'suspicious_activity'
            ];
        }

        // Regular session validation
        if ($this->shouldRegenerateSession($request)) {
            $newSessionId = $this->regenerateSession($request);
            $this->migrateCartToNewSession($oldSessionId, $newSessionId);
            
            return [
                'regenerated' => true,
                'old_session_id' => $oldSessionId,
                'new_session_id' => $newSessionId,
                'reason' => 'routine_regeneration'
            ];
        }

        return [
            'regenerated' => false,
            'session_id' => $oldSessionId
        ];
    }

    /**
     * Detect suspicious session activity.
     */
    protected function detectSuspiciousActivity(Request $request): bool
    {
        $sessionId = session()->getId();
        $currentIp = $request->ip();
        $currentUserAgent = $request->userAgent();

        // Check for IP address changes (with some tolerance for mobile networks)
        $lastIp = session()->get('last_ip');
        if ($lastIp && $lastIp !== $currentIp) {
            // Allow IP changes within same subnet for mobile users
            if (!$this->isIpInSameSubnet($lastIp, $currentIp)) {
                return true;
            }
        }

        // Check for user agent changes
        $lastUserAgent = session()->get('last_user_agent');
        if ($lastUserAgent && $lastUserAgent !== $currentUserAgent) {
            return true;
        }

        // Check for rapid session creation (potential attack)
        $sessionCreationTime = session()->get('session_created_at', now());
        if (now()->diffInMinutes($sessionCreationTime) < 1) {
            $requestCount = session()->get('request_count', 0);
            if ($requestCount > 10) {
                return true;
            }
        }

        // Update session tracking data
        session()->put([
            'last_ip' => $currentIp,
            'last_user_agent' => $currentUserAgent,
            'session_created_at' => session()->get('session_created_at', now()),
            'request_count' => session()->get('request_count', 0) + 1,
        ]);

        return false;
    }

    /**
     * Check if session should be regenerated based on time/activity.
     */
    protected function shouldRegenerateSession(Request $request): bool
    {
        // Regenerate session every 30 minutes for security
        $lastRegeneration = session()->get('last_regeneration', session()->get('session_created_at', now()));
        if (now()->diffInMinutes($lastRegeneration) > 30) {
            session()->put('last_regeneration', now());
            return true;
        }

        // Regenerate on authentication state changes
        if (auth()->check() && !session()->get('was_authenticated', false)) {
            session()->put('was_authenticated', true);
            return true;
        }

        if (!auth()->check() && session()->get('was_authenticated', false)) {
            session()->put('was_authenticated', false);
            return true;
        }

        return false;
    }

    /**
     * Check if two IP addresses are in the same subnet.
     */
    protected function isIpInSameSubnet(string $ip1, string $ip2): bool
    {
        // Simple subnet check for /24 networks
        $subnet1 = substr($ip1, 0, strrpos($ip1, '.'));
        $subnet2 = substr($ip2, 0, strrpos($ip2, '.'));
        
        return $subnet1 === $subnet2;
    }

    /**
     * Clean up old session data.
     */
    public function cleanupOldSessions(): int
    {
        // This would typically be handled by Laravel's session garbage collection
        // But we can add additional cleanup for cart-specific session data
        
        $expiredCarts = ShoppingCart::whereNotNull('session_id')
            ->where('expires_at', '<', now())
            ->where('status', 'active')
            ->get();

        $count = 0;
        foreach ($expiredCarts as $cart) {
            $cart->update(['status' => 'expired']);
            $count++;
        }

        Log::info('Expired cart sessions cleaned up', ['count' => $count]);

        return $count;
    }

    /**
     * Validate session integrity.
     */
    public function validateSessionIntegrity(Request $request): bool
    {
        $sessionId = session()->getId();
        
        // Check session format
        if (!preg_match('/^[a-zA-Z0-9]{40}$/', $sessionId)) {
            Log::warning('Invalid session ID format', [
                'session_id' => substr($sessionId, 0, 8) . '...',
                'ip' => $request->ip(),
            ]);
            return false;
        }

        // Check session age
        $sessionCreated = session()->get('session_created_at');
        if (!$sessionCreated) {
            session()->put('session_created_at', now());
            $sessionCreated = now();
        }

        // Sessions older than 24 hours should be regenerated
        if (now()->diffInHours($sessionCreated) > 24) {
            Log::info('Session too old, needs regeneration', [
                'session_id' => substr($sessionId, 0, 8) . '...',
                'age_hours' => now()->diffInHours($sessionCreated),
            ]);
            return false;
        }

        return true;
    }

    /**
     * Get session security metrics.
     */
    public function getSessionSecurityMetrics(): array
    {
        return [
            'session_id' => substr(session()->getId(), 0, 8) . '...',
            'created_at' => session()->get('session_created_at'),
            'last_regeneration' => session()->get('last_regeneration'),
            'request_count' => session()->get('request_count', 0),
            'is_authenticated' => auth()->check(),
            'last_ip' => session()->get('last_ip'),
            'integrity_valid' => $this->validateSessionIntegrity(request()),
        ];
    }
}
