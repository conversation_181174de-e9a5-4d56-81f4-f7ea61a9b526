<?php

namespace App\Services;

use App\Models\Order;
use App\Models\ReturnRequest;
use App\Traits\HelperTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ReturnService
{
    use HelperTrait;

    public function createReturn(Request $request): ReturnRequest
    {
        DB::beginTransaction();
        try {
            $order = Order::findOrFail($request->order_id);

            $return = ReturnRequest::create([
                'order_id' => $request->order_id,
                'user_id' => Auth::id(),
                'description' => $request->description,
            ]);

            foreach ($request->items as $item) {
                $orderItem = $order->items()->where('product_id', $item['product_id'])->first();
                if (!$orderItem) {
                    throw new \Exception("Product not found in order");
                }

                $totalAmount = $orderItem->price * $item['quantity'];

                $return->items()->create([
                    'product_id' => $item['product_id'],
                    'reason_id' => $item['reason_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $orderItem->price,
                    'total_amount' => $totalAmount,
                    'attachments' => $item['attachments'] ?? [],
                    'reason' => $item['reason'] ?? null,
                ]);
            }

            DB::commit();
            return $return->load(['order', 'user', 'items.product']);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function getUserReturns(Request $request)
    {

        $query = ReturnRequest::with(['order:id,user_id,vendor_id,order_number,subtotal,discount_total,tax_total,shipping_fee,total,payment_status,fulfillment_status', 'items', 'items.product:id,user_id,vendor_id,uuid,category_id,sub_category_id,class_id,sub_class_id,brand_id,vendor_sku,system_sku,barcode,model_number,title_en,title_ar,short_name'])
        ->where('user_id', Auth::id());

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('order_id')) {
            $query->where('order_id', $request->order_id);
        }

        $this->applySorting($query, $request);
        return $this->paginateOrGet($query, $request);
    }

    public function getReturnDetails(int $id): ReturnRequest
    {
        return ReturnRequest::with(['order', 'user', 'approvedBy', 'items.product', 'items.reason'])
            // ->where('user_id', Auth::id())
            ->findOrFail($id);
    }

    public function cancelReturn(int $id): ReturnRequest
    {
        $return = ReturnRequest::where('user_id', Auth::id())
            ->where('status', 'pending')
            ->findOrFail($id);

        $return->update(['status' => 'cancelled']);
        return $return;
    }

    public function adminGetReturns(Request $request)
    {
        $query = ReturnRequest::with(['order:id,user_id,vendor_id,order_number,subtotal,discount_total,tax_total,shipping_fee,total,payment_status,fulfillment_status', 'user', 'items.product:id,user_id,vendor_id,uuid,category_id,sub_category_id,class_id,sub_class_id,brand_id,vendor_sku,system_sku,barcode,model_number,title_en,title_ar,short_name']);

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('order_id')) {
            $query->where('order_id', $request->order_id);
        }

        $searchKeys = ['return_number', 'reason'];
        $this->applySearch($query, $request->input('search'), $searchKeys);
        $this->applySorting($query, $request);

        return $this->paginateOrGet($query, $request);
    }

    public function adminUpdateReturn(Request $request, int $id): ReturnRequest
    {
        $return = ReturnRequest::findOrFail($id);

        $updateData = [
            'status' => $request->status,
            'admin_notes' => $request->admin_notes,
        ];

        if ($request->status === 'approved') {
            $updateData['approved_at'] = now();
            $updateData['approved_by'] = Auth::id();
        }

        if ($request->status === 'completed') {
            $updateData['completed_at'] = now();
        }

        $return->update($updateData);
        return $return->load(['order', 'user', 'items.product']);
    }

    private function calculateReturnAmount(Order $order, array $items): float
    {
        $total = 0;
        foreach ($items as $item) {
            $orderItem = $order->items()->where('product_id', $item['product_id'])->first();
            if ($orderItem && $item['quantity'] <= $orderItem->quantity) {
                $total += $orderItem->price * $item['quantity'];
            }
        }
        return $total;
    }

    public function validateReturnItems(int $orderId, array $items): bool
    {
        $order = Order::with('items')->findOrFail($orderId);

        foreach ($items as $item) {
            $orderItem = $order->items->where('product_id', $item['product_id'])->first();

            if (!$orderItem) {
                throw new \Exception("Product not found in this order");
            }

            // Check if return quantity exceeds ordered quantity
            if ($item['quantity'] > $orderItem->quantity) {
                throw new \Exception("Return quantity cannot exceed ordered quantity");
            }

            // Check if product was already returned (optional)
            $alreadyReturned = ReturnRequest::where('order_id', $orderId)
                ->where('status', '!=', 'cancelled')
                ->get()
                ->sum(function ($return) use ($item) {
                    $returnItems = collect($return->items);
                    return $returnItems->where('product_id', $item['product_id'])->sum('quantity');
                });

            if (($alreadyReturned + $item['quantity']) > $orderItem->quantity) {
                throw new \Exception("Total return quantity exceeds available quantity for this product");
            }
        }

        return true;
    }

    public function getReturnStatistics(): array
    {

        return [
            'total' => ReturnRequest::count(),
            'pending' => ReturnRequest::where('status', 'pending')->count(),
            'approved' => ReturnRequest::where('status', 'approved')->count(),
            'completed' => ReturnRequest::where('status', 'completed')->count(),
            'rejected' => ReturnRequest::where('status', 'rejected')->count(),
        ];
    }
}
