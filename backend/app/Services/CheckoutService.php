<?php

namespace App\Services;

use App\Models\ShoppingCart;
use App\Models\UserAddress;
use App\Models\UserCard;
use App\Models\Order;
use App\Models\User;
use App\Models\PaymentMethod;
use App\Services\OrderService;
use App\Services\CartValidationService;
use App\Services\CartService;
use App\Http\Resources\Cart\CartResource;
use App\Http\Resources\Checkout\CheckoutResource;
use App\Traits\HelperTrait;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class CheckoutService
{
    use HelperTrait;

    protected OrderService $orderService;
    protected CartValidationService $cartValidationService;
    protected CartService $cartService;

    public function __construct(
        OrderService $orderService,
        CartValidationService $cartValidationService,
        CartService $cartService
    ) {
        $this->orderService = $orderService;
        $this->cartValidationService = $cartValidationService;
        $this->cartService = $cartService;
    }

    /**
     * Get user's addresses for checkout
     */
    public function getUserAddresses(User $user, ?string $type = null): array
    {
        $query = $user->addresses();

        if ($type) {
            $query->where(function ($q) use ($type) {
                if ($type === 'shipping') {
                    $q->where('is_shipping', true)->orWhere('is_default', true);
                } elseif ($type === 'billing') {
                    $q->where('is_billing', true)->orWhere('is_default', true);
                }
            });
        }

        $addresses = $query->orderBy('is_default', 'desc')
                          ->orderBy('created_at', 'desc')
                          ->get();

        // Ensure we return a proper array with all necessary fields
        return $addresses->map(function ($address) {
            return [
                'id' => $address->id,
                'user_id' => $address->user_id,
                'address_type' => $address->address_type,
                'flat_or_villa_number' => $address->flat_or_villa_number,
                'building_name' => $address->building_name,
                'address_line_1' => $address->address_line_1,
                'address_line_2' => $address->address_line_2,
                'city' => $address->city,
                'state' => $address->state,
                'postal_code' => $address->postal_code,
                'country' => $address->country,
                'is_default' => $address->is_default,
                'is_shipping' => $address->is_shipping,
                'is_billing' => $address->is_billing,
                'created_at' => $address->created_at,
                'updated_at' => $address->updated_at,
            ];
        })->toArray();
    }

    /**
     * Get user's payment methods for checkout
     */
    public function getUserPaymentMethods(User $user): array
    {
        return $user->cards()
                   ->where('is_active', true)
                   ->orderBy('is_default', 'desc')
                   ->orderBy('created_at', 'desc')
                   ->get()
                   ->toArray();
    }

    /**
     * Create new address during checkout
     */
    public function createCheckoutAddress(User $user, array $addressData): UserAddress
    {
        // Use the same validation rules as StoreUserAddressRequest with correct enum values
        $validator = Validator::make($addressData, [
            'address_type' => 'required|string|in:home,office,school',
            'flat_or_villa_number' => 'required|string',
            'building_name' => 'required|string',
            'address_line_1' => 'required|string',
            'address_line_2' => 'required|string',
            'city' => 'required|string',
            'state' => 'required|string',
            'postal_code' => 'required|string',
            'country' => 'required|string',
            'is_default' => 'required|boolean',
            'is_shipping' => 'required|boolean',
            'is_billing' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        $validated = $validator->validated();
        $validated['user_id'] = $user->id;

        // Set address type flags based on address_type
        if (isset($validated['address_type'])) {
            switch ($validated['address_type']) {
                case 'shipping':
                    $validated['is_shipping'] = true;
                    $validated['is_billing'] = false;
                    break;
                case 'billing':
                    $validated['is_shipping'] = false;
                    $validated['is_billing'] = true;
                    break;
                case 'both':
                    $validated['is_shipping'] = true;
                    $validated['is_billing'] = true;
                    break;
            }
        }

        // Handle default address logic
        if ($validated['is_default'] ?? false) {
            UserAddress::where('user_id', $user->id)
                      ->where('is_default', true)
                      ->update(['is_default' => false]);
        }

        return UserAddress::create($validated);
    }

    /**
     * Get available payment method types from database
     */
    public function getAvailablePaymentMethodTypes(): array
    {
        $paymentMethods = PaymentMethod::where('status', 'active')
                                     ->orderBy('name_en')
                                     ->get();

        return $paymentMethods->map(function ($method) {
            return [
                'id' => $method->id,
                'type' => $method->slug,
                'name' => $method->name_en,
                'name_ar' => $method->name_ar,
                'description' => $method->description_en,
                'description_ar' => $method->description_ar,
                'icon' => $method->icon_url,
                'enabled' => $method->status === 'active',
                'requires_selection' => in_array($method->slug, ['card', 'apple-pay', 'google-pay', 'paypal']),
            ];
        })->toArray();
    }

    /**
     * Get enhanced payment methods with additional metadata
     */
    public function getEnhancedPaymentMethods(): array
    {
        try {
            $paymentMethods = PaymentMethod::where('status', 'active')
                                         ->orderBy('name_en')
                                         ->get();

            return $paymentMethods->map(function ($method) {
                return [
                    'id' => $method->id,
                    'type' => $method->slug ?? 'unknown',
                    'name' => $method->name_en ?? 'Unknown Payment Method',
                    'name_ar' => $method->name_ar ?? 'طريقة دفع غير معروفة',
                    'description' => $method->description_en ?? '',
                    'description_ar' => $method->description_ar ?? '',
                    'icon' => $method->icon_url ?? '',
                    'enabled' => $method->status === 'active',
                    'requires_selection' => in_array($method->slug ?? '', ['card', 'apple-pay', 'google-pay', 'paypal']),
                    'requires_card_details' => in_array($method->slug ?? '', ['card']),
                    'supports_saved_cards' => in_array($method->slug ?? '', ['card', 'apple-pay', 'google-pay']),
                    'processing_fee' => $method->processing_fee ?? 0,
                    'min_amount' => $method->min_amount ?? 0,
                    'max_amount' => $method->max_amount ?? null,
                    'supported_currencies' => ['AED', 'USD'], // Default currencies
                    'estimated_processing_time' => $this->getProcessingTime($method->slug ?? 'unknown'),
                    'security_features' => $this->getSecurityFeatures($method->slug ?? 'unknown'),
                ];
            })->toArray();
        } catch (\Exception $e) {
            // Return empty array if there's an error
            return [];
        }
    }

    /**
     * Select payment method with enhanced validation
     */
    public function selectPaymentMethod(User $user, array $paymentData): array
    {
        $paymentMethod = PaymentMethod::where('id', $paymentData['payment_method_id'])
                                     ->where('status', 'active')
                                     ->firstOrFail();

        $result = [
            'payment_method' => [
                'id' => $paymentMethod->id,
                'type' => $paymentMethod->slug,
                'name' => $paymentMethod->name_en,
                'selected' => true,
            ],
            'requires_card_selection' => false,
            'user_cards' => [],
        ];

        // Handle card-based payment methods
        if (in_array($paymentMethod->slug, ['card', 'apple-pay', 'google-pay'])) {
            $result['requires_card_selection'] = true;
            $result['user_cards'] = $user->cards()
                                        ->where('is_active', true)
                                        ->orderBy('is_default', 'desc')
                                        ->get()
                                        ->map(function ($card) {
                                            return [
                                                'id' => $card->id,
                                                'last_four' => $card->last_four,
                                                'brand' => $card->brand,
                                                'expires_at' => $card->expires_at,
                                                'is_default' => $card->is_default,
                                                'cardholder_name' => $card->cardholder_name,
                                            ];
                                        })
                                        ->toArray();

            // If user selected a specific card
            if (!empty($paymentData['user_card_id'])) {
                $selectedCard = $user->cards()
                                   ->where('id', $paymentData['user_card_id'])
                                   ->where('is_active', true)
                                   ->first();

                if ($selectedCard) {
                    $result['selected_card'] = [
                        'id' => $selectedCard->id,
                        'last_four' => $selectedCard->last_four,
                        'brand' => $selectedCard->brand,
                        'expires_at' => $selectedCard->expires_at,
                        'cardholder_name' => $selectedCard->cardholder_name,
                    ];
                    $result['requires_card_selection'] = false;
                }
            }
        }

        return $result;
    }

    /**
     * Validate checkout data
     */
    public function validateCheckoutData(ShoppingCart $cart, array $checkoutData): array
    {
        // Validate cart first
        $cartValidation = $this->cartValidationService->validateCartForCheckout($cart);

        if (!$cartValidation['is_valid']) {
            return [
                'is_valid' => false,
                'errors' => $cartValidation['errors'],
                'cart_validation' => $cartValidation
            ];
        }

        $errors = [];

        // Validate shipping address
        if (empty($checkoutData['shipping_address_id']) && empty($checkoutData['shipping_address'])) {
            $errors[] = 'Shipping address is required';
        }

        // Validate payment method ID
        if (empty($checkoutData['payment_method_id'])) {
            $errors[] = 'Payment method selection is required';
        } else {
            // Validate that the payment method exists and is active
            $paymentMethod = PaymentMethod::where('id', $checkoutData['payment_method_id'])
                                         ->where('status', 'active')
                                         ->first();
            if (!$paymentMethod) {
                $errors[] = 'Selected payment method is not available';
            } else {
                // Validate payment method selection for card payments
                if (in_array($paymentMethod->slug, ['card', 'apple-pay', 'google-pay', 'paypal'])) {
                    if (empty($checkoutData['user_card_id']) && empty($checkoutData['payment_method_data'])) {
                        $errors[] = 'Card selection is required for card payments';
                    }
                }
            }
        }

        // Validate terms acceptance
        if (!($checkoutData['terms_accepted'] ?? false)) {
            $errors[] = 'Terms and conditions must be accepted';
        }

        return [
            'is_valid' => empty($errors),
            'errors' => $errors,
            'cart_validation' => $cartValidation
        ];
    }

    /**
     * Process complete checkout
     */
    public function processCheckout(ShoppingCart $cart, array $checkoutData): Order
    {
        return DB::transaction(function () use ($cart, $checkoutData) {
            $user = $cart->user;
            
            // Validate checkout data
            $validation = $this->validateCheckoutData($cart, $checkoutData);
            if (!$validation['is_valid']) {
                throw new \Exception('Checkout validation failed: ' . implode(', ', $validation['errors']));
            }

            // Prepare order data
            $orderData = $this->prepareOrderData($cart, $checkoutData);

            // Create order using existing OrderService
            $order = $this->orderService->createFromCart($cart, $orderData);

            // Clear cart after successful order creation
            if (!($checkoutData['preserve_cart'] ?? false)) {
                $cart->items()->delete();
                $cart->update([
                    'subtotal' => 0,
                    'total_amount' => 0,
                    'applied_coupons' => null,
                ]);
            }

            return $order;
        });
    }

    /**
     * Prepare order data from checkout data
     */
    protected function prepareOrderData(ShoppingCart $cart, array $checkoutData): array
    {
        $user = $cart->user;
        $orderData = [
            'user_id' => $user->id,
            'payment_method_id' => $checkoutData['payment_method_id'],
            'customer_note' => $checkoutData['customer_note'] ?? null,
            'terms_accepted' => $checkoutData['terms_accepted'] ?? false,
            'metadata' => array_merge(
                $checkoutData['metadata'] ?? [],
                [
                    'checkout_timestamp' => now()->toISOString(),
                    'checkout_method' => 'api',
                    'payment_method_slug' => $this->getPaymentMethodSlug($checkoutData['payment_method_id']),
                ]
            ),
        ];

        // Handle shipping address
        if (!empty($checkoutData['shipping_address_id'])) {
            $address = UserAddress::where('id', $checkoutData['shipping_address_id'])
                                 ->where('user_id', $user->id)
                                 ->with('user')
                                 ->first();
            if ($address) {
                $orderData['shipping_address'] = $this->formatAddressForOrder($address);
            }
        } elseif (!empty($checkoutData['shipping_address'])) {
            $orderData['shipping_address'] = $checkoutData['shipping_address'];
        }

        // Handle billing address
        if ($checkoutData['use_shipping_for_billing'] ?? false) {
            $orderData['billing_address'] = $orderData['shipping_address'] ?? null;
        } elseif (!empty($checkoutData['billing_address_id'])) {
            $address = UserAddress::where('id', $checkoutData['billing_address_id'])
                                 ->where('user_id', $user->id)
                                 ->with('user')
                                 ->first();
            if ($address) {
                $orderData['billing_address'] = $this->formatAddressForOrder($address);
            }
        } elseif (!empty($checkoutData['billing_address'])) {
            $orderData['billing_address'] = $checkoutData['billing_address'];
        }

        return $orderData;
    }

    /**
     * Format address for order storage
     */
    protected function formatAddressForOrder(UserAddress $address): array
    {
        // Get user name parts - if address doesn't have first_name/last_name, use user's name
        $firstName = $address->first_name ?? '';
        $lastName = $address->last_name ?? '';

        // If address doesn't have name fields, use the user's name
        if (empty($firstName) && empty($lastName) && $address->user) {
            $nameParts = explode(' ', $address->user->name, 2);
            $firstName = $nameParts[0] ?? '';
            $lastName = $nameParts[1] ?? '';
        }

        return [
            'first_name' => $firstName,
            'last_name' => $lastName,
            'phone' => $address->phone,
            'flat_or_villa_number' => $address->flat_or_villa_number,
            'building_name' => $address->building_name,
            'address_line_1' => $address->address_line_1,
            'address_line_2' => $address->address_line_2,
            'city' => $address->city,
            'state' => $address->state,
            'postal_code' => $address->postal_code,
            'country' => $address->country,
        ];
    }

    /**
     * Initialize checkout with all required data in one call
     */
    public function initializeCheckout(ShoppingCart $cart): CheckoutResource
    {
        $validation = $this->cartValidationService->validateCartForCheckout($cart);

        // Load cart with all relationships for consistent response structure
        $cart->load(['items.product', 'items.variant']);

        $checkoutData = [
            'cart' => $cart,
            'validation' => $validation,
            'user_addresses' => $this->getUserAddresses($cart->user),
            'user_payment_methods' => $this->getUserPaymentMethods($cart->user),
            'available_payment_methods' => $this->getAvailablePaymentMethodTypes(),
            'checkout_ready' => $validation['is_valid'] && !empty($this->getUserAddresses($cart->user)),
        ];

        return new CheckoutResource($checkoutData);
    }

    /**
     * Get checkout summary
     */
    public function getCheckoutSummary(ShoppingCart $cart): array
    {
        $validation = $this->cartValidationService->validateCartForCheckout($cart);

        return [
            'cart' => [
                'uuid' => $cart->uuid,
                'items_count' => $cart->items()->count(),
                'subtotal' => $cart->subtotal,
                'total_amount' => $cart->total_amount,
                'currency' => $cart->currency,
                'applied_coupons' => $cart->applied_coupons,
            ],
            'validation' => $validation,
            'user_addresses' => $this->getUserAddresses($cart->user),
            'user_payment_methods' => $this->getUserPaymentMethods($cart->user),
            'available_payment_methods' => $this->getAvailablePaymentMethodTypes(),
        ];
    }

    /**
     * Apply coupon during checkout with consistent response structure
     */
    public function applyCouponToCheckout(ShoppingCart $cart, string $couponCode): array
    {
        $result = $this->cartService->applyCoupon($cart, $couponCode);

        // Return consistent structure with cart data
        $cart->load(['items.product', 'items.variant']);

        return [
            'coupon' => [
                'code' => $couponCode,
                'discount_amount' => $result['discount_amount'],
            ],
            'cart' => new CartResource($cart->fresh()),
            'cart_totals' => [
                'original_total' => $cart->subtotal + $result['discount_amount'],
                'discount_amount' => $result['discount_amount'],
                'total_amount' => $cart->total_amount,
            ],
        ];
    }

    /**
     * Remove coupon during checkout with consistent response structure
     */
    public function removeCouponFromCheckout(ShoppingCart $cart, string $couponCode): array
    {
        $this->cartService->removeCoupon($cart, $couponCode);

        // Return consistent structure with cart data
        $cart->load(['items.product', 'items.variant']);

        return [
            'coupon_removed' => $couponCode,
            'cart' => new CartResource($cart->fresh()),
        ];
    }

    /**
     * Get payment method slug by ID
     */
    protected function getPaymentMethodSlug(int $paymentMethodId): ?string
    {
        $paymentMethod = PaymentMethod::find($paymentMethodId);
        return $paymentMethod ? $paymentMethod->slug : null;
    }

    /**
     * Get estimated processing time for payment method
     */
    protected function getProcessingTime(string $paymentMethodSlug): string
    {
        return match ($paymentMethodSlug) {
            'cod' => 'On delivery',
            'card', 'apple-pay', 'google-pay' => 'Instant',
            'bank-transfer' => '1-2 business days',
            'wallet' => 'Instant',
            default => 'Varies',
        };
    }

    /**
     * Get security features for payment method
     */
    protected function getSecurityFeatures(string $paymentMethodSlug): array
    {
        return match ($paymentMethodSlug) {
            'card' => ['3D Secure', 'PCI DSS Compliant', 'Fraud Detection'],
            'apple-pay' => ['Touch ID/Face ID', 'Tokenization', 'Device Authentication'],
            'google-pay' => ['Biometric Authentication', 'Tokenization', 'Device Lock'],
            'cod' => ['Cash on Delivery', 'No Online Payment Required'],
            'bank-transfer' => ['Bank-level Security', 'Account Verification'],
            'wallet' => ['PIN Protection', 'Balance Verification'],
            default => ['Standard Security'],
        };
    }
}
