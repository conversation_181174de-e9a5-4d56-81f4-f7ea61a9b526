<?php

namespace App\Services;

use App\Models\CartItem;
use App\Models\CartReservation;
use App\Models\Inventory;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ShoppingCart;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InventoryReservationService
{
    /**
     * Reserve inventory for all items in a cart.
     */
    public function reserveCartInventory(ShoppingCart $cart, int $reservationMinutes = 15): array
    {
        $results = [
            'success' => true,
            'reservations' => [],
            'conflicts' => [],
            'errors' => [],
        ];

        DB::transaction(function () use ($cart, $reservationMinutes, &$results) {
            foreach ($cart->items as $item) {
                try {
                    $reservation = $this->reserveItemInventory($item, $reservationMinutes);
                    
                    if ($reservation) {
                        $results['reservations'][] = [
                            'cart_item_id' => $item->id,
                            'product_id' => $item->product_id,
                            'variant_id' => $item->variant_id,
                            'quantity_reserved' => $reservation->quantity_reserved,
                            'reserved_until' => $reservation->reserved_until,
                            'reservation_token' => $reservation->reservation_token,
                        ];
                    }
                } catch (InsufficientStockException $e) {
                    $results['conflicts'][] = [
                        'cart_item_id' => $item->id,
                        'product_id' => $item->product_id,
                        'variant_id' => $item->variant_id,
                        'requested_quantity' => $item->quantity,
                        'available_quantity' => $e->getAvailableQuantity(),
                        'error' => $e->getMessage(),
                    ];
                    $results['success'] = false;
                } catch (\Exception $e) {
                    $results['errors'][] = [
                        'cart_item_id' => $item->id,
                        'product_id' => $item->product_id,
                        'error' => $e->getMessage(),
                    ];
                    $results['success'] = false;
                }
            }
        });

        return $results;
    }

    /**
     * Reserve inventory for a specific cart item.
     */
    public function reserveItemInventory(CartItem $item, int $reservationMinutes = 15): ?CartReservation
    {
        // Check if item already has an active reservation
        $existingReservation = $item->reservations()
            ->active()
            ->first();

        if ($existingReservation) {
            // Extend existing reservation
            $existingReservation->extend($reservationMinutes);
            return $existingReservation;
        }

        // Check available stock
        $availableStock = $this->getAvailableStock($item->product_id, $item->variant_id);
        
        if ($availableStock < $item->quantity) {
            throw new InsufficientStockException(
                "Insufficient stock for product {$item->product_id}. Requested: {$item->quantity}, Available: {$availableStock}",
                $availableStock
            );
        }

        // Get inventory record
        $inventory = $this->getInventoryRecord($item->product_id, $item->variant_id);
        
        if (!$inventory) {
            throw new \Exception("No inventory record found for product {$item->product_id}");
        }

        // Create reservation
        $reservation = CartReservation::create([
            'cart_item_id' => $item->id,
            'product_id' => $item->product_id,
            'variant_id' => $item->variant_id,
            'inventory_id' => $inventory->id,
            'quantity_reserved' => $item->quantity,
            'reserved_until' => now()->addMinutes($reservationMinutes),
            'status' => 'active',
        ]);

        // Update inventory reserved count
        $inventory->increment('reserved', $item->quantity);

        Log::info('Inventory reserved for cart item', [
            'cart_item_id' => $item->id,
            'product_id' => $item->product_id,
            'variant_id' => $item->variant_id,
            'quantity' => $item->quantity,
            'reservation_id' => $reservation->id,
            'reserved_until' => $reservation->reserved_until,
        ]);

        return $reservation;
    }

    /**
     * Release all reservations for a cart.
     */
    public function releaseCartReservations(ShoppingCart $cart): array
    {
        $results = [
            'released_count' => 0,
            'errors' => [],
        ];

        DB::transaction(function () use ($cart, &$results) {
            $reservations = CartReservation::whereHas('cartItem', function ($query) use ($cart) {
                $query->where('cart_id', $cart->id);
            })->active()->get();

            foreach ($reservations as $reservation) {
                try {
                    $this->releaseReservation($reservation);
                    $results['released_count']++;
                } catch (\Exception $e) {
                    $results['errors'][] = [
                        'reservation_id' => $reservation->id,
                        'error' => $e->getMessage(),
                    ];
                }
            }
        });

        return $results;
    }

    /**
     * Release a specific reservation.
     */
    public function releaseReservation(CartReservation $reservation): bool
    {
        if ($reservation->status !== 'active') {
            return false;
        }

        DB::transaction(function () use ($reservation) {
            // Update inventory reserved count
            if ($reservation->inventory) {
                $reservation->inventory->decrement('reserved', $reservation->quantity_reserved);
            }

            // Mark reservation as released
            $reservation->update(['status' => 'released']);
        });

        Log::info('Inventory reservation released', [
            'reservation_id' => $reservation->id,
            'cart_item_id' => $reservation->cart_item_id,
            'product_id' => $reservation->product_id,
            'quantity' => $reservation->quantity_reserved,
        ]);

        return true;
    }

    /**
     * Convert reservations to actual inventory deduction (on order completion).
     */
    public function convertReservationsToSales(ShoppingCart $cart): array
    {
        $results = [
            'converted_count' => 0,
            'errors' => [],
        ];

        DB::transaction(function () use ($cart, &$results) {
            $reservations = CartReservation::whereHas('cartItem', function ($query) use ($cart) {
                $query->where('cart_id', $cart->id);
            })->active()->get();

            foreach ($reservations as $reservation) {
                try {
                    $this->convertReservationToSale($reservation);
                    $results['converted_count']++;
                } catch (\Exception $e) {
                    $results['errors'][] = [
                        'reservation_id' => $reservation->id,
                        'error' => $e->getMessage(),
                    ];
                }
            }
        });

        return $results;
    }

    /**
     * Convert a specific reservation to sale.
     */
    public function convertReservationToSale(CartReservation $reservation): bool
    {
        if ($reservation->status !== 'active') {
            return false;
        }

        DB::transaction(function () use ($reservation) {
            $inventory = $reservation->inventory;
            
            if ($inventory) {
                // Reduce actual stock and reserved count
                $inventory->decrement('stock', $reservation->quantity_reserved);
                $inventory->decrement('reserved', $reservation->quantity_reserved);
                
                // Update stock status if needed
                if ($inventory->stock <= 0) {
                    $inventory->update(['stock_status' => 'out_of_stock']);
                } elseif ($inventory->stock <= $inventory->low_stock_threshold) {
                    $inventory->update(['stock_status' => 'low_stock']);
                }
            }

            // Mark reservation as converted
            $reservation->update(['status' => 'converted']);
        });

        Log::info('Inventory reservation converted to sale', [
            'reservation_id' => $reservation->id,
            'cart_item_id' => $reservation->cart_item_id,
            'product_id' => $reservation->product_id,
            'quantity' => $reservation->quantity_reserved,
        ]);

        return true;
    }

    /**
     * Extend reservation time for a cart.
     */
    public function extendCartReservations(ShoppingCart $cart, int $additionalMinutes = 15): array
    {
        $results = [
            'extended_count' => 0,
            'errors' => [],
        ];

        $reservations = CartReservation::whereHas('cartItem', function ($query) use ($cart) {
            $query->where('cart_id', $cart->id);
        })->active()->get();

        foreach ($reservations as $reservation) {
            try {
                if ($reservation->canBeExtended()) {
                    $reservation->extend($additionalMinutes);
                    $results['extended_count']++;
                }
            } catch (\Exception $e) {
                $results['errors'][] = [
                    'reservation_id' => $reservation->id,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    /**
     * Get available stock for a product/variant.
     */
    public function getAvailableStock(int $productId, ?int $variantId = null): int
    {
        $inventory = $this->getInventoryRecord($productId, $variantId);
        
        if (!$inventory) {
            return 0;
        }

        // Available stock = total stock - reserved stock
        return max(0, $inventory->stock - $inventory->reserved);
    }

    /**
     * Get inventory record for product/variant.
     */
    protected function getInventoryRecord(int $productId, ?int $variantId = null): ?Inventory
    {
        $query = Inventory::where('product_id', $productId)
                          ->where('is_active', true);

        if ($variantId) {
            $query->where('variant_id', $variantId);
        }

        return $query->first();
    }

    /**
     * Process expired reservations (called by scheduled job).
     */
    public function processExpiredReservations(): array
    {
        $processed = 0;
        $errors = 0;

        $expiredReservations = CartReservation::expired()->get();

        foreach ($expiredReservations as $reservation) {
            try {
                $this->releaseReservation($reservation);
                $reservation->update(['status' => 'expired']);
                $processed++;
            } catch (\Exception $e) {
                $errors++;
                Log::error('Failed to process expired reservation', [
                    'reservation_id' => $reservation->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return [
            'processed' => $processed,
            'errors' => $errors,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Get reservation statistics.
     */
    public function getReservationStatistics(string $period = '24h'): array
    {
        $startDate = match ($period) {
            '1h' => now()->subHour(),
            '24h' => now()->subDay(),
            '7d' => now()->subDays(7),
            '30d' => now()->subDays(30),
            default => now()->subDay(),
        };

        $totalReservations = CartReservation::where('created_at', '>=', $startDate)->count();
        $activeReservations = CartReservation::active()->count();
        $expiredReservations = CartReservation::where('created_at', '>=', $startDate)
                                             ->where('status', 'expired')
                                             ->count();
        $convertedReservations = CartReservation::where('created_at', '>=', $startDate)
                                               ->where('status', 'converted')
                                               ->count();

        return [
            'period' => $period,
            'start_date' => $startDate->toISOString(),
            'end_date' => now()->toISOString(),
            'totals' => [
                'total_reservations' => $totalReservations,
                'active_reservations' => $activeReservations,
                'expired_reservations' => $expiredReservations,
                'converted_reservations' => $convertedReservations,
            ],
            'rates' => [
                'conversion_rate' => $totalReservations > 0 ? 
                    round(($convertedReservations / $totalReservations) * 100, 2) : 0,
                'expiration_rate' => $totalReservations > 0 ? 
                    round(($expiredReservations / $totalReservations) * 100, 2) : 0,
            ],
        ];
    }
}

class InsufficientStockException extends \Exception
{
    protected int $availableQuantity;

    public function __construct(string $message, int $availableQuantity)
    {
        parent::__construct($message);
        $this->availableQuantity = $availableQuantity;
    }

    public function getAvailableQuantity(): int
    {
        return $this->availableQuantity;
    }
}
