<?php

namespace App\Services;

use App\Models\UserCard;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class UserCardService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = UserCard::query();
        $query->where('user_id', auth()->id());

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);
        // Filtering
        $filters = ['is_active' => '=','is_default' => '=']; // Define the filters you want to apply
        $this->applyFilters($query, $request, $filters);


        // Searching
        $searchKeys = ['card_name','last_four','card_number']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareUserCardData($request);

        return UserCard::create($data);
    }

    private function prepareUserCardData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new UserCard())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'userCard')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'userCard');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'userCard');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): UserCard
    {
        return UserCard::findOrFail($id);
    }

    public function update( $request, int $id)
    {
        $userCard = UserCard::findOrFail($id);
        $updateData = $this->prepareUserCardData($request, false);

         $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $userCard->update($updateData);

        return $userCard;
    }

    public function destroy(int $id): bool
    {
        $userCard = UserCard::findOrFail($id);
        return $userCard->delete();
    }
}
