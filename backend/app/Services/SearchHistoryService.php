<?php

namespace App\Services;

use App\Models\SearchHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SearchHistoryService
{
    public function store(Request $request): void
    {
       
        SearchHistory::create([
            'user_id' => Auth::id(),
            'keyword' => $request->input('keyword'),
            'ip_address' => $request->ip(),
        ]);
    }

    public function getUserHistory(Request $request, int $limit = 10)
    {
        if (Auth::check()) {
            // Return user's personal search history
            return SearchHistory::where('user_id', Auth::id())
                ->select('keyword', 'created_at')
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get()
                ->unique('keyword')
                ->values();
        } else {
            // Return popular searches for non-authenticated users
            return SearchHistory::selectRaw('keyword, COUNT(*) as count')
                ->groupBy('keyword')
                ->orderBy('count', 'desc')
                ->limit($limit)
                ->get();
        }
    }

    public function clearUserHistory(Request $request): void
    {
        $query = SearchHistory::query();
        
        if (Auth::check()) {
            $query->where('user_id', Auth::id());
        } else {
            $query->where('ip_address', $request->ip());
        }
        
        $query->delete();
    }
}