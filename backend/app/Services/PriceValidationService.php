<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\CartItem;
use App\Models\ShoppingCart;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class PriceValidationService
{
    private const PRICE_TOLERANCE = 0.01; // 1 cent tolerance for floating point precision
    private const CACHE_TTL = 300; // 5 minutes cache for prices

    /**
     * Validate cart item price against current product price.
     */
    public function validateCartItemPrice(CartItem $item): array
    {
        $currentPrice = $this->getCurrentPrice($item->product_id, $item->variant_id);
        
        if ($currentPrice === null) {
            return [
                'valid' => false,
                'error' => 'Product or variant not found',
                'current_price' => null,
                'cart_price' => $item->unit_price,
            ];
        }

        $priceDifference = abs($currentPrice - $item->unit_price);
        $isValid = $priceDifference <= self::PRICE_TOLERANCE;

        if (!$isValid) {
            Log::warning('Price mismatch detected', [
                'cart_item_id' => $item->id,
                'product_id' => $item->product_id,
                'variant_id' => $item->variant_id,
                'cart_price' => $item->unit_price,
                'current_price' => $currentPrice,
                'difference' => $priceDifference,
            ]);
        }

        return [
            'valid' => $isValid,
            'current_price' => $currentPrice,
            'cart_price' => $item->unit_price,
            'difference' => $priceDifference,
            'percentage_change' => $item->unit_price > 0 ? 
                (($currentPrice - $item->unit_price) / $item->unit_price) * 100 : 0,
        ];
    }

    /**
     * Validate entire cart for price integrity.
     */
    public function validateCartPrices(ShoppingCart $cart): array
    {
        $results = [
            'valid' => true,
            'items' => [],
            'invalid_count' => 0,
            'total_price_difference' => 0,
            'requires_update' => false,
        ];

        foreach ($cart->items as $item) {
            $validation = $this->validateCartItemPrice($item);
            $validation['item_id'] = $item->id;
            
            if (!$validation['valid']) {
                $results['valid'] = false;
                $results['invalid_count']++;
                $results['requires_update'] = true;
            }

            if (isset($validation['difference'])) {
                $results['total_price_difference'] += $validation['difference'] * $item->quantity;
            }

            $results['items'][] = $validation;
        }

        return $results;
    }

    /**
     * Get current price for product/variant with caching.
     */
    public function getCurrentPrice(int $productId, ?int $variantId = null): ?float
    {
        $cacheKey = "price:{$productId}:" . ($variantId ?? 'null');
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($productId, $variantId) {
            if ($variantId) {
                $variant = ProductVariant::where('id', $variantId)
                    ->where('product_id', $productId)
                    ->where('is_active', true)
                    ->first();
                
                if (!$variant) {
                    return null;
                }

                // Return offer price if available, otherwise regular price
                return $variant->offer_price ?? $variant->regular_price;
            }

            $product = Product::where('id', $productId)
                ->where('status', 'submitted')
                ->where('is_active', true)
                ->where('is_approved', true)
                ->first();

            if (!$product) {
                return null;
            }

            // Return offer price if available, otherwise regular price
            return $product->offer_price ?? $product->regular_price;
        });
    }

    /**
     * Update cart item with current price.
     */
    public function updateCartItemPrice(CartItem $item): bool
    {
        $currentPrice = $this->getCurrentPrice($item->product_id, $item->variant_id);
        
        if ($currentPrice === null) {
            return false;
        }

        $oldPrice = $item->unit_price;
        $oldTotal = $item->total_price;

        $item->update([
            'unit_price' => $currentPrice,
            'total_price' => $currentPrice * $item->quantity,
        ]);

        Log::info('Cart item price updated', [
            'cart_item_id' => $item->id,
            'product_id' => $item->product_id,
            'variant_id' => $item->variant_id,
            'old_price' => $oldPrice,
            'new_price' => $currentPrice,
            'old_total' => $oldTotal,
            'new_total' => $item->total_price,
        ]);

        return true;
    }

    /**
     * Auto-correct cart prices and return summary.
     */
    public function autoCorrectCartPrices(ShoppingCart $cart): array
    {
        $results = [
            'updated_items' => 0,
            'failed_items' => 0,
            'total_adjustment' => 0,
            'items' => [],
        ];

        foreach ($cart->items as $item) {
            $validation = $this->validateCartItemPrice($item);
            
            if (!$validation['valid']) {
                $oldTotal = $item->total_price;
                
                if ($this->updateCartItemPrice($item)) {
                    $results['updated_items']++;
                    $adjustment = $item->fresh()->total_price - $oldTotal;
                    $results['total_adjustment'] += $adjustment;
                    
                    $results['items'][] = [
                        'item_id' => $item->id,
                        'product_id' => $item->product_id,
                        'old_price' => $validation['cart_price'],
                        'new_price' => $validation['current_price'],
                        'adjustment' => $adjustment,
                        'status' => 'updated',
                    ];
                } else {
                    $results['failed_items']++;
                    $results['items'][] = [
                        'item_id' => $item->id,
                        'product_id' => $item->product_id,
                        'status' => 'failed',
                        'error' => 'Product not available',
                    ];
                }
            }
        }

        // Recalculate cart totals if any items were updated
        if ($results['updated_items'] > 0) {
            app(\App\Services\CartCalculationService::class)->recalculateCart($cart);
        }

        return $results;
    }

    /**
     * Validate price before adding item to cart.
     */
    public function validatePriceBeforeAdd(int $productId, ?int $variantId, float $submittedPrice): array
    {
        $currentPrice = $this->getCurrentPrice($productId, $variantId);
        
        if ($currentPrice === null) {
            return [
                'valid' => false,
                'error' => 'Product not available',
                'current_price' => null,
            ];
        }

        $priceDifference = abs($currentPrice - $submittedPrice);
        $isValid = $priceDifference <= self::PRICE_TOLERANCE;

        if (!$isValid) {
            Log::warning('Invalid price submitted for cart addition', [
                'product_id' => $productId,
                'variant_id' => $variantId,
                'submitted_price' => $submittedPrice,
                'current_price' => $currentPrice,
                'difference' => $priceDifference,
            ]);
        }

        return [
            'valid' => $isValid,
            'current_price' => $currentPrice,
            'submitted_price' => $submittedPrice,
            'should_use_current' => !$isValid,
        ];
    }

    /**
     * Get price history for monitoring.
     */
    public function getPriceChangeHistory(int $productId, ?int $variantId = null, int $days = 7): array
    {
        // This would typically query a price history table
        // For now, return current price as baseline
        $currentPrice = $this->getCurrentPrice($productId, $variantId);
        
        return [
            'product_id' => $productId,
            'variant_id' => $variantId,
            'current_price' => $currentPrice,
            'history' => [
                [
                    'price' => $currentPrice,
                    'date' => now()->toDateString(),
                    'change_percentage' => 0,
                ]
            ],
        ];
    }

    /**
     * Invalidate price cache when product prices change.
     */
    public function invalidatePriceCache(int $productId, ?int $variantId = null): void
    {
        $cacheKey = "price:{$productId}:" . ($variantId ?? 'null');
        Cache::forget($cacheKey);
        
        // Also invalidate related cart calculations
        $relatedCarts = CartItem::where('product_id', $productId)
            ->when($variantId, function ($query) use ($variantId) {
                return $query->where('variant_id', $variantId);
            })
            ->pluck('cart_id')
            ->unique();

        foreach ($relatedCarts as $cartId) {
            Cache::forget("cart_calculations:{$cartId}");
        }
    }

    /**
     * Bulk validate prices for multiple products.
     */
    public function bulkValidatePrices(array $items): array
    {
        $results = [];
        
        foreach ($items as $item) {
            $validation = $this->validatePriceBeforeAdd(
                $item['product_id'],
                $item['variant_id'] ?? null,
                $item['price']
            );
            
            $results[] = array_merge($validation, [
                'product_id' => $item['product_id'],
                'variant_id' => $item['variant_id'] ?? null,
            ]);
        }

        return $results;
    }
}
