<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class PixabayService
{
    private ?string $apiKey;
    private string $apiUrl;

    public function __construct()
    {
        $this->apiKey = config('services.pixabay.api_key', env('PIXABAY_API_KEY'));
        $this->apiUrl = config('services.pixabay.api_url', env('PIXABAY_API_URL', 'https://pixabay.com/api/'));
    }

    /**
     * Search for images on Pixabay based on query terms
     *
     * @param string $query Search query (blog title, category, or keywords)
     * @param array $options Additional search options
     * @return array|null Array of image data or null on failure
     */
    public function searchImages(string $query, array $options = []): ?array
    {
        if (empty($this->apiKey)) {
            Log::warning('Pixabay API key not configured');
            return null;
        }

        try {
            $defaultOptions = [
                'key' => $this->apiKey,
                'q' => $this->sanitizeQuery($query),
                'image_type' => 'photo',
                'orientation' => 'horizontal',
                'category' => 'business',
                'min_width' => 1280,
                'min_height' => 720,
                'safesearch' => 'true',
                'per_page' => 20,
                'page' => 1,
            ];

            $params = array_merge($defaultOptions, $options);

            Log::info('Searching Pixabay for images', ['query' => $query, 'params' => $params]);

            $response = Http::timeout(30)->get($this->apiUrl, $params);

            if ($response->successful()) {
                $data = $response->json();
                
                if (isset($data['hits']) && count($data['hits']) > 0) {
                    Log::info('Pixabay search successful', [
                        'query' => $query,
                        'total_hits' => $data['totalHits'],
                        'returned_hits' => count($data['hits'])
                    ]);
                    return $data['hits'];
                }

                Log::warning('No images found on Pixabay', ['query' => $query]);
                return [];
            }

            Log::error('Pixabay API request failed', [
                'status' => $response->status(),
                'body' => $response->body(),
                'query' => $query
            ]);

            return null;
        } catch (Exception $e) {
            Log::error('Pixabay API error', [
                'message' => $e->getMessage(),
                'query' => $query,
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Get a random suitable image from search results with retry logic
     *
     * @param string $query Search query
     * @param array $options Additional search options
     * @param int $maxRetries Maximum number of retries
     * @return array|null Single image data or null on failure
     */
    public function getRandomImage(string $query, array $options = [], int $maxRetries = 3): ?array
    {
        $attempt = 0;

        while ($attempt < $maxRetries) {
            $images = $this->searchImages($query, $options);

            if (!empty($images)) {
                // Filter images by quality and size
                $suitableImages = array_filter($images, function ($image) {
                    return $image['webformatWidth'] >= 1280
                        && $image['webformatHeight'] >= 720
                        && !empty($image['webformatURL'])
                        && $this->isImageUrlAccessible($image['webformatURL']);
                });

                if (!empty($suitableImages)) {
                    return $suitableImages[array_rand($suitableImages)];
                }

                // Fallback to any available image if no suitable ones found
                if (!empty($images)) {
                    $fallbackImages = array_filter($images, function ($image) {
                        return !empty($image['webformatURL']);
                    });

                    if (!empty($fallbackImages)) {
                        return $fallbackImages[array_rand($fallbackImages)];
                    }
                }
            }

            $attempt++;
            if ($attempt < $maxRetries) {
                Log::info("Pixabay search attempt {$attempt} failed, retrying...", ['query' => $query]);
                sleep(1); // Wait 1 second before retry
            }
        }

        Log::warning("Failed to get image after {$maxRetries} attempts", ['query' => $query]);
        return null;
    }

    /**
     * Check if image URL is accessible
     *
     * @param string $url Image URL
     * @return bool True if accessible, false otherwise
     */
    private function isImageUrlAccessible(string $url): bool
    {
        try {
            $response = Http::timeout(10)->head($url);
            return $response->successful();
        } catch (\Exception $e) {
            Log::debug('Image URL not accessible', ['url' => $url, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Generate search queries based on blog content
     *
     * @param string $title Blog post title
     * @param string $category Blog category
     * @param string $keywords Blog keywords
     * @return array Array of search queries to try
     */
    public function generateSearchQueries(string $title, string $category, string $keywords = ''): array
    {
        $queries = [];

        // Extract meaningful words from title
        $titleWords = $this->extractKeywords($title);
        if (!empty($titleWords)) {
            $queries[] = implode(' ', array_slice($titleWords, 0, 3));
        }

        // Use category as search term
        if (!empty($category)) {
            $queries[] = $this->sanitizeQuery($category);
        }

        // Use keywords if available
        if (!empty($keywords)) {
            $keywordArray = explode(',', $keywords);
            $queries[] = trim($keywordArray[0]);
        }

        // Fallback generic terms for health and wellness blogs
        $fallbackTerms = [
            'health',
            'wellness',
            'nutrition',
            'fitness',
            'healthy lifestyle',
            'natural health',
            'vitamins',
            'supplements',
            'medical',
            'healthcare',
            'exercise',
            'meditation'
        ];

        $queries = array_merge($queries, $fallbackTerms);

        // Remove duplicates and empty values
        return array_unique(array_filter($queries));
    }

    /**
     * Sanitize search query for Pixabay API
     *
     * @param string $query Raw query string
     * @return string Sanitized query
     */
    private function sanitizeQuery(string $query): string
    {
        // Remove special characters and normalize
        $query = preg_replace('/[^\w\s-]/', '', $query);
        $query = preg_replace('/\s+/', ' ', $query);
        $query = trim($query);

        // Limit length
        if (strlen($query) > 100) {
            $query = substr($query, 0, 100);
        }

        return $query;
    }

    /**
     * Extract meaningful keywords from text
     *
     * @param string $text Input text
     * @return array Array of keywords
     */
    private function extractKeywords(string $text): array
    {
        // Common stop words to exclude
        $stopWords = [
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'how', 'what', 'when', 'where', 'why', 'who', 'which', 'that', 'this', 'these', 'those',
            'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
            'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must', 'shall'
        ];

        // Extract words
        $words = preg_split('/\s+/', strtolower($text));
        $words = array_filter($words, function ($word) use ($stopWords) {
            return strlen($word) > 2 && !in_array($word, $stopWords) && preg_match('/^[a-z]+$/', $word);
        });

        return array_values($words);
    }

    /**
     * Check if Pixabay service is properly configured
     *
     * @return bool True if configured, false otherwise
     */
    public function isConfigured(): bool
    {
        return !empty($this->apiKey) && !empty($this->apiUrl);
    }
}
