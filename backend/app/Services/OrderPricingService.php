<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Customer;

class OrderPricingService
{
    /**
     * Pricing hierarchy priority (higher number = higher priority)
     */
    protected array $pricingHierarchy = [
        'base' => 1,
        'promotional' => 2,
        'member' => 3,
        'vip' => 4,
        'wholesale' => 5,
    ];

    /**
     * Get product pricing with all available options
     */
    public function getProductPricing(Product $product, ?int $variantId = null): array
    {
        $variant = $variantId ? ProductVariant::find($variantId) : null;

        // Base pricing from product or variant
        $basePrice = $variant?->price ?? $product->regular_price;
        $promotionalPrice = $variant?->offer_price ?? $product->offer_price;

        // Member-specific pricing
        $memberPrice = $product->member_price;
        $wholesalePrice = $product->wholesale_price;
        $vipPrice = $product->vip_price;

        return [
            'base_price' => $basePrice,
            'promotional_price' => $promotionalPrice,
            'member_price' => $memberPrice,
            'wholesale_price' => $wholesalePrice,
            'vip_price' => $vipPrice,
            'variant_id' => $variantId,
            'product_id' => $product->id,
        ];
    }

    /**
     * Resolve pricing conflicts using hierarchy
     */
    public function resolvePricingConflicts(array $basePricing, ?array $memberPricing = null): array
    {
        $availablePrices = [];

        // Add base pricing
        if ($basePricing['base_price']) {
            $availablePrices['base'] = [
                'price' => $basePricing['base_price'],
                'type' => 'base',
                'priority' => $this->pricingHierarchy['base'],
            ];
        }

        // Add promotional pricing
        if ($basePricing['promotional_price']) {
            $availablePrices['promotional'] = [
                'price' => $basePricing['promotional_price'],
                'type' => 'promotional',
                'priority' => $this->pricingHierarchy['promotional'],
            ];
        }

        // Add member-specific pricing
        if ($basePricing['member_price']) {
            $availablePrices['member'] = [
                'price' => $basePricing['member_price'],
                'type' => 'member',
                'priority' => $this->pricingHierarchy['member'],
            ];
        }

        if ($basePricing['vip_price']) {
            $availablePrices['vip'] = [
                'price' => $basePricing['vip_price'],
                'type' => 'vip',
                'priority' => $this->pricingHierarchy['vip'],
            ];
        }

        if ($basePricing['wholesale_price']) {
            $availablePrices['wholesale'] = [
                'price' => $basePricing['wholesale_price'],
                'type' => 'wholesale',
                'priority' => $this->pricingHierarchy['wholesale'],
            ];
        }

        // Add member pricing service results
        if ($memberPricing) {
            $availablePrices['calculated_member'] = [
                'price' => $memberPricing['final_price'],
                'type' => $memberPricing['applied_type'],
                'priority' => $this->pricingHierarchy[$memberPricing['applied_type']] ?? 3,
            ];
        }

        // Apply pricing rules and find the best price
        $bestPrice = $this->findBestPrice($availablePrices);

        return [
            'base_price' => $basePricing['base_price'],
            'promotional_price' => $basePricing['promotional_price'],
            'member_price' => $basePricing['member_price'],
            'wholesale_price' => $basePricing['wholesale_price'],
            'vip_price' => $basePricing['vip_price'],
            'final_price' => $bestPrice['price'],
            'applied_type' => $bestPrice['type'],
            'available_prices' => $availablePrices,
            'savings_amount' => $basePricing['base_price'] - $bestPrice['price'],
        ];
    }

    /**
     * Find the best price for customer
     */
    protected function findBestPrice(array $availablePrices): array
    {
        if (empty($availablePrices)) {
            throw new \Exception('No pricing available for product');
        }

        // Strategy: Always give customer the lowest price
        $lowestPrice = null;
        $selectedPrice = null;

        foreach ($availablePrices as $priceData) {
            if ($lowestPrice === null || $priceData['price'] < $lowestPrice) {
                $lowestPrice = $priceData['price'];
                $selectedPrice = $priceData;
            }
        }

        return $selectedPrice;
    }

    /**
     * Calculate quantity-based discounts
     */
    public function calculateQuantityDiscount(Product $product, int $quantity): array
    {
        // Define quantity discount tiers
        $discountTiers = [
            ['min_quantity' => 10, 'discount_percentage' => 5],
            ['min_quantity' => 25, 'discount_percentage' => 10],
            ['min_quantity' => 50, 'discount_percentage' => 15],
            ['min_quantity' => 100, 'discount_percentage' => 20],
        ];

        $applicableDiscount = null;

        // Find the highest applicable discount
        foreach ($discountTiers as $tier) {
            if ($quantity >= $tier['min_quantity']) {
                $applicableDiscount = $tier;
            }
        }

        if (!$applicableDiscount) {
            return [
                'applicable' => false,
                'discount_percentage' => 0,
                'discount_amount' => 0,
            ];
        }

        return [
            'applicable' => true,
            'discount_percentage' => $applicableDiscount['discount_percentage'],
            'discount_amount' => ($product->regular_price * $applicableDiscount['discount_percentage']) / 100,
            'min_quantity' => $applicableDiscount['min_quantity'],
        ];
    }

    /**
     * Calculate bundle pricing
     */
    public function calculateBundleDiscount(array $products): array
    {
        // Basic bundle discount logic
        $totalProducts = count($products);
        $bundleDiscountPercentage = 0;

        if ($totalProducts >= 3) {
            $bundleDiscountPercentage = 5; // 5% discount for 3+ products
        }

        if ($totalProducts >= 5) {
            $bundleDiscountPercentage = 10; // 10% discount for 5+ products
        }

        $subtotal = array_sum(array_column($products, 'price'));
        $discountAmount = ($subtotal * $bundleDiscountPercentage) / 100;

        return [
            'applicable' => $bundleDiscountPercentage > 0,
            'discount_percentage' => $bundleDiscountPercentage,
            'discount_amount' => $discountAmount,
            'subtotal' => $subtotal,
            'final_total' => $subtotal - $discountAmount,
        ];
    }

    /**
     * Get pricing breakdown for display
     */
    public function getPricingBreakdown(Product $product, int $quantity = 1, ?Customer $customer = null, ?int $variantId = null): array
    {
        $basePricing = $this->getProductPricing($product, $variantId);
        
        $memberPricing = null;
        if ($customer && $customer->canAccessMemberPricing()) {
            $memberPricingService = app(MemberPricingService::class);
            $memberPricing = $memberPricingService->calculateMemberPrice($product, $customer, $variantId);
        }

        $finalPricing = $this->resolvePricingConflicts($basePricing, $memberPricing);
        $quantityDiscount = $this->calculateQuantityDiscount($product, $quantity);

        $unitPrice = $finalPricing['final_price'];
        $subtotal = $unitPrice * $quantity;
        
        // Apply quantity discount if applicable
        $quantityDiscountAmount = 0;
        if ($quantityDiscount['applicable']) {
            $quantityDiscountAmount = $quantityDiscount['discount_amount'] * $quantity;
        }

        $finalTotal = $subtotal - $quantityDiscountAmount;

        return [
            'product_id' => $product->id,
            'variant_id' => $variantId,
            'quantity' => $quantity,
            'base_price' => $basePricing['base_price'],
            'promotional_price' => $basePricing['promotional_price'],
            'member_price' => $finalPricing['member_price'],
            'final_unit_price' => $unitPrice,
            'applied_pricing_type' => $finalPricing['applied_type'],
            'subtotal' => $subtotal,
            'quantity_discount' => $quantityDiscount,
            'quantity_discount_amount' => $quantityDiscountAmount,
            'final_total' => $finalTotal,
            'total_savings' => ($basePricing['base_price'] * $quantity) - $finalTotal,
            'customer_tier' => $customer?->pricingTier?->code,
            'pricing_details' => $finalPricing,
        ];
    }

    /**
     * Validate pricing configuration
     */
    public function validatePricingConfiguration(Product $product): array
    {
        $issues = [];

        // Check if base price exists
        if (!$product->regular_price || $product->regular_price <= 0) {
            $issues[] = 'Base price is required and must be greater than 0';
        }

        // Check promotional price logic
        if ($product->offer_price && $product->offer_price >= $product->regular_price) {
            $issues[] = 'Promotional price must be less than base price';
        }

        // Check member pricing logic
        if ($product->member_price && $product->member_price >= $product->regular_price) {
            $issues[] = 'Member price should be less than base price';
        }

        if ($product->wholesale_price && $product->wholesale_price >= $product->regular_price) {
            $issues[] = 'Wholesale price should be less than base price';
        }

        if ($product->vip_price && $product->vip_price >= $product->regular_price) {
            $issues[] = 'VIP price should be less than base price';
        }

        return [
            'valid' => empty($issues),
            'issues' => $issues,
        ];
    }

    /**
     * Get pricing recommendations
     */
    public function getPricingRecommendations(Product $product): array
    {
        $recommendations = [];

        // Recommend promotional pricing
        if (!$product->offer_price) {
            $suggestedPromoPrice = $product->regular_price * 0.9; // 10% off
            $recommendations[] = [
                'type' => 'promotional',
                'message' => 'Consider adding promotional pricing',
                'suggested_price' => $suggestedPromoPrice,
            ];
        }

        // Recommend member pricing
        if (!$product->member_price) {
            $suggestedMemberPrice = $product->regular_price * 0.85; // 15% off
            $recommendations[] = [
                'type' => 'member',
                'message' => 'Consider adding member pricing',
                'suggested_price' => $suggestedMemberPrice,
            ];
        }

        // Recommend wholesale pricing
        if (!$product->wholesale_price) {
            $suggestedWholesalePrice = $product->regular_price * 0.75; // 25% off
            $recommendations[] = [
                'type' => 'wholesale',
                'message' => 'Consider adding wholesale pricing',
                'suggested_price' => $suggestedWholesalePrice,
            ];
        }

        return $recommendations;
    }
}
