<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Coupon;

class OrderCalculationService
{
    protected OrderPricingService $pricingService;
    protected MemberPricingService $memberPricingService;

    public function __construct(
        OrderPricingService $pricingService,
        MemberPricingService $memberPricingService
    ) {
        $this->pricingService = $pricingService;
        $this->memberPricingService = $memberPricingService;
    }

    /**
     * Calculate order totals
     */
    public function calculateOrderTotals(Order $order): void
    {
        $subtotal = 0;
        $discountTotal = 0;
        $taxTotal = 0;

        // Calculate item totals
        foreach ($order->items as $item) {
            $subtotal += $item->total;
            $discountTotal += $item->discount;
            $taxTotal += $item->tax;
        }

        // Apply order-level discounts (coupons)
        $couponDiscount = $this->calculateCouponDiscounts($order, $subtotal);
        $discountTotal += $couponDiscount;

        // Calculate final total
        $total = $subtotal - $discountTotal + $taxTotal + $order->shipping_fee;

        // Update order
        $order->update([
            'subtotal' => $subtotal,
            'discount_total' => $discountTotal,
            'tax_total' => $taxTotal,
            'total' => max(0, $total), // Ensure total is not negative
        ]);
    }

    /**
     * Calculate item pricing with hierarchy
     */
    public function calculateItemPricing(Product $product, int $quantity, ?Customer $customer = null, ?int $variantId = null): array
    {
        // Get base pricing
        $basePricing = $this->pricingService->getProductPricing($product, $variantId);
        
        // Apply member pricing if customer has tier
        $memberPricing = null;
        if ($customer && $customer->canAccessMemberPricing()) {
            $memberPricing = $this->memberPricingService->calculateMemberPrice($product, $customer, $variantId);
        }

        // Determine final price using pricing hierarchy
        $finalPrice = $this->pricingService->resolvePricingConflicts($basePricing, $memberPricing);

        // Calculate totals
        $unitPrice = $finalPrice['final_price'];
        $totalPrice = $unitPrice * $quantity;
        $discountAmount = ($finalPrice['base_price'] - $unitPrice) * $quantity;

        return [
            'base_price' => $finalPrice['base_price'],
            'promotional_price' => $finalPrice['promotional_price'],
            'member_price' => $finalPrice['member_price'],
            'unit_price' => $unitPrice,
            'total_price' => $totalPrice,
            'discount_amount' => $discountAmount,
            'applied_pricing_type' => $finalPrice['applied_type'],
            'pricing_breakdown' => $finalPrice,
        ];
    }

    /**
     * Calculate tax for order item
     */
    public function calculateItemTax(OrderItem $item): float
    {
        // Basic tax calculation - can be enhanced with tax rules
        $taxRate = $this->getTaxRate($item);
        return $item->total * ($taxRate / 100);
    }

    /**
     * Calculate shipping for order
     */
    public function calculateShipping(Order $order): float
    {
        // Basic shipping calculation - can be enhanced with shipping rules
        $shippingRules = $this->getShippingRules($order);
        
        // Free shipping for orders above threshold
        if ($order->subtotal >= $shippingRules['free_shipping_threshold']) {
            return 0;
        }

        // Calculate based on vendor groups
        $vendorGroups = $order->vendor_groups;
        $totalShipping = 0;

        foreach ($vendorGroups as $vendorGroup) {
            $vendorShipping = $this->calculateVendorShipping($vendorGroup, $shippingRules);
            $totalShipping += $vendorShipping;
        }

        return $totalShipping;
    }

    /**
     * Calculate coupon discounts
     */
    public function calculateCouponDiscounts(Order $order, float $subtotal): float
    {
        $totalDiscount = 0;

        foreach ($order->coupons as $orderCoupon) {
            $discount = $this->calculateSingleCouponDiscount($orderCoupon, $subtotal);
            $totalDiscount += $discount;
        }

        return $totalDiscount;
    }

    /**
     * Apply coupon to order
     */
    public function applyCoupon(Order $order, Coupon $coupon): array
    {
        // Validate coupon
        if (!$this->validateCouponForOrder($coupon, $order)) {
            return [
                'success' => false,
                'message' => 'Coupon is not valid for this order',
                'discount_amount' => 0,
            ];
        }

        // Calculate discount
        $discountAmount = $coupon->calculateDiscount($order->subtotal);

        if ($discountAmount <= 0) {
            return [
                'success' => false,
                'message' => 'Coupon does not provide any discount',
                'discount_amount' => 0,
            ];
        }

        // Create order coupon record
        $order->coupons()->create([
            'coupon_id' => $coupon->id,
            'vendor_id' => $coupon->vendor_id,
            'coupon_code' => $coupon->code,
            'coupon_title' => $coupon->title_en,
            'coupon_type' => $coupon->type,
            'coupon_value' => $coupon->value,
            'min_order_value' => $coupon->min_order_value,
            'discount_amount' => $discountAmount,
            'order_subtotal_at_application' => $order->subtotal,
            'applied_at' => now(),
        ]);

        // Recalculate order totals
        $this->calculateOrderTotals($order);

        return [
            'success' => true,
            'message' => 'Coupon applied successfully',
            'discount_amount' => $discountAmount,
        ];
    }

    /**
     * Remove coupon from order
     */
    public function removeCoupon(Order $order, string $couponCode): bool
    {
        $orderCoupon = $order->coupons()->where('coupon_code', $couponCode)->first();

        if (!$orderCoupon) {
            return false;
        }

        $orderCoupon->delete();

        // Recalculate order totals
        $this->calculateOrderTotals($order);

        return true;
    }

    /**
     * Get pricing summary for order
     */
    public function getOrderPricingSummary(Order $order): array
    {
        $itemsBreakdown = [];
        $totalSavings = 0;

        foreach ($order->items as $item) {
            $itemBreakdown = [
                'product_title' => $item->product_title,
                'quantity' => $item->quantity,
                'base_price' => $item->base_price,
                'final_price' => $item->price,
                'total' => $item->total,
                'discount' => $item->discount,
                'tax' => $item->tax,
                'savings' => $item->savings_amount,
                'applied_pricing_type' => $item->getAppliedPricingType(),
            ];
            
            $itemsBreakdown[] = $itemBreakdown;
            $totalSavings += $item->savings_amount;
        }

        $couponsBreakdown = [];
        foreach ($order->coupons as $coupon) {
            $couponsBreakdown[] = [
                'code' => $coupon->coupon_code,
                'title' => $coupon->coupon_title,
                'type' => $coupon->coupon_type,
                'discount_amount' => $coupon->discount_amount,
            ];
            $totalSavings += $coupon->discount_amount;
        }

        return [
            'subtotal' => $order->subtotal,
            'discount_total' => $order->discount_total,
            'tax_total' => $order->tax_total,
            'shipping_fee' => $order->shipping_fee,
            'total' => $order->total,
            'total_savings' => $totalSavings,
            'items_breakdown' => $itemsBreakdown,
            'coupons_breakdown' => $couponsBreakdown,
            'currency' => $order->currency,
        ];
    }

    /**
     * Protected helper methods
     */
    protected function getTaxRate(OrderItem $item): float
    {
        // Default tax rate - can be enhanced with product-specific or location-based rates
        return 5.0; // 5% VAT for UAE
    }

    protected function getShippingRules(Order $order): array
    {
        return [
            'free_shipping_threshold' => 200.00, // AED
            'base_shipping_rate' => 25.00, // AED
            'per_vendor_rate' => 15.00, // AED
            'express_shipping_rate' => 50.00, // AED
        ];
    }

    protected function calculateVendorShipping(array $vendorGroup, array $shippingRules): float
    {
        // Basic vendor shipping calculation
        return $shippingRules['per_vendor_rate'];
    }

    protected function calculateSingleCouponDiscount($orderCoupon, float $subtotal): float
    {
        if ($orderCoupon->coupon_type === 'percentage') {
            return ($subtotal * $orderCoupon->coupon_value) / 100;
        }

        if ($orderCoupon->coupon_type === 'fixed') {
            return min($orderCoupon->coupon_value, $subtotal);
        }

        return 0;
    }

    protected function validateCouponForOrder(Coupon $coupon, Order $order): bool
    {
        // Check if coupon is active and valid
        if (!$coupon->isValidForUse()) {
            return false;
        }

        // Check minimum order value
        if ($coupon->min_order_value && $order->subtotal < $coupon->min_order_value) {
            return false;
        }

        // Check if vendor-specific coupon matches order vendor
        if ($coupon->vendor_id && $order->vendor_id !== $coupon->vendor_id) {
            return false;
        }

        // Check if coupon is already applied
        if ($order->coupons()->where('coupon_code', $coupon->code)->exists()) {
            return false;
        }

        return true;
    }
}
