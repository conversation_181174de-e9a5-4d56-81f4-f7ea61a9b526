<?php

namespace App\Services;

use App\Models\ShoppingCart;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CartTokenService
{
    private const TOKEN_LIFETIME_HOURS = 24;
    private const MAX_ROTATION_COUNT = 5;
    private const ROTATION_COOLDOWN_MINUTES = 15;

    /**
     * Generate a new secure cart token.
     */
    public function generateToken(ShoppingCart $cart): string
    {
        $payload = [
            'cart_id' => $cart->id,
            'uuid' => $cart->uuid,
            'timestamp' => microtime(true),
            'random' => random_bytes(16)
        ];

        $token = hash('sha256', serialize($payload));
        
        // Set expiration time
        $expiresAt = now()->addHours(self::TOKEN_LIFETIME_HOURS);
        
        $cart->update([
            'cart_token' => $token,
            'cart_token_expires_at' => $expiresAt,
            'last_token_rotation' => now(),
        ]);

        // Cache token for quick validation
        Cache::put("cart_token:{$token}", $cart->id, $expiresAt);

        Log::info('Cart token generated', [
            'cart_id' => $cart->id,
            'expires_at' => $expiresAt->toISOString(),
        ]);

        return $token;
    }

    /**
     * Validate cart token with security checks.
     */
    public function validateToken(ShoppingCart $cart, string $token): bool
    {
        // Check if token matches
        if (!hash_equals($cart->cart_token ?? '', $token)) {
            Log::warning('Cart token mismatch', [
                'cart_id' => $cart->id,
                'provided_token' => substr($token, 0, 8) . '...',
            ]);
            return false;
        }

        // Check if token is expired
        if ($cart->cart_token_expires_at && $cart->cart_token_expires_at->isPast()) {
            Log::info('Cart token expired', [
                'cart_id' => $cart->id,
                'expired_at' => $cart->cart_token_expires_at->toISOString(),
            ]);
            return false;
        }

        // Check cache for additional validation
        $cachedCartId = Cache::get("cart_token:{$token}");
        if ($cachedCartId !== $cart->id) {
            Log::warning('Cart token cache mismatch', [
                'cart_id' => $cart->id,
                'cached_cart_id' => $cachedCartId,
            ]);
            return false;
        }

        return true;
    }

    /**
     * Rotate cart token for enhanced security.
     */
    public function rotateToken(ShoppingCart $cart): ?string
    {
        // Check rotation limits
        if ($cart->token_rotation_count >= self::MAX_ROTATION_COUNT) {
            Log::warning('Cart token rotation limit exceeded', [
                'cart_id' => $cart->id,
                'rotation_count' => $cart->token_rotation_count,
            ]);
            return null;
        }

        // Check cooldown period
        if ($cart->last_token_rotation && 
            $cart->last_token_rotation->diffInMinutes(now()) < self::ROTATION_COOLDOWN_MINUTES) {
            Log::info('Cart token rotation in cooldown', [
                'cart_id' => $cart->id,
                'last_rotation' => $cart->last_token_rotation->toISOString(),
            ]);
            return null;
        }

        // Invalidate old token
        if ($cart->cart_token) {
            Cache::forget("cart_token:{$cart->cart_token}");
        }

        // Generate new token
        $newToken = $this->generateToken($cart);
        
        // Update rotation count
        $cart->increment('token_rotation_count');

        Log::info('Cart token rotated', [
            'cart_id' => $cart->id,
            'rotation_count' => $cart->token_rotation_count + 1,
        ]);

        return $newToken;
    }

    /**
     * Invalidate cart token.
     */
    public function invalidateToken(ShoppingCart $cart): void
    {
        if ($cart->cart_token) {
            Cache::forget("cart_token:{$cart->cart_token}");
        }

        $cart->update([
            'cart_token' => null,
            'cart_token_expires_at' => null,
        ]);

        Log::info('Cart token invalidated', ['cart_id' => $cart->id]);
    }

    /**
     * Clean up expired tokens (for scheduled job).
     */
    public function cleanupExpiredTokens(): int
    {
        $expiredCarts = ShoppingCart::whereNotNull('cart_token')
            ->where('cart_token_expires_at', '<', now())
            ->get();

        $count = 0;
        foreach ($expiredCarts as $cart) {
            $this->invalidateToken($cart);
            $count++;
        }

        Log::info('Expired cart tokens cleaned up', ['count' => $count]);

        return $count;
    }

    /**
     * Check if token needs rotation based on usage patterns.
     */
    public function shouldRotateToken(ShoppingCart $cart): bool
    {
        // Rotate if token is more than 12 hours old
        if ($cart->last_token_rotation && 
            $cart->last_token_rotation->diffInHours(now()) > 12) {
            return true;
        }

        // Rotate if cart has been very active (security measure)
        if ($cart->last_activity_at && 
            $cart->last_activity_at->diffInMinutes($cart->last_token_rotation ?? now()) > 120) {
            return true;
        }

        return false;
    }
}
