<?php

namespace App\Services;

use App\Models\PaymentMethod;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class PaymentMethodService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = PaymentMethod::query();

        $query->select(['id', 'name_en', 'name_ar', 'icon', 'status', 'slug', 'created_at', 'updated_at']);

        if ($request->has('status') && $request->status !== null) {
            $query->where('status', $request->status);
        }

        $this->applySorting($query, $request);

        $searchKeys = ['name_en', 'name_ar'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {

        $data = $this->preparePaymentMethodData($request);
    
        return PaymentMethod::create($data);
    }

    private function preparePaymentMethodData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        $fillable = (new PaymentMethod())->getFillable();
        $data = array_intersect_key($validated, array_flip($fillable));

        if ($isNew) {
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): PaymentMethod
    {
        return PaymentMethod::findOrFail($id);
    }

    public function update($request, int $id)
    {
        $paymentMethod = PaymentMethod::findOrFail($id);
        $updateData = $this->preparePaymentMethodData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $paymentMethod->update($updateData);
        return $paymentMethod;
    }

    public function destroy(int $id): bool
    {
        $paymentMethod = PaymentMethod::findOrFail($id);
        return $paymentMethod->delete();
    }

    public function activeList()
    {
        return PaymentMethod::where('status', 'active')
            ->select(['id', 'name_en', 'name_ar', 'icon', 'slug','description_ar', 'description_en'])
            ->orderBy('name_en', 'asc')
            ->get();
    }
}