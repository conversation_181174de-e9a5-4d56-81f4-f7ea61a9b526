<?php

namespace App\Services;

use App\Models\Product;
use App\Models\User;
use App\Traits\HelperTrait;

class UserDashboardService
{
    use HelperTrait;



    public function profile($request)
    {
        $user = User::select(
            'id',
            'uid',
            'name',
            'email',
            'phone',
            'avatar',
            'is_verified',
            'vendor_id',
            'tpl_id',
            'status'
        )->with('customer', 'addresses', 'cards')->findOrFail(auth()->id());

        $limit = $request->input('limit', 10);

        $products = Product::with([
            'category:id,name_en,name_ar,code',
            'productClass:id,name_en,name_ar,code',
            'brand:id,name_en,name_ar',
            'productMedia' => function ($query) {
                $query->where('is_primary', true)->select('id', 'product_id', 'path', 'alt_text');
            }
        ])
            ->where('is_active', true)
            ->where('is_approved', true)

            //TODO: Uncomment when product class is implemented

            // ->whereHas('productClass', function ($query) {
            //     $query->where('is_popular', true);
            // })
            ->select([
                'id',
                'uuid',
                'title_en',
                'title_ar',
                'short_name',
                'regular_price',
                'offer_price',
                'category_id',
                'class_id',
                'brand_id',
                'is_vegan',
                'is_vegetarian',
                'is_halal'
            ])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
        $user->products = $products;


        return $user;
    }

    public function updateProfile($request)
    {
        $user = auth()->user();
        $user->update($request->validated());
        return $user;
    }
}
