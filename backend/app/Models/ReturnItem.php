<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReturnItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'return_id',
        'product_id',
        'reason_id',
        'quantity',
        'unit_price',
        'total_amount',
        'attachments',
        'reason',
    ];

    protected $casts = [
        'attachments' => 'array',
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    public function returnRequest()
    {
        return $this->belongsTo(ReturnRequest::class, 'return_id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function getAttachmentsAttribute($value)
    {
        $attachments = json_decode($value, true) ?? [];
        $s3Url = config('filesystems.disks.s3.url');
        
        return array_map(function ($attachment) use ($s3Url) {
            return $s3Url . '/' . ltrim($attachment, '/');
        }, $attachments);
    }
      public function reason()
    {
        return $this->belongsTo(DropdownOption::class, 'reason_id','id');
    }
    
}