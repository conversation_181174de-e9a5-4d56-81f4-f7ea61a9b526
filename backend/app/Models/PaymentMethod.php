<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class PaymentMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'name_en',
        'name_ar',
        'icon',
        'slug',
        'description_en',
        'description_ar',
        'status',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($paymentMethod) {
            if (empty($paymentMethod->slug)) {
                $paymentMethod->slug = Str::slug($paymentMethod->name_en);
            }
        });
    }

    public function getIconUrlAttribute()
    {
        if ($this->icon) {
            return config('filesystems.disks.s3.url') . '/' . $this->icon;
        }
        return null;
    }
}