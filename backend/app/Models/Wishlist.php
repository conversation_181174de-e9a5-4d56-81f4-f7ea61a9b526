<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Wishlist extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'product_id',
        'product_variant_id',
        'vendor_id',
        'note',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user who owns the wishlist item.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the product associated with the wishlist item.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the product variant associated with the wishlist item (nullable).
     */
    public function productVariant()
    {
        return $this->belongsTo(ProductVariant::class, 'product_variant_id');
    }

    /**
     * Get the vendor associated with the wishlist item (nullable).
     */
    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Scope to filter wishlist items by user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter wishlist items by product.
     */
    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Check if a wishlist item already exists for a user and product combination.
     */
    public static function existsForUser($userId, $productId, $variantId = null)
    {
        return static::where('user_id', $userId)
            ->where('product_id', $productId)
            ->where('product_variant_id', $variantId)
            ->exists();
    }

    /**
     * Get the effective price for the wishlist item based on user membership.
     */
    public function getEffectivePriceAttribute()
    {
        $product = $this->productVariant ?? $this->product;
        
        if (!$product) {
            return 0;
        }

        // Check if user has member pricing enabled
        $user = $this->user;
        $isMember = $user && $user->customer && $user->customer->is_member_pricing_enabled;

        if ($isMember && $product->member_price) {
            return $product->member_price;
        }

        return $product->offer_price ?? $product->regular_price;
    }

    /**
     * Check if the wishlist item is available (product is active and in stock).
     */
    public function getIsAvailableAttribute()
    {
        $product = $this->product;

        if (!$product || !$product->is_active || !$product->is_approved) {
            return false;
        }

        // Check if product status is valid for cart
        if ($product->status !== 'submitted') {
            return false;
        }

        // If there's a variant, check variant availability
        if ($this->productVariant) {
            if (!$this->productVariant->is_active) {
                return false;
            }

            // Check variant inventory
            $variantInventory = $this->productVariant->inventory;
            if ($variantInventory) {
                return $variantInventory->available_stock > 0 || $product->allow_backorder;
            }

            // If no inventory record, check if backorders are allowed
            return $product->allow_backorder;
        }

        // Check main product availability
        $productInventory = $product->inventory;
        if ($productInventory) {
            return $productInventory->available_stock > 0 || $product->allow_backorder;
        }

        // If no inventory record, check if backorders are allowed
        return $product->allow_backorder;
    }
}
