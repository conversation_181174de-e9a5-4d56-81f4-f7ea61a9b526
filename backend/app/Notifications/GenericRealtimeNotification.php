<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class GenericRealtimeNotification extends Notification implements ShouldQueue, ShouldBroadcastNow
{
    use Queueable;

    protected string $title;
    protected string $body;
    protected array $meta;
    protected string $category;
    protected ?string $actionUrl;
    protected string $scope;
    protected int $scopeId;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        string $title,
        string $body,
        string $category = 'system',
        array $meta = [],
        ?string $actionUrl = null,
        string $scope = 'user',
        int $scopeId = 0
    ) {
        $this->title = $title;
        $this->body = $body;
        $this->category = $category;
        $this->meta = $meta;
        $this->actionUrl = $actionUrl;
        $this->scope = $scope;
        $this->scopeId = $scopeId;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'title' => $this->title,
            'body' => $this->body,
            'category' => $this->category,
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the broadcast channel name.
     */
    public function broadcastOn(): array
    {
        return match ($this->scope) {
            'user' => ["private-user.{$this->scopeId}"],
            'vendor' => ["private-vendor.{$this->scopeId}"],
            'admin' => ['private-admin.global'],
            'order' => ["private-order.{$this->scopeId}"],
            default => ["private-user.{$this->scopeId}"],
        };
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return 'notification.created';
    }

    /**
     * Get the array representation of the notification for database storage.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => $this->title,
            'body' => $this->body,
            'meta' => $this->meta,
            'category' => $this->category,
            'action_url' => $this->actionUrl,
        ];
    }
}
