<?php

namespace App\Policies;

use App\Models\Order;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class OrderPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // Admins can view all orders, vendors can view their orders, customers can view their orders
        return $user->hasRole(['admin', 'super-admin', 'vendor', 'vendor_assistant', 'customer']);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Order $order): bool
    {
        // Admin can view any order
        if ($user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // Customer can view their own orders
        if ($user->hasRole('customer') && $order->user_id === $user->id) {
            return true;
        }

        // Vendor can view orders from their vendor
        if ($user->hasRole(['vendor', 'vendor_assistant']) && $order->vendor_id === $user->vendor_id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can view order for broadcasting channel authorization.
     * This is used specifically for the private-order.{orderId} channel.
     */
    public function viewOrder(User $user, int $orderId): bool
    {
        $order = Order::find($orderId);

        if (!$order) {
            return false;
        }

        return $this->view($user, $order);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        // Only customers can create orders
        return $user->hasRole('customer');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Order $order): bool
    {
        // Admin can update any order
        if ($user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // Vendor can update orders from their vendor (status updates, etc.)
        if ($user->hasRole(['vendor', 'vendor_assistant']) && $order->vendor_id === $user->vendor_id) {
            return true;
        }

        // Customer can update their own orders (limited fields like shipping address before processing)
        if ($user->hasRole('customer') && $order->user_id === $user->id && $order->payment_status === 'pending') {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can cancel the order.
     */
    public function cancel(User $user, Order $order): bool
    {
        // Admin can cancel any order
        if ($user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // Customer can cancel their own orders if not yet shipped
        if ($user->hasRole('customer') &&
            $order->user_id === $user->id &&
            !in_array($order->fulfillment_status, ['shipped', 'delivered', 'cancelled'])) {
            return true;
        }

        // Vendor can cancel orders from their vendor if not yet shipped
        if ($user->hasRole(['vendor', 'vendor_assistant']) &&
            $order->vendor_id === $user->vendor_id &&
            !in_array($order->fulfillment_status, ['shipped', 'delivered', 'cancelled'])) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Order $order): bool
    {
        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Order $order): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Order $order): bool
    {
        return false;
    }
}
