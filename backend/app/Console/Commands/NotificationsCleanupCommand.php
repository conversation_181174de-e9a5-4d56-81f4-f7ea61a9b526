<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class NotificationsCleanupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:cleanup
                            {--days=90 : Number of days to keep notifications}
                            {--batch-size=1000 : Number of notifications to delete per batch}
                            {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old notifications from the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $batchSize = (int) $this->option('batch-size');
        $dryRun = $this->option('dry-run');

        if ($days <= 0) {
            $this->error('Days must be a positive integer');
            return 1;
        }

        if ($batchSize <= 0) {
            $this->error('Batch size must be a positive integer');
            return 1;
        }

        $cutoffDate = now()->subDays($days);

        $this->info("Cleaning up notifications older than {$days} days (before {$cutoffDate->format('Y-m-d H:i:s')})");

        // Count notifications to be deleted
        $totalCount = DB::table('notifications')
            ->where('created_at', '<', $cutoffDate)
            ->count();

        if ($totalCount === 0) {
            $this->info('No notifications found to clean up.');
            return 0;
        }

        $this->info("Found {$totalCount} notifications to clean up");

        if ($dryRun) {
            $this->warn('DRY RUN: No notifications will actually be deleted');

            // Show breakdown by type
            $breakdown = DB::table('notifications')
                ->select('type', DB::raw('count(*) as count'))
                ->where('created_at', '<', $cutoffDate)
                ->groupBy('type')
                ->get();

            $this->table(['Type', 'Count'], $breakdown->map(function ($item) {
                return [$item->type, $item->count];
            })->toArray());

            return 0;
        }

        if (!$this->confirm("Are you sure you want to delete {$totalCount} notifications?")) {
            $this->info('Operation cancelled');
            return 0;
        }

        $deletedCount = 0;
        $bar = $this->output->createProgressBar($totalCount);
        $bar->start();

        do {
            $deleted = DB::table('notifications')
                ->where('created_at', '<', $cutoffDate)
                ->limit($batchSize)
                ->delete();

            $deletedCount += $deleted;
            $bar->advance($deleted);

            // Small delay to prevent overwhelming the database
            if ($deleted > 0) {
                usleep(100000); // 0.1 seconds
            }

        } while ($deleted > 0);

        $bar->finish();
        $this->newLine();

        $this->info("Successfully deleted {$deletedCount} notifications");

        // Log the cleanup operation
        \Log::info('Notifications cleanup completed', [
            'deleted_count' => $deletedCount,
            'cutoff_date' => $cutoffDate->toISOString(),
            'batch_size' => $batchSize,
        ]);

        return 0;
    }
}
