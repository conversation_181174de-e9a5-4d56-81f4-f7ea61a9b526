<?php

namespace App\Console\Commands;

use Database\Seeders\UserCardSeeder;
use Illuminate\Console\Command;

class SeedUserCardsCommand extends Command
{
    protected $signature = 'seed:user-cards';
    protected $description = 'Seed user cards data';

    public function handle()
    {
        $this->info('Seeding user cards...');
        
        $seeder = new UserCardSeeder();
        $seeder->run();
        
        $this->info('User cards seeded successfully!');
    }
}