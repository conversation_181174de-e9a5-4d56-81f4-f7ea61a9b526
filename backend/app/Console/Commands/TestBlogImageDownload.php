<?php

namespace App\Console\Commands;

use App\Services\PixabayService;
use App\Services\ImageDownloadService;
use Illuminate\Console\Command;

class TestBlogImageDownload extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'blog:test-image-download {query?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Pixabay image download and S3 upload functionality';

    private PixabayService $pixabayService;
    private ImageDownloadService $imageDownloadService;

    public function __construct()
    {
        parent::__construct();
        $this->pixabayService = new PixabayService();
        $this->imageDownloadService = new ImageDownloadService();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $query = $this->argument('query') ?? 'ecommerce business';

        $this->info("🧪 Testing Blog Image Download System");
        $this->info("Query: {$query}");
        $this->newLine();

        // Check configurations
        $this->checkConfigurations();

        if (!$this->pixabayService->isConfigured()) {
            $this->error('❌ Pixabay API not configured. Please set PIXABAY_API_KEY in your .env file.');
            return 1;
        }

        if (!$this->imageDownloadService->isConfigured()) {
            $this->error('❌ S3 not configured. Please set AWS credentials in your .env file.');
            return 1;
        }

        // Test Pixabay search
        $this->info("🔍 Searching Pixabay for: {$query}");
        $image = $this->pixabayService->getRandomImage($query);

        if (!$image) {
            $this->error('❌ No suitable image found on Pixabay');
            return 1;
        }

        $this->info("✅ Found image: {$image['webformatURL']}");
        $this->info("   - Size: {$image['webformatWidth']}x{$image['webformatHeight']}");
        $this->info("   - Tags: {$image['tags']}");
        $this->newLine();

        // Test image download and S3 upload
        $this->info("📥 Downloading and uploading to S3...");
        $result = $this->imageDownloadService->downloadAndUploadToS3(
            $image['webformatURL'],
            'blog-images-test'
        );

        if (!$result) {
            $this->error('❌ Failed to download and upload image');
            return 1;
        }

        $this->info("✅ Image successfully uploaded to S3!");
        $this->info("   - S3 Path: {$result['path']}");
        $this->info("   - S3 URL: {$result['url']}");
        $this->newLine();

        $this->info("🎉 Test completed successfully!");
        return 0;
    }

    private function checkConfigurations(): void
    {
        $this->info("🔧 Checking configurations...");

        // Check Pixabay
        if ($this->pixabayService->isConfigured()) {
            $this->info("✅ Pixabay API configured");
        } else {
            $this->warn("⚠️  Pixabay API not configured");
        }

        // Check S3
        if ($this->imageDownloadService->isConfigured()) {
            $this->info("✅ S3 storage configured");
        } else {
            $this->warn("⚠️  S3 storage not configured");
        }

        $this->newLine();
    }
}
