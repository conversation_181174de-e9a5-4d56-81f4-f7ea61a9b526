<?php

namespace App\Console\Commands;

use App\Services\CartTokenService;
use App\Services\PriceValidationService;
use App\Services\SecureSessionService;
use App\Models\ShoppingCart;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CartMaintenanceCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'cart:maintenance 
                            {--cleanup-tokens : Clean up expired cart tokens}
                            {--validate-prices : Validate and update cart prices}
                            {--cleanup-sessions : Clean up expired sessions}
                            {--all : Run all maintenance tasks}';

    /**
     * The console command description.
     */
    protected $description = 'Perform cart maintenance tasks including token cleanup, price validation, and session cleanup';

    protected CartTokenService $tokenService;
    protected PriceValidationService $priceValidator;
    protected SecureSessionService $sessionService;

    public function __construct(
        CartTokenService $tokenService,
        PriceValidationService $priceValidator,
        SecureSessionService $sessionService
    ) {
        parent::__construct();
        $this->tokenService = $tokenService;
        $this->priceValidator = $priceValidator;
        $this->sessionService = $sessionService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting cart maintenance...');

        $runAll = $this->option('all');
        $results = [];

        if ($runAll || $this->option('cleanup-tokens')) {
            $results['tokens'] = $this->cleanupExpiredTokens();
        }

        if ($runAll || $this->option('validate-prices')) {
            $results['prices'] = $this->validateCartPrices();
        }

        if ($runAll || $this->option('cleanup-sessions')) {
            $results['sessions'] = $this->cleanupExpiredSessions();
        }

        $this->displayResults($results);
        $this->info('Cart maintenance completed.');

        return Command::SUCCESS;
    }

    /**
     * Clean up expired cart tokens.
     */
    protected function cleanupExpiredTokens(): array
    {
        $this->info('Cleaning up expired cart tokens...');
        
        $count = $this->tokenService->cleanupExpiredTokens();
        
        $this->info("Cleaned up {$count} expired cart tokens.");
        
        return [
            'cleaned_tokens' => $count,
            'status' => 'completed'
        ];
    }

    /**
     * Validate and update cart prices.
     */
    protected function validateCartPrices(): array
    {
        $this->info('Validating cart prices...');
        
        $activeCarts = ShoppingCart::where('status', 'active')
            ->with(['items.product', 'items.variant'])
            ->get();

        $results = [
            'total_carts' => $activeCarts->count(),
            'carts_with_issues' => 0,
            'items_updated' => 0,
            'items_removed' => 0,
            'status' => 'completed'
        ];

        $progressBar = $this->output->createProgressBar($activeCarts->count());
        $progressBar->start();

        foreach ($activeCarts as $cart) {
            $validation = $this->priceValidator->validateCartPrices($cart);
            
            if (!$validation['valid']) {
                $results['carts_with_issues']++;
                
                // Auto-correct prices
                $correction = $this->priceValidator->autoCorrectCartPrices($cart);
                $results['items_updated'] += $correction['updated_items'];
                $results['items_removed'] += $correction['failed_items'];
                
                if ($correction['updated_items'] > 0 || $correction['failed_items'] > 0) {
                    Log::info('Cart prices corrected', [
                        'cart_id' => $cart->id,
                        'updated_items' => $correction['updated_items'],
                        'failed_items' => $correction['failed_items'],
                        'total_adjustment' => $correction['total_adjustment'],
                    ]);
                }
            }
            
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
        
        $this->info("Validated {$results['total_carts']} carts.");
        $this->info("Found issues in {$results['carts_with_issues']} carts.");
        $this->info("Updated {$results['items_updated']} items.");
        $this->info("Removed {$results['items_removed']} unavailable items.");

        return $results;
    }

    /**
     * Clean up expired sessions.
     */
    protected function cleanupExpiredSessions(): array
    {
        $this->info('Cleaning up expired sessions...');
        
        $count = $this->sessionService->cleanupOldSessions();
        
        $this->info("Cleaned up {$count} expired sessions.");
        
        return [
            'cleaned_sessions' => $count,
            'status' => 'completed'
        ];
    }

    /**
     * Display maintenance results.
     */
    protected function displayResults(array $results): void
    {
        $this->newLine();
        $this->info('=== Maintenance Results ===');
        
        foreach ($results as $task => $result) {
            $this->info("Task: {$task}");
            
            if (is_array($result)) {
                foreach ($result as $key => $value) {
                    if ($key !== 'status') {
                        $this->line("  {$key}: {$value}");
                    }
                }
            } else {
                $this->line("  Result: {$result}");
            }
            
            $this->newLine();
        }
    }
}
