<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class TokenMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        // Force Laravel to use the Passport API guard
        if ($request->bearerToken()) {
            try {
                Auth::shouldUse('api');
                Auth::guard('api')->user(); // triggers token validation internally
            } catch (\Exception $e) {
                // Invalid token - we fail silently (optional auth behavior)
            }
        }

        return $next($request);
    }
}
