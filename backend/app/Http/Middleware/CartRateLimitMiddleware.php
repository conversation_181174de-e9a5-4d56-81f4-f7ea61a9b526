<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

class CartRateLimitMiddleware
{
    /**
     * Rate limiting configurations for different cart operations.
     */
    protected array $rateLimits = [
        'cart.create' => ['limit' => 10, 'window' => 60], // 10 carts per minute
        'cart.add_item' => ['limit' => 30, 'window' => 60], // 30 add operations per minute
        'cart.update_item' => ['limit' => 50, 'window' => 60], // 50 updates per minute
        'cart.remove_item' => ['limit' => 50, 'window' => 60], // 50 removals per minute
        'cart.bulk_update' => ['limit' => 5, 'window' => 60], // 5 bulk operations per minute
        'cart.apply_coupon' => ['limit' => 10, 'window' => 60], // 10 coupon applications per minute
        'cart.validate' => ['limit' => 20, 'window' => 60], // 20 validations per minute
        'cart.reserve' => ['limit' => 15, 'window' => 60], // 15 reservations per minute
        'cart.recovery' => ['limit' => 5, 'window' => 300], // 5 recovery attempts per 5 minutes
        'default' => ['limit' => 100, 'window' => 60], // Default: 100 requests per minute
    ];

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, ?string $operation = null): SymfonyResponse
    {
        // Determine the operation type
        $operationType = $this->determineOperationType($request, $operation);
        
        // Get rate limit configuration
        $config = $this->rateLimits[$operationType] ?? $this->rateLimits['default'];
        
        // Generate cache key for this user/IP and operation
        $cacheKey = $this->generateCacheKey($request, $operationType);
        
        // Check current request count
        $currentCount = Cache::get($cacheKey, 0);
        
        // Check if limit exceeded
        if ($currentCount >= $config['limit']) {
            Log::warning('Cart rate limit exceeded', [
                'operation' => $operationType,
                'user_id' => auth()->id(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'current_count' => $currentCount,
                'limit' => $config['limit'],
                'window' => $config['window'],
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Rate limit exceeded. Please try again later.',
                'error' => 'Too many requests',
                'data' => [
                    'limit' => $config['limit'],
                    'window_seconds' => $config['window'],
                    'retry_after' => $this->getRetryAfter($cacheKey, $config['window']),
                ],
            ], Response::HTTP_TOO_MANY_REQUESTS);
        }

        // Increment counter
        $this->incrementCounter($cacheKey, $config['window']);
        
        // Add rate limit headers to response
        $response = $next($request);
        
        return $this->addRateLimitHeaders($response, $config, $currentCount + 1);
    }

    /**
     * Determine the operation type based on request.
     */
    protected function determineOperationType(Request $request, ?string $operation = null): string
    {
        if ($operation) {
            return "cart.{$operation}";
        }

        $method = $request->method();
        $path = $request->path();

        // Cart creation
        if ($method === 'POST' && preg_match('/\/cart$/', $path)) {
            return 'cart.create';
        }

        // Add item to cart
        if ($method === 'POST' && preg_match('/\/cart\/[^\/]+\/items$/', $path)) {
            return 'cart.add_item';
        }

        // Update cart item
        if ($method === 'PUT' && preg_match('/\/cart\/[^\/]+\/items\/[^\/]+$/', $path)) {
            return 'cart.update_item';
        }

        // Remove cart item
        if ($method === 'DELETE' && preg_match('/\/cart\/[^\/]+\/items\/[^\/]+$/', $path)) {
            return 'cart.remove_item';
        }

        // Bulk update items
        if ($method === 'POST' && preg_match('/\/cart\/[^\/]+\/items\/bulk$/', $path)) {
            return 'cart.bulk_update';
        }

        // Apply coupon
        if ($method === 'POST' && preg_match('/\/cart\/[^\/]+\/apply-coupon$/', $path)) {
            return 'cart.apply_coupon';
        }

        // Cart validation
        if ($method === 'POST' && preg_match('/\/cart\/[^\/]+\/validate$/', $path)) {
            return 'cart.validate';
        }

        // Inventory reservation
        if ($method === 'POST' && preg_match('/\/cart\/[^\/]+\/reserve$/', $path)) {
            return 'cart.reserve';
        }

        // Cart recovery
        if (preg_match('/\/cart-recovery\//', $path)) {
            return 'cart.recovery';
        }

        return 'default';
    }

    /**
     * Generate cache key for rate limiting.
     */
    protected function generateCacheKey(Request $request, string $operationType): string
    {
        $identifier = auth()->check() ? 'user:' . auth()->id() : 'ip:' . $request->ip();
        return "cart_rate_limit:{$operationType}:{$identifier}";
    }

    /**
     * Increment the request counter.
     */
    protected function incrementCounter(string $cacheKey, int $windowSeconds): void
    {
        $currentCount = Cache::get($cacheKey, 0);
        Cache::put($cacheKey, $currentCount + 1, $windowSeconds);

        // For non-Redis stores, also store the expiration time
        $store = Cache::getStore();
        if (!method_exists($store, 'getRedis')) {
            $expirationKey = $cacheKey . '_expires_at';
            Cache::put($expirationKey, time() + $windowSeconds, $windowSeconds);
        }
    }

    /**
     * Get retry after seconds.
     */
    protected function getRetryAfter(string $cacheKey, int $windowSeconds): int
    {
        $store = Cache::getStore();

        // Handle different cache store types
        if (method_exists($store, 'getRedis')) {
            $ttl = $store->getRedis()->ttl($cacheKey);
            return $ttl > 0 ? $ttl : $windowSeconds;
        }

        // For non-Redis stores, calculate TTL based on cache expiration
        $expirationKey = $cacheKey . '_expires_at';
        $expiresAt = Cache::get($expirationKey);

        if ($expiresAt) {
            $ttl = $expiresAt - time();
            return $ttl > 0 ? $ttl : $windowSeconds;
        }

        return $windowSeconds;
    }

    /**
     * Add rate limit headers to response.
     */
    protected function addRateLimitHeaders($response, array $config, int $currentCount)
    {
        $remaining = max(0, $config['limit'] - $currentCount);
        
        $response->headers->set('X-RateLimit-Limit', $config['limit']);
        $response->headers->set('X-RateLimit-Remaining', $remaining);
        $response->headers->set('X-RateLimit-Window', $config['window']);
        
        if ($remaining === 0) {
            $response->headers->set('Retry-After', $config['window']);
        }

        return $response;
    }

    /**
     * Get rate limit status for a specific operation.
     */
    public static function getRateLimitStatus(Request $request, string $operationType): array
    {
        $middleware = new static();
        $config = $middleware->rateLimits[$operationType] ?? $middleware->rateLimits['default'];
        $cacheKey = $middleware->generateCacheKey($request, $operationType);
        $currentCount = Cache::get($cacheKey, 0);
        
        return [
            'operation' => $operationType,
            'limit' => $config['limit'],
            'current' => $currentCount,
            'remaining' => max(0, $config['limit'] - $currentCount),
            'window_seconds' => $config['window'],
            'reset_at' => now()->addSeconds($middleware->getRetryAfter($cacheKey, $config['window']))->toISOString(),
        ];
    }

    /**
     * Clear rate limit for a specific user/IP and operation.
     */
    public static function clearRateLimit(Request $request, string $operationType): bool
    {
        $middleware = new static();
        $cacheKey = $middleware->generateCacheKey($request, $operationType);
        return Cache::forget($cacheKey);
    }

    /**
     * Get all rate limit statuses for current user/IP.
     */
    public static function getAllRateLimitStatuses(Request $request): array
    {
        $middleware = new static();
        $statuses = [];
        
        foreach (array_keys($middleware->rateLimits) as $operation) {
            if ($operation !== 'default') {
                $statuses[$operation] = static::getRateLimitStatus($request, $operation);
            }
        }
        
        return $statuses;
    }

    /**
     * Check if operation is rate limited without incrementing counter.
     */
    public static function isRateLimited(Request $request, string $operationType): bool
    {
        $middleware = new static();
        $config = $middleware->rateLimits[$operationType] ?? $middleware->rateLimits['default'];
        $cacheKey = $middleware->generateCacheKey($request, $operationType);
        $currentCount = Cache::get($cacheKey, 0);
        
        return $currentCount >= $config['limit'];
    }

    /**
     * Get rate limit configuration.
     */
    public static function getRateLimitConfig(): array
    {
        return (new static())->rateLimits;
    }

    /**
     * Update rate limit configuration (for admin use).
     */
    public static function updateRateLimitConfig(string $operation, int $limit, int $window): void
    {
        // This would typically be stored in database or config file
        // For now, we'll just log the change
        Log::info('Rate limit configuration updated', [
            'operation' => $operation,
            'limit' => $limit,
            'window' => $window,
            'updated_by' => auth()->id(),
        ]);
    }
}
