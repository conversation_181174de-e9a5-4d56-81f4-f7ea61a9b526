<?php

namespace App\Http\Resources\Checkout;

use App\Http\Resources\Cart\CartResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CheckoutResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        // Handle both array and object resource types
        $data = is_array($this->resource) ? $this->resource : (array) $this->resource;

        return [
            // Cart data using consistent CartResource structure
            'cart' => isset($data['cart']) ? new CartResource($data['cart']) : null,

            // Validation status
            'validation' => [
                'is_valid' => $data['validation']['is_valid'] ?? false,
                'errors' => $data['validation']['errors'] ?? [],
                'cart_validation' => $data['validation']['cart_validation'] ?? [],
            ],

            // User addresses with consistent structure
            'user_addresses' => [
                'shipping' => collect($data['user_addresses'] ?? [])
                    ->filter(function($addr) {
                        // Convert array to object-like access if needed
                        $address = is_array($addr) ? $addr : (array) $addr;
                        return ($address['is_shipping'] ?? false) || ($address['is_default'] ?? false);
                    })
                    ->values()
                    ->toArray(),
                'billing' => collect($data['user_addresses'] ?? [])
                    ->filter(function($addr) {
                        // Convert array to object-like access if needed
                        $address = is_array($addr) ? $addr : (array) $addr;
                        return ($address['is_billing'] ?? false) || ($address['is_default'] ?? false);
                    })
                    ->values()
                    ->toArray(),
                'all' => $data['user_addresses'] ?? [],
            ],

            // User payment methods (saved cards)
            'user_payment_methods' => $data['user_payment_methods'] ?? [],

            // Available payment method types from database
            'available_payment_methods' => collect($data['available_payment_methods'] ?? [])
                ->map(function ($method) {
                    return [
                        'id' => $method['id'] ?? null,
                        'type' => $method['type'] ?? null,
                        'name' => $method['name'] ?? null,
                        'name_ar' => $method['name_ar'] ?? null,
                        'description' => $method['description'] ?? null,
                        'description_ar' => $method['description_ar'] ?? null,
                        'icon' => $method['icon'] ?? null,
                        'enabled' => $method['enabled'] ?? false,
                        'requires_selection' => $method['requires_selection'] ?? false,
                    ];
                }),

            // Checkout readiness status
            'checkout_ready' => $data['checkout_ready'] ?? false,
            
            // Applied coupons (consistent with cart structure)
            'applied_coupons' => isset($data['cart']) && is_object($data['cart']) ? ($data['cart']->applied_coupons ?? []) : [],
            
            // Checkout metadata
            'checkout_metadata' => [
                'can_apply_coupons' => true,
                'requires_shipping_address' => true,
                'requires_payment_method' => true,
                'supports_guest_checkout' => false,
                'min_order_value' => config('checkout.min_order_value', 0),
                'max_order_value' => config('checkout.max_order_value', 50000),
            ],
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        $data = is_array($this->resource) ? $this->resource : (array) $this->resource;
        $currency = 'AED';

        if (isset($data['cart']) && is_object($data['cart']) && isset($data['cart']->currency)) {
            $currency = $data['cart']->currency;
        }

        return [
            'meta' => [
                'currency_symbol' => $this->getCurrencySymbol($currency),
                'tax_rate' => 0.05, // 5% VAT in UAE
                'checkout_version' => '2.0',
                'api_optimized' => true,
            ],
        ];
    }

    /**
     * Get currency symbol.
     */
    protected function getCurrencySymbol(string $currency): string
    {
        return match ($currency) {
            'AED' => 'د.إ',
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            default => $currency,
        };
    }
}
