<?php

namespace App\Http\Resources\Wishlist;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WishlistResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'product_id' => $this->product_id,
            'product_variant_id' => $this->product_variant_id,
            'vendor_id' => $this->vendor_id,
            'note' => $this->note,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            
            // Product information
            'product' => $this->whenLoaded('product', function () {
                return [
                    'id' => $this->product->id,
                    'title_en' => $this->product->title_en,
                    'title_ar' => $this->product->title_ar,
                    'regular_price' => $this->product->regular_price,
                    'offer_price' => $this->product->offer_price,
                    'member_price' => $this->product->member_price,
                    'is_active' => $this->product->is_active,
                ];
            }),
            
            // Product variant information (if applicable)
            'product_variant' => $this->whenLoaded('productVariant', function () {
                return $this->productVariant ? [
                    'id' => $this->productVariant->id,
                    'product_id' => $this->productVariant->product_id,
                    'regular_price' => $this->productVariant->regular_price,
                    'offer_price' => $this->productVariant->offer_price,
                    'member_price' => $this->productVariant->member_price,
                    'is_active' => $this->productVariant->is_active,
                    'stock' => $this->productVariant->stock,
                ] : null;
            }),
            
            // Vendor information
            'vendor' => $this->whenLoaded('vendor', function () {
                return $this->vendor ? [
                    'id' => $this->vendor->id,
                    'name_en' => $this->vendor->vendor_display_name_en,
                    'name_ar' => $this->vendor->vendor_display_name_ar,
                ] : null;
            }),
            
            // Computed attributes
            'effective_price' => $this->effective_price,
            'is_available' => $this->is_available,
        ];
    }
}
