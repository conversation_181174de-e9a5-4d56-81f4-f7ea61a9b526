<?php

namespace App\Http\Resources\Order;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Lightweight resource for client order list views
 * Optimized for customer-facing order history with minimal data loading
 */
class ClientOrderListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            // Essential identifiers
            'id' => $this->id,
            'uuid' => $this->uuid,
            'order_number' => $this->order_number,
            'order_date' => $this->created_at->format('Y-m-d H:i'),
            'order_date_human' => $this->created_at->diffForHumans(),
            
            // Financial summary (what customers care about)
            'pricing' => [
                'total' => number_format((float)$this->total, 2),
                'total_raw' => (float)$this->total,
                'currency' => $this->currency ?? 'AED',
                // Use database-level aggregations from withCount/withSum
                'items_count' => (int)($this->items_count ?? 0),
                'total_quantity' => (int)($this->total_quantity ?? 0),
            ],
            
            // Status information (essential for customers)
            'status' => [
                'fulfillment' => $this->fulfillment_status,
                'fulfillment_display' => $this->status_display,
                'payment' => $this->payment_status,
                'payment_display' => $this->payment_status_display,
                'is_paid' => $this->is_paid,
            ],
            
            // Vendor info (minimal - only if multi-vendor)
            'vendor' => $this->when($this->vendor_id, [
                'id' => $this->vendor_id,
                'name' => $this->vendor_name ?? 'Platform',
            ]),
            
            // Essential details for customer
            'details' => [
                'payment_method' => $this->getPaymentMethodForBackwardCompatibility(),
                'payment_method_name' => $this->paymentMethod?->name_en,
                'payment_method_display' => $this->getPaymentMethodDisplay(),
                'tracking_number' => $this->tracking_number,
                'has_customer_note' => !empty($this->customer_note),
                'estimated_delivery' => $this->getEstimatedDeliveryDate(),
            ],
            
            // Customer actions (what they can do)
            'actions' => [
                'can_cancel' => $this->can_cancel,
                'can_reorder' => true, // Customers can always reorder
                'view_url' => route('api.client.orders.show', $this->uuid),
                'track_url' => $this->tracking_number ? $this->getTrackingUrl() : null,
            ],
            
            // Priority and urgency indicators
            'priority' => $this->getPriorityLevel(),
            'requires_attention' => $this->requiresCustomerAttention(),
            
            // Timestamps
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'order_age_days' => $this->created_at->diffInDays(now()),
        ];
    }
    
    /**
     * Get payment method for backward compatibility
     * Maps new payment method names to old enum values where possible
     */
    protected function getPaymentMethodForBackwardCompatibility(): ?string
    {
        if (!$this->paymentMethod?->slug) {
            return null;
        }

        // Map new payment method slugs to old enum values for backward compatibility
        return match($this->paymentMethod->slug) {
            'cash-on-delivery', 'cod' => 'cod',
            'credit-card', 'debit-card', 'card' => 'card',
            'digital-wallet', 'wallet' => 'wallet',
            'bank-transfer', 'bank' => 'bank',
            default => $this->paymentMethod->slug,
        };
    }

    /**
     * Get payment method display name
     */
    protected function getPaymentMethodDisplay(): string
    {
        // Use the actual payment method name if available, otherwise fall back to mapping
        if ($this->paymentMethod?->name_en) {
            return $this->paymentMethod->name_en;
        }

        $paymentMethod = $this->getPaymentMethodForBackwardCompatibility();
        return match($paymentMethod) {
            'card' => 'Credit/Debit Card',
            'cod' => 'Cash on Delivery',
            'bank' => 'Bank Transfer',
            'wallet' => 'Digital Wallet',
            default => $paymentMethod ? ucfirst(str_replace(['_', '-'], ' ', $paymentMethod)) : 'Unknown',
        };
    }
    
    /**
     * Get estimated delivery date for customer
     */
    protected function getEstimatedDeliveryDate(): ?string
    {
        if ($this->fulfillment_status === 'delivered') {
            return null;
        }
        
        $estimatedDays = match($this->fulfillment_status) {
            'pending' => 5,
            'confirmed' => 4,
            'processing' => 3,
            'shipped' => 2,
            default => null,
        };
        
        if ($estimatedDays) {
            return now()->addDays($estimatedDays)->format('M d, Y');
        }
        
        return null;
    }
    
    /**
     * Get priority level for customer view
     */
    protected function getPriorityLevel(): string
    {
        // For customers, priority is mainly about delivery urgency
        if ($this->fulfillment_status === 'shipped') {
            return 'high'; // Arriving soon
        }
        
        if ($this->fulfillment_status === 'processing') {
            return 'medium'; // Being prepared
        }
        
        return 'normal';
    }
    
    /**
     * Check if order requires customer attention
     */
    protected function requiresCustomerAttention(): bool
    {
        // Orders that need customer action or attention
        return in_array($this->fulfillment_status, ['cancelled', 'refunded']) ||
               ($this->payment_status === 'failed') ||
               ($this->fulfillment_status === 'pending' && $this->created_at->diffInDays(now()) > 2);
    }
    
    /**
     * Get tracking URL if available
     */
    protected function getTrackingUrl(): ?string
    {
        if (!$this->tracking_number) {
            return null;
        }
        
        // This would typically integrate with shipping providers
        // For now, return a generic tracking URL
        return "https://track.example.com/{$this->tracking_number}";
    }
}
