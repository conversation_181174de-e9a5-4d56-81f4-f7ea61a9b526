<?php

namespace App\Http\Resources\Order;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Optimized resource for admin orders list table
 * Designed for performance with large datasets (1M+ orders)
 * Returns only essential data needed for table display
 */
class OrderListItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * 
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            // Essential identifiers
            'id' => $this->id,
            'uuid' => $this->uuid,
            'order_number' => $this->order_number,
            
            // Order date (formatted for display)
            'order_date' => $this->created_at->format('Y-m-d H:i'),
            'order_date_human' => $this->created_at->diffForHumans(),
            'order_age_days' => $this->created_at->diffInDays(now()),
            
            // Customer information (minimal - only what's needed for table)
            'customer' => [
                'id' => $this->user_id,
                'name' => $this->customer_name ?? 'N/A',
                'email' => $this->customer_email ?? 'N/A',
            ],
            
            // Vendor/Seller information (minimal)
            'vendor' => $this->when($this->vendor_id, [
                'id' => $this->vendor_id,
                'name' => $this->vendor_name ?? 'Platform',
            ]),
            
            // Financial summary (essential for table)
            'pricing' => [
                'total' => number_format((float)$this->total, 2),
                'total_raw' => (float)$this->total,
                'currency' => $this->currency ?? 'AED',
                'items_count' => (int)($this->items_count ?? 0),
                'total_quantity' => (int)($this->total_quantity ?? 0),
            ],
            
            // Status information (critical for admin overview)
            'status' => [
                'fulfillment' => $this->fulfillment_status,
                'fulfillment_display' => $this->getFulfillmentStatusDisplay(),
                'payment' => $this->payment_status,
                'payment_display' => $this->getPaymentStatusDisplay(),
                'is_paid' => (bool)$this->is_paid,
            ],
            
            // Payment method (important for admin) - backward compatibility + new structure
            'payment_method' => $this->getPaymentMethodForBackwardCompatibility(),
            'payment_method_name' => $this->payment_method_name ?? $this->getPaymentMethodForBackwardCompatibility(),
            'payment_method_display' => $this->getPaymentMethodDisplay(),
            'payment_method_object' => $this->when($this->payment_method_name, [
                'id' => $this->payment_method_id,
                'name_en' => $this->payment_method_name,
                'name_ar' => $this->payment_method_name_ar,
                'slug' => $this->payment_method_slug,
                'icon' => $this->payment_method_icon,
                'icon_url' => $this->payment_method_icon ? config('filesystems.disks.s3.url') . '/' . $this->payment_method_icon : null,
                'status' => $this->payment_method_status,
            ]),
            
            // Quick action indicators
            'actions' => [
                'can_cancel' => $this->getCanCancel(),
                'can_refund' => $this->getCanRefund(),
                'view_url' => route('admin.orders.show', $this->uuid),
                'has_notes' => $this->hasNotes(),
            ],
            
            // Tracking information
            'tracking_number' => $this->tracking_number,
            'has_tracking' => !empty($this->tracking_number),
            
            // Priority indicators for admin attention
            'priority' => $this->getPriorityLevel(),
            'requires_attention' => $this->requiresAttention(),
        ];
    }

    /**
     * Get formatted fulfillment status display
     */
    protected function getFulfillmentStatusDisplay(): string
    {
        return match($this->fulfillment_status) {
            'pending' => 'Pending',
            'confirmed' => 'Confirmed',
            'processing' => 'Processing',
            'shipped' => 'Shipped',
            'delivered' => 'Delivered',
            'cancelled' => 'Cancelled',
            'returned' => 'Returned',
            default => ucfirst(str_replace('_', ' ', $this->fulfillment_status)),
        };
    }

    /**
     * Get formatted payment status display
     */
    protected function getPaymentStatusDisplay(): string
    {
        return match($this->payment_status) {
            'pending' => 'Pending',
            'paid' => 'Paid',
            'failed' => 'Failed',
            'refunded' => 'Refunded',
            default => ucfirst(str_replace('_', ' ', $this->payment_status)),
        };
    }

    /**
     * Get payment method for backward compatibility
     * Maps new payment method names to old enum values where possible
     */
    protected function getPaymentMethodForBackwardCompatibility(): ?string
    {
        if (!$this->payment_method_slug) {
            return null;
        }

        // Map new payment method slugs to old enum values for backward compatibility
        return match($this->payment_method_slug) {
            'cash-on-delivery', 'cod' => 'cod',
            'credit-card', 'debit-card', 'card' => 'card',
            'digital-wallet', 'wallet' => 'wallet',
            'bank-transfer', 'bank' => 'bank',
            default => $this->payment_method_slug,
        };
    }

    /**
     * Get formatted payment method display
     */
    protected function getPaymentMethodDisplay(): string
    {
        // Use the actual payment method name if available, otherwise fall back to mapping
        if ($this->payment_method_name) {
            return $this->payment_method_name;
        }

        $paymentMethod = $this->getPaymentMethodForBackwardCompatibility();
        return match($paymentMethod) {
            'cod' => 'Cash on Delivery',
            'card' => 'Credit/Debit Card',
            'wallet' => 'Digital Wallet',
            'bank' => 'Bank Transfer',
            default => $paymentMethod ? ucfirst(str_replace('-', ' ', $paymentMethod)) : 'N/A',
        };
    }

    /**
     * Check if order can be cancelled
     */
    protected function getCanCancel(): bool
    {
        return in_array($this->fulfillment_status, ['pending', 'confirmed', 'processing']);
    }

    /**
     * Check if order can be refunded
     */
    protected function getCanRefund(): bool
    {
        return $this->payment_status === 'paid' && 
               in_array($this->fulfillment_status, ['delivered', 'cancelled']);
    }

    /**
     * Check if order has notes
     */
    protected function hasNotes(): bool
    {
        return !empty($this->customer_note) || !empty($this->admin_note);
    }

    /**
     * Get priority level for admin attention
     */
    protected function getPriorityLevel(): string
    {
        // High priority for large orders
        if ($this->total > 1000) {
            return 'high';
        }
        
        // Medium priority for orders over 500 AED
        if ($this->total > 500) {
            return 'medium';
        }
        
        return 'normal';
    }

    /**
     * Check if order requires admin attention
     */
    protected function requiresAttention(): bool
    {
        // Orders that need attention
        $attentionStatuses = ['failed', 'cancelled'];
        $urgentFulfillmentStatuses = ['pending'];
        
        // Payment issues
        if (in_array($this->payment_status, $attentionStatuses)) {
            return true;
        }
        
        // Old pending orders (more than 2 days)
        if ($this->fulfillment_status === 'pending' && $this->created_at->diffInDays(now()) > 2) {
            return true;
        }
        
        // High value orders that are still pending
        if ($this->total > 1000 && in_array($this->fulfillment_status, $urgentFulfillmentStatuses)) {
            return true;
        }
        
        return false;
    }
}
