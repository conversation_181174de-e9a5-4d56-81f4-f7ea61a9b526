<?php

namespace App\Http\Resources\Order;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Lightweight resource for vendor order list views
 * Optimized for vendor-facing order management with vendor-relevant data
 */
class VendorOrderListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * Flattened structure to match admin API format for consistent UI components
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            // Essential identifiers (matching admin format)
            'id' => $this->id,
            'uuid' => $this->uuid,
            'order_number' => $this->order_number,
            'order_date' => $this->created_at->format('Y-m-d H:i'),

            // Customer info (flattened, minimal for vendor privacy)
            'customer_name' => $this->customer_name ?? 'N/A',
            'customer_email' => $this->customer_email ?? 'N/A',

            // Vendor info (for multi-vendor scenarios)
            'vendor_name' => $this->vendor_name ?? 'Platform',

            // Financial data (flattened, matching admin structure)
            'total_amount' => number_format((float)$this->total, 2),
            'currency' => $this->currency ?? 'AED',
            'items_count' => (int)($this->items_count ?? 0),
            'total_quantity' => (int)($this->total_quantity ?? 0),

            // Status fields (flattened, matching admin format)
            'delivery_status' => $this->fulfillment_status,
            'delivery_status_display' => $this->status_display,
            'payment_status' => $this->payment_status,
            'payment_status_display' => $this->payment_status_display,
            'payment_method' => $this->getPaymentMethodForBackwardCompatibility(),
            'payment_method_name' => $this->paymentMethod?->name_en,
            'payment_method_display' => $this->getPaymentMethodDisplay(),
            'order_status' => $this->fulfillment_status, // For consistency with admin
            'order_status_display' => $this->status_display,

            // Vendor-specific fields (flattened to root level)
            'vendor_commission' => $this->calculateVendorCommission(),
            'can_fulfill' => $this->canVendorFulfill(),
            'requires_vendor_attention' => $this->requiresVendorAttention(),
            'processing_deadline' => $this->getProcessingDeadline(),
            'vendor_priority_level' => $this->getVendorPriorityLevel(),

            // Additional vendor business fields
            'can_update_status' => $this->canVendorUpdateStatus(),
            'can_add_tracking' => $this->canVendorAddTracking(),
            'can_mark_shipped' => $this->canVendorMarkShipped(),
            'tracking_number' => $this->tracking_number,
            'shipping_method' => $this->getShippingMethod(),
            'estimated_ship_date' => $this->getEstimatedShipDate(),
            'special_instructions' => $this->customer_note,
            'urgency_reason' => $this->getUrgencyReason(),
            'days_since_order' => $this->created_at->diffInDays(now()),
            'is_processing_overdue' => $this->isProcessingOverdue(),

            // Shipping address (minimal for privacy)
            'shipping_city' => $this->shipping_city,
            'shipping_country' => $this->shipping_country,
            'shipping_postal_code' => $this->shipping_postal_code,

            // Timestamps (matching admin format)
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
    
    /**
     * Calculate vendor commission (simplified)
     */
    protected function calculateVendorCommission(): string
    {
        // This would typically be based on vendor agreement
        // For now, assume 85% goes to vendor (15% platform fee)
        $commission = $this->total * 0.85;
        return number_format($commission, 2);
    }
    
    /**
     * Check if vendor can fulfill this order
     */
    protected function canVendorFulfill(): bool
    {
        return in_array($this->fulfillment_status, ['confirmed', 'processing']) && 
               $this->payment_status === 'paid';
    }
    
    /**
     * Check if vendor can update order status
     */
    protected function canVendorUpdateStatus(): bool
    {
        return in_array($this->fulfillment_status, ['confirmed', 'processing', 'shipped']);
    }
    
    /**
     * Check if vendor can add tracking information
     */
    protected function canVendorAddTracking(): bool
    {
        return in_array($this->fulfillment_status, ['processing', 'shipped']) && 
               empty($this->tracking_number);
    }
    
    /**
     * Check if vendor can mark as shipped
     */
    protected function canVendorMarkShipped(): bool
    {
        return $this->fulfillment_status === 'processing' && 
               $this->payment_status === 'paid';
    }
    
    /**
     * Get shipping method
     */
    protected function getShippingMethod(): string
    {
        // This would come from order metadata or shipping service
        return 'Standard Delivery'; // Placeholder
    }
    
    /**
     * Get estimated ship date for vendor
     */
    protected function getEstimatedShipDate(): ?string
    {
        if (in_array($this->fulfillment_status, ['shipped', 'delivered'])) {
            return null;
        }
        
        $processingDays = match($this->fulfillment_status) {
            'confirmed' => 2,
            'processing' => 1,
            default => 3,
        };
        
        return now()->addDays($processingDays)->format('M d, Y');
    }
    
    /**
     * Get shipping address summary for vendor
     */
    protected function getShippingAddressSummary(): array
    {
        return [
            'city' => $this->shipping_city,
            'country' => $this->shipping_country,
            'postal_code' => $this->shipping_postal_code,
            // Don't expose full address for privacy
        ];
    }
    
    /**
     * Get priority level from vendor perspective
     */
    protected function getVendorPriorityLevel(): string
    {
        // High priority for large orders or overdue processing
        if ($this->total > 1000 || $this->isProcessingOverdue()) {
            return 'high';
        }
        
        // Medium priority for orders over 500 AED or confirmed orders
        if ($this->total > 500 || $this->fulfillment_status === 'confirmed') {
            return 'medium';
        }
        
        return 'normal';
    }
    
    /**
     * Check if order requires vendor attention
     */
    protected function requiresVendorAttention(): bool
    {
        return $this->isProcessingOverdue() ||
               ($this->fulfillment_status === 'confirmed' && $this->created_at->diffInHours(now()) > 24) ||
               ($this->fulfillment_status === 'processing' && $this->created_at->diffInDays(now()) > 3);
    }
    
    /**
     * Get urgency reason for vendor
     */
    protected function getUrgencyReason(): ?string
    {
        if ($this->isProcessingOverdue()) {
            return 'Processing overdue';
        }
        
        if ($this->fulfillment_status === 'confirmed' && $this->created_at->diffInHours(now()) > 24) {
            return 'Awaiting processing for over 24 hours';
        }
        
        if ($this->total > 1000) {
            return 'High value order';
        }
        
        return null;
    }
    
    /**
     * Get expected processing time
     */
    protected function getExpectedProcessingTime(): string
    {
        return '1-2 business days'; // This would be configurable per vendor
    }
    
    /**
     * Check if processing is overdue
     */
    protected function isProcessingOverdue(): bool
    {
        if ($this->fulfillment_status === 'confirmed') {
            return $this->created_at->diffInDays(now()) > 2;
        }

        if ($this->fulfillment_status === 'processing') {
            return $this->created_at->diffInDays(now()) > 5;
        }

        return false;
    }

    /**
     * Get processing deadline for vendor
     */
    protected function getProcessingDeadline(): ?string
    {
        return match($this->fulfillment_status) {
            'pending' => $this->created_at->addDay()->format('M j, Y H:i'),
            'confirmed' => $this->created_at->addBusinessDays(2)->format('M j, Y H:i'),
            'processing' => $this->created_at->addBusinessDays(5)->format('M j, Y H:i'),
            default => null,
        };
    }

    /**
     * Get payment method for backward compatibility
     * Maps new payment method names to old enum values where possible
     */
    protected function getPaymentMethodForBackwardCompatibility(): ?string
    {
        if (!$this->paymentMethod?->slug) {
            return null;
        }

        // Map new payment method slugs to old enum values for backward compatibility
        return match($this->paymentMethod->slug) {
            'cash-on-delivery', 'cod' => 'cod',
            'credit-card', 'debit-card', 'card' => 'card',
            'digital-wallet', 'wallet' => 'wallet',
            'bank-transfer', 'bank' => 'bank',
            default => $this->paymentMethod->slug,
        };
    }

    /**
     * Get payment method display
     */
    protected function getPaymentMethodDisplay(): string
    {
        // Use the actual payment method name if available, otherwise fall back to mapping
        if ($this->paymentMethod?->name_en) {
            return $this->paymentMethod->name_en;
        }

        $paymentMethod = $this->getPaymentMethodForBackwardCompatibility();
        return match($paymentMethod) {
            'card' => 'Credit/Debit Card',
            'cod' => 'Cash on Delivery',
            'bank' => 'Bank Transfer',
            'wallet' => 'Digital Wallet',
            default => $paymentMethod ? ucfirst(str_replace('-', ' ', $paymentMethod)) : 'Unknown',
        };
    }
}
