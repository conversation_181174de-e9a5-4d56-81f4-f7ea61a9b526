<?php

namespace App\Http\Requests\Settings;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSettingRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'value' => 'nullable',
            'type' => 'nullable|in:string,boolean,integer,float,json,array,text',
            'category' => 'nullable|string|max:50',
            'display_name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'is_public' => 'nullable|boolean',
            'validation_rules' => 'nullable|array',
            'default_value' => 'nullable',
            'options' => 'nullable|array',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'nullable|boolean',
            'environment' => 'nullable|string|in:development,staging,production',
        ];
    }

    public function messages(): array
    {
        return [
            'type.in' => 'Setting type must be one of: string, boolean, integer, float, json, array, text.',
            'environment.in' => 'Environment must be one of: development, staging, production.',
        ];
    }
}
