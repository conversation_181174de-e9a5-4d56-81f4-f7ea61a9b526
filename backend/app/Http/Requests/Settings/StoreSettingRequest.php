<?php

namespace App\Http\Requests\Settings;

use Illuminate\Foundation\Http\FormRequest;

class StoreSettingRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'key' => 'required|string|max:255|unique:settings,key',
            'value' => 'nullable',
            'type' => 'required|in:string,boolean,integer,float,json,array,text',
            'category' => 'required|string|max:50',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_public' => 'nullable|boolean',
            'validation_rules' => 'nullable|array',
            'default_value' => 'nullable',
            'options' => 'nullable|array',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'nullable|boolean',
            'environment' => 'nullable|string|in:development,staging,production',
        ];
    }

    public function messages(): array
    {
        return [
            'key.required' => 'Setting key is required.',
            'key.unique' => 'Setting key must be unique.',
            'type.required' => 'Setting type is required.',
            'type.in' => 'Setting type must be one of: string, boolean, integer, float, json, array, text.',
            'category.required' => 'Setting category is required.',
            'display_name.required' => 'Display name is required.',
            'environment.in' => 'Environment must be one of: development, staging, production.',
        ];
    }
}
