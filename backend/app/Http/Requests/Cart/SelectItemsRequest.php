<?php

namespace App\Http\Requests\Cart;

use Illuminate\Foundation\Http\FormRequest;

class SelectItemsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'items' => 'required|array|min:1',
            'items.*.item_id' => 'required|integer|exists:cart_items,id',
            'items.*.is_selected' => 'required|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'items.required' => 'The items array is required.',
            'items.array' => 'The items must be an array.',
            'items.min' => 'At least one item must be provided.',
            'items.*.item_id.required' => 'Each item must have an item_id.',
            'items.*.item_id.integer' => 'The item_id must be an integer.',
            'items.*.item_id.exists' => 'The item_id must exist in the cart.',
            'items.*.is_selected.required' => 'Each item must have an is_selected value.',
            'items.*.is_selected.boolean' => 'The is_selected value must be true or false.',
        ];
    }

    /**
     * Get the validated items data.
     */
    public function getItemsData(): array
    {
        return $this->validated()['items'];
    }
}
