<?php

namespace App\Http\Requests\Cart;

use Illuminate\Foundation\Http\FormRequest;

class TokenBasedCartRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'cartId' => 'required|uuid|exists:shopping_carts,uuid',
            'cart_token' => 'nullable|string|max:255', // Optional cart token for guest carts
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'cartId.required' => 'Cart ID is required.',
            'cartId.uuid' => 'Cart ID must be a valid UUID format.',
            'cartId.exists' => 'Cart not found.',
            'cart_token.string' => 'Cart token must be a string.',
            'cart_token.max' => 'Cart token is too long.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'cartId' => $this->route('cartId'),
            'cart_token' => $this->header('X-Cart-Token') ?? $this->input('cart_token'),
        ]);
    }
}