<?php

namespace App\Http\Requests\Cart;

use Illuminate\Foundation\Http\FormRequest;

class ToggleSelectionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'select_all' => 'required|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'select_all.required' => 'The select_all parameter is required.',
            'select_all.boolean' => 'The select_all parameter must be true or false.',
        ];
    }

    /**
     * Get the selection action.
     */
    public function shouldSelectAll(): bool
    {
        return $this->boolean('select_all');
    }
}
