<?php

namespace App\Http\Requests\Cart;

use App\Models\ShoppingCart;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SendCartReminderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled in controller
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'cart_id' => [
                'required',
                'string',
                Rule::exists('shopping_carts', 'uuid')->where(function ($query) {
                    $query->where('status', '!=', 'converted');
                }),
            ],
            'customer_email' => [
                'required',
                'email:rfc,dns',
                'max:255',
            ],
            'customer_name' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'reminder_type' => [
                'nullable',
                'string',
                'in:immediate,scheduled,follow_up',
            ],
            'send_delay_minutes' => [
                'nullable',
                'integer',
                'min:0',
                'max:1440', // Max 24 hours
                'required_if:reminder_type,scheduled',
            ],
            'include_discount' => [
                'nullable',
                'boolean',
            ],
            'discount_percentage' => [
                'nullable',
                'numeric',
                'min:1',
                'max:50',
                'required_if:include_discount,true',
            ],
            'custom_message' => [
                'nullable',
                'string',
                'max:500',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'cart_id.required' => 'Cart ID is required.',
            'cart_id.exists' => 'Cart not found or already converted.',
            'customer_email.required' => 'Customer email is required.',
            'customer_email.email' => 'Please provide a valid email address.',
            'customer_name.regex' => 'Customer name contains invalid characters.',
            'reminder_type.in' => 'Invalid reminder type selected.',
            'send_delay_minutes.max' => 'Send delay cannot exceed 24 hours.',
            'discount_percentage.required_if' => 'Discount percentage is required when including discount.',
            'discount_percentage.max' => 'Discount percentage cannot exceed 50%.',
            'custom_message.max' => 'Custom message cannot exceed 500 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'cart_id' => 'cart',
            'customer_email' => 'email address',
            'customer_name' => 'customer name',
            'reminder_type' => 'reminder type',
            'send_delay_minutes' => 'send delay',
            'include_discount' => 'discount option',
            'discount_percentage' => 'discount percentage',
            'custom_message' => 'custom message',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $this->validateCartEligibility($validator);
            $this->validateReminderFrequency($validator);
            $this->validateDiscountSettings($validator);
        });
    }

    /**
     * Validate that the cart is eligible for recovery reminders.
     */
    protected function validateCartEligibility($validator): void
    {
        if (!$this->cart_id) {
            return;
        }

        $cart = ShoppingCart::where('uuid', $this->cart_id)->first();

        if (!$cart) {
            $validator->errors()->add('cart_id', 'Cart not found.');
            return;
        }

        // Check if cart has items
        if ($cart->items()->count() === 0) {
            $validator->errors()->add('cart_id', 'Cannot send reminder for empty cart.');
        }

        // Check if cart is not too old
        $maxAgeHours = config('cart.max_recovery_age_hours', 168); // 7 days default
        if ($cart->created_at->addHours($maxAgeHours)->isPast()) {
            $validator->errors()->add('cart_id', 'Cart is too old for recovery reminders.');
        }

        // Check if cart is already converted
        if ($cart->status === 'converted') {
            $validator->errors()->add('cart_id', 'Cannot send reminder for converted cart.');
        }
    }

    /**
     * Validate reminder frequency limits.
     */
    protected function validateReminderFrequency($validator): void
    {
        if (!$this->cart_id) {
            return;
        }

        $cart = ShoppingCart::where('uuid', $this->cart_id)->first();
        if (!$cart) {
            return;
        }

        // Check existing abandoned cart records
        $abandonedCart = $cart->abandonedCart()
            ->where('customer_email', $this->customer_email)
            ->where('is_recovered', false)
            ->first();

        if ($abandonedCart) {
            $maxReminders = config('cart.max_recovery_reminders', 3);
            if ($abandonedCart->reminder_count >= $maxReminders) {
                $validator->errors()->add('cart_id', 'Maximum number of reminders already sent for this cart.');
            }

            // Check minimum time between reminders
            $minHours = config('cart.min_hours_between_reminders', 24);
            if ($abandonedCart->last_reminder_sent_at && 
                $abandonedCart->last_reminder_sent_at->addHours($minHours)->isFuture()) {
                $validator->errors()->add('cart_id', "Must wait at least {$minHours} hours between reminders.");
            }
        }
    }

    /**
     * Validate discount settings.
     */
    protected function validateDiscountSettings($validator): void
    {
        if ($this->include_discount && $this->discount_percentage) {
            // Check if discount percentage is reasonable
            $minDiscount = config('cart.min_recovery_discount', 5);
            $maxDiscount = config('cart.max_recovery_discount', 25);

            if ($this->discount_percentage < $minDiscount) {
                $validator->errors()->add('discount_percentage', "Discount must be at least {$minDiscount}%.");
            }

            if ($this->discount_percentage > $maxDiscount) {
                $validator->errors()->add('discount_percentage', "Discount cannot exceed {$maxDiscount}%.");
            }
        }
    }

    /**
     * Get validated data with computed values.
     */
    public function validatedWithComputed(): array
    {
        $validated = $this->validated();

        // Set default values
        $validated['reminder_type'] = $validated['reminder_type'] ?? 'immediate';
        $validated['send_delay_minutes'] = $validated['send_delay_minutes'] ?? 0;
        $validated['include_discount'] = $validated['include_discount'] ?? false;

        // Add computed fields
        $validated['scheduled_send_at'] = $validated['send_delay_minutes'] > 0 
            ? now()->addMinutes($validated['send_delay_minutes'])
            : now();

        return $validated;
    }

    /**
     * Get the cart instance.
     */
    public function getCart(): ?ShoppingCart
    {
        if (!$this->cart_id) {
            return null;
        }

        return ShoppingCart::where('uuid', $this->cart_id)->first();
    }
}
