<?php

namespace App\Http\Requests\Checkout;

use App\Models\Coupon;
use App\Models\ShoppingCart;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ApplyCouponToCheckoutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'coupon_code' => [
                'required',
                'string',
                'max:50',
                Rule::exists('coupons', 'code')->where(function ($query) {
                    $query->where('is_active', true)
                          ->where('start_date', '<=', now())
                          ->where('end_date', '>=', now());
                }),
                function ($attribute, $value, $fail) {
                    $this->validateCouponEligibility($value, $fail);
                },
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'coupon_code.required' => 'Coupon code is required.',
            'coupon_code.exists' => 'Invalid or expired coupon code.',
            'coupon_code.max' => 'Coupon code cannot exceed 50 characters.',
        ];
    }

    /**
     * Validate coupon eligibility for the current cart during checkout.
     */
    protected function validateCouponEligibility(string $couponCode, $fail): void
    {
        $coupon = Coupon::where('code', $couponCode)->first();
        
        if (!$coupon) {
            $fail('Coupon not found.');
            return;
        }

        // Get cart from route parameter
        $cartId = $this->route('cartId');
        $cart = ShoppingCart::where('uuid', $cartId)
                           ->where('user_id', auth()->id())
                           ->first();
        
        if (!$cart) {
            $fail('Cart not found or access denied.');
            return;
        }

        // Check if coupon is already applied
        $appliedCoupons = $cart->applied_coupons ?? [];
        if (in_array($couponCode, array_column($appliedCoupons, 'code'))) {
            $fail('This coupon is already applied to your cart.');
            return;
        }

        // Check usage limits
        if ($coupon->usage_limit && $coupon->usage_count >= $coupon->usage_limit) {
            $fail('This coupon has reached its usage limit.');
            return;
        }

        // Check minimum order value
        if ($coupon->min_order_value && $cart->subtotal < $coupon->min_order_value) {
            $fail("Minimum order value of {$coupon->min_order_value} {$cart->currency} required for this coupon.");
            return;
        }

        // Check vendor-specific coupons
        if ($coupon->vendor_id) {
            $cartHasVendorItems = $cart->items()
                ->where('vendor_id', $coupon->vendor_id)
                ->exists();
                
            if (!$cartHasVendorItems) {
                $fail('This coupon is only valid for specific vendor products not in your cart.');
                return;
            }
        }

        // Check user-specific coupons using new target_user_ids logic
        if (!$coupon->canBeUsedBy(auth()->id())) {
            $fail('This coupon is not valid for your account.');
            return;
        }

        // Check if cart is empty
        if ($cart->items()->count() === 0) {
            $fail('Cannot apply coupon to an empty cart.');
            return;
        }

        // Check if cart is expired
        if ($cart->hasExpired()) {
            $fail('Cannot apply coupon to an expired cart.');
            return;
        }
    }

    /**
     * Get the cart instance for validation.
     */
    public function getCart(): ?ShoppingCart
    {
        $cartId = $this->route('cartId');
        return ShoppingCart::where('uuid', $cartId)
                          ->where('user_id', auth()->id())
                          ->first();
    }
}
