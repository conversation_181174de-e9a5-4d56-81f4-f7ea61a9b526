<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateReturnRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'order_id' => 'required|exists:orders,id',
            'description' => 'nullable|string|max:1000',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.reason_id' => 'required|integer',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.reason' => 'nullable|string|max:255',
            'items.*.attachments' => 'nullable|array',
            'items.*.attachments.*' => 'string',
        ];
    }

    public function messages(): array
    {
        return [
            'order_id.required' => 'Order is required',
            'order_id.exists' => 'Invalid order selected',
            'items.required' => 'At least one item must be selected for return',
            'items.*.product_id.exists' => 'Invalid product selected',
            'items.*.reason_id.required' => 'Reason is required for each item',
            'items.*.quantity.min' => 'Quantity must be at least 1',
        ];
    }
}