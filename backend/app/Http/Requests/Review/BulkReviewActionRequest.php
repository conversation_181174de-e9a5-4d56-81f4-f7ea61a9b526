<?php

namespace App\Http\Requests\Review;

use Illuminate\Foundation\Http\FormRequest;

class BulkReviewActionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'review_ids' => 'required|array|min:1',
            'review_ids.*' => 'required|integer|exists:reviews,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'review_ids.required' => 'Review items are required',
            'review_ids.array' => 'Review items must be an array',
            'review_ids.min' => 'At least one review item is required',
            'review_ids.*.required' => 'Each review item ID is required',
            'review_ids.*.integer' => 'Each review item ID must be an integer',
            'review_ids.*.exists' => 'One or more selected review items do not exist',
        ];
    }
}
