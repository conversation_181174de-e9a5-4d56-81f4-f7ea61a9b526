<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSearchHistoryRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'keyword' => 'required|string|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'keyword.required' => 'Search keyword is required',
            'keyword.max' => 'Search keyword cannot exceed 255 characters'
        ];
    }
}
