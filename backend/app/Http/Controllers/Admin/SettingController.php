<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Settings\StoreSettingRequest;
use App\Http\Requests\Settings\UpdateSettingRequest;
use App\Http\Requests\Settings\BulkUpdateSettingsRequest;
use App\Services\SettingService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SettingController extends Controller
{
    use HelperTrait;

    private $service;

    public function __construct(SettingService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->service->index($request);

            return $this->successResponse($data, 'Settings retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve settings', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSettingRequest $request): JsonResponse
    {
        try {
            $resource = $this->service->store($request);

            return $this->successResponse($resource, 'Setting created successfully!', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create setting', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $key): JsonResponse
    {
        try {
            $resource = $this->service->show($key);

            return $this->successResponse($resource, 'Setting retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve setting', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSettingRequest $request, string $key): JsonResponse
    {
        try {
            $resource = $this->service->update($request, $key);

            return $this->successResponse($resource, 'Setting updated successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to update setting', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $key): JsonResponse
    {
        try {
            $this->service->destroy($key);

            return $this->successResponse(null, 'Setting deleted successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to delete setting', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get settings by category.
     */
    public function getByCategory(string $category): JsonResponse
    {
        try {
            $data = $this->service->getByCategory($category);

            return $this->successResponse($data, 'Category settings retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve category settings', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Bulk update multiple settings.
     */
    public function bulkUpdate(BulkUpdateSettingsRequest $request): JsonResponse
    {
        try {
            $settings = $this->service->bulkUpdate($request->input('settings'));

            return $this->successResponse($settings, 'Settings updated successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to update settings', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get active settings list for dropdowns.
     */
    public function activeList(Request $request): JsonResponse
    {
        try {
            $data = $this->service->activeList($request);

            return $this->successResponse($data, 'Active settings retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve active settings', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
