<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Review\BulkReviewActionRequest;
use App\Services\ReviewService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ReviewModerationController extends Controller
{
    use HelperTrait;

    private $service;

    public function __construct(ReviewService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of all reviews for moderation.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->service->index($request);

            return $this->successResponse($data, 'Reviews retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified review for moderation.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $resource = $this->service->show($id);

            return $this->successResponse($resource, 'Review retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve review', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Approve a single review.
     */
    public function approve(int $id): JsonResponse
    {
        try {
            $results = $this->service->bulkApprove([$id]);

            return $this->successResponse($results, 'Review approved successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to approve review', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Reject a single review.
     */
    public function reject(int $id): JsonResponse
    {
        try {
            $results = $this->service->bulkReject([$id]);

            return $this->successResponse($results, 'Review rejected successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to reject review', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Hide a single review.
     */
    public function hide(int $id): JsonResponse
    {
        try {
            $results = $this->service->bulkHide([$id]);

            return $this->successResponse($results, 'Review hidden successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to hide review', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Show a single review.
     */
    public function showReview(int $id): JsonResponse
    {
        try {
            $results = $this->service->bulkShow([$id]);

            return $this->successResponse($results, 'Review shown successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to show review', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Delete a single review.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $results = $this->service->bulkDelete([$id]);

            return $this->successResponse($results, 'Review deleted successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to delete review', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Bulk approve reviews.
     */
    public function bulkApprove(BulkReviewActionRequest $request): JsonResponse
    {
        try {
            $results = $this->service->bulkApprove($request->review_ids);

            return $this->successResponse($results, 'Reviews approved successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to approve reviews', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Bulk reject reviews.
     */
    public function bulkReject(BulkReviewActionRequest $request): JsonResponse
    {
        try {
            $results = $this->service->bulkReject($request->review_ids);

            return $this->successResponse($results, 'Reviews rejected successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to reject reviews', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Bulk hide reviews.
     */
    public function bulkHide(BulkReviewActionRequest $request): JsonResponse
    {
        try {
            $results = $this->service->bulkHide($request->review_ids);

            return $this->successResponse($results, 'Reviews hidden successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to hide reviews', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Bulk show reviews.
     */
    public function bulkShow(BulkReviewActionRequest $request): JsonResponse
    {
        try {
            $results = $this->service->bulkShow($request->review_ids);

            return $this->successResponse($results, 'Reviews shown successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to show reviews', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Bulk delete reviews.
     */
    public function bulkDelete(BulkReviewActionRequest $request): JsonResponse
    {
        try {
            $results = $this->service->bulkDelete($request->review_ids);

            return $this->successResponse($results, 'Reviews deleted successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to delete reviews', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
