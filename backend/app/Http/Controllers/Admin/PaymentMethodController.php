<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\IsActiveUpdateRequest;
use App\Http\Requests\StorePaymentMethodRequest;
use App\Http\Requests\UpdatePaymentMethodRequest;
use App\Models\PaymentMethod;
use App\Services\PaymentMethodService;
use App\Services\StatusService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PaymentMethodController extends Controller
{
    use HelperTrait;

    private $service;
    private $statusService;

    public function __construct(PaymentMethodService $service, StatusService $statusService)
    {
        $this->service = $service;
        $this->statusService = $statusService;
    }

    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->service->index($request);
            return $this->successResponse($data, 'PaymentMethod data retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function store(StorePaymentMethodRequest $request): JsonResponse
    {
        try {
            $resource = $this->service->store($request);
            return $this->successResponse($resource, 'PaymentMethod created successfully!', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create PaymentMethod', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function show(int $id): JsonResponse
    {
        try {
            $resource = $this->service->show($id);
            return $this->successResponse($resource, 'PaymentMethod retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve PaymentMethod', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function update(UpdatePaymentMethodRequest $request, int $id): JsonResponse
    {
        try {
            $resource = $this->service->update($request, $id);
            return $this->successResponse($resource, 'PaymentMethod updated successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to update PaymentMethod', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function destroy(int $id): JsonResponse
    {
        try {
            $this->service->destroy($id);
            return $this->successResponse(null, 'PaymentMethod deleted successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to delete PaymentMethod', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function activeList(): JsonResponse
    {
        try {
            $data = $this->service->activeList();
            return $this->successResponse($data, 'PaymentMethod list retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve PaymentMethod list', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function statusUpdate(IsActiveUpdateRequest $request, int $id): JsonResponse
    {
        try {
            $model = PaymentMethod::class;
            $resource = $this->statusService->IsActiveUpdate($request, $id, $model);
            return $this->successResponse($resource, 'PaymentMethod status updated successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to update PaymentMethod status', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}