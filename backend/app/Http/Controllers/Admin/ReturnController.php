<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateReturnRequest;
use App\Services\ReturnService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ReturnController extends Controller
{
    use HelperTrait;

    private $service;

    public function __construct(ReturnService $service)
    {
        $this->service = $service;
    }

    /**
     * Get all return requests (Admin)
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $returns = $this->service->adminGetReturns($request);
            return $this->successResponse($returns, 'Returns retrieved successfully', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve returns', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get return request details (Admin)
     */
    public function show(int $id): JsonResponse
    {
        try {
            $return = $this->service->getReturnDetails($id);
            return $this->successResponse($return, 'Return details retrieved successfully', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve return details', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update return request status (Admin)
     */
    public function update(UpdateReturnRequest $request, int $id): JsonResponse
    {

        try {
            $return = $this->service->adminUpdateReturn($request, $id);
            return $this->successResponse($return, 'Return updated successfully', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to update return', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get return statistics (Admin)
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = $this->service->getReturnStatistics();
            return $this->successResponse($stats, 'Return statistics retrieved successfully', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve statistics', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}