<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Vendor;
use App\Notifications\GenericRealtimeNotification;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ExampleTriggerController extends Controller
{
    use HelperTrait;

    public function __construct()
    {
        // Middleware is now handled in routes
    }

    /**
     * Send a user notification example
     */
    public function sendUserNotification(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:1000',
            'category' => 'required|in:order,refund,system,promotion,inventory',
            'action_url' => 'nullable|url',
        ]);

        $user = User::findOrFail($request->user_id);

        $notification = new GenericRealtimeNotification(
            $request->title,
            $request->body,
            $request->category,
            ['triggered_by' => Auth::id(), 'timestamp' => now()->toISOString()],
            $request->action_url,
            'user',
            $user->id
        );

        $user->notify($notification);

        return $this->successResponse(
            ['notification_sent' => true],
            'User notification sent successfully'
        );
    }

    /**
     * Send a vendor notification example
     */
    public function sendVendorNotification(Request $request): JsonResponse
    {
        $request->validate([
            'vendor_id' => 'required|exists:vendors,id',
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:1000',
            'category' => 'required|in:order,refund,system,promotion,inventory',
            'action_url' => 'nullable|url',
        ]);

        $vendor = Vendor::findOrFail($request->vendor_id);

        // Get all users associated with this vendor
        $vendorUsers = User::where('vendor_id', $vendor->id)->get();

        if ($vendorUsers->isEmpty()) {
            return $this->errorResponse('No users found for this vendor', 'Not Found', 404);
        }

        $notification = new GenericRealtimeNotification(
            $request->title,
            $request->body,
            $request->category,
            ['vendor_id' => $vendor->id, 'triggered_by' => Auth::id()],
            $request->action_url,
            'vendor',
            $vendor->id
        );

        // Send to all vendor users
        foreach ($vendorUsers as $user) {
            $user->notify($notification);
        }

        return $this->successResponse(
            ['notification_sent' => true, 'recipients' => $vendorUsers->count()],
            'Vendor notification sent successfully'
        );
    }

    /**
     * Send an admin notification example
     */
    public function sendAdminNotification(Request $request): JsonResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:1000',
            'category' => 'required|in:order,refund,system,promotion,inventory',
            'action_url' => 'nullable|url',
        ]);

        // Get all admin users
        $adminUsers = User::role(['admin', 'super-admin'])->get();

        if ($adminUsers->isEmpty()) {
            return $this->errorResponse('No admin users found', 'Not Found', 404);
        }

        $notification = new GenericRealtimeNotification(
            $request->title,
            $request->body,
            $request->category,
            ['triggered_by' => Auth::id(), 'broadcast_type' => 'admin'],
            $request->action_url,
            'admin',
            0
        );

        // Send to all admin users
        foreach ($adminUsers as $user) {
            $user->notify($notification);
        }

        return $this->successResponse(
            ['notification_sent' => true, 'recipients' => $adminUsers->count()],
            'Admin notification sent successfully'
        );
    }

    /**
     * Simulate order status update notification
     */
    public function simulateOrderUpdate(Request $request): JsonResponse
    {
        $request->validate([
            'order_id' => 'required|integer',
            'user_id' => 'required|exists:users,id',
            'status' => 'required|string|in:confirmed,processing,shipped,delivered,cancelled',
        ]);

        $user = User::findOrFail($request->user_id);
        $orderId = $request->order_id;
        $status = $request->status;

        $statusMessages = [
            'confirmed' => 'Your order has been confirmed and is being prepared.',
            'processing' => 'Your order is currently being processed.',
            'shipped' => 'Great news! Your order has been shipped and is on its way.',
            'delivered' => 'Your order has been delivered successfully.',
            'cancelled' => 'Your order has been cancelled. If you have any questions, please contact support.',
        ];

        $notification = new GenericRealtimeNotification(
            "Order #{$orderId} " . ucfirst($status),
            $statusMessages[$status],
            'order',
            [
                'order_id' => $orderId,
                'status' => $status,
                'updated_at' => now()->toISOString(),
            ],
            "/orders/{$orderId}",
            'user',
            $user->id
        );

        $user->notify($notification);

        return $this->successResponse(
            ['order_notification_sent' => true],
            "Order {$status} notification sent successfully"
        );
    }

    /**
     * Simulate inventory low stock alert
     */
    public function simulateInventoryAlert(Request $request): JsonResponse
    {
        $request->validate([
            'product_name' => 'required|string|max:255',
            'current_stock' => 'required|integer|min:0',
            'threshold' => 'required|integer|min:1',
            'vendor_id' => 'required|exists:vendors,id',
        ]);

        $vendor = Vendor::findOrFail($request->vendor_id);
        $vendorUsers = User::where('vendor_id', $vendor->id)->get();

        if ($vendorUsers->isEmpty()) {
            return $this->errorResponse('No users found for this vendor', 'Not Found', 404);
        }

        $notification = new GenericRealtimeNotification(
            'Low Stock Alert',
            "Product '{$request->product_name}' is running low. Current stock: {$request->current_stock} (threshold: {$request->threshold})",
            'inventory',
            [
                'product_name' => $request->product_name,
                'current_stock' => $request->current_stock,
                'threshold' => $request->threshold,
                'vendor_id' => $vendor->id,
            ],
            '/inventory',
            'vendor',
            $vendor->id
        );

        foreach ($vendorUsers as $user) {
            $user->notify($notification);
        }

        return $this->successResponse(
            ['inventory_alert_sent' => true, 'recipients' => $vendorUsers->count()],
            'Inventory alert sent successfully'
        );
    }

    /**
     * Simulate promotional notification
     */
    public function simulatePromotion(Request $request): JsonResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'discount_percentage' => 'required|integer|min:1|max:100',
            'valid_until' => 'required|date|after:now',
            'target_audience' => 'required|in:all_users,customers,vendors',
        ]);

        $targetUsers = collect();

        switch ($request->target_audience) {
            case 'all_users':
                $targetUsers = User::where('is_active', true)->get();
                break;
            case 'customers':
                $targetUsers = User::role('customer')->where('is_active', true)->get();
                break;
            case 'vendors':
                $targetUsers = User::role(['vendor', 'vendor_assistant'])->where('is_active', true)->get();
                break;
        }

        if ($targetUsers->isEmpty()) {
            return $this->errorResponse('No target users found', 'Not Found', 404);
        }

        $notification = new GenericRealtimeNotification(
            $request->title,
            "Enjoy {$request->discount_percentage}% off! Valid until " . date('M j, Y', strtotime($request->valid_until)),
            'promotion',
            [
                'discount_percentage' => $request->discount_percentage,
                'valid_until' => $request->valid_until,
                'target_audience' => $request->target_audience,
            ],
            '/promotions',
            'user',
            0 // Will be set individually for each user
        );

        foreach ($targetUsers as $user) {
            // Create individual notification for each user
            $userNotification = new GenericRealtimeNotification(
                $request->title,
                "Enjoy {$request->discount_percentage}% off! Valid until " . date('M j, Y', strtotime($request->valid_until)),
                'promotion',
                [
                    'discount_percentage' => $request->discount_percentage,
                    'valid_until' => $request->valid_until,
                    'target_audience' => $request->target_audience,
                ],
                '/promotions',
                'user',
                $user->id
            );

            $user->notify($userNotification);
        }

        return $this->successResponse(
            ['promotion_sent' => true, 'recipients' => $targetUsers->count()],
            'Promotional notification sent successfully'
        );
    }
}
