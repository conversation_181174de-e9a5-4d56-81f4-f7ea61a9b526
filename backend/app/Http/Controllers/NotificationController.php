<?php

namespace App\Http\Controllers;

use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class NotificationController extends Controller
{
    use HelperTrait;

    public function __construct()
    {
        // Middleware is now handled in routes
    }

    /**
     * Get paginated list of notifications for the authenticated user.
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'unread_only' => 'boolean',
            'category' => Rule::in(['order', 'refund', 'system', 'promotion', 'inventory']),
            'per_page' => 'integer|min:1|max:100',
        ]);

        $user = Auth::user();
        $query = $user->notifications();

        // Filter by read status
        if ($request->boolean('unread_only')) {
            $query->whereNull('read_at');
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->whereJsonContains('data->category', $request->category);
        }

        $notifications = $query
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        // Transform the data
        $notifications->getCollection()->transform(function ($notification) {
            return [
                'id' => $notification->id,
                'title' => $notification->data['title'] ?? '',
                'body' => $notification->data['body'] ?? '',
                'meta' => $notification->data['meta'] ?? [],
                'category' => $notification->data['category'] ?? 'system',
                'action_url' => $notification->data['action_url'] ?? null,
                'read_at' => $notification->read_at?->toISOString(),
                'created_at' => $notification->created_at->toISOString(),
            ];
        });

        return $this->successResponse($notifications, 'Notifications retrieved successfully');
    }

    /**
     * Get count of unread notifications.
     */
    public function unreadCount(): JsonResponse
    {
        $user = Auth::user();
        $count = $user->unreadNotifications()->count();

        return $this->successResponse(['count' => $count], 'Unread count retrieved successfully');
    }

    /**
     * Mark a specific notification as read.
     */
    public function markAsRead(string $id): JsonResponse
    {
        $user = Auth::user();
        $notification = $user->notifications()->where('id', $id)->first();

        if (!$notification) {
            return $this->errorResponse('Notification not found', 'Not Found', 404);
        }

        if ($notification->read_at) {
            return $this->successResponse(null, 'Notification already marked as read');
        }

        $notification->markAsRead();

        return $this->successResponse(null, 'Notification marked as read successfully');
    }

    /**
     * Mark all notifications as read for the authenticated user.
     */
    public function markAllAsRead(): JsonResponse
    {
        $user = Auth::user();
        $user->unreadNotifications->markAsRead();

        return $this->successResponse(null, 'All notifications marked as read successfully');
    }

    /**
     * Delete a specific notification.
     */
    public function destroy(string $id): JsonResponse
    {
        $user = Auth::user();
        $notification = $user->notifications()->where('id', $id)->first();

        if (!$notification) {
            return $this->errorResponse('Notification not found', 'Not Found', 404);
        }

        $notification->delete();

        return $this->successResponse(null, 'Notification deleted successfully');
    }
}
