<?php

namespace App\Http\Controllers;

use App\Models\DeviceToken;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class DeviceTokenController extends Controller
{
    use HelperTrait;

    public function __construct()
    {
        // Middleware is now handled in routes
    }

    /**
     * Register or update a device token for push notifications.
     */
    public function register(Request $request): JsonResponse
    {
        $request->validate([
            'device_token' => 'required|string|max:255',
            'platform' => ['required', Rule::in(['ios', 'android', 'web'])],
            'os_id' => 'nullable|string|max:255',
        ]);

        $user = Auth::user();

        // Upsert device token (update if exists, create if not)
        $deviceToken = DeviceToken::updateOrCreate(
            [
                'device_token' => $request->device_token,
            ],
            [
                'user_id' => $user->id,
                'platform' => $request->platform,
                'os_id' => $request->os_id,
            ]
        );

        return $this->successResponse(
            [
                'id' => $deviceToken->id,
                'platform' => $deviceToken->platform,
                'registered_at' => $deviceToken->updated_at->toISOString(),
            ],
            'Device token registered successfully'
        );
    }

    /**
     * Unregister a device token.
     */
    public function unregister(Request $request): JsonResponse
    {
        $request->validate([
            'device_token' => 'required|string|max:255',
        ]);

        $user = Auth::user();

        $deleted = DeviceToken::where('device_token', $request->device_token)
            ->where('user_id', $user->id)
            ->delete();

        if ($deleted) {
            return $this->successResponse(null, 'Device token unregistered successfully');
        }

        return $this->errorResponse('Device token not found', 'Not Found', 404);
    }

    /**
     * Get all registered device tokens for the authenticated user.
     */
    public function index(): JsonResponse
    {
        $user = Auth::user();
        $tokens = $user->deviceTokens()
            ->select(['id', 'platform', 'os_id', 'created_at', 'updated_at'])
            ->orderBy('updated_at', 'desc')
            ->get();

        return $this->successResponse($tokens, 'Device tokens retrieved successfully');
    }
}
