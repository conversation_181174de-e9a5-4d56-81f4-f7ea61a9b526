<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateReturnRequest;
use App\Services\ReturnService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ReturnController extends Controller
{
    use HelperTrait;

    private $service;

    public function __construct(ReturnService $service)
    {
        $this->service = $service;
    }

    /**
     * Get user's return requests
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $returns = $this->service->getUserReturns($request);
            return $this->successResponse($returns, 'Returns retrieved successfully', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve returns', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Create new return request
     */
    public function store(CreateReturnRequest $request): JsonResponse
    {
        try {
            $return = $this->service->createReturn($request);
            return $this->successResponse($return, 'Return request created successfully', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 'Failed to create return request', Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create return request', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get return request details
     */
    public function show(int $id): JsonResponse
    {
        try {
            $return = $this->service->getReturnDetails($id);
            return $this->successResponse($return, 'Return details retrieved successfully', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve return details', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Cancel return request
     */
    public function cancel(int $id): JsonResponse
    {
        try {
            $return = $this->service->cancelReturn($id);
            return $this->successResponse($return, 'Return request cancelled successfully', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to cancel return request', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}