<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Http\Resources\Order\OrderResource;
use App\Http\Resources\Checkout\CheckoutResource;
use App\Http\Resources\Cart\CartResource;
use App\Http\Requests\Checkout\ProcessCheckoutRequest;
use App\Http\Requests\Checkout\ApplyCouponToCheckoutRequest;
use App\Models\PaymentMethod;
use App\Models\ShoppingCart;
use App\Models\UserAddress;
use App\Models\UserCard;
use App\Services\CheckoutService;
use App\Services\CartService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class CheckoutController extends Controller
{
    use HelperTrait;

    protected CheckoutService $checkoutService;
    protected CartService $cartService;

    public function __construct(CheckoutService $checkoutService, CartService $cartService)
    {
        $this->checkoutService = $checkoutService;
        $this->cartService = $cartService;
    }

    /**
     * Get checkout summary including cart, addresses, and payment methods
     */
    public function getCheckoutSummary(string $cartId): JsonResponse
    {
        try {
            $cart = ShoppingCart::where('uuid', $cartId)
                              ->where('user_id', auth()->id())
                              ->firstOrFail();

            $summary = $this->checkoutService->getCheckoutSummary($cart);

            return $this->successResponse(
                $summary,
                'Checkout summary retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to get checkout summary',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Consolidated checkout initialization - combines cart validation, addresses, and payment methods
     */
    public function initializeCheckout(string $cartId): JsonResponse
    {
        try {
            $cart = ShoppingCart::where('uuid', $cartId)
                              ->where('user_id', auth()->id())
                              ->firstOrFail();

            $checkoutData = $this->checkoutService->initializeCheckout($cart);

            return $this->successResponse(
                $checkoutData,
                'Checkout initialized successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to initialize checkout',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get user addresses for checkout
     */
    public function getUserAddresses(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'type' => 'nullable|string|in:shipping,billing'
            ]);

            $addresses = $this->checkoutService->getUserAddresses(
                auth()->user(),
                $request->input('type')
            );

            return $this->successResponse(
                $addresses,
                'User addresses retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get user payment methods for checkout
     */
    public function getUserPaymentMethods(): JsonResponse
    {
        try {
            $paymentMethods = $this->checkoutService->getUserPaymentMethods(auth()->user());

            return $this->successResponse(
                $paymentMethods,
                'User payment methods retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Create new address during checkout
     */
    public function createCheckoutAddress(Request $request): JsonResponse
    {
        try {
            $address = $this->checkoutService->createCheckoutAddress(
                auth()->user(),
                $request->all()
            );

            return $this->successResponse(
                $address,
                'Address created successfully!',
                Response::HTTP_CREATED
            );
        } catch (ValidationException $e) {
            return $this->errorResponse(
                $e->errors(),
                'Validation failed',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to create address',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get available payment method types for checkout with enhanced information
     */
    public function getAvailablePaymentMethods(): JsonResponse
    {
        try {
            $paymentMethods = $this->checkoutService->getEnhancedPaymentMethods();

            return $this->successResponse(
                $paymentMethods,
                'Available payment methods retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Enhanced payment method selection with validation
     */
    public function selectPaymentMethodEnhanced(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'payment_method_id' => 'required|integer|exists:payment_methods,id',
                'user_card_id' => 'nullable|integer|exists:user_cards,id',
                'save_for_future' => 'nullable|boolean',
                'payment_method_data' => 'nullable|array',
            ]);

            $result = $this->checkoutService->selectPaymentMethod(
                auth()->user(),
                $request->all()
            );

            return $this->successResponse(
                $result,
                'Payment method selected successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to select payment method',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Validate checkout data
     */
    public function validateCheckout(string $cartId, Request $request): JsonResponse
    {
        try {
            $cart = ShoppingCart::where('uuid', $cartId)
                              ->where('user_id', auth()->id())
                              ->firstOrFail();

            $validation = $this->checkoutService->validateCheckoutData($cart, $request->all());

            return $this->successResponse(
                $validation,
                'Checkout validation completed!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to validate checkout',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Apply coupon during checkout with enhanced validation
     */
    public function applyCouponToCheckout(string $cartId, ApplyCouponToCheckoutRequest $request): JsonResponse
    {
        try {
            $cart = $request->getCart();

            if (!$cart) {
                return $this->errorResponse(
                    'Cart not found or access denied',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $this->cartService->applyCoupon($cart, $request->coupon_code);

            // Return complete cart data for consistent frontend implementation
            $updatedCart = $this->cartService->getCartForApiResponse($cart->uuid);

            return $this->successResponse(
                new CartResource($updatedCart),
                'Coupon applied successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to apply coupon',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Remove coupon during checkout
     */
    public function removeCouponFromCheckout(string $cartId, Request $request): JsonResponse
    {
        try {
            $request->validate([
                'coupon_code' => 'required|string|max:50',
            ]);

            $cart = ShoppingCart::where('uuid', $cartId)
                              ->where('user_id', auth()->id())
                              ->firstOrFail();

            $this->cartService->removeCoupon($cart, $request->coupon_code);

            // Return complete cart data for consistent frontend implementation
            $updatedCart = $this->cartService->getCartForApiResponse($cartId);

            return $this->successResponse(
                new CartResource($updatedCart),
                'Coupon removed successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to remove coupon',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Process complete checkout and create order with enhanced validation
     */
    public function processCheckout(string $cartId, ProcessCheckoutRequest $request): JsonResponse
    {
        try {
            $cart = ShoppingCart::where('uuid', $cartId)
                              ->where('user_id', auth()->id())
                              ->firstOrFail();

            $order = $this->checkoutService->processCheckout($cart, $request->validatedWithComputed());

            return $this->successResponse(
                new OrderResource($order->load(['items', 'addresses', 'statusHistories'])),
                'Order created successfully!',
                Response::HTTP_CREATED
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to process checkout',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Select existing address for checkout
     */
    public function selectAddress(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'address_id' => 'required|integer|exists:user_addresses,id',
                'address_type' => 'required|string|in:shipping,billing'
            ]);

            $address = UserAddress::where('id', $request->input('address_id'))
                                 ->where('user_id', auth()->id())
                                 ->firstOrFail();

            return $this->successResponse(
                [
                    'address' => $address,
                    'type' => $request->input('address_type'),
                    'selected' => true
                ],
                'Address selected successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to select address',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Select existing payment method for checkout
     */
    public function selectPaymentMethod(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'payment_method_id' => 'required|integer|exists:user_cards,id'
            ]);

            $paymentMethod = UserCard::where('id', $request->input('payment_method_id'))
                                   ->where('user_id', auth()->id())
                                   ->where('is_active', true)
                                   ->firstOrFail();

            return $this->successResponse(
                [
                    'payment_method' => $paymentMethod,
                    'selected' => true
                ],
                'Payment method selected successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to select payment method',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
