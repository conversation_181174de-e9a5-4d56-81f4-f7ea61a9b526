<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Http\Requests\Cart\SendCartReminderRequest;
use App\Http\Resources\Cart\CartResource;
use App\Jobs\SendAbandonedCartReminderJob;
use App\Models\AbandonedCart;
use App\Models\ShoppingCart;
use App\Services\CartRecoveryService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CartRecoveryController extends Controller
{
    use HelperTrait;

    protected CartRecoveryService $recoveryService;

    public function __construct(CartRecoveryService $recoveryService)
    {
        $this->recoveryService = $recoveryService;
    }

    /**
     * Send cart recovery reminder email.
     */
    public function sendReminder(SendCartReminderRequest $request): JsonResponse
    {
        try {
            $cart = ShoppingCart::where('uuid', $request->cart_id)
                ->with(['items.product', 'items.vendor'])
                ->first();

            if (!$cart) {
                return $this->errorResponse(
                    'Cart not found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            // Check if cart is eligible for recovery
            if (!$this->recoveryService->isEligibleForRecovery($cart)) {
                return $this->errorResponse(
                    'Cart is not eligible for recovery',
                    'Recovery not available',
                    Response::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            // Create or update abandoned cart record
            $abandonedCart = $this->recoveryService->createAbandonedCartRecord($cart, $request->validated());

            // Send recovery email
            SendAbandonedCartReminderJob::dispatch($abandonedCart);

            return $this->successResponse(
                [
                    'recovery_token' => $abandonedCart->recovery_token,
                    'expires_at' => $abandonedCart->recovery_expires_at,
                    'reminder_sent' => true,
                ],
                'Recovery reminder sent successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            Log::error('Cart recovery reminder failed', [
                'cart_id' => $request->cart_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                $e->getMessage(),
                'Failed to send recovery reminder',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            Log::error('Cart recovery reminder system error', [
                'cart_id' => $request->cart_id ?? 'unknown',
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'An unexpected error occurred',
                'System error',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Recover cart using recovery token.
     */
    public function recoverCart(string $token): JsonResponse
    {
        try {
            $abandonedCart = AbandonedCart::where('recovery_token', $token)
                ->where('recovery_expires_at', '>', now())
                ->where('is_recovered', false)
                ->with(['cart.items.product', 'cart.items.vendor'])
                ->first();

            if (!$abandonedCart) {
                return $this->errorResponse(
                    'Invalid or expired recovery token',
                    'Recovery failed',
                    Response::HTTP_NOT_FOUND
                );
            }

            // Validate cart is still recoverable
            $cart = $abandonedCart->cart;
            if (!$cart || !$this->recoveryService->isCartStillValid($cart)) {
                return $this->errorResponse(
                    'Cart is no longer available for recovery',
                    'Recovery failed',
                    Response::HTTP_GONE
                );
            }

            // Recover the cart
            $recoveredCart = $this->recoveryService->recoverCart($abandonedCart);

            return $this->successResponse(
                [
                    'cart' => new CartResource($recoveredCart),
                    'recovery_details' => [
                        'recovered_at' => now()->toISOString(),
                        'items_recovered' => $recoveredCart->items->count(),
                        'total_value' => $recoveredCart->total_amount,
                    ],
                ],
                'Cart recovered successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            Log::error('Cart recovery failed', [
                'token' => $token,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                $e->getMessage(),
                'Failed to recover cart',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            Log::error('Cart recovery system error', [
                'token' => $token,
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'An unexpected error occurred',
                'System error',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Convert abandoned cart (mark as converted after successful checkout).
     */
    public function convertAbandonedCart(string $token, Request $request): JsonResponse
    {
        try {
            $request->validate([
                'order_id' => 'required|string|max:255',
                'conversion_value' => 'required|numeric|min:0',
            ]);

            $abandonedCart = AbandonedCart::where('recovery_token', $token)
                ->where('is_recovered', true)
                ->where('is_converted', false)
                ->first();

            if (!$abandonedCart) {
                return $this->errorResponse(
                    'Invalid token or cart not recovered',
                    'Conversion failed',
                    Response::HTTP_NOT_FOUND
                );
            }

            // Mark as converted
            $this->recoveryService->markAsConverted($abandonedCart, $request->validated());

            return $this->successResponse(
                [
                    'converted_at' => $abandonedCart->fresh()->converted_at,
                    'order_id' => $request->order_id,
                    'conversion_value' => $request->conversion_value,
                ],
                'Cart conversion tracked successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            Log::error('Cart conversion tracking failed', [
                'token' => $token,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                $e->getMessage(),
                'Failed to track conversion',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            Log::error('Cart conversion system error', [
                'token' => $token,
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'An unexpected error occurred',
                'System error',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get cart recovery statistics.
     */
    public function getRecoveryStatistics(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'period' => 'nullable|string|in:7d,30d,90d,1y',
                'vendor_id' => 'nullable|integer|exists:vendors,id',
            ]);

            $statistics = $this->recoveryService->getRecoveryStatistics(
                $request->period ?? '30d',
                $request->vendor_id
            );

            return $this->successResponse(
                $statistics,
                'Recovery statistics retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to retrieve statistics',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                'An unexpected error occurred',
                'System error',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
