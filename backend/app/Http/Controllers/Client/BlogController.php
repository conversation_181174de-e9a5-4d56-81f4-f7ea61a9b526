<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Services\ClientBlogService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class BlogController extends Controller
{
    use HelperTrait;

    private $service;

    public function __construct(ClientBlogService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of published blogs.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->service->index($request);

            return $this->successResponse($data, 'Blog data retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified blog by slug with related articles included.
     */
    public function show(string $slug, Request $request): JsonResponse
    {
        try {
            $resource = $this->service->show($slug, $request);

            return $this->successResponse($resource, 'Blog retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve Blog', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get blogs by category slug.
     */
    public function getByCategory(string $categorySlug, Request $request): JsonResponse
    {
        try {
            $data = $this->service->getByCategory($categorySlug, $request);

            return $this->successResponse($data, 'Category blogs retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve category blogs', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get featured blogs.
     */
    public function getFeatured(Request $request): JsonResponse
    {
        try {
            $data = $this->service->getFeatured($request);

            return $this->successResponse($data, 'Featured blogs retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve featured blogs', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Search blogs.
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $data = $this->service->search($request);

            return $this->successResponse($data, 'Blog search results retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to search blogs', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get related blogs for a specific blog.
     *
     * @deprecated This endpoint is deprecated. Related articles are now included
     *             in the main blog show endpoint response. Use GET /blogs/{slug} instead.
     */
    public function getRelated(string $slug, Request $request): JsonResponse
    {
        try {
            // For backward compatibility, return only the related articles from the main show response
            $blogData = $this->service->show($slug, $request);
            $relatedArticles = $blogData['related_articles'] ?? [];

            return $this->successResponse(
                ['data' => $relatedArticles],
                'Related blogs retrieved successfully! Note: This endpoint is deprecated. Use GET /blogs/{slug} for complete blog data including related articles.',
                Response::HTTP_OK
            );
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve related blogs', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get categories with article counts for sidebar navigation.
     */
    public function getCategories(Request $request): JsonResponse
    {
        try {
            $data = $this->service->getCategories($request);

            return $this->successResponse($data, 'Categories retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve categories', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get popular blogs based on view count.
     */
    public function getPopular(Request $request): JsonResponse
    {
        try {
            $data = $this->service->getPopular($request);

            return $this->successResponse($data, 'Popular blogs retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve popular blogs', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get blog statistics for navigation.
     */
    public function getStats(Request $request): JsonResponse
    {
        try {
            $data = $this->service->getStats($request);

            return $this->successResponse($data, 'Blog statistics retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve blog statistics', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get featured authors.
     */
    public function getAuthors(Request $request): JsonResponse
    {
        try {
            $data = $this->service->getAuthors($request);

            return $this->successResponse($data, 'Authors retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve authors', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
