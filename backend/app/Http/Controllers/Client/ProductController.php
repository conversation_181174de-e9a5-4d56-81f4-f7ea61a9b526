<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Services\ProductService;
use App\Traits\HelperTrait;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ProductController extends Controller
{
    use HelperTrait;

    private $service;

    public function __construct(
        ProductService $productService

    ) {
        $this->service =  $productService;
    }

    public function viewProduct(Request $request,$id)

    {
        try {
            $resource = $this->service->viewProduct($request,$id);

            return $this->successResponse($resource, 'View successfully!', Response::HTTP_CREATED);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create product View', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
