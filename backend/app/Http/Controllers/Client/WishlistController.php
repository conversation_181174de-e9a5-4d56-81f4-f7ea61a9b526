<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Http\Requests\Wishlist\StoreWishlistRequest;
use App\Http\Requests\Wishlist\BulkWishlistActionRequest;
use App\Services\WishlistService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class WishlistController extends Controller
{
    use HelperTrait;

    private $service;

    public function __construct(WishlistService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->service->index($request);

            return $this->successResponse($data, 'Wishlist data retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreWishlistRequest $request): JsonResponse
    {
        try {
            $resource = $this->service->store($request);

            return $this->successResponse($resource, 'Item added to wishlist successfully!', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to add item to wishlist', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $resource = $this->service->show($id);

            return $this->successResponse($resource, 'Wishlist item retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve wishlist item', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->service->destroy($id);

            return $this->successResponse(null, 'Item removed from wishlist successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to remove item from wishlist', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Move wishlist item to cart.
     */
    public function moveToCart(int $id): JsonResponse
    {
        try {
            $cartItem = $this->service->moveToCart($id, auth()->id());

            return $this->successResponse($cartItem, 'Item moved to cart successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to move item to cart', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Move multiple wishlist items to cart.
     */
    public function bulkMoveToCart(BulkWishlistActionRequest $request): JsonResponse
    {
        try {
            $results = $this->service->bulkMoveToCart($request->wishlist_ids, auth()->id());

            return $this->successResponse($results, 'Bulk move to cart completed!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to move items to cart', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove multiple items from wishlist.
     */
    public function bulkDelete(BulkWishlistActionRequest $request): JsonResponse
    {
        try {
            $results = $this->service->bulkDelete($request->wishlist_ids, auth()->id());

            return $this->successResponse($results, 'Items removed from wishlist successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to remove items from wishlist', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
