<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Services\SettingService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class SettingController extends Controller
{
    use HelperTrait;

    private $service;

    public function __construct(SettingService $service)
    {
        $this->service = $service;
    }

    /**
     * Get all public settings.
     */
    public function getPublicSettings(): JsonResponse
    {
        try {
            $data = $this->service->getPublicSettings();

            return $this->successResponse($data, 'Public settings retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve public settings', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get public settings by category.
     */
    public function getPublicByCategory(string $category): JsonResponse
    {
        try {
            $data = $this->service->getPublicByCategory($category);

            return $this->successResponse($data, 'Public category settings retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve public category settings', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
