<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreSearchHistoryRequest;
use App\Services\SearchHistoryService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SearchHistoryController extends Controller
{
    use HelperTrait;

    private $service;

    public function __construct(SearchHistoryService $service)
    {
        $this->service = $service;
    }

    /**
     * Store search history
     */
    public function store(StoreSearchHistoryRequest $request): JsonResponse
    {
        try {
            $this->service->store($request);
            return $this->successResponse(null, 'Search history saved', Response::HTTP_CREATED);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to save search history', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get user search history
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $limit = $request->input('limit', 10);
            $histories = $this->service->getUserHistory($request, $limit);
            return $this->successResponse($histories, 'Search history retrieved', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to get search history', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Clear user search history
     */
    public function clear(Request $request): JsonResponse
    {
        try {
            $this->service->clearUserHistory($request);
            return $this->successResponse(null, 'Search history cleared', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to clear search history', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

}