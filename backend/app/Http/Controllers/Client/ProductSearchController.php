<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

use function Laravel\Prompts\error;

class ProductSearchController extends Controller
{
    use HelperTrait;

    /**
     * Replace the manual search with Scout search
     */
    private function applySearch($query, Request $request): void
    {
        $searchValue = $request->input('search');

        if ($searchValue) {
            // Use Scout search
            $searchResults = Product::search($searchValue)->get();
            $searchIds = $searchResults->pluck('id')->toArray();

            if (!empty($searchIds)) {
                $query->whereIn('id', $searchIds);
            } else {
                // If no Scout results, return empty result
                $query->whereRaw('1 = 0');
            }
        }
    }

    /**
     * Search products using Scout
     */
    public function searchProducts(Request $request): JsonResponse
    {
        try {
            $searchValue = $request->input('search', '');
            $limit = $request->input('limit', 20);

            if (empty($searchValue)) {
                return $this->successResponse([], 'Search query is required', Response::HTTP_OK);
            }

            // Direct Scout search
            $products = Product::search($searchValue)
                ->where('is_active', true)
                ->take($limit)
                ->get();

            return $this->successResponse($products, 'Products found successfully', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Search failed', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }


    /**
     * Get product title suggestions for search bar
     */
    public function getProductSuggestions(Request $request): JsonResponse
    {
        try {
            $query = $request->input('q', '');

            if (strlen($query) < 2) {
                return $this->errorResponse('Query must be at least 2 characters', 'Invalid query', Response::HTTP_BAD_REQUEST);
            }

            $products = Product::search($query)
                // ->where('is_active', true)
                ->take(50)
                ->get()
                ->load('productMedia');

            // Extract keywords from titles
            $keywords = $this->extractKeywords($products, $query);

            // Get product details
            $productDetails = $products
                ->unique('title_en')
                ->take(5)
                ->map(function ($product) {
                    return [
                        'id' => $product->id,
                        'uuid' => $product->uuid,
                        'title_en' => $product->title_en,
                        'title_ar' => $product->title_ar,
                        'image' => $product->main_image_url,
                        'regular_price' =>  $product->regular_price,
                        'offer_price' => $product->offer_price,

                    ];
                })
                ->values()
                ->toArray();

            $result = [
                'keywords' => $keywords,
                'products' => $productDetails
            ];

            return $this->successResponse($result, 'Suggestions retrieved successfully', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return  $this->errorResponse($th->getMessage(), 'Failed to retrieve suggestions', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Extract keywords from search results
     */
    private function extractKeywords($products, $query): array
    {
        $keywords = [];
        $query = strtolower($query);

        foreach ($products as $product) {
            $words = explode(' ', strtolower($product->title_en ?? ''));

            foreach ($words as $word) {
                $word = trim($word);
                if (strlen($word) > 2 && str_contains($word, $query) && !in_array($word, $keywords)) {
                    $keywords[] = $word;
                }
            }
        }

        return array_slice($keywords, 0, 5);
    }
}
