<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreBlogCommentRequest;
use App\Http\Requests\UpdateBlogCommentRequest;
use App\Services\BlogCommentService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class BlogCommentController extends Controller
{
    use HelperTrait;

    private $service;

    public function __construct(BlogCommentService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of comments for a specific blog.
     */
    public function index(int $blogId, Request $request): JsonResponse
    {
        try {
            $data = $this->service->getByBlog($blogId, $request);

            return $this->successResponse($data, 'Blog comments retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Store a newly created comment.
     */
    public function store(StoreBlogCommentRequest $request): JsonResponse
    {
        try {
            $resource = $this->service->store($request);

            return $this->successResponse($resource, 'Comment created successfully!', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        }
         catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create comment', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified comment.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $resource = $this->service->show($id);

            return $this->successResponse($resource, 'Comment retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve comment', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update the specified comment.
     */
    public function update(UpdateBlogCommentRequest $request, int $id): JsonResponse
    {
        try {
            $resource = $this->service->update($request, $id);

            return $this->successResponse($resource, 'Comment updated successfully!', Response::HTTP_OK);
        }catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        }
         catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to update comment', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified comment.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->service->destroy($id);

            return $this->successResponse(null, 'Comment deleted successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to delete comment', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Reply to a comment.
     */
    public function reply(StoreBlogCommentRequest $request): JsonResponse
    {
        try {
            $resource = $this->service->reply($request);

            return $this->successResponse($resource, 'Reply created successfully!', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        }
         catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create reply', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get user's own comments.
     */
    public function myComments(Request $request): JsonResponse
    {
        try {
            $data = $this->service->getUserComments($request);

            return $this->successResponse($data, 'User comments retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve user comments', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
