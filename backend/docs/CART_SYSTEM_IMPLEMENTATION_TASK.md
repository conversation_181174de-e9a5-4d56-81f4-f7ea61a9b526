# Shopping Cart System Implementation Task

## Project Overview
**Platform:** Vitamins.ae Multi-Vendor E-Commerce  
**Framework:** <PERSON><PERSON> 12  
**Database:** PostgreSQL with Redis caching  
**Implementation Type:** Advanced Shopping Cart with Multi-Vendor Support

---

## 1. Database Analysis

### 1.1 Existing Relevant Tables
Based on the current schema, we have:
- `users` - User authentication and basic info
- `customers` - Customer-specific data with loyalty points
- `products` - Product catalog with variants
- `vendors` - Vendor information and management
- `orders` and `order_items` - Order processing system
- `wishlists` - Customer wishlist functionality
- `abandoned_carts` - Basic cart abandonment tracking

### 1.2 Missing Cart Infrastructure
The following cart-related tables are missing:
- **Shopping Carts** - Main cart container
- **Cart Items** - Individual cart line items
- **Cart Sessions** - Guest user cart management
- **Cart Rules** - Discount and promotion rules
- **Cart Reservations** - Inventory hold during checkout

---

## 2. Required Database Changes

### 2.1 New Migration Files

#### Cart Tables Migration
```sql
-- Shopping Carts Table
CREATE TABLE shopping_carts (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE NOT NULL DEFAULT gen_random_uuid(),
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    session_id VARCHAR(255),
    currency VARCHAR(3) DEFAULT 'AED',
    subtotal DECIMAL(10,2) DEFAULT 0.00,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Cart Items Table
CREATE TABLE cart_items (
    id BIGSERIAL PRIMARY KEY,
    cart_id BIGINT REFERENCES shopping_carts(id) ON DELETE CASCADE,
    product_id BIGINT REFERENCES products(id) ON DELETE CASCADE,
    vendor_id BIGINT REFERENCES vendors(id) ON DELETE CASCADE,
    variant_id BIGINT,
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    product_snapshot JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Cart Sessions Table (for guest users)
CREATE TABLE cart_sessions (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    cart_data JSONB NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Cart Reservations Table (inventory hold)
CREATE TABLE cart_reservations (
    id BIGSERIAL PRIMARY KEY,
    cart_item_id BIGINT REFERENCES cart_items(id) ON DELETE CASCADE,
    product_id BIGINT REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL,
    reserved_until TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2.2 Schema Modifications
```sql
-- Add cart-related fields to existing tables
ALTER TABLE products ADD COLUMN max_cart_quantity INTEGER DEFAULT NULL;
ALTER TABLE products ADD COLUMN min_cart_quantity INTEGER DEFAULT 1;
ALTER TABLE products ADD COLUMN cart_increment INTEGER DEFAULT 1;

-- Add cart tracking to customers
ALTER TABLE customers ADD COLUMN cart_abandonment_count INTEGER DEFAULT 0;
ALTER TABLE customers ADD COLUMN last_cart_activity TIMESTAMP;

-- Update abandoned_carts table structure
ALTER TABLE abandoned_carts ADD COLUMN cart_id BIGINT REFERENCES shopping_carts(id);
ALTER TABLE abandoned_carts ADD COLUMN recovery_token VARCHAR(255);
ALTER TABLE abandoned_carts ADD COLUMN recovery_expires_at TIMESTAMP;
```

---

## 3. API Endpoints Specification

### 3.1 Cart Management Endpoints
```php
// routes/client.php additions

Route::prefix('cart')->group(function () {
    // Guest cart operations
    Route::post('/', [CartController::class, 'createCart']);
    Route::get('/{cartId}', [CartController::class, 'getCart']);
    Route::delete('/{cartId}', [CartController::class, 'clearCart']);
    
    // Cart item operations
    Route::post('/{cartId}/items', [CartController::class, 'addItem']);
    Route::put('/{cartId}/items/{itemId}', [CartController::class, 'updateItem']);
    Route::delete('/{cartId}/items/{itemId}', [CartController::class, 'removeItem']);
    Route::post('/{cartId}/items/bulk', [CartController::class, 'bulkUpdateItems']);
    
    // Cart calculations
    Route::post('/{cartId}/calculate', [CartController::class, 'recalculateCart']);
    Route::post('/{cartId}/apply-coupon', [CartController::class, 'applyCoupon']);
    Route::delete('/{cartId}/remove-coupon', [CartController::class, 'removeCoupon']);
    
    // Multi-vendor operations
    Route::get('/{cartId}/vendors', [CartController::class, 'getVendorSplit']);
    Route::post('/{cartId}/merge', [CartController::class, 'mergeCarts']);
    
    // Cart validation
    Route::post('/{cartId}/validate', [CartController::class, 'validateCart']);
    Route::post('/{cartId}/reserve', [CartController::class, 'reserveInventory']);
});

// Authenticated user cart operations
Route::middleware('auth:api')->prefix('my-cart')->group(function () {
    Route::get('/', [UserCartController::class, 'getCurrentCart']);
    Route::post('/migrate', [UserCartController::class, 'migrateGuestCart']);
    Route::get('/history', [UserCartController::class, 'getCartHistory']);
    Route::post('/save-for-later', [UserCartController::class, 'saveForLater']);
    Route::get('/saved-items', [UserCartController::class, 'getSavedItems']);
});
```

### 3.2 Cart Recovery Endpoints
```php
Route::prefix('cart-recovery')->group(function () {
    Route::post('/send-reminder', [CartRecoveryController::class, 'sendReminder']);
    Route::get('/recover/{token}', [CartRecoveryController::class, 'recoverCart']);
    Route::post('/convert/{token}', [CartRecoveryController::class, 'convertAbandonedCart']);
});
```

---

## 4. Multi-Vendor Cart Logic

### 4.1 Cart Splitting Strategy
```php
// Automatic vendor-based cart organization
class VendorCartSplitter
{
    public function splitCartByVendor(ShoppingCart $cart): array
    {
        return $cart->items->groupBy('vendor_id')->map(function ($items, $vendorId) {
            return [
                'vendor_id' => $vendorId,
                'vendor_name' => $items->first()->vendor->name,
                'items' => $items,
                'subtotal' => $items->sum('total_price'),
                'shipping_required' => $this->requiresShipping($items),
                'estimated_delivery' => $this->calculateDeliveryTime($vendorId)
            ];
        });
    }
}
```

### 4.2 Vendor-Specific Operations
- **Separate Shipping Calculations** per vendor
- **Individual Vendor Policies** (min order, shipping rules)
- **Vendor-Specific Discounts** and promotions
- **Independent Checkout Flows** for each vendor group

---

## 5. Implementation Tasks

### 5.1 Priority 1 (Core Infrastructure)
1. **Create Migration Files**
   - `create_shopping_carts_table.php`
   - `create_cart_items_table.php`
   - `create_cart_sessions_table.php`
   - `create_cart_reservations_table.php`
   - `modify_existing_tables_for_cart.php`

2. **Create Eloquent Models**
   - `ShoppingCart.php` with relationships
   - `CartItem.php` with product snapshot
   - `CartSession.php` for guest management
   - `CartReservation.php` for inventory holds

3. **Core Cart Service**
   - `CartService.php` - Main cart operations
   - `CartCalculationService.php` - Price calculations
   - `CartValidationService.php` - Business rule validation

### 5.2 Priority 2 (API Controllers)
4. **Create Controllers**
   - `CartController.php` - Guest cart operations
   - `UserCartController.php` - Authenticated user carts
   - `CartRecoveryController.php` - Abandonment recovery

5. **Request Validation**
   - `AddToCartRequest.php`
   - `UpdateCartItemRequest.php`
   - `ApplyCouponRequest.php`

### 5.3 Priority 3 (Advanced Features)
6. **Multi-Vendor Logic**
   - `VendorCartSplitter.php`
   - `VendorShippingCalculator.php`
   - `MultiVendorCheckoutService.php`

7. **Cart Recovery System**
   - `AbandonedCartDetector.php`
   - `CartRecoveryEmailService.php`
   - Recovery email templates

### 5.4 Priority 4 (Optimization)
8. **Caching Layer**
   - Redis cart caching
   - Cart calculation caching
   - Session management optimization

9. **Background Jobs**
   - `ProcessAbandonedCartJob.php`
   - `CleanExpiredCartsJob.php`
   - `SendCartReminderJob.php`

---

## 6. Integration Points

### 6.1 Product System Integration
- **Product Availability Checking** before adding to cart
- **Price Synchronization** with product updates
- **Variant Management** for configurable products
- **Inventory Reservation** during checkout process

### 6.2 User System Integration
- **Guest to Registered Migration** of cart contents
- **Customer Loyalty Points** calculation in cart
- **User Preferences** for cart behavior
- **Address Integration** for shipping calculations

### 6.3 Order System Integration
- **Cart to Order Conversion** workflow
- **Multi-Vendor Order Splitting** from cart
- **Payment Integration** with cart totals
- **Order Confirmation** and cart cleanup

---

## 7. Business Rules

### 7.1 Cart Validation Rules
```php
class CartValidationRules
{
    // Product availability
    public function validateProductAvailability(CartItem $item): bool;
    
    // Quantity constraints
    public function validateQuantityLimits(CartItem $item): bool;
    
    // Vendor-specific rules
    public function validateVendorRules(array $vendorItems): bool;
    
    // Cart value limits
    public function validateCartLimits(ShoppingCart $cart): bool;
}
```

### 7.2 Pricing Calculation Rules
- **Base Price** from product catalog
- **Quantity Discounts** based on volume
- **Vendor Promotions** and coupon codes
- **Tax Calculations** based on customer location
- **Shipping Costs** per vendor group

### 7.3 Session Management
- **Guest Cart Expiry**: 7 days of inactivity
- **Registered User Carts**: 30 days persistence
- **Cart Migration**: Automatic on user login
- **Session Cleanup**: Daily job for expired carts

---

## 8. Technical Requirements

### 8.1 Required Models
```php
// app/Models/ShoppingCart.php
class ShoppingCart extends Model
{
    protected $fillable = ['user_id', 'session_id', 'currency', 'expires_at'];
    protected $casts = ['expires_at' => 'datetime'];
    
    public function items(): HasMany;
    public function user(): BelongsTo;
    public function reservations(): HasMany;
}

// app/Models/CartItem.php
class CartItem extends Model
{
    protected $fillable = ['cart_id', 'product_id', 'vendor_id', 'quantity', 'unit_price'];
    protected $casts = ['product_snapshot' => 'array'];
    
    public function cart(): BelongsTo;
    public function product(): BelongsTo;
    public function vendor(): BelongsTo;
}
```

### 8.2 Required Services
```php
// app/Services/CartService.php
class CartService
{
    public function createCart(array $data): ShoppingCart;
    public function addItem(ShoppingCart $cart, array $itemData): CartItem;
    public function updateItem(CartItem $item, array $data): CartItem;
    public function removeItem(CartItem $item): bool;
    public function calculateTotals(ShoppingCart $cart): array;
}

// app/Services/CartCalculationService.php
class CartCalculationService
{
    public function calculateSubtotal(Collection $items): float;
    public function calculateTax(ShoppingCart $cart): float;
    public function calculateShipping(array $vendorGroups): array;
    public function applyDiscounts(ShoppingCart $cart, array $coupons): float;
}
```

### 8.3 Required Middleware
```php
// app/Http/Middleware/CartSessionMiddleware.php
class CartSessionMiddleware
{
    // Manage cart sessions for guest users
    // Handle cart migration on user authentication
    // Validate cart ownership and permissions
}
```

### 8.4 Background Jobs
```php
// app/Jobs/ProcessAbandonedCartJob.php
class ProcessAbandonedCartJob implements ShouldQueue
{
    // Detect abandoned carts
    // Send recovery emails
    // Update abandonment statistics
}

// app/Jobs/CleanExpiredCartsJob.php
class CleanExpiredCartsJob implements ShouldQueue
{
    // Remove expired guest carts
    // Clean up cart reservations
    // Archive old cart data
}
```

---

## 9. Implementation Timeline

### Week 1: Foundation
- Database migrations and models
- Basic cart service implementation
- Core API endpoints (CRUD operations)

### Week 2: Business Logic
- Cart calculation service
- Validation rules implementation
- Multi-vendor splitting logic

### Week 3: Advanced Features
- Cart recovery system
- Background job implementation
- Caching layer integration

### Week 4: Testing & Optimization
- Unit and integration tests
- Performance optimization
- API documentation updates

---

## 10. Testing Requirements

### 10.1 Unit Tests
- Cart model relationships and methods
- Service class business logic
- Validation rule implementations
- Calculation accuracy tests

### 10.2 Integration Tests
- API endpoint functionality
- Multi-vendor cart splitting
- Cart migration workflows
- Payment integration flows

### 10.3 Performance Tests
- Cart loading performance
- Calculation speed optimization
- Concurrent user cart operations
- Database query optimization

---

## 11. Documentation Requirements

### 11.1 API Documentation
- Swagger/OpenAPI specifications
- Request/response examples
- Error code documentation
- Rate limiting information

### 11.2 Developer Documentation
- Service class usage examples
- Integration guidelines
- Customization options
- Troubleshooting guide

---

This implementation task provides a comprehensive roadmap for building an advanced shopping cart system that integrates seamlessly with the existing vitamins.ae platform architecture and supports the multi-vendor business model.