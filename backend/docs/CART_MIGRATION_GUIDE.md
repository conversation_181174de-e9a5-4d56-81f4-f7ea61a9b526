# 🔄 Cart Migration Guide

## Overview
This guide explains how to handle cart migration when a user with a guest cart logs into their account.

## 🚀 Complete Migration Flow

### JavaScript/React Example:

```javascript
// 1. Store guest cart info before login
const guestCartInfo = {
  cartId: localStorage.getItem('guest_cart_id'),
  cartToken: localStorage.getItem('guest_cart_token')
};

// 2. User logs in successfully
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email, password })
});

const { access_token } = await loginResponse.json();

// 3. Store JWT token
localStorage.setItem('auth_token', access_token);

// 4. Migrate guest cart if exists
if (guestCartInfo.cartId && guestCartInfo.cartToken) {
  await migrateGuestCart(access_token, guestCartInfo);
}

// Migration function
async function migrateGuestCart(authToken, guestCart) {
  try {
    const response = await fetch('/api/client/my-cart/migrate', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        guest_cart_id: guestCart.cartId,
        guest_cart_token: guestCart.cartToken,
        merge_strategy: 'combine' // or 'replace', 'keep_user'
      })
    });

    if (response.ok) {
      const result = await response.json();
      console.log('Cart migrated successfully:', result.data);
      
      // Clear guest cart info from storage
      localStorage.removeItem('guest_cart_id');
      localStorage.removeItem('guest_cart_token');
      
      // Update UI with merged cart
      updateCartUI(result.data.migrated_cart);
      
      return result.data;
    } else {
      const error = await response.json();
      console.error('Migration failed:', error);
      
      // Handle migration failure
      handleMigrationError(error);
    }
  } catch (error) {
    console.error('Migration request failed:', error);
  }
}
```

## 📋 API Response Examples

### Successful Migration Response:
```json
{
  "success": true,
  "message": "Guest cart migrated successfully!",
  "data": {
    "migrated_cart": {
      "id": "user-cart-uuid",
      "user_id": "user-uuid",
      "status": "active",
      "currency": "USD",
      "items": [
        {
          "id": "item-uuid-1",
          "product_id": "product-uuid-1",
          "quantity": 2,
          "unit_price": 29.99,
          "total_price": 59.98,
          "source": "guest_cart"
        },
        {
          "id": "item-uuid-2", 
          "product_id": "product-uuid-2",
          "quantity": 1,
          "unit_price": 49.99,
          "total_price": 49.99,
          "source": "user_cart"
        }
      ],
      "totals": {
        "subtotal": 109.97,
        "tax_amount": 8.80,
        "discount_amount": 0,
        "shipping_amount": 9.99,
        "total_amount": 128.76
      }
    },
    "migration_summary": {
      "items_migrated": 3,
      "items_merged": 1,
      "items_replaced": 0,
      "guest_cart_cleared": true
    }
  }
}
```

### Migration Error Response:
```json
{
  "success": false,
  "message": "No guest cart found to migrate",
  "error": "Migration failed",
  "data": {
    "error_code": "CART_001",
    "details": "Guest cart not found or already migrated"
  }
}
```

## 🔧 Merge Strategy Details

### 1. "combine" Strategy (Recommended)
- Adds guest cart items to existing user cart
- If same product exists in both carts, quantities are combined
- Preserves all items from both carts

**Example:**
```
Guest Cart: Product A (qty: 2), Product B (qty: 1)
User Cart:  Product A (qty: 1), Product C (qty: 3)
Result:     Product A (qty: 3), Product B (qty: 1), Product C (qty: 3)
```

### 2. "replace" Strategy
- Replaces user cart entirely with guest cart items
- User's existing cart items are lost
- Use when guest cart is more important

**Example:**
```
Guest Cart: Product A (qty: 2), Product B (qty: 1)
User Cart:  Product C (qty: 3), Product D (qty: 1)
Result:     Product A (qty: 2), Product B (qty: 1)
```

### 3. "keep_user" Strategy
- Keeps user cart unchanged
- Guest cart is discarded
- Use when user cart should take priority

**Example:**
```
Guest Cart: Product A (qty: 2), Product B (qty: 1)
User Cart:  Product C (qty: 3), Product D (qty: 1)
Result:     Product C (qty: 3), Product D (qty: 1)
```

## 🎯 Best Practices

### 1. Always Check for Guest Cart
```javascript
// Check if user has guest cart before login
const hasGuestCart = localStorage.getItem('guest_cart_id') && 
                    localStorage.getItem('guest_cart_token');

if (hasGuestCart) {
  // Show migration options to user
  showMigrationDialog();
}
```

### 2. Handle Migration Errors Gracefully
```javascript
function handleMigrationError(error) {
  switch (error.data?.error_code) {
    case 'CART_001':
      // Guest cart not found - already migrated or expired
      clearGuestCartStorage();
      break;
    case 'CART_002':
      // Invalid token - cart may be compromised
      clearGuestCartStorage();
      showSecurityWarning();
      break;
    default:
      // Generic error - retry or manual intervention
      showRetryOption();
  }
}
```

### 3. Provide User Choice (Optional)
```javascript
// Let user choose merge strategy
function showMigrationDialog() {
  const strategy = confirm(
    "You have items in your cart from before logging in. " +
    "Would you like to combine them with your saved cart items?"
  ) ? 'combine' : 'keep_user';
  
  return strategy;
}
```

### 4. Update UI After Migration
```javascript
function updateCartUI(migratedCart) {
  // Update cart counter
  document.getElementById('cart-count').textContent = migratedCart.items.length;
  
  // Update cart total
  document.getElementById('cart-total').textContent = 
    `$${migratedCart.totals.total_amount}`;
  
  // Refresh cart dropdown/page
  refreshCartDisplay(migratedCart);
  
  // Show success message
  showNotification('Cart items merged successfully!');
}
```

## 🔄 Alternative: Automatic Migration

For seamless UX, you can migrate automatically without user intervention:

```javascript
// Automatic migration on login
async function handleLogin(credentials) {
  try {
    // 1. Login user
    const loginResponse = await login(credentials);
    const { access_token } = loginResponse;
    
    // 2. Auto-migrate guest cart
    const guestCartInfo = getGuestCartInfo();
    if (guestCartInfo.cartId) {
      await migrateGuestCart(access_token, guestCartInfo, 'combine');
      showNotification('Your cart items have been merged!');
    }
    
    // 3. Redirect to dashboard or cart
    window.location.href = '/dashboard';
    
  } catch (error) {
    console.error('Login or migration failed:', error);
  }
}
```

## 📱 Mobile App Considerations

For mobile apps, store cart info in secure storage:

```javascript
// React Native example
import AsyncStorage from '@react-native-async-storage/async-storage';

// Store guest cart info
await AsyncStorage.setItem('guest_cart_id', cartId);
await AsyncStorage.setItem('guest_cart_token', cartToken);

// Retrieve for migration
const guestCartId = await AsyncStorage.getItem('guest_cart_id');
const guestCartToken = await AsyncStorage.getItem('guest_cart_token');

// Clear after migration
await AsyncStorage.multiRemove(['guest_cart_id', 'guest_cart_token']);
```

## ⚠️ Important Notes

1. **Security**: Always validate guest cart token before migration
2. **Cleanup**: Clear guest cart storage after successful migration
3. **Error Handling**: Handle cases where guest cart no longer exists
4. **UX**: Consider showing migration progress to user
5. **Testing**: Test all merge strategies thoroughly
6. **Performance**: Migration should be fast to avoid blocking login flow

## 🧪 Testing Migration

```bash
# Test migration API
curl -X POST http://localhost:8000/api/client/my-cart/migrate \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "guest_cart_id": "guest-cart-uuid",
    "guest_cart_token": "guest-cart-token", 
    "merge_strategy": "combine"
  }'
```
