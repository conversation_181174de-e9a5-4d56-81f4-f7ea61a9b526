# 🛒 CLEAN CART API ROUTES (Token-Based)

This document provides the cleaned-up cart routes without session-based middleware, focusing on token-based authentication for both guest and authenticated users.

## 🔧 Updated Route Configuration

Replace the existing cart routes in `routes/client.php` with the following clean implementation:

```php
<?php

// ============================================================================
// GUEST CART APIs - Token-based authentication
// ============================================================================

Route::middleware(['cart.rate_limit'])->prefix('cart')->group(function () {
    // Core cart operations
    Route::post('/', [CartController::class, 'createCart']);
    Route::get('/{cartId}', [CartController::class, 'getCart']);
    Route::delete('/{cartId}', [CartController::class, 'clearCart']);

    // Cart item management
    Route::get('/{cartId}/items', [CartController::class, 'getCartItems']);
    Route::post('/{cartId}/items', [CartController::class, 'addItem']);
    Route::put('/{cartId}/items/{itemId}', [CartController::class, 'updateItem']);
    Route::delete('/{cartId}/items/{itemId}', [CartController::class, 'removeItem']);
    Route::post('/{cartId}/items/bulk', [CartController::class, 'bulkUpdateItems']);

    // Coupon management
    Route::post('/{cartId}/apply-coupon', [CartController::class, 'applyCoupon']);
    Route::delete('/{cartId}/remove-coupon', [CartController::class, 'removeCoupon']);

    // Multi-vendor operations
    Route::get('/{cartId}/vendors', [CartController::class, 'getVendorSplit']);
    Route::post('/{cartId}/merge', [CartController::class, 'mergeCarts']);

    // Cart validation and inventory
    Route::post('/{cartId}/validate', [CartController::class, 'validateCart']);
    Route::get('/{cartId}/inventory-availability', [CartController::class, 'getInventoryAvailability']);

    // Inventory reservation system
    Route::post('/{cartId}/reserve', [CartController::class, 'reserveInventory']);
    Route::delete('/{cartId}/reservations', [CartController::class, 'releaseReservations']);
    Route::patch('/{cartId}/reservations/extend', [CartController::class, 'extendReservations']);

    // Token management (optional - for enhanced security)
    Route::post('/{cartId}/rotate-token', [CartController::class, 'rotateToken']);
});

// ============================================================================
// AUTHENTICATED USER CART APIs - JWT-based authentication
// ============================================================================

Route::middleware(['auth:api'])->prefix('my-cart')->group(function () {
    // User cart operations
    Route::get('/', [UserCartController::class, 'getCurrentCart']);
    Route::delete('/clear', [UserCartController::class, 'clearCurrentCart']);

    // Cart migration and history
    Route::post('/migrate', [UserCartController::class, 'migrateGuestCart']);
    Route::get('/history', [UserCartController::class, 'getCartHistory']);

    // Save for later functionality
    Route::post('/save-for-later', [UserCartController::class, 'saveForLater']);
    Route::get('/saved-items', [UserCartController::class, 'getSavedItems']);
    Route::post('/saved-items/{savedItemId}/move-to-cart', [UserCartController::class, 'moveToCart']);
    Route::delete('/saved-items/{savedItemId}', [UserCartController::class, 'removeSavedItem']);

    // User analytics
    Route::get('/statistics', [UserCartController::class, 'getCartStatistics']);
});

// ============================================================================
// CART RECOVERY APIs - Rate limited for security
// ============================================================================

Route::middleware(['cart.rate_limit:recovery'])->prefix('cart-recovery')->group(function () {
    Route::post('/send-reminder', [CartRecoveryController::class, 'sendReminder']);
    Route::get('/recover/{token}', [CartRecoveryController::class, 'recoverCart']);
    Route::post('/convert/{token}', [CartRecoveryController::class, 'convertAbandonedCart']);
    Route::get('/statistics', [CartRecoveryController::class, 'getRecoveryStatistics']);
});
```

## 🔐 Authentication Methods

### Guest Cart Authentication
All guest cart operations use **token-based authentication**:

**Header Method (Recommended):**
```
X-Cart-Token: sha256-hashed-secure-token
```

**Body Method (Fallback):**
```json
{
  "cart_token": "sha256-hashed-secure-token",
  "other_data": "..."
}
```

### Authenticated User Authentication
All user cart operations use **JWT Bearer tokens**:

```
Authorization: Bearer your-jwt-token
```

## 🚫 Removed Dependencies

The following middleware and dependencies have been removed:

1. **`cart.session` middleware** - No longer needed with token-based auth
2. **Session-based cart tracking** - Replaced with secure tokens
3. **Session ID validation** - Replaced with token validation
4. **Automatic session management** - Handled by token lifecycle

## 🔧 Required Controller Updates

### CartController Updates Needed:

1. **Remove session-based validation**
2. **Implement token-based authentication in all methods**
3. **Update `canAccessCart()` method to use only token validation**
4. **Remove session ID dependencies**

### UserCartController Updates Needed:

1. **Remove cart.session middleware dependency**
2. **Ensure all methods use `auth:api` middleware only**
3. **Update cart migration to handle token-based guest carts**

## 📋 Implementation Checklist

- [ ] Update route definitions in `routes/client.php`
- [ ] Remove `cart.session` middleware from cart routes
- [ ] Update CartController to use token-based auth only
- [ ] Update UserCartController to remove session dependencies
- [ ] Test all guest cart operations with X-Cart-Token header
- [ ] Test all user cart operations with JWT tokens
- [ ] Update frontend to use token-based authentication
- [ ] Remove session-based cart tracking code
- [ ] Update API documentation

## 🧪 Testing Commands

### Test Guest Cart APIs:
```bash
# Create cart
curl -X POST http://localhost:8000/api/client/cart \
  -H "Content-Type: application/json" \
  -d '{"currency": "USD"}'

# Get cart with token
curl -X GET http://localhost:8000/api/client/cart/{cartId} \
  -H "X-Cart-Token: your-cart-token"

# Add item to cart
curl -X POST http://localhost:8000/api/client/cart/{cartId}/items \
  -H "X-Cart-Token: your-cart-token" \
  -H "Content-Type: application/json" \
  -d '{"product_id": "product-uuid", "quantity": 2}'
```

### Test User Cart APIs:
```bash
# Get user cart
curl -X GET http://localhost:8000/api/client/my-cart \
  -H "Authorization: Bearer your-jwt-token"

# Migrate guest cart
curl -X POST http://localhost:8000/api/client/my-cart/migrate \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"guest_cart_id": "cart-uuid", "guest_cart_token": "cart-token"}'
```

## 🔄 Migration Strategy

1. **Phase 1**: Update routes and remove session middleware
2. **Phase 2**: Update controllers to use token-based auth
3. **Phase 3**: Update frontend to send X-Cart-Token headers
4. **Phase 4**: Remove session-based cart tracking code
5. **Phase 5**: Test and validate all cart operations

## 📝 Notes

- All cart tokens expire after 72 hours
- Rate limiting is still applied to prevent abuse
- Cart recovery functionality remains unchanged
- Multi-vendor support is maintained
- Inventory reservation system is preserved
