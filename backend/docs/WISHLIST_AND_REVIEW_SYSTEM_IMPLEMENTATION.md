# Wishlist Management and Review & Rating System Implementation

## Project Overview

This document outlines the comprehensive task breakdown for implementing two key modules in the multi-vendor eCommerce platform:

1. **Wishlist Management Module** - User wishlist creation, management, sharing, and persistence
2. **Review & Rating System Module** - Product reviews, ratings, moderation, and vendor responses

## Current Project Context

### Existing Infrastructure
- **Framework**: Laravel with API-first architecture
- **Authentication**: Laravel Passport (API tokens)
- **Database**: MySQL with comprehensive migrations
- **Architecture**: Multi-vendor platform with User, Vendor, Product, and Cart systems
- **Existing Models**: User, Product, Vendor, ShoppingCart, CartItem, Customer
- **Middleware**: CartSessionMiddleware for session management
- **Services**: CartService, ProductService, UserService patterns

### Database Foundation
- **Wishlist Migration**: `2025_05_14_054908_create_wishlists_table.php` (exists)
- **Review Migration**: `2025_04_28_101329_create_reviews_table.php` (exists)
- **Missing**: Wishlist and Review model files need to be created

## 🏗️ **CRITICAL: Architectural Patterns and Conventions**

**⚠️ MANDATORY REQUIREMENT**: All new controllers and services MUST follow the established patterns used in this Laravel project. Use `app/Http/Controllers/Admin/BannerController.php` as the reference pattern.

### Required Controller Structure Pattern

**Reference Controller**: `app/Http/Controllers/Admin/BannerController.php`

#### 1. **Controller Class Structure**
```php
<?php

namespace App\Http\Controllers\[Admin|Client];

use App\Http\Controllers\Controller;
use App\Http\Requests\[RequestClasses];
use App\Services\[ServiceClass];
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class [ControllerName] extends Controller
{
    use HelperTrait;

    private $service;

    public function __construct([ServiceClass] $service)
    {
        $this->service = $service;
    }

    // Methods follow established patterns...
}
```

#### 2. **Method Implementation Patterns**

**Index Method Pattern**:
```php
public function index(Request $request): JsonResponse
{
    try {
        $data = $this->service->index($request);

        return $this->successResponse($data, '[Resource] data retrieved successfully!', Response::HTTP_OK);
    } catch (\Throwable $th) {
        return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
```

**Store Method Pattern**:
```php
public function store(Store[Resource]Request $request): JsonResponse
{
    try {
        $resource = $this->service->store($request);

        return $this->successResponse($resource, '[Resource] created successfully!', Response::HTTP_CREATED);
    } catch (\Exception $e) {
        return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
    } catch (\Throwable $th) {
        return $this->errorResponse($th->getMessage(), 'Failed to create [Resource]', Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
```

**Show Method Pattern**:
```php
public function show(int $id): JsonResponse
{
    try {
        $resource = $this->service->show($id);

        return $this->successResponse($resource, '[Resource] retrieved successfully!', Response::HTTP_OK);
    } catch (\Throwable $th) {
        return $this->errorResponse($th->getMessage(), 'Failed to retrieve [Resource]', Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
```

**Update Method Pattern**:
```php
public function update(Update[Resource]Request $request, int $id): JsonResponse
{
    try {
        $resource = $this->service->update($request, $id);

        return $this->successResponse($resource, '[Resource] updated successfully!', Response::HTTP_OK);
    } catch (\Exception $e) {
        return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
    } catch (\Throwable $th) {
        return $this->errorResponse($th->getMessage(), 'Failed to update [Resource]', Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
```

**Destroy Method Pattern**:
```php
public function destroy(int $id): JsonResponse
{
    try {
        $this->service->destroy($id);

        return $this->successResponse(null, '[Resource] deleted successfully!', Response::HTTP_OK);
    } catch (\Throwable $th) {
        return $this->errorResponse($th->getMessage(), 'Failed to delete [Resource]', Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
```

#### 3. **Service Class Structure Pattern**

**Reference Service**: `app/Services/BannerService.php`

```php
<?php

namespace App\Services;

use App\Models\[ModelName];
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class [ServiceName]
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = [ModelName]::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Filtering
        $filters = ['field' => '=']; // Define filters
        $this->applyFilters($query, $request, $filters);

        // Searching
        $searchKeys = ['field1', 'field2']; // Define search fields
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepare[ModelName]Data($request);
        return [ModelName]::create($data);
    }

    public function show(int $id): [ModelName]
    {
        return [ModelName]::with(['relationships'])->findOrFail($id);
    }

    public function update($request, int $id)
    {
        $model = [ModelName]::findOrFail($id);
        $updateData = $this->prepare[ModelName]Data($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $model->update($updateData);
        return $model;
    }

    public function destroy(int $id): bool
    {
        $model = [ModelName]::findOrFail($id);
        return $model->delete();
    }

    private function prepare[ModelName]Data($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        $fillable = (new [ModelName]())->getFillable();
        $data = array_intersect_key($validated, array_flip($fillable));

        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }
}
```

#### 4. **Request Validation Pattern**

**Reference Requests**: `StoreBannerRequest.php`, `UpdateBannerRequest.php`

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class Store[Resource]Request extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'field' => 'required|string|max:255',
            'optional_field' => 'nullable|string',
            'boolean_field' => 'nullable|in:0,1',
        ];
    }
}
```

#### 5. **Response Formatting (HelperTrait)**

**Success Response**:
```php
return $this->successResponse($data, $message, Response::HTTP_OK);
```

**Error Response**:
```php
return $this->errorResponse($errors, $message, $statusCode);
```

#### 6. **HTTP Status Codes**
- `Response::HTTP_OK` (200) - Successful GET, PUT, DELETE
- `Response::HTTP_CREATED` (201) - Successful POST
- `Response::HTTP_UNPROCESSABLE_ENTITY` (422) - Validation errors
- `Response::HTTP_INTERNAL_SERVER_ERROR` (500) - Server errors
- `Response::HTTP_NOT_FOUND` (404) - Resource not found

### 🎯 **Implementation Requirements**

1. **Before implementing any controller**, examine `BannerController.php` structure
2. **All controllers** must use the exact same patterns for:
   - Constructor dependency injection
   - Method signatures and return types
   - Try-catch error handling structure
   - Response method usage from HelperTrait
   - HTTP status code consistency
3. **All services** must follow the BannerService pattern:
   - Use HelperTrait methods for filtering, sorting, searching
   - Implement prepare[Model]Data methods
   - Use findOrFail for single resource retrieval
   - Follow the same method signatures
4. **All request classes** must follow the established validation patterns
5. **Maintain naming conventions** exactly as shown in existing code

## Module 1: Wishlist Management System

### 1.1 Complete saveForLater Method in UserCartController

**Priority**: HIGH - Immediate Implementation Required

**Current Status**: Method exists but incomplete (lines 137-203 in UserCartController.php)

**Task Details**:
- **File**: `app/Http/Controllers/Client/UserCartController.php`
- **Issue**: Wishlist model creation is commented out (line 173)
- **Requirements**: 
  - Create Wishlist model
  - Implement proper wishlist item creation
  - Handle duplicate prevention
  - Add proper error handling
  - Update response structure

**Implementation Steps**:
1. Create Wishlist model with relationships
2. Update saveForLater method to use Wishlist model
3. Add validation for duplicate wishlist items
4. Implement proper cart item removal
5. Add comprehensive error handling

### 1.2 Create Wishlist Model and Relationships

**Priority**: HIGH

**Task Details**:
- **File**: `app/Models/Wishlist.php`
- **Database**: Uses existing `wishlists` table migration
- **Relationships**:
  - `belongsTo(User::class)`
  - `belongsTo(Product::class)`
  - `belongsTo(ProductVariant::class)` (nullable)
  - `belongsTo(Vendor::class)` (nullable)

**Model Features**:
- Fillable fields: user_id, product_id, product_variant_id, vendor_id, note
- Timestamps support
- Unique constraint handling (user_id + product_id + variant_id)
- Soft deletes consideration

### 1.3 Create WishlistController

**Priority**: HIGH

**Task Details**:
- **File**: `app/Http/Controllers/Client/WishlistController.php`
- **Pattern**: MUST follow `BannerController.php` structure exactly
- **Middleware**: `auth:api` for all methods
- **Required Structure**:
  - Use HelperTrait
  - Private $service property
  - Constructor with WishlistService injection
  - All methods must follow BannerController patterns
- **Methods Required**:
  - `index(Request $request): JsonResponse` - Get user's wishlist with pagination
  - `store(StoreWishlistRequest $request): JsonResponse` - Add item to wishlist
  - `destroy(int $id): JsonResponse` - Remove item from wishlist
  - `moveToCart(int $id): JsonResponse` - Move wishlist item to cart
  - `bulkMoveToCart(BulkWishlistActionRequest $request): JsonResponse` - Move multiple items to cart
  - `bulkDelete(BulkWishlistActionRequest $request): JsonResponse` - Remove multiple items
- **Error Handling**: Use exact try-catch patterns from BannerController
- **Responses**: Use successResponse/errorResponse from HelperTrait with proper HTTP status codes

### 1.4 Create WishlistService

**Priority**: MEDIUM

**Task Details**:
- **File**: `app/Services/WishlistService.php`
- **Pattern**: MUST follow `BannerService.php` structure exactly
- **Required Structure**:
  - Use HelperTrait
  - Implement standard CRUD methods with exact signatures
  - Use applySorting, applyFilters, applySearch, paginateOrGet methods
  - Implement prepareWishlistData method
- **Methods Required**:
  - `index($request): Collection|LengthAwarePaginator|array`
  - `store(Request $request)`
  - `show(int $id): Wishlist`
  - `update($request, int $id)`
  - `destroy(int $id): bool`
  - `moveToCart(int $id, int $userId)`
  - `bulkMoveToCart(array $ids, int $userId)`
  - `bulkDelete(array $ids, int $userId)`
- **Business Logic**:
  - Duplicate prevention (user_id + product_id + variant_id uniqueness)
  - Cart integration via CartService
  - Inventory validation before moving to cart
  - User authorization checks

### 1.5 Create Wishlist API Resources

**Priority**: MEDIUM

**Task Details**:
- **Files**: 
  - `app/Http/Resources/Wishlist/WishlistResource.php`
  - `app/Http/Resources/Wishlist/WishlistItemResource.php`
- **Features**:
  - Product information formatting
  - Price display with member pricing
  - Availability status
  - Vendor information
  - Variant details

### 1.6 Create Wishlist Request Validation

**Priority**: MEDIUM

**Task Details**:
- **Files**:
  - `app/Http/Requests/Wishlist/StoreWishlistRequest.php`
  - `app/Http/Requests/Wishlist/BulkWishlistActionRequest.php`
- **Pattern**: MUST follow `StoreBannerRequest.php` and `UpdateBannerRequest.php` patterns exactly
- **Required Structure**:
  - Extend FormRequest
  - `authorize(): bool` method returning true
  - `rules(): array` method with validation rules
- **StoreWishlistRequest Validation Rules**:
  - `product_id` => 'required|exists:products,id'
  - `product_variant_id` => 'nullable|exists:product_variants,id'
  - `note` => 'nullable|string|max:500'
- **BulkWishlistActionRequest Validation Rules**:
  - `wishlist_ids` => 'required|array|min:1'
  - `wishlist_ids.*` => 'required|integer|exists:wishlists,id'
- **Authorization**: Implement user ownership validation in authorize() method

### 1.7 Add Wishlist Routes

**Priority**: MEDIUM

**Task Details**:
- **File**: `routes/client.php`
- **Route Group**: `Route::middleware(['auth:api'])->prefix('wishlist')`
- **Routes**:
  - `GET /` - List wishlist items
  - `POST /` - Add to wishlist
  - `DELETE /{id}` - Remove from wishlist
  - `POST /move-to-cart` - Move to cart
  - `POST /bulk-move-to-cart` - Bulk move to cart
  - `DELETE /bulk-delete` - Bulk delete

### 1.8 Extend User Model with Wishlist Relationship

**Priority**: LOW

**Task Details**:
- **File**: `app/Models/User.php`
- **Addition**: `public function wishlists() { return $this->hasMany(Wishlist::class); }`

## Module 2: Review & Rating System

### 2.1 Create Review Model

**Priority**: HIGH

**Task Details**:
- **File**: `app/Models/Review.php`
- **Database**: Uses existing `reviews` table migration
- **Relationships**:
  - `belongsTo(User::class)` - Reviewer
  - `belongsTo(Product::class)` - Reviewed product
  - `belongsTo(Vendor::class)` - Reviewed vendor
  - `belongsTo(Order::class)` - Optional order reference
- **Features**:
  - Rating validation (1-5 stars)
  - Moderation status
  - Soft deletes for content management

### 2.2 Create ReviewController

**Priority**: HIGH

**Task Details**:
- **File**: `app/Http/Controllers/Client/ReviewController.php`
- **Pattern**: MUST follow `BannerController.php` structure exactly
- **Required Structure**:
  - Use HelperTrait
  - Private $service property
  - Constructor with ReviewService injection
  - All methods must follow BannerController patterns
- **Methods Required**:
  - `index(Request $request): JsonResponse` - List user's reviews with filtering
  - `store(StoreReviewRequest $request): JsonResponse` - Submit new review
  - `show(int $id): JsonResponse` - Get specific review
  - `update(UpdateReviewRequest $request, int $id): JsonResponse` - Update own review
  - `destroy(int $id): JsonResponse` - Delete own review
  - `getProductReviews(int $productId, Request $request): JsonResponse` - Get reviews for specific product
  - `getVendorReviews(int $vendorId, Request $request): JsonResponse` - Get reviews for specific vendor
- **Error Handling**: Use exact try-catch patterns from BannerController
- **Responses**: Use successResponse/errorResponse from HelperTrait with proper HTTP status codes
- **Authorization**: Ensure users can only modify their own reviews

### 2.3 Create Review Admin Controller

**Priority**: MEDIUM

**Task Details**:
- **File**: `app/Http/Controllers/Admin/ReviewModerationController.php`
- **Pattern**: MUST follow `BannerController.php` structure exactly
- **Required Structure**:
  - Use HelperTrait
  - Private $service property
  - Constructor with ReviewService injection
  - All methods must follow BannerController patterns
- **Methods Required**:
  - `index(Request $request): JsonResponse` - List all reviews for moderation
  - `approve(int $id): JsonResponse` - Approve review
  - `reject(int $id): JsonResponse` - Reject review
  - `bulkApprove(BulkReviewActionRequest $request): JsonResponse` - Bulk approve reviews
  - `bulkReject(BulkReviewActionRequest $request): JsonResponse` - Bulk reject reviews
  - `toggleVisibility(int $id): JsonResponse` - Toggle review visibility
- **Error Handling**: Use exact try-catch patterns from BannerController
- **Responses**: Use successResponse/errorResponse from HelperTrait with proper HTTP status codes
- **Authorization**: Admin-only access with proper middleware

### 2.4 Create ReviewService

**Priority**: MEDIUM

**Task Details**:
- **File**: `app/Services/ReviewService.php`
- **Pattern**: MUST follow `BannerService.php` structure exactly
- **Required Structure**:
  - Use HelperTrait
  - Implement standard CRUD methods with exact signatures
  - Use applySorting, applyFilters, applySearch, paginateOrGet methods
  - Implement prepareReviewData method
- **Methods Required**:
  - `index($request): Collection|LengthAwarePaginator|array`
  - `store(Request $request)`
  - `show(int $id): Review`
  - `update($request, int $id)`
  - `destroy(int $id): bool`
  - `approve(int $id): Review`
  - `reject(int $id): Review`
  - `bulkApprove(array $ids): array`
  - `bulkReject(array $ids): array`
  - `toggleVisibility(int $id): Review`
  - `getProductReviews(int $productId, $request)`
  - `getVendorReviews(int $vendorId, $request)`
- **Business Logic**:
  - Duplicate review prevention (user can only review once per product)
  - Rating calculations and caching
  - Moderation workflow management
  - User authorization checks

### 2.5 Create Review API Resources

**Priority**: MEDIUM

**Task Details**:
- **Files**:
  - `app/Http/Resources/Review/ReviewResource.php`
  - `app/Http/Resources/Review/ReviewSummaryResource.php`
- **Features**:
  - User information (with privacy controls)
  - Rating display
  - Review content formatting
  - Moderation status
  - Timestamps formatting

### 2.6 Create Review Request Validation

**Priority**: MEDIUM

**Task Details**:
- **Files**:
  - `app/Http/Requests/Review/StoreReviewRequest.php`
  - `app/Http/Requests/Review/UpdateReviewRequest.php`
  - `app/Http/Requests/Review/BulkReviewActionRequest.php`
- **Pattern**: MUST follow `StoreBannerRequest.php` and `UpdateBannerRequest.php` patterns exactly
- **Required Structure**:
  - Extend FormRequest
  - `authorize(): bool` method with proper authorization logic
  - `rules(): array` method with validation rules
- **StoreReviewRequest Validation Rules**:
  - `product_id` => 'nullable|exists:products,id'
  - `vendor_id` => 'nullable|exists:vendors,id'
  - `order_id` => 'nullable|exists:orders,id'
  - `rating` => 'required|integer|min:1|max:5'
  - `comment` => 'nullable|string|max:1000'
- **UpdateReviewRequest Validation Rules**:
  - Same as store but with route parameter handling for uniqueness
- **BulkReviewActionRequest Validation Rules**:
  - `review_ids` => 'required|array|min:1'
  - `review_ids.*` => 'required|integer|exists:reviews,id'
- **Authorization**: Implement user ownership and admin role validation

### 2.7 Add Review Routes

**Priority**: MEDIUM

**Task Details**:
- **Files**: `routes/client.php` and `routes/api.php`
- **Client Routes** (authenticated):
  - `GET /reviews` - User's reviews
  - `POST /reviews` - Submit review
  - `GET /reviews/{id}` - Get review
  - `PUT /reviews/{id}` - Update review
  - `DELETE /reviews/{id}` - Delete review
- **Public Routes**:
  - `GET /products/{id}/reviews` - Product reviews
  - `GET /vendors/{id}/reviews` - Vendor reviews
- **Admin Routes**:
  - `GET /admin/reviews` - All reviews for moderation
  - `POST /admin/reviews/{id}/approve` - Approve review
  - `POST /admin/reviews/{id}/reject` - Reject review

### 2.8 Extend Product Model with Review Relationships

**Priority**: LOW

**Task Details**:
- **File**: `app/Models/Product.php`
- **Additions**:
  - `public function reviews() { return $this->hasMany(Review::class); }`
  - `public function averageRating()` - Calculate average rating
  - `public function reviewsCount()` - Count total reviews

### 2.9 Extend Vendor Model with Review Relationships

**Priority**: LOW

**Task Details**:
- **File**: `app/Models/Vendor.php`
- **Additions**:
  - `public function reviews() { return $this->hasMany(Review::class); }`
  - `public function averageRating()` - Calculate average rating
  - `public function reviewsCount()` - Count total reviews

## Integration Requirements

### 2.10 Update Product API to Include Review Data

**Priority**: MEDIUM

**Task Details**:
- **Files**: Product-related resources and controllers
- **Requirements**:
  - Include average rating in product listings
  - Include review count in product details
  - Add review summary in product detail API

### 2.11 Update Vendor API to Include Review Data

**Priority**: MEDIUM

**Task Details**:
- **Files**: Vendor-related resources and controllers
- **Requirements**:
  - Include vendor rating in vendor listings
  - Include review count in vendor profiles
  - Add review summary in vendor detail API

## Testing Requirements

### 2.12 Create Wishlist Feature Tests

**Priority**: MEDIUM

**Task Details**:
- **File**: `tests/Feature/WishlistTest.php`
- **Test Cases**:
  - Add item to wishlist
  - Remove item from wishlist
  - Move item to cart
  - Prevent duplicate additions
  - Bulk operations
  - Authorization checks

### 2.13 Create Review Feature Tests

**Priority**: MEDIUM

**Task Details**:
- **File**: `tests/Feature/ReviewTest.php`
- **Test Cases**:
  - Submit review
  - Update review
  - Delete review
  - Review moderation
  - Rating calculations
  - Authorization checks

## Database Considerations

### 2.14 Add Database Indexes for Performance

**Priority**: LOW

**Task Details**:
- **Wishlist Indexes**:
  - `(user_id, product_id)` - Unique constraint
  - `user_id` - User wishlist queries
  - `product_id` - Product popularity
- **Review Indexes**:
  - `(product_id, is_approved)` - Product reviews
  - `(vendor_id, is_approved)` - Vendor reviews
  - `user_id` - User reviews
  - `rating` - Rating-based queries

## 🚨 **CRITICAL IMPLEMENTATION CHECKLIST**

Before implementing ANY controller or service, developers MUST:

### ✅ **Pre-Implementation Requirements**
1. **Study Reference Files**:
   - Examine `app/Http/Controllers/Admin/BannerController.php` line by line
   - Study `app/Services/BannerService.php` structure and methods
   - Review `app/Http/Requests/StoreBannerRequest.php` and `UpdateBannerRequest.php`
   - Understand `app/Traits/HelperTrait.php` methods

2. **Verify Pattern Compliance**:
   - Controller uses exact same constructor pattern
   - Service injection follows identical structure
   - Try-catch blocks match BannerController exactly
   - Response methods use HelperTrait consistently
   - HTTP status codes match established patterns
   - Method signatures follow exact conventions

3. **Code Review Checklist**:
   - [ ] Uses `use HelperTrait;`
   - [ ] Private `$service` property declared
   - [ ] Constructor injects service correctly
   - [ ] All methods return `JsonResponse`
   - [ ] Try-catch structure matches BannerController
   - [ ] Uses `successResponse()` and `errorResponse()` methods
   - [ ] HTTP status codes are consistent
   - [ ] Service methods follow BannerService patterns
   - [ ] Request validation follows established patterns

### ⚠️ **Common Mistakes to Avoid**
- **DON'T** create custom response formats
- **DON'T** use different error handling patterns
- **DON'T** skip HelperTrait usage
- **DON'T** use different HTTP status codes
- **DON'T** create custom service method signatures
- **DON'T** ignore the established naming conventions

### 🎯 **Quality Assurance**
Every implemented file must pass this test:
> "Does this file look like it was written by the same developer who wrote BannerController and BannerService?"

If the answer is NO, the implementation must be refactored to match the established patterns.

## Next Steps

1. **Phase 1**: Complete saveForLater method and create Wishlist model (following patterns)
2. **Phase 2**: Implement WishlistController and basic CRUD operations (using BannerController as template)
3. **Phase 3**: Create Review model and ReviewController (maintaining consistency)
4. **Phase 4**: Implement review moderation system (following admin controller patterns)
5. **Phase 5**: Add API integrations and testing (using established response formats)
6. **Phase 6**: Performance optimization and indexing

## Estimated Timeline

- **Wishlist Module**: 3-4 development days
- **Review Module**: 4-5 development days
- **Integration & Testing**: 2-3 development days
- **Total**: 9-12 development days

## Dependencies

- Existing User, Product, Vendor models
- CartService for cart integration
- Authentication middleware
- Email notification system (for review notifications)
- File upload system (for review attachments - future enhancement)

## 📚 **Reference Files for Implementation**

**MANDATORY STUDY MATERIALS**:
- `app/Http/Controllers/Admin/BannerController.php` - Controller pattern reference
- `app/Services/BannerService.php` - Service pattern reference
- `app/Http/Requests/StoreBannerRequest.php` - Request validation pattern
- `app/Http/Requests/UpdateBannerRequest.php` - Update request pattern
- `app/Traits/HelperTrait.php` - Response and utility methods
- `app/Models/Banner.php` - Model structure reference

**SUCCESS CRITERIA**: New modules integrate seamlessly with existing codebase and maintain 100% architectural consistency.
