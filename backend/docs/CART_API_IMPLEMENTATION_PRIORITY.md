# 🛒 CART API IMPLEMENTATION GUIDE (PRIORITY ORDER)

## 📋 Overview
This document organizes cart APIs by **implementation priority** and **user flow sequence**. Follow this order for optimal development workflow from guest cart creation to checkout readiness.

## 🚀 Implementation Roadmap

### 🎯 Phase 1: Core Guest Cart (ESSENTIAL - Implement First)
**Priority:** ⭐⭐⭐⭐⭐ | **Timeline:** Week 1
1. Create Cart
2. Add Items to Cart  
3. Get Cart Details
4. Update Cart Items
5. Remove Cart Items

### 🎯 Phase 2: Cart Management (IMPORTANT - Implement Second)  
**Priority:** ⭐⭐⭐⭐ | **Timeline:** Week 2
6. Apply/Remove Coupons
7. Cart Validation
8. Clear Cart

### 🎯 Phase 3: User Authentication (CRITICAL - Implement Third)
**Priority:** ⭐⭐⭐⭐⭐ | **Timeline:** Week 2-3
9. User Login & Cart Migration
10. Get User Cart
11. Save Items for Later

### 🎯 Phase 4: Advanced Features (ENHANCEMENT - Implement Fourth)
**Priority:** ⭐⭐⭐ | **Timeline:** Week 3-4
12. Bulk Operations
13. Multi-vendor Support
14. Cart Recovery

### 🎯 Phase 5: Checkout Preparation (FINAL - Implement Last)
**Priority:** ⭐⭐⭐⭐ | **Timeline:** Week 4
15. Inventory Management
16. Checkout Validation
17. Order Conversion

---

## 🔐 Authentication Methods

### Guest Cart Authentication
- **Method**: Token-based using `X-Cart-Token` header
- **Header**: `X-Cart-Token: sha256-hashed-secure-token`
- **Storage**: Store `cart_id` and `cart_token` in localStorage

### Authenticated User Authentication  
- **Method**: JWT Bearer token
- **Header**: `Authorization: Bearer your-jwt-token`

---

# 🎯 PHASE 1: CORE GUEST CART APIs (WEEK 1)

## 1️⃣ Create Cart (IMPLEMENT FIRST)
**POST** `/api/client/cart/`

**Purpose:** Initialize shopping session  
**When:** User visits site, adds first item  
**Priority:** ⭐⭐⭐⭐⭐

```javascript
// Request
POST /api/client/cart/
Content-Type: application/json

{
  "currency": "USD"
}

// Response
{
  "success": true,
  "data": {
    "id": "cart-uuid",
    "cart_token": "secure-token",
    "currency": "USD",
    "items": [],
    "totals": { "total_amount": 0 }
  }
}

// Frontend Implementation
const cartData = response.data;
localStorage.setItem('guest_cart_id', cartData.id);
localStorage.setItem('guest_cart_token', cartData.cart_token);
```

## 2️⃣ Add Item to Cart (IMPLEMENT SECOND)
**POST** `/api/client/cart/{cartId}/items`

**Purpose:** Add products to cart  
**When:** User clicks "Add to Cart"  
**Priority:** ⭐⭐⭐⭐⭐

```javascript
// Request
POST /api/client/cart/{cartId}/items
X-Cart-Token: secure-token
Content-Type: application/json

{
  "product_id": "product-uuid",
  "quantity": 2
}

// Response
{
  "success": true,
  "data": {
    "item": {
      "id": "item-uuid",
      "quantity": 2,
      "total_price": 59.98
    },
    "cart_totals": {
      "total_amount": 70.77
    }
  }
}

// Frontend Implementation
updateCartCounter(response.data.cart_totals.items_count);
showAddToCartSuccess();
```

## 3️⃣ Get Cart Details (IMPLEMENT THIRD)
**GET** `/api/client/cart/{cartId}`

**Purpose:** Display cart contents  
**When:** Cart page, checkout, dropdown  
**Priority:** ⭐⭐⭐⭐⭐

```javascript
// Request
GET /api/client/cart/{cartId}
X-Cart-Token: secure-token

// Response
{
  "success": true,
  "data": {
    "id": "cart-uuid",
    "items": [
      {
        "id": "item-uuid",
        "product_id": "product-uuid",
        "quantity": 2,
        "unit_price": 29.99,
        "total_price": 59.98,
        "product": {
          "name": "Product Name",
          "image": "image.jpg"
        }
      }
    ],
    "totals": {
      "subtotal": 59.98,
      "tax_amount": 4.80,
      "total_amount": 70.77
    }
  }
}

// Frontend Implementation
renderCartItems(response.data.items);
updateCartTotals(response.data.totals);
```

## 4️⃣ Update Cart Item (IMPLEMENT FOURTH)
**PUT** `/api/client/cart/{cartId}/items/{itemId}`

**Purpose:** Change item quantity/options  
**When:** Cart page quantity changes  
**Priority:** ⭐⭐⭐⭐⭐

```javascript
// Request
PUT /api/client/cart/{cartId}/items/{itemId}
X-Cart-Token: secure-token
Content-Type: application/json

{
  "quantity": 3
}

// Response
{
  "success": true,
  "data": {
    "item": {
      "quantity": 3,
      "total_price": 89.97
    },
    "cart_totals": {
      "total_amount": 100.76
    }
  }
}
```

## 5️⃣ Remove Cart Item (IMPLEMENT FIFTH)
**DELETE** `/api/client/cart/{cartId}/items/{itemId}`

**Purpose:** Remove items from cart  
**When:** User clicks remove button  
**Priority:** ⭐⭐⭐⭐⭐

```javascript
// Request
DELETE /api/client/cart/{cartId}/items/{itemId}
X-Cart-Token: secure-token

// Response
{
  "success": true,
  "data": {
    "removed_item_id": "item-uuid",
    "cart_totals": {
      "total_amount": 0
    }
  }
}
```

---

# 🎯 PHASE 2: CART MANAGEMENT APIs (WEEK 2)

## 6️⃣ Apply Coupon
**POST** `/api/client/cart/{cartId}/apply-coupon`

**Purpose:** Apply discount codes  
**Priority:** ⭐⭐⭐⭐

```javascript
// Request
{
  "coupon_code": "SAVE10"
}

// Response
{
  "success": true,
  "data": {
    "coupon": {
      "code": "SAVE10",
      "discount_amount": 11.99
    },
    "cart_totals": {
      "discount_amount": 11.99,
      "total_amount": 123.96
    }
  }
}
```

## 7️⃣ Remove Coupon
**DELETE** `/api/client/cart/{cartId}/remove-coupon`

## 8️⃣ Validate Cart
**POST** `/api/client/cart/{cartId}/validate`

**Purpose:** Check cart before checkout  
**Priority:** ⭐⭐⭐⭐

## 9️⃣ Clear Cart
**DELETE** `/api/client/cart/{cartId}`

**Purpose:** Empty entire cart  
**Priority:** ⭐⭐⭐

---

# 🎯 PHASE 3: USER AUTHENTICATION APIs (WEEK 2-3)

## 🔑 Cart Migration (CRITICAL)
**POST** `/api/client/my-cart/migrate`

**Purpose:** Merge guest cart after login  
**Priority:** ⭐⭐⭐⭐⭐

```javascript
// Request
POST /api/client/my-cart/migrate
Authorization: Bearer jwt-token
Content-Type: application/json

{
  "guest_session_id": "session-id",  // NOT guest_cart_id
  "merge_strategy": "merge",         // NOT "combine"
  "clear_guest_cart": true
}

// Response
{
  "status": true,
  "message": "Guest cart migrated successfully!",
  "data": {
    "id": 2,
    "uuid": "user-cart-uuid",
    "user_id": 1,
    "items_count": 2,
    "total_quantity": 4
  }
}

// Frontend Implementation
async function handleLogin(credentials) {
  // 1. Login user
  const loginResponse = await login(credentials);
  const { access_token } = loginResponse;
  
  // 2. Get guest cart info
  const guestSessionId = localStorage.getItem('guest_session_id');
  
  // 3. Migrate if guest cart exists
  if (guestSessionId) {
    await migrateGuestCart(access_token, guestSessionId);
    localStorage.removeItem('guest_cart_id');
    localStorage.removeItem('guest_cart_token');
    localStorage.removeItem('guest_session_id');
  }
}
```

## 👤 Get User Cart
**GET** `/api/client/my-cart/`

**Purpose:** Get authenticated user's cart  
**Priority:** ⭐⭐⭐⭐

## 💾 Save for Later
**POST** `/api/client/my-cart/save-for-later`

**Purpose:** Move items to wishlist  
**Priority:** ⭐⭐⭐

---

# 🧪 TESTING CHECKLIST

## Phase 1 Testing
- [ ] Create cart returns valid token
- [ ] Add item updates cart totals
- [ ] Get cart shows all items
- [ ] Update item changes quantity
- [ ] Remove item updates totals

## Phase 2 Testing  
- [ ] Apply coupon reduces total
- [ ] Remove coupon restores total
- [ ] Validate cart checks inventory
- [ ] Clear cart empties all items

## Phase 3 Testing
- [ ] Migration merges carts correctly
- [ ] User cart persists after login
- [ ] Save for later moves items

## Integration Testing
- [ ] Guest to user flow works
- [ ] Cart persists across sessions
- [ ] Checkout validation passes

---

# 📝 IMPLEMENTATION NOTES

1. **Start with Phase 1** - Essential for basic shopping
2. **Phase 2** - Adds cart management features  
3. **Phase 3** - Critical for user accounts
4. **Test each phase** before moving to next
5. **Use correct field names** in migration API
6. **Store session_id** for migration (not just cart_id)

This priority-based approach ensures you have a working cart system at each phase! 🚀
