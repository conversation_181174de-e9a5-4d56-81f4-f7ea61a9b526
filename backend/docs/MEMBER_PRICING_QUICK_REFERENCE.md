# Member Pricing Quick Reference Guide

## 🚀 Quick Start

### Basic Setup
```php
use App\Services\SimpleMemberPricingService;

$service = new SimpleMemberPricingService();
$customer = auth()->user()->customer;
$product = Product::find($productId);
```

### Check Member Status
```php
// Simple checks
$customer->is_member;                    // Boolean: is member
$customer->canAccessMemberPricing();     // Boolean: can access pricing
$customer->isMember();                   // Boolean: member pricing enabled
```

### Calculate Member Pricing
```php
// Get member pricing
$memberPricing = $service->calculateMemberPrice($product, $customer);

// Returns null if no member pricing, or:
[
    'base_price' => 100.00,
    'member_price' => 85.00,
    'savings_amount' => 15.00,
    'savings_percentage' => 15.0,
    'is_member_price' => true
]
```

## 🎯 Common Use Cases

### 1. Product Page Display
```php
// Controller
$memberPricing = null;
if (auth()->check() && auth()->user()->customer) {
    $memberPricing = $service->calculateMemberPrice($product, auth()->user()->customer);
}

// Blade Template
@if($memberPricing)
    <div class="member-price">
        <span class="original">AED {{ $product->regular_price }}</span>
        <span class="member">AED {{ $memberPricing['member_price'] }}</span>
        <span class="savings">Save {{ $memberPricing['savings_percentage'] }}%</span>
    </div>
@else
    <div class="regular-price">AED {{ $product->regular_price }}</div>
@endif
```

### 2. Cart Calculations
```php
$cartTotal = 0;
$totalSavings = 0;

foreach ($cartItems as $item) {
    $bestPrice = $service->getBestPrice($item->product, $customer);
    $itemTotal = $bestPrice['final_price'] * $item->quantity;
    $cartTotal += $itemTotal;
    
    if ($bestPrice['is_member_pricing']) {
        $totalSavings += $bestPrice['savings_amount'] * $item->quantity;
    }
}
```

### 3. API Response
```php
// In your API controller
public function show(Product $product)
{
    $customer = auth()->user()->customer ?? null;
    $memberPricing = null;
    
    if ($customer) {
        $memberPricing = $this->pricingService->calculateMemberPrice($product, $customer);
    }
    
    return response()->json([
        'product' => $product,
        'pricing' => [
            'regular_price' => $product->regular_price,
            'member_pricing' => $memberPricing,
        ]
    ]);
}
```

### 4. Bulk Operations
```php
// Multiple products
$products = [
    ['product' => $product1, 'quantity' => 2],
    ['product' => $product2, 'quantity' => 1],
];

$bulkResults = $service->calculateBulkMemberPricing($products, $customer);

// Preview for product list
$productIds = [1, 2, 3, 4, 5];
$preview = $service->getMemberPricingPreview($customer, $productIds);
```

## ⚙️ Configuration

### Enable Member Pricing for Product
```php
$product->update([
    'enable_member_pricing' => true,
    'member_price' => 85.00,
]);
```

### Manage Customer Member Status
```php
// Enable member pricing
$customer->update(['is_member_pricing_enabled' => true]);

// Disable member pricing
$customer->update(['is_member_pricing_enabled' => false]);
```

### Bulk Updates
```php
// All customers become members
Customer::query()->update(['is_member_pricing_enabled' => true]);

// All products in category get member pricing
Product::where('category_id', $categoryId)
    ->update(['enable_member_pricing' => true]);
```

## 🔍 Database Queries

### Find Members
```php
// All members
$members = Customer::members()->get();

// Active members only
$activeMembers = Customer::activeMembers()->get();

// Members with orders
$membersWithOrders = Customer::members()
    ->whereHas('orders')
    ->get();
```

### Find Products with Member Pricing
```php
// Products with member pricing enabled
$memberProducts = Product::where('enable_member_pricing', true)->get();

// Products with actual member prices set
$memberProducts = Product::where('enable_member_pricing', true)
    ->whereNotNull('member_price')
    ->where('member_price', '>', 0)
    ->get();
```

## 🛠️ Service Methods Reference

### SimpleMemberPricingService Methods

```php
// Core pricing calculation
calculateMemberPrice(Product $product, Customer $customer, ?int $variantId = null): ?array

// Best price (considers all pricing types)
getBestPrice(Product $product, Customer $customer, ?int $variantId = null): array

// Bulk calculations
calculateBulkMemberPricing(array $products, Customer $customer): array

// Preview for multiple products
getMemberPricingPreview(Customer $customer, array $productIds): array

// Simple qualification check
customerQualifiesForMemberPricing(Customer $customer): bool
```

### Customer Model Methods

```php
// Accessor attributes
$customer->is_member;                    // Boolean

// Instance methods
$customer->isMember(): bool;
$customer->canAccessMemberPricing(): bool;

// Query scopes
Customer::members();
Customer::activeMembers();
```

## 🚨 Error Handling

### Null Checks
```php
// Always check for null response
$memberPricing = $service->calculateMemberPrice($product, $customer);

if ($memberPricing === null) {
    // No member pricing available
    $finalPrice = $product->regular_price;
} else {
    // Member pricing available
    $finalPrice = $memberPricing['member_price'];
}
```

### Edge Cases Handled Automatically
- Customer with inactive user account → No member pricing
- Product without member pricing enabled → No member pricing  
- Member price higher than regular price → No member pricing
- Missing member price value → No member pricing

## 📊 Testing Examples

### Unit Tests
```php
/** @test */
public function member_customer_gets_member_price()
{
    $customer = Customer::factory()->create(['is_member_pricing_enabled' => true]);
    $product = Product::factory()->create([
        'regular_price' => 100.00,
        'member_price' => 85.00,
        'enable_member_pricing' => true,
    ]);
    
    $memberPricing = $this->service->calculateMemberPrice($product, $customer);
    
    $this->assertNotNull($memberPricing);
    $this->assertEquals(85.00, $memberPricing['member_price']);
    $this->assertEquals(15.00, $memberPricing['savings_amount']);
}
```

### Feature Tests
```php
/** @test */
public function api_returns_member_pricing_for_authenticated_member()
{
    $user = User::factory()->create();
    $customer = Customer::factory()->create([
        'user_id' => $user->id,
        'is_member_pricing_enabled' => true,
    ]);
    
    $response = $this->actingAs($user)->get("/api/products/{$product->id}");
    
    $response->assertJson([
        'pricing' => [
            'member_pricing' => [
                'member_price' => 85.00,
                'savings_amount' => 15.00,
            ]
        ]
    ]);
}
```

## 🎨 Frontend Examples

### Vue.js Component
```javascript
// In your Vue component
computed: {
    displayPrice() {
        if (this.memberPricing) {
            return {
                price: this.memberPricing.member_price,
                originalPrice: this.memberPricing.base_price,
                savings: this.memberPricing.savings_amount,
                isMemberPrice: true
            };
        }
        return {
            price: this.product.regular_price,
            isMemberPrice: false
        };
    }
}
```

### React Component
```jsx
const PriceDisplay = ({ product, memberPricing }) => {
    if (memberPricing) {
        return (
            <div className="member-pricing">
                <span className="member-price">AED {memberPricing.member_price}</span>
                <span className="original-price">AED {memberPricing.base_price}</span>
                <span className="savings">Save {memberPricing.savings_percentage}%</span>
            </div>
        );
    }
    
    return <span className="regular-price">AED {product.regular_price}</span>;
};
```

## 🔧 Performance Tips

### Caching
```php
// Cache member pricing for frequently accessed products
$cacheKey = "member_pricing_{$product->id}_{$customer->id}";
$memberPricing = Cache::remember($cacheKey, 3600, function() {
    return $this->service->calculateMemberPrice($product, $customer);
});
```

### Eager Loading
```php
// Load customer with user relationship
$customer = Customer::with('user')->find($customerId);

// Load products with necessary relationships
$products = Product::where('enable_member_pricing', true)
    ->select(['id', 'regular_price', 'member_price', 'enable_member_pricing'])
    ->get();
```

## 📋 Checklist for Implementation

- [ ] Customer has `is_member_pricing_enabled` field
- [ ] Product has `member_price` and `enable_member_pricing` fields
- [ ] SimpleMemberPricingService is instantiated
- [ ] Member status is checked before pricing calculations
- [ ] Null responses are handled gracefully
- [ ] Frontend displays member pricing appropriately
- [ ] API responses include member pricing data
- [ ] Tests cover member and non-member scenarios

## 🆘 Quick Debugging

```php
// Debug customer member status
dd([
    'customer_id' => $customer->id,
    'is_member' => $customer->is_member,
    'member_enabled' => $customer->is_member_pricing_enabled,
    'user_active' => $customer->user->is_active,
    'can_access' => $customer->canAccessMemberPricing(),
]);

// Debug product member pricing
dd([
    'product_id' => $product->id,
    'regular_price' => $product->regular_price,
    'member_price' => $product->member_price,
    'enable_member_pricing' => $product->enable_member_pricing,
]);

// Debug service calculation
$memberPricing = $service->calculateMemberPrice($product, $customer);
dd($memberPricing);
```
