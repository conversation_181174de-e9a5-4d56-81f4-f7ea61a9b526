# Order Management API Guide for UI Team

## Table of Contents
- [Quick Reference](#quick-reference)
- [Client/Customer APIs](#clientcustomer-apis)
- [Vendor APIs](#vendor-apis)
- [Admin APIs](#admin-apis)
- [Order Status Values](#order-status-values)
- [Error Responses](#error-responses)
- [Order Data Structure](#order-data-structure)
- [Frontend Implementation Tips](#frontend-implementation-tips)
- [Business Logic Guidelines](#business-logic-guidelines)

## Quick Reference

### Base URLs
- **Admin**: `/api/admin/orders`
- **Customer**: `/api/client/orders`
- **Vendor**: `/api/vendor/orders`

### Authentication
All endpoints require Bearer token in header:
```
Authorization: Bearer {access_token}
```

---

## Client/Customer APIs

### Base URL: `/api/client/orders`

#### Get Customer Orders (Optimized List View)
```http
GET /api/client/orders
```
**Query Parameters:**
- `status` - Filter by fulfillment status
- `payment_status` - Filter by payment status
- `search` - Search by order number or product
- `per_page` - Items per page (default: 15)
- `page` - Page number

**Response (Lightweight for Performance):**
```json
{
  "status": true,
  "data": {
    "data": [
      {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "order_number": "INV-2025-0001",
        "order_date": "2025-01-15 10:30",
        "order_date_human": "2 hours ago",
        "pricing": {
          "total": "150.00",
          "total_raw": 150.00,
          "currency": "AED",
          "items_count": 3,
          "total_quantity": 5
        },
        "status": {
          "fulfillment": "processing",
          "fulfillment_display": "Processing",
          "payment": "paid",
          "payment_display": "Paid",
          "is_paid": true
        },
        "vendor": {
          "id": 5,
          "name": "Tech Store"
        },
        "details": {
          "payment_method": "card",
          "payment_method_display": "Credit/Debit Card",
          "tracking_number": "TRK123456789",
          "has_customer_note": true,
          "estimated_delivery": "Jan 20, 2025"
        },
        "actions": {
          "can_cancel": true,
          "can_reorder": true,
          "view_url": "/api/client/orders/550e8400-e29b-41d4-a716-************",
          "track_url": "https://track.example.com/TRK123456789"
        },
        "priority": "normal",
        "requires_attention": false,
        "created_at": "2025-01-15T10:30:00Z",
        "updated_at": "2025-01-15T11:00:00Z",
        "order_age_days": 0
      }
    ],
    "current_page": 1,
    "per_page": 15,
    "total": 25,
    "last_page": 2
  }
}
```

#### Get Single Order (Detailed View)
```http
GET /api/client/orders/{uuid}
```

**Response (Full Details for Order View):**
```json
{
  "status": true,
  "data": {
    "id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "order_number": "INV-2025-0001",
    "customer": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+************"
    },
    "vendor": {
      "id": 5,
      "name": "Tech Store",
      "email": "<EMAIL>",
      "phone": "+************"
    },
    "pricing": {
      "subtotal": "130.00",
      "tax": "19.50",
      "shipping": "0.00",
      "total": "149.50",
      "currency": "AED",
      "discount": "0.00"
    },
    "status": {
      "payment": "paid",
      "fulfillment": "processing",
      "payment_display": "Paid",
      "fulfillment_display": "Processing",
      "is_paid": true,
      "can_cancel": true,
      "can_refund": false
    },
    "details": {
      "payment_method": "card",
      "tracking_number": "TRK123456789",
      "customer_note": "Please deliver after 6 PM",
      "admin_note": null,
      "total_items": 2,
      "total_quantity": 2
    },
    "items": [
      {
        "id": 1,
        "product_id": 10,
        "product_name": "Wireless Headphones",
        "product_sku": "WH-001",
        "quantity": 2,
        "unit_price": "65.00",
        "total": "130.00",
        "vendor": {
          "id": 5,
          "name": "Tech Store"
        }
      }
    ],
    "addresses": {
      "shipping": {
        "name": "John Doe",
        "address_line_1": "123 Main St",
        "address_line_2": "Apt 4B",
        "city": "Dubai",
        "state": "Dubai",
        "postal_code": "12345",
        "country": "UAE"
      },
      "billing": {
        "name": "John Doe",
        "address_line_1": "123 Main St",
        "city": "Dubai",
        "country": "UAE"
      }
    },
    "status_histories": [
      {
        "id": 1,
        "status": "confirmed",
        "note": "Order confirmed by system",
        "created_at": "2025-01-15T10:30:00Z",
        "user": {
          "name": "System"
        }
      }
    ],
    "vendor_groups": [
      {
        "vendor_id": 5,
        "vendor": {
          "id": 5,
          "name": "Tech Store"
        },
        "items": [...],
        "subtotal": "130.00",
        "items_count": 1
      }
    ],
    "created_at": "2025-01-15T10:30:00Z",
    "updated_at": "2025-01-15T11:00:00Z"
  }
}
```

#### Create Order from Cart
```http
POST /api/client/orders
```
**Request Body:**
```json
{
  "cart_id": 123,
  "payment_method": "card",
  "shipping_address": {
    "address": "123 Main St",
    "city": "Dubai",
    "country": "UAE",
    "postal_code": "12345",
    "phone": "+************"
  },
  "customer_note": "Please deliver after 6 PM"
}
```

#### Cancel Order
```http
PATCH /api/client/orders/{uuid}/cancel
```
**Request Body:**
```json
{
  "reason": "Changed mind",
  "refund_method": "original_payment"
}
```

---

## Vendor APIs

### Base URL: `/api/vendor/orders`

#### Get Vendor Orders (Optimized List View)
```http
GET /api/vendor/orders
```
**Query Parameters:**
- `status` - Filter by fulfillment status
- `payment_status` - Filter by payment status
- `search` - Search by order number or customer
- `per_page` - Items per page (default: 15)
- `page` - Page number

**Response (Flattened Structure - Matches Admin Format):**
```json
{
  "status": true,
  "data": {
    "data": [
      {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "order_number": "INV-2025-0001",
        "order_date": "2025-01-15 10:30",
        "customer_name": "John Doe",
        "customer_email": "<EMAIL>",
        "vendor_name": "Tech Store",
        "total_amount": "150.00",
        "currency": "AED",
        "items_count": 3,
        "total_quantity": 5,
        "delivery_status": "processing",
        "delivery_status_display": "Processing",
        "payment_status": "paid",
        "payment_status_display": "Paid",
        "payment_method": "card",
        "payment_method_display": "Credit/Debit Card",
        "order_status": "processing",
        "order_status_display": "Processing",
        "vendor_commission": "127.50",
        "can_fulfill": true,
        "requires_vendor_attention": false,
        "processing_deadline": "Jan 17, 2025 10:30",
        "vendor_priority_level": "medium",
        "can_update_status": true,
        "can_add_tracking": false,
        "can_mark_shipped": true,
        "tracking_number": "TRK123456789",
        "shipping_method": "Standard Delivery",
        "estimated_ship_date": "Jan 17, 2025",
        "special_instructions": "Please deliver after 6 PM",
        "urgency_reason": null,
        "days_since_order": 0,
        "is_processing_overdue": false,
        "shipping_city": "Dubai",
        "shipping_country": "UAE",
        "shipping_postal_code": "12345",
        "created_at": "2025-01-15T10:30:00Z",
        "updated_at": "2025-01-15T11:00:00Z"
      }
    ],
    "current_page": 1,
    "per_page": 15,
    "total": 25,
    "last_page": 2
  }
}
```

**Key Changes for UI Consistency:**
- **Flattened Structure**: All fields at root level (no nested objects)
- **Matches Admin Format**: Same field names and structure as admin API
- **Vendor-Specific Fields**: Added vendor business logic fields
- **Performance Optimized**: Uses database aggregations, no N+1 queries

### Vendor-Specific Fields Explained

**Business Logic Fields:**
- `vendor_commission` - Calculated commission amount (85% of total, 15% platform fee)
- `can_fulfill` - Boolean indicating if vendor can fulfill (confirmed/processing + paid)
- `requires_vendor_attention` - Boolean flag for orders needing vendor action
- `processing_deadline` - Estimated deadline for order processing based on status
- `vendor_priority_level` - Priority from vendor perspective (high/medium/normal)

**Action Flags:**
- `can_update_status` - Can vendor update fulfillment status
- `can_add_tracking` - Can vendor add tracking information
- `can_mark_shipped` - Can vendor mark order as shipped

**Processing Metrics:**
- `days_since_order` - Number of days since order was placed
- `is_processing_overdue` - Boolean indicating if processing is overdue
- `urgency_reason` - Reason for urgency (e.g., "Processing overdue", "High value order")

**Shipping Information:**
- `shipping_method` - Delivery method (Standard Delivery, Express, etc.)
- `estimated_ship_date` - Estimated shipping date based on current status
- `shipping_city`, `shipping_country`, `shipping_postal_code` - Minimal address info for privacy

### Vendor-Specific Fields Explained

**Business Logic Fields:**
- `vendor_commission` - Calculated commission amount (85% of total, 15% platform fee)
- `can_fulfill` - Boolean indicating if vendor can fulfill (confirmed/processing + paid)
- `requires_vendor_attention` - Boolean flag for orders needing vendor action
- `processing_deadline` - Estimated deadline for order processing based on status
- `vendor_priority_level` - Priority from vendor perspective (high/medium/normal)

**Action Flags:**
- `can_update_status` - Can vendor update fulfillment status
- `can_add_tracking` - Can vendor add tracking information
- `can_mark_shipped` - Can vendor mark order as shipped

**Processing Metrics:**
- `days_since_order` - Number of days since order was placed
- `is_processing_overdue` - Boolean indicating if processing is overdue
- `urgency_reason` - Reason for urgency (e.g., "Processing overdue", "High value order")

**Shipping Information:**
- `shipping_method` - Delivery method (Standard Delivery, Express, etc.)
- `estimated_ship_date` - Estimated shipping date based on current status
- `shipping_city`, `shipping_country`, `shipping_postal_code` - Minimal address info for privacy

#### Get Vendor Orders Requiring Action
```http
GET /api/vendor/orders/pending-actions
```
**Response:** Same structure as above, filtered for orders needing vendor attention

#### Get Vendor Dashboard
```http
GET /api/vendor/orders/dashboard
```
**Response:**
```json
{
  "status": true,
  "data": {
    "summary": {
      "total_orders": 150,
      "pending_orders": 12,
      "processing_orders": 8,
      "completed_orders": 130
    },
    "recent_orders": [...],
    "revenue": {
      "today": "1250.00",
      "this_month": "45000.00"
    }
  }
}
```

#### Update Order Status
```http
PATCH /api/vendor/orders/{uuid}/status
```
**Request Body:**
```json
{
  "fulfillment_status": "processing",
  "reason": "Order confirmed and being prepared",
  "tracking_number": "TRK123456789"
}
```

#### Get Pending Actions
```http
GET /api/vendor/orders/pending-actions
```

---

## Admin APIs

### Base URL: `/api/admin/orders`

#### Get All Orders (Enterprise-Optimized List View)
```http
GET /api/admin/orders
```
**Query Parameters:**
- `status` - Filter by fulfillment status
- `payment_status` - Filter by payment status
- `vendor_id` - Filter by vendor
- `user_id` - Filter by customer
- `search` - Search by order number, customer, or product
- `date_from` - Filter from date (YYYY-MM-DD)
- `date_to` - Filter to date (YYYY-MM-DD)
- `per_page` - Items per page (default: 15)
- `page` - Page number

**Response (Optimized with Database Aggregations):**
```json
{
  "status": true,
  "data": {
    "data": [
      {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "order_number": "INV-2025-0001",
        "order_date": "2025-01-15 10:30",
        "customer_name": "John Doe",
        "customer_email": "<EMAIL>",
        "vendor_name": "Tech Store",
        "total_amount": "150.00",
        "currency": "AED",
        "items_count": 3,
        "total_quantity": 5,
        "delivery_status": "processing",
        "delivery_status_display": "Processing",
        "payment_status": "paid",
        "payment_status_display": "Paid",
        "payment_method": "card",
        "payment_method_display": "Credit/Debit Card",
        "order_status": "confirmed",
        "order_status_display": "Confirmed",
        "created_at": "2025-01-15T10:30:00Z",
        "updated_at": "2025-01-15T11:00:00Z"
      }
    ],
    "current_page": 1,
    "per_page": 15,
    "total": 1250,
    "last_page": 84
  }
}
```

**Performance Notes:**
- Uses database-level aggregations (withCount/withSum)
- Optimized for handling 1M+ orders efficiently
- No N+1 query problems
- Minimal memory usage even with large datasets

#### Get Single Order (Admin Detailed View)
```http
GET /api/admin/orders/{uuid}
```
**Response:** Full OrderResource with all relationships and admin-specific data

#### Update Order Status (Admin)
```http
PATCH /api/admin/orders/{uuid}/status
```
**Request Body:**
```json
{
  "payment_status": "paid",
  "fulfillment_status": "shipped",
  "reason": "Payment confirmed",
  "admin_note": "Priority order"
}
```

#### Bulk Status Update
```http
PATCH /api/admin/orders/bulk-status
```
**Request Body:**
```json
{
  "order_uuids": ["uuid1", "uuid2"],
  "status": "confirmed",
  "reason": "Bulk confirmation"
}
```

## Order Status Values

### Payment Status
- `pending` - Payment not yet processed
- `paid` - Payment successful
- `failed` - Payment failed
- `refunded` - Payment refunded
- `partially_refunded` - Partial refund issued

### Fulfillment Status
- `pending` - Order placed, awaiting confirmation
- `confirmed` - Order confirmed by vendor
- `processing` - Order being prepared
- `shipped` - Order shipped
- `delivered` - Order delivered
- `cancelled` - Order cancelled
- `returned` - Order returned

## Error Responses

All APIs return consistent error format:
```json
{
  "status": false,
  "error": "Validation error",
  "errors": {
    "field_name": ["Error message"]
  },
  "message": "Human readable error message"
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Server Error

## Order Data Structure

### Lightweight List Resources
Each user type gets optimized data for consistent UI components:

**Client Orders (ClientOrderListResource):**
- Essential customer info (pricing, status, actions)
- Delivery tracking and estimated dates
- Customer-relevant actions (cancel, reorder, track)

**Vendor Orders (VendorOrderListResource - Flattened Structure):**
- **Matches admin format** for consistent UI components
- Customer details (minimal for privacy): `customer_name`, `customer_email`
- Vendor-specific business fields: `vendor_commission`, `processing_deadline`, `vendor_priority_level`
- Action flags: `can_fulfill`, `requires_vendor_attention`, `can_update_status`, `can_mark_shipped`
- Fulfillment data: `shipping_method`, `tracking_number`, `estimated_ship_date`
- Processing metrics: `days_since_order`, `is_processing_overdue`, `urgency_reason`

**Admin Orders (Direct Model with Aggregations):**
- Complete overview data with database-aggregated counts and totals
- Enterprise-scale performance optimization
- Flattened structure for efficient UI rendering

### Detailed View Resources
Single order endpoints provide full data including:
- Complete item details with relationships
- Full address information
- Status history and audit trail
- Vendor groups for multi-vendor orders (loaded selectively)

### Complete Order Object (Detail Views)
```json
{
  "id": 1,
  "uuid": "550e8400-e29b-41d4-a716-************",
  "order_number": "INV-2025-0001",
  "customer": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+************"
  },
  "vendor": {
    "id": 5,
    "name": "Tech Store",
    "email": "<EMAIL>"
  },
  "items": [
    {
      "id": 1,
      "product_title": "iPhone 15",
      "sku": "IPH15-128-BLK",
      "quantity": 1,
      "price": "3500.00",
      "total": "3500.00"
    }
  ],
  "pricing": {
    "subtotal": "3500.00",
    "discount_total": "350.00",
    "tax_total": "157.50",
    "shipping_fee": "25.00",
    "total": "3332.50",
    "currency": "AED"
  },
  "status": {
    "payment": "paid",
    "fulfillment": "processing"
  },
  "addresses": {
    "shipping": {
      "address": "123 Main St",
      "city": "Dubai",
      "country": "UAE",
      "postal_code": "12345",
      "phone": "+************"
    }
  },
  "tracking_number": "TRK123456789",
  "customer_note": "Please deliver after 6 PM",
  "created_at": "2025-01-15T10:30:00Z",
  "updated_at": "2025-01-15T11:00:00Z"
}
```

## Frontend Implementation Tips

### 1. Order Status Display
```javascript
const getStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    confirmed: 'blue',
    processing: 'purple',
    shipped: 'green',
    delivered: 'success',
    cancelled: 'red'
  };
  return colors[status] || 'gray';
};
```

### 2. Date Formatting
All dates are in ISO 8601 format. Use libraries like `date-fns` or `moment.js` for formatting.

### 3. Currency Display
Always display currency with the `currency` field from the response.

### 4. Real-time Updates
Consider implementing WebSocket or polling for real-time order status updates.

### 5. Error Handling
```javascript
const handleApiError = (error) => {
  if (error.response?.data?.errors) {
    // Show validation errors
    Object.entries(error.response.data.errors).forEach(([field, messages]) => {
      showFieldError(field, messages[0]);
    });
  } else {
    // Show general error
    showToast(error.response?.data?.message || 'Something went wrong');
  }
};
```

## Rate Limiting
- Public endpoints: 60 requests/minute
- Authenticated endpoints: 1000 requests/minute
- Admin endpoints: 2000 requests/minute

## Testing
Use these test credentials in development:
- **Customer**: `<EMAIL>` / `password`
- **Vendor**: `<EMAIL>` / `password`
- **Admin**: `<EMAIL>` / `password`

## Vendor Portal Specific Features

### Order Notifications
Vendors receive notifications for:
- New orders requiring confirmation
- Payment confirmations
- Customer cancellation requests
- Return requests

### Vendor Permissions
Different vendor roles have different access:
- **Vendor Owner**: Full access to all orders
- **Vendor Staff**: Limited to assigned orders
- **Vendor Logistics**: Shipping and tracking updates only

### Multi-Vendor Order Handling
When a customer order contains items from multiple vendors:
- Each vendor sees only their portion of the order
- Order numbers remain the same across vendors
- Each vendor manages their items independently

## Business Logic Guidelines

### Order Lifecycle
1. **Pending** → Customer places order
2. **Confirmed** → Vendor accepts order
3. **Processing** → Vendor prepares items
4. **Shipped** → Items dispatched with tracking
5. **Delivered** → Customer receives order

### Cancellation Rules
- **Customer**: Can cancel before "processing" status
- **Vendor**: Can cancel with valid reason at any time
- **Admin**: Can cancel any order

### Refund Processing
- Automatic refunds for cancelled orders
- Partial refunds for returned items
- Refund processing time: 3-5 business days

### Inventory Integration
- Stock automatically reserved on order confirmation
- Stock released on cancellation
- Low stock warnings for vendors

## Email Notifications

### Customer Notifications
- Order confirmation
- Status updates
- Shipping notifications
- Delivery confirmation

### Vendor Notifications
- New order alerts
- Payment confirmations
- Cancellation requests

### Admin Notifications
- Failed payments
- Dispute alerts
- System errors

## UI Components Recommendations

### Order Status Badge
```jsx
<Badge color={getStatusColor(order.status.fulfillment)}>
  {order.status.fulfillment.toUpperCase()}
</Badge>
```

### Order Timeline
Show status progression with timestamps from `status_history` array.

### Action Buttons
Conditionally show based on user role and order status:
- Cancel (customers, before processing)
- Confirm (vendors, pending orders)
- Update Status (vendors/admin)
- Refund (admin only)

---

## Frontend Implementation Tips

### Performance Optimization
```javascript
// Use appropriate endpoints for different views
// List view - lightweight data
const orders = await fetch('/api/client/orders?per_page=20');

// Detail view - full data
const order = await fetch(`/api/client/orders/${uuid}`);
```

### State Management
```javascript
// Example order state structure
const orderState = {
  list: {
    data: [],
    pagination: {},
    filters: {},
    loading: false
  },
  current: {
    data: null,
    loading: false
  }
};
```

### Real-time Updates
```javascript
// WebSocket for order updates
const ws = new WebSocket('wss://api.example.com/orders/updates');
ws.onmessage = (event) => {
  const orderUpdate = JSON.parse(event.data);
  updateOrderInUI(orderUpdate);
};
```

### Filtering and Search
```javascript
// Build query parameters
const buildQuery = (filters) => {
  const params = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value) params.append(key, value);
  });
  return params.toString();
};

// Usage
const query = buildQuery({
  status: 'processing',
  payment_status: 'paid',
  search: 'john doe',
  per_page: 20
});
```

### Pagination Implementation
```javascript
// Handle pagination
const handlePageChange = (page) => {
  const url = `/api/admin/orders?page=${page}&per_page=${perPage}`;
  fetchOrders(url);
};
```

### UI Component Reusability
```javascript
// Same OrderTable component for both admin and vendor
const OrderTable = ({ orders, userType }) => {
  return (
    <table>
      <thead>
        <tr>
          <th>Order #</th>
          <th>Customer</th>
          <th>Total</th>
          <th>Status</th>
          {userType === 'vendor' && <th>Commission</th>}
          {userType === 'vendor' && <th>Deadline</th>}
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {orders.map(order => (
          <OrderRow
            key={order.id}
            order={order}
            userType={userType}
          />
        ))}
      </tbody>
    </table>
  );
};

// Same OrderRow component with conditional fields
const OrderRow = ({ order, userType }) => {
  return (
    <tr>
      <td>{order.order_number}</td>
      <td>{order.customer_name}</td>
      <td>{order.total_amount} {order.currency}</td>
      <td>
        <StatusBadge status={order.delivery_status} />
      </td>
      {userType === 'vendor' && (
        <td>{order.vendor_commission}</td>
      )}
      {userType === 'vendor' && (
        <td>{order.processing_deadline}</td>
      )}
      <td>
        <ActionButtons order={order} userType={userType} />
      </td>
    </tr>
  );
};
```

---

## Enterprise Performance Notes

### Optimization Features
- **Database Aggregations**: Uses withCount/withSum for efficient queries
- **Selective Loading**: Heavy attributes loaded only when needed
- **Lightweight Resources**: Different data for list vs detail views
- **No N+1 Queries**: Optimized relationship loading
- **Enterprise Scale**: Handles 1M+ orders efficiently

### Resource Types by Endpoint
- **Client List**: `ClientOrderListResource` - Customer-focused data
- **Vendor List**: `VendorOrderListResource` - **Flattened structure matching admin format**
- **Admin List**: Direct model with aggregations - Enterprise performance
- **Detail Views**: Full `OrderResource` with all relationships

### API Structure Consistency
- **Admin & Vendor Lists**: Both use flattened structure for consistent UI components
- **Same Field Names**: `customer_name`, `total_amount`, `delivery_status`, etc.
- **Vendor Extensions**: Additional fields like `vendor_commission`, `processing_deadline`
- **UI Reusability**: Same table/list components can be used for both admin and vendor

### Performance Benchmarks
- List endpoints: <50ms response time
- Detail endpoints: <100ms response time
- Memory usage: <50MB for 50 orders
- Database queries: <10 per request

---

## Support
For API issues, contact the backend team with:
1. Endpoint URL
2. Request payload
3. Response received
4. Expected behavior

### Quick Links
- **Client Orders**: [/api/client/orders](#clientcustomer-apis)
- **Vendor Orders**: [/api/vendor/orders](#vendor-apis)
- **Admin Orders**: [/api/admin/orders](#admin-apis)
