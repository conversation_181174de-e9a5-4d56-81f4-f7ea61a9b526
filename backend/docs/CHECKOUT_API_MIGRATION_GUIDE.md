# Checkout API Migration Guide

## Overview

This guide helps developers migrate from the legacy checkout API to the optimized checkout system. All legacy endpoints remain functional for backward compatibility.

## Breaking Changes

**None.** All existing endpoints continue to work as before. The new optimized endpoints are additive.

## New Endpoints Summary

| Endpoint | Purpose | Replaces |
|----------|---------|----------|
| `GET /checkout/initialize/{cartId}` | Consolidated checkout data | Multiple separate calls |
| `POST /checkout/{cartId}/apply-coupon` | Apply coupon on checkout | New functionality |
| `DELETE /checkout/{cartId}/remove-coupon` | Remove coupon on checkout | New functionality |
| `POST /checkout/select-payment-method-enhanced` | Enhanced payment selection | Extends existing functionality |

## Migration Steps

### Step 1: Update Checkout Initialization

#### Before (Legacy - Still Works)
```javascript
async function initializeCheckout(cartId) {
  const [summary, addresses, paymentMethods, userCards] = await Promise.all([
    api.get(`/checkout/summary/${cartId}`),
    api.get('/checkout/addresses'),
    api.get('/checkout/available-payment-methods'),
    api.get('/checkout/payment-methods')
  ]);
  
  return {
    cart: summary.data.cart,
    addresses: addresses.data,
    paymentMethods: paymentMethods.data,
    userCards: userCards.data
  };
}
```

#### After (Optimized - Recommended)
```javascript
async function initializeCheckout(cartId) {
  const response = await api.get(`/checkout/initialize/${cartId}`);
  return response.data;
}
```

### Step 2: Add Promo Code Functionality to Checkout

#### New Functionality
```javascript
// Apply coupon on checkout page
async function applyCouponOnCheckout(cartId, couponCode) {
  const response = await api.post(`/checkout/${cartId}/apply-coupon`, {
    coupon_code: couponCode
  });
  return response.data;
}

// Remove coupon from checkout page
async function removeCouponFromCheckout(cartId, couponCode) {
  const response = await api.delete(`/checkout/${cartId}/remove-coupon`, {
    data: { coupon_code: couponCode }
  });
  return response.data;
}
```

### Step 3: Enhanced Payment Method Selection

#### Before (Legacy - Still Works)
```javascript
async function selectPaymentMethod(paymentMethodId) {
  const response = await api.post('/checkout/select-payment-method', {
    payment_method_id: paymentMethodId
  });
  return response.data;
}
```

#### After (Enhanced - Recommended)
```javascript
async function selectPaymentMethodEnhanced(paymentMethodId, userCardId = null) {
  const response = await api.post('/checkout/select-payment-method-enhanced', {
    payment_method_id: paymentMethodId,
    user_card_id: userCardId,
    save_for_future: true
  });
  return response.data;
}
```

## Response Structure Changes

### CheckoutResource Structure

The new `CheckoutResource` provides a consistent structure:

```typescript
interface CheckoutResponse {
  cart: CartResource;           // Full cart data with consistent structure
  validation: {
    is_valid: boolean;
    errors: string[];
    cart_validation: object;
  };
  user_addresses: {
    shipping: Address[];
    billing: Address[];
    all: Address[];
  };
  user_payment_methods: UserCard[];
  available_payment_methods: PaymentMethod[];
  checkout_ready: boolean;
  applied_coupons: Coupon[];
  checkout_metadata: {
    can_apply_coupons: boolean;
    requires_shipping_address: boolean;
    requires_payment_method: boolean;
    supports_guest_checkout: boolean;
    min_order_value: number;
    max_order_value: number;
  };
}
```

### Enhanced Payment Method Structure

```typescript
interface PaymentMethod {
  id: number;
  type: string;
  name: string;
  name_ar: string;
  description: string;
  description_ar: string;
  icon: string;
  enabled: boolean;
  requires_selection: boolean;
  requires_card_details: boolean;
  supports_saved_cards: boolean;
  processing_fee: number;
  min_amount: number;
  max_amount: number | null;
  supported_currencies: string[];
  estimated_processing_time: string;
  security_features: string[];
}
```

## Frontend Implementation Examples

### React Component Migration

#### Before
```jsx
function CheckoutPage({ cartId }) {
  const [loading, setLoading] = useState(true);
  const [cart, setCart] = useState(null);
  const [addresses, setAddresses] = useState([]);
  const [paymentMethods, setPaymentMethods] = useState([]);

  useEffect(() => {
    async function loadCheckoutData() {
      setLoading(true);
      try {
        const [summaryRes, addressRes, paymentRes] = await Promise.all([
          api.get(`/checkout/summary/${cartId}`),
          api.get('/checkout/addresses'),
          api.get('/checkout/available-payment-methods')
        ]);
        
        setCart(summaryRes.data.cart);
        setAddresses(addressRes.data);
        setPaymentMethods(paymentRes.data);
      } catch (error) {
        console.error('Failed to load checkout data:', error);
      } finally {
        setLoading(false);
      }
    }
    
    loadCheckoutData();
  }, [cartId]);

  // ... rest of component
}
```

#### After
```jsx
function CheckoutPage({ cartId }) {
  const [loading, setLoading] = useState(true);
  const [checkoutData, setCheckoutData] = useState(null);

  useEffect(() => {
    async function loadCheckoutData() {
      setLoading(true);
      try {
        const response = await api.get(`/checkout/initialize/${cartId}`);
        setCheckoutData(response.data);
      } catch (error) {
        console.error('Failed to load checkout data:', error);
      } finally {
        setLoading(false);
      }
    }
    
    loadCheckoutData();
  }, [cartId]);

  if (loading) return <LoadingSpinner />;
  if (!checkoutData) return <ErrorMessage />;

  const { cart, user_addresses, available_payment_methods, checkout_ready } = checkoutData;

  // ... rest of component with simplified data access
}
```

### Promo Code Component

```jsx
function CheckoutPromoCode({ cartId, onCouponApplied }) {
  const [couponCode, setCouponCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const applyCoupon = async () => {
    if (!couponCode.trim()) return;
    
    setLoading(true);
    setError('');
    
    try {
      const response = await api.post(`/checkout/${cartId}/apply-coupon`, {
        coupon_code: couponCode
      });
      
      onCouponApplied(response.data);
      setCouponCode('');
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to apply coupon');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="promo-code-section">
      <input
        type="text"
        value={couponCode}
        onChange={(e) => setCouponCode(e.target.value)}
        placeholder="Enter promo code"
        disabled={loading}
      />
      <button onClick={applyCoupon} disabled={loading || !couponCode.trim()}>
        {loading ? 'Applying...' : 'Apply'}
      </button>
      {error && <div className="error">{error}</div>}
    </div>
  );
}
```

## Error Handling

### Enhanced Error Responses

The new system provides more detailed error information:

```javascript
try {
  await api.post(`/checkout/${cartId}/apply-coupon`, { coupon_code: 'INVALID' });
} catch (error) {
  if (error.response?.status === 422) {
    // Validation errors
    const errors = error.response.data.errors;
    console.log('Validation errors:', errors);
  } else if (error.response?.status === 404) {
    // Cart not found
    console.log('Cart not found');
  }
}
```

## Performance Considerations

### Caching Strategy

With the new consolidated endpoint, you can implement more effective caching:

```javascript
const checkoutCache = new Map();

async function getCachedCheckoutData(cartId) {
  const cacheKey = `checkout_${cartId}`;
  
  if (checkoutCache.has(cacheKey)) {
    const cached = checkoutCache.get(cacheKey);
    if (Date.now() - cached.timestamp < 30000) { // 30 seconds
      return cached.data;
    }
  }
  
  const response = await api.get(`/checkout/initialize/${cartId}`);
  checkoutCache.set(cacheKey, {
    data: response.data,
    timestamp: Date.now()
  });
  
  return response.data;
}
```

## Testing Migration

### Update Your Tests

```javascript
// Before
describe('Checkout Flow', () => {
  it('should load checkout data', async () => {
    const mockSummary = { data: { cart: mockCart } };
    const mockAddresses = { data: mockAddresses };
    
    api.get.mockResolvedValueOnce(mockSummary);
    api.get.mockResolvedValueOnce(mockAddresses);
    
    // ... test implementation
  });
});

// After
describe('Optimized Checkout Flow', () => {
  it('should load checkout data with single API call', async () => {
    const mockCheckoutData = {
      data: {
        cart: mockCart,
        user_addresses: { all: mockAddresses },
        available_payment_methods: mockPaymentMethods,
        checkout_ready: true
      }
    };
    
    api.get.mockResolvedValueOnce(mockCheckoutData);
    
    // ... test implementation
  });
});
```

## Rollback Plan

If you need to rollback to the legacy system:

1. All legacy endpoints remain functional
2. Simply revert your frontend code to use the old endpoints
3. No backend changes required for rollback

## Support

For questions or issues during migration:

1. Check the test suite: `php artisan cart:test-functionality`
2. Review the comprehensive documentation in `docs/OPTIMIZED_CHECKOUT_SYSTEM.md`
3. All legacy endpoints remain available for comparison
