# Wishlist UI Implementation Guide

## Overview

This document provides comprehensive frontend implementation guidelines for the Wishlist Management System. It covers API integration, UI components, user interactions, and authentication requirements for building a complete wishlist experience.

## Table of Contents

1. [API Endpoints Reference](#api-endpoints-reference)
2. [Authentication Requirements](#authentication-requirements)
3. [UI Component Specifications](#ui-component-specifications)
4. [Frontend Integration Guidelines](#frontend-integration-guidelines)
5. [Error Handling Patterns](#error-handling-patterns)
6. [UI States and User Interactions](#ui-states-and-user-interactions)
7. [Implementation Examples](#implementation-examples)

## API Endpoints Reference

### Base URL
All wishlist endpoints are prefixed with `/api/client/wishlist` and require authentication.

### 1. Get User's Wishlist
```http
GET /api/client/wishlist
Authorization: Bearer {token}
```

**Query Parameters:**
- `page` (optional): Page number for pagination
- `per_page` (optional): Items per page (default: 15)
- `sort_by` (optional): Sort field (created_at, product_title, etc.)
- `sort_order` (optional): asc/desc
- `search` (optional): Search in notes
- `product_id` (optional): Filter by product
- `vendor_id` (optional): Filter by vendor

**Response Example:**
```json
{
  "status": true,
  "data": {
    "data": [
      {
        "id": 1,
        "product_id": 123,
        "product_variant_id": null,
        "vendor_id": 45,
        "note": "Want to buy this later",
        "created_at": "2024-01-15 10:30:00",
        "product": {
          "id": 123,
          "title_en": "Premium Vitamin D3",
          "title_ar": "فيتامين د3 المميز",
          "regular_price": 100.00,
          "offer_price": 80.00,
          "is_active": true
        },
        "vendor": {
          "id": 45,
          "name_en": "Health Store",
          "name_ar": "متجر الصحة"
        },
        "effective_price": 80.00,
        "is_available": true
      }
    ],
    "current_page": 1,
    "total": 5,
    "per_page": 15
  },
  "message": "Wishlist data retrieved successfully!"
}
```

### 2. Add Item to Wishlist
```http
POST /api/client/wishlist
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "product_id": 123,
  "product_variant_id": 456, // optional
  "note": "Want to buy this later" // optional
}
```

**Response Example:**
```json
{
  "status": true,
  "data": {
    "id": 1,
    "user_id": 789,
    "product_id": 123,
    "product_variant_id": 456,
    "vendor_id": 45,
    "note": "Want to buy this later",
    "created_at": "2024-01-15 10:30:00"
  },
  "message": "Item added to wishlist successfully!"
}
```

### 3. Get Specific Wishlist Item
```http
GET /api/client/wishlist/{id}
Authorization: Bearer {token}
```

### 4. Remove Item from Wishlist
```http
DELETE /api/client/wishlist/{id}
Authorization: Bearer {token}
```

**Response Example:**
```json
{
  "status": true,
  "data": null,
  "message": "Item removed from wishlist successfully!"
}
```

### 5. Move Item to Cart
```http
POST /api/client/wishlist/{id}/move-to-cart
Authorization: Bearer {token}
```

**Response Example:**
```json
{
  "status": true,
  "data": {
    "cart_item_id": 789,
    "product_id": 123,
    "quantity": 1
  },
  "message": "Item moved to cart successfully!"
}
```

### 6. Bulk Move to Cart
```http
POST /api/client/wishlist/bulk-move-to-cart
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "wishlist_ids": [1, 2, 3]
}
```

**Response Example:**
```json
{
  "status": true,
  "data": {
    "moved": [
      {"wishlist_id": 1, "cart_item_id": 789},
      {"wishlist_id": 2, "cart_item_id": 790}
    ],
    "failed": [
      {"wishlist_id": 3, "error": "Product is no longer available"}
    ]
  },
  "message": "Bulk move to cart completed!"
}
```

### 7. Bulk Delete Items
```http
POST /api/client/wishlist/bulk-delete
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "wishlist_ids": [1, 2, 3]
}
```

## Authentication Requirements

### Required Headers
```javascript
const headers = {
  'Authorization': `Bearer ${userToken}`,
  'Content-Type': 'application/json',
  'Accept': 'application/json'
};
```

### Token Validation
- All wishlist endpoints require valid JWT token
- Expired tokens return 401 Unauthorized
- Invalid tokens return 401 Unauthorized
- Users can only access their own wishlist items

### Unauthenticated State
- Show login prompt when accessing wishlist features
- Redirect to login page for wishlist operations
- Display "Sign in to save items" message

## UI Component Specifications

### 1. Wishlist Button Component
```jsx
<WishlistButton 
  productId={123}
  variantId={456} // optional
  isInWishlist={false}
  isLoading={false}
  onToggle={(productId, variantId) => handleWishlistToggle(productId, variantId)}
  className="wishlist-btn"
/>
```

**States:**
- Default: Heart outline icon
- In Wishlist: Filled heart icon (red/pink)
- Loading: Spinner icon
- Disabled: Grayed out

### 2. Wishlist Page Component
```jsx
<WishlistPage>
  <WishlistHeader 
    itemCount={5}
    onBulkSelect={handleBulkSelect}
    onBulkAction={handleBulkAction}
  />
  <WishlistFilters 
    onFilter={handleFilter}
    onSort={handleSort}
    onSearch={handleSearch}
  />
  <WishlistGrid 
    items={wishlistItems}
    onRemove={handleRemove}
    onMoveToCart={handleMoveToCart}
    onBulkSelect={handleBulkSelect}
  />
  <Pagination 
    currentPage={1}
    totalPages={3}
    onPageChange={handlePageChange}
  />
</WishlistPage>
```

### 3. Wishlist Item Card
```jsx
<WishlistItemCard>
  <ProductImage src={item.product.image} />
  <ProductInfo>
    <ProductTitle>{item.product.title_en}</ProductTitle>
    <VendorName>{item.vendor.name_en}</VendorName>
    <PriceDisplay 
      regularPrice={item.product.regular_price}
      offerPrice={item.effective_price}
    />
    <AvailabilityStatus isAvailable={item.is_available} />
  </ProductInfo>
  <ItemActions>
    <MoveToCartButton 
      disabled={!item.is_available}
      onClick={() => handleMoveToCart(item.id)}
    />
    <RemoveButton onClick={() => handleRemove(item.id)} />
  </ItemActions>
  <SelectCheckbox 
    checked={selectedItems.includes(item.id)}
    onChange={(checked) => handleItemSelect(item.id, checked)}
  />
</WishlistItemCard>
```

### 4. Bulk Actions Toolbar
```jsx
<BulkActionsToolbar 
  selectedCount={selectedItems.length}
  onMoveAllToCart={() => handleBulkMoveToCart(selectedItems)}
  onDeleteAll={() => handleBulkDelete(selectedItems)}
  onClearSelection={handleClearSelection}
/>
```

## Frontend Integration Guidelines

### 1. Adding Items to Wishlist

**From Product Page:**
```javascript
const addToWishlist = async (productId, variantId = null, note = '') => {
  try {
    setLoading(true);
    const response = await fetch('/api/client/wishlist', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        product_id: productId,
        product_variant_id: variantId,
        note: note
      })
    });
    
    const data = await response.json();
    
    if (data.status) {
      showSuccessMessage(data.message);
      updateWishlistState(productId, true);
    } else {
      showErrorMessage(data.message || 'Failed to add to wishlist');
    }
  } catch (error) {
    showErrorMessage('Network error occurred');
  } finally {
    setLoading(false);
  }
};
```

**From Cart (Save for Later):**
```javascript
const saveForLater = async (cartItemIds) => {
  try {
    const response = await fetch('/api/client/cart/save-for-later', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        cart_item_ids: cartItemIds
      })
    });
    
    const data = await response.json();
    
    if (data.status) {
      showSuccessMessage(data.message);
      updateCartState(data.data.cart);
      if (data.data.skipped_items_count > 0) {
        showInfoMessage(`${data.data.skipped_items_count} items were already in wishlist`);
      }
    }
  } catch (error) {
    showErrorMessage('Failed to save items for later');
  }
};
```

### 2. Wishlist State Management

**Using React Context:**
```javascript
const WishlistContext = createContext();

const WishlistProvider = ({ children }) => {
  const [wishlistItems, setWishlistItems] = useState([]);
  const [wishlistCount, setWishlistCount] = useState(0);
  const [loading, setLoading] = useState(false);
  
  const fetchWishlist = async () => {
    // Implementation
  };
  
  const addToWishlist = async (productId, variantId, note) => {
    // Implementation
  };
  
  const removeFromWishlist = async (itemId) => {
    // Implementation
  };
  
  const moveToCart = async (itemId) => {
    // Implementation
  };
  
  return (
    <WishlistContext.Provider value={{
      wishlistItems,
      wishlistCount,
      loading,
      fetchWishlist,
      addToWishlist,
      removeFromWishlist,
      moveToCart
    }}>
      {children}
    </WishlistContext.Provider>
  );
};
```

### 3. Real-time Updates

**WebSocket Integration:**
```javascript
const useWishlistSocket = () => {
  const { updateWishlistCount } = useWishlist();
  
  useEffect(() => {
    const socket = io('/wishlist');
    
    socket.on('wishlist_updated', (data) => {
      updateWishlistCount(data.count);
    });
    
    socket.on('item_moved_to_cart', (data) => {
      showSuccessMessage(`${data.product_name} moved to cart`);
    });
    
    return () => socket.disconnect();
  }, []);
};
```

## Error Handling Patterns

### 1. API Error Responses
```javascript
const handleApiError = (response, data) => {
  switch (response.status) {
    case 401:
      // Redirect to login
      redirectToLogin();
      break;
    case 422:
      // Validation errors
      if (data.errors) {
        showValidationErrors(data.errors);
      } else {
        showErrorMessage(data.message || 'Validation failed');
      }
      break;
    case 404:
      showErrorMessage('Item not found');
      break;
    case 500:
      showErrorMessage('Server error occurred');
      break;
    default:
      showErrorMessage(data.message || 'An error occurred');
  }
};
```

### 2. Network Error Handling
```javascript
const makeWishlistRequest = async (url, options) => {
  try {
    const response = await fetch(url, options);
    const data = await response.json();
    
    if (!response.ok) {
      handleApiError(response, data);
      return null;
    }
    
    return data;
  } catch (error) {
    if (error.name === 'NetworkError') {
      showErrorMessage('Please check your internet connection');
    } else {
      showErrorMessage('An unexpected error occurred');
    }
    return null;
  }
};
```

### 3. Validation Error Display
```jsx
const WishlistForm = () => {
  const [errors, setErrors] = useState({});
  
  const handleSubmit = async (formData) => {
    const result = await addToWishlist(formData);
    
    if (result && result.errors) {
      setErrors(result.errors);
    } else {
      setErrors({});
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <input name="note" />
      {errors.note && <span className="error">{errors.note[0]}</span>}
      <button type="submit">Add to Wishlist</button>
    </form>
  );
};
```

## UI States and User Interactions

### 1. Loading States
- **Initial Load**: Skeleton cards while fetching wishlist
- **Adding Item**: Spinner on wishlist button
- **Moving to Cart**: Loading indicator on move button
- **Bulk Operations**: Progress bar for multiple items

### 2. Empty States
```jsx
const EmptyWishlist = () => (
  <div className="empty-wishlist">
    <HeartIcon className="empty-icon" />
    <h3>Your wishlist is empty</h3>
    <p>Save items you love to buy them later</p>
    <Button onClick={() => navigate('/products')}>
      Start Shopping
    </Button>
  </div>
);
```

### 3. Success States
- **Item Added**: Toast notification with undo option
- **Item Moved to Cart**: Toast with "View Cart" link
- **Bulk Actions**: Summary notification

### 4. Error States
- **Item Unavailable**: Grayed out with "Out of Stock" label
- **Network Error**: Retry button
- **Authentication Error**: Login prompt

### 5. Interactive States
- **Hover Effects**: Subtle animations on cards
- **Selection Mode**: Checkboxes appear for bulk operations
- **Drag and Drop**: Reorder wishlist items (optional)

## Implementation Examples

### 1. Complete Wishlist Hook
```javascript
const useWishlist = () => {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const fetchWishlist = useCallback(async (params = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const queryString = new URLSearchParams(params).toString();
      const response = await fetch(`/api/client/wishlist?${queryString}`, {
        headers: { 'Authorization': `Bearer ${getToken()}` }
      });
      
      const data = await response.json();
      
      if (data.status) {
        setItems(data.data.data);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Failed to fetch wishlist');
    } finally {
      setLoading(false);
    }
  }, []);
  
  const addItem = useCallback(async (productId, variantId, note) => {
    try {
      const response = await fetch('/api/client/wishlist', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          product_id: productId,
          product_variant_id: variantId,
          note
        })
      });
      
      const data = await response.json();
      
      if (data.status) {
        setItems(prev => [data.data, ...prev]);
        return { success: true, data: data.data };
      } else {
        return { success: false, error: data.message };
      }
    } catch (err) {
      return { success: false, error: 'Failed to add item' };
    }
  }, []);
  
  const removeItem = useCallback(async (itemId) => {
    try {
      const response = await fetch(`/api/client/wishlist/${itemId}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${getToken()}` }
      });
      
      const data = await response.json();
      
      if (data.status) {
        setItems(prev => prev.filter(item => item.id !== itemId));
        return { success: true };
      } else {
        return { success: false, error: data.message };
      }
    } catch (err) {
      return { success: false, error: 'Failed to remove item' };
    }
  }, []);
  
  const moveToCart = useCallback(async (itemId) => {
    try {
      const response = await fetch(`/api/client/wishlist/${itemId}/move-to-cart`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${getToken()}` }
      });
      
      const data = await response.json();
      
      if (data.status) {
        setItems(prev => prev.filter(item => item.id !== itemId));
        return { success: true, data: data.data };
      } else {
        return { success: false, error: data.message };
      }
    } catch (err) {
      return { success: false, error: 'Failed to move item to cart' };
    }
  }, []);
  
  const bulkMoveToCart = useCallback(async (itemIds) => {
    try {
      const response = await fetch('/api/client/wishlist/bulk-move-to-cart', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ wishlist_ids: itemIds })
      });
      
      const data = await response.json();
      
      if (data.status) {
        const movedIds = data.data.moved.map(item => item.wishlist_id);
        setItems(prev => prev.filter(item => !movedIds.includes(item.id)));
        return { success: true, data: data.data };
      } else {
        return { success: false, error: data.message };
      }
    } catch (err) {
      return { success: false, error: 'Failed to move items to cart' };
    }
  }, []);
  
  const bulkDelete = useCallback(async (itemIds) => {
    try {
      const response = await fetch('/api/client/wishlist/bulk-delete', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ wishlist_ids: itemIds })
      });
      
      const data = await response.json();
      
      if (data.status) {
        setItems(prev => prev.filter(item => !itemIds.includes(item.id)));
        return { success: true, data: data.data };
      } else {
        return { success: false, error: data.message };
      }
    } catch (err) {
      return { success: false, error: 'Failed to delete items' };
    }
  }, []);
  
  return {
    items,
    loading,
    error,
    fetchWishlist,
    addItem,
    removeItem,
    moveToCart,
    bulkMoveToCart,
    bulkDelete
  };
};
```

### 2. Wishlist Button Component
```jsx
const WishlistButton = ({ 
  productId, 
  variantId = null, 
  isInWishlist = false, 
  className = '',
  size = 'medium' 
}) => {
  const [loading, setLoading] = useState(false);
  const [inWishlist, setInWishlist] = useState(isInWishlist);
  const { addItem, removeItem } = useWishlist();
  
  const handleToggle = async () => {
    if (loading) return;
    
    setLoading(true);
    
    try {
      if (inWishlist) {
        // Find and remove item
        const result = await removeItem(/* item id needed */);
        if (result.success) {
          setInWishlist(false);
          toast.success('Removed from wishlist');
        } else {
          toast.error(result.error);
        }
      } else {
        // Add item
        const result = await addItem(productId, variantId);
        if (result.success) {
          setInWishlist(true);
          toast.success('Added to wishlist');
        } else {
          toast.error(result.error);
        }
      }
    } catch (error) {
      toast.error('Something went wrong');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <button
      onClick={handleToggle}
      disabled={loading}
      className={`wishlist-btn ${className} ${size} ${inWishlist ? 'active' : ''}`}
      aria-label={inWishlist ? 'Remove from wishlist' : 'Add to wishlist'}
    >
      {loading ? (
        <Spinner size="small" />
      ) : (
        <HeartIcon filled={inWishlist} />
      )}
    </button>
  );
};
```

### 3. Responsive Design Considerations
```css
/* Mobile-first approach */
.wishlist-page {
  padding: 1rem;
}

.wishlist-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .wishlist-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .wishlist-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .wishlist-page {
    padding: 2rem;
  }
}

/* Touch-friendly buttons on mobile */
@media (max-width: 767px) {
  .wishlist-btn {
    min-height: 44px;
    min-width: 44px;
  }

  .bulk-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    padding: 1rem;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
  }
}
```

### 4. Accessibility Guidelines
```jsx
const AccessibleWishlistButton = ({ productId, isInWishlist, onToggle }) => {
  return (
    <button
      onClick={() => onToggle(productId)}
      aria-label={isInWishlist ? 'Remove from wishlist' : 'Add to wishlist'}
      aria-pressed={isInWishlist}
      className={`wishlist-btn ${isInWishlist ? 'active' : ''}`}
    >
      <HeartIcon aria-hidden="true" />
      <span className="sr-only">
        {isInWishlist ? 'Remove from wishlist' : 'Add to wishlist'}
      </span>
    </button>
  );
};

// Keyboard navigation support
const WishlistGrid = ({ items, onItemSelect }) => {
  const handleKeyDown = (e, itemId) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onItemSelect(itemId);
    }
  };

  return (
    <div className="wishlist-grid" role="grid">
      {items.map(item => (
        <div
          key={item.id}
          role="gridcell"
          tabIndex={0}
          onKeyDown={(e) => handleKeyDown(e, item.id)}
          className="wishlist-item"
        >
          <WishlistItemCard item={item} />
        </div>
      ))}
    </div>
  );
};
```

### 5. Performance Optimization
```javascript
// Lazy loading for wishlist images
const LazyWishlistImage = ({ src, alt, ...props }) => {
  const [imageSrc, setImageSrc] = useState(null);
  const [imageRef, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  useEffect(() => {
    if (inView && src) {
      setImageSrc(src);
    }
  }, [inView, src]);

  return (
    <div ref={imageRef} className="image-container">
      {imageSrc ? (
        <img src={imageSrc} alt={alt} {...props} />
      ) : (
        <div className="image-placeholder">
          <ImageIcon />
        </div>
      )}
    </div>
  );
};

// Debounced search for wishlist filtering
const useWishlistSearch = (initialQuery = '') => {
  const [query, setQuery] = useState(initialQuery);
  const [debouncedQuery, setDebouncedQuery] = useState(initialQuery);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query]);

  return [query, setQuery, debouncedQuery];
};

// Virtual scrolling for large wishlists
const VirtualizedWishlistGrid = ({ items, itemHeight = 200 }) => {
  const parentRef = useRef();

  const rowVirtualizer = useVirtualizer({
    count: Math.ceil(items.length / 3), // 3 items per row
    getScrollElement: () => parentRef.current,
    estimateSize: () => itemHeight,
  });

  return (
    <div ref={parentRef} className="wishlist-scroll-container">
      <div
        style={{
          height: `${rowVirtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {rowVirtualizer.getVirtualItems().map((virtualRow) => {
          const startIndex = virtualRow.index * 3;
          const endIndex = Math.min(startIndex + 3, items.length);
          const rowItems = items.slice(startIndex, endIndex);

          return (
            <div
              key={virtualRow.index}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualRow.size}px`,
                transform: `translateY(${virtualRow.start}px)`,
              }}
              className="wishlist-row"
            >
              {rowItems.map(item => (
                <WishlistItemCard key={item.id} item={item} />
              ))}
            </div>
          );
        })}
      </div>
    </div>
  );
};
```

### 6. Testing Guidelines
```javascript
// Unit tests for wishlist functionality
describe('Wishlist Hook', () => {
  test('should add item to wishlist', async () => {
    const { result } = renderHook(() => useWishlist());

    await act(async () => {
      const response = await result.current.addItem(123, null, 'Test note');
      expect(response.success).toBe(true);
    });

    expect(result.current.items).toHaveLength(1);
  });

  test('should handle duplicate item error', async () => {
    const { result } = renderHook(() => useWishlist());

    // Add item first time
    await act(async () => {
      await result.current.addItem(123);
    });

    // Try to add same item again
    await act(async () => {
      const response = await result.current.addItem(123);
      expect(response.success).toBe(false);
      expect(response.error).toContain('already in wishlist');
    });
  });
});

// Integration tests
describe('Wishlist Integration', () => {
  test('should move item from wishlist to cart', async () => {
    render(<WishlistPage />);

    // Wait for wishlist to load
    await waitFor(() => {
      expect(screen.getByText('Premium Vitamin D3')).toBeInTheDocument();
    });

    // Click move to cart button
    const moveButton = screen.getByLabelText('Move to cart');
    fireEvent.click(moveButton);

    // Verify success message
    await waitFor(() => {
      expect(screen.getByText('Item moved to cart successfully!')).toBeInTheDocument();
    });

    // Verify item is removed from wishlist
    expect(screen.queryByText('Premium Vitamin D3')).not.toBeInTheDocument();
  });
});

// E2E tests with Cypress
describe('Wishlist E2E', () => {
  it('should allow user to manage wishlist items', () => {
    cy.login('<EMAIL>', 'password');
    cy.visit('/products/123');

    // Add to wishlist
    cy.get('[data-testid="wishlist-button"]').click();
    cy.contains('Added to wishlist').should('be.visible');

    // Go to wishlist page
    cy.visit('/wishlist');
    cy.contains('Premium Vitamin D3').should('be.visible');

    // Move to cart
    cy.get('[data-testid="move-to-cart"]').first().click();
    cy.contains('Item moved to cart successfully!').should('be.visible');

    // Verify item removed from wishlist
    cy.contains('Premium Vitamin D3').should('not.exist');
  });
});
```

This comprehensive guide provides all the necessary information for implementing a complete wishlist UI that integrates seamlessly with the backend API. The examples show real-world implementation patterns that can be adapted to different frontend frameworks and design systems, with considerations for performance, accessibility, responsive design, and thorough testing.
