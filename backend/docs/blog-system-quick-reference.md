# Blog System API - Quick Reference

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install axios react-helmet-async
# or
yarn add axios react-helmet-async
```

### 2. Basic Setup
```typescript
import { BlogApiClient } from './blog-api-client';

const blogApi = new BlogApiClient();
```

### 3. Get Blog List
```typescript
const blogs = await blogApi.getBlogs({
  page: 1,
  per_page: 15,
  category_id: 2
});
```

---

## 📋 API Endpoints Cheat Sheet

| Method | Endpoint | Auth Required | Description |
|--------|----------|---------------|-------------|
| `GET` | `/api/client/blogs` | ❌ | Get published blogs list |
| `GET` | `/api/client/blogs/{slug}` | ❌ | Get single blog by slug |
| `GET` | `/api/client/blogs/category/{slug}` | ❌ | Get blogs by category |
| `GET` | `/api/client/blogs/featured` | ❌ | Get featured blogs |
| `GET` | `/api/client/blogs/{slug}/related` | ❌ | Get related blogs |
| `GET` | `/api/client/blogs/{id}/comments` | ❌ | Get blog comments |
| `GET` | `/api/client/comments/{id}` | ❌ | Get single comment |
| `POST` | `/api/client/comments` | ✅ | Create comment |
| `POST` | `/api/client/comments/reply` | ✅ | Reply to comment |
| `PUT` | `/api/client/comments/{id}` | ✅ | Update own comment |
| `DELETE` | `/api/client/comments/{id}` | ✅ | Delete own comment |
| `GET` | `/api/client/comments/my-comments` | ✅ | Get user's comments |

---

## 🔧 Common Query Parameters

### Blog List Parameters
```typescript
interface BlogListParams {
  page?: number;           // Page number (default: 1)
  per_page?: number;       // Items per page (default: 15, max: 50)
  category_id?: number;    // Filter by category ID
  search?: string;         // Search in title and content
  featured?: boolean;      // Filter featured blogs only
}
```

### Comment List Parameters
```typescript
interface CommentListParams {
  page?: number;           // Page number (default: 1)
  per_page?: number;       // Items per page (default: 20, max: 50)
}
```

---

## 📝 Request/Response Examples

### Get Blogs
```typescript
// Request
const response = await fetch('/api/client/blogs?page=1&category_id=2');

// Response
{
  "status": true,
  "message": "Blogs retrieved successfully",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "title_en": "Blog Title",
        "title_ar": "عنوان المدونة",
        "slug": "blog-title",
        "summary_en": "Blog summary...",
        "featured_image": "blog-images/image.jpg",
        "published_at": "2024-01-15T10:30:00.000000Z",
        "category": {
          "id": 2,
          "title_en": "Shopping Tips",
          "slug": "shopping-tips"
        },
        "author": {
          "id": 1,
          "name": "John Doe"
        },
        "comments_count": 15,
        "reading_time": 5
      }
    ],
    "total": 68,
    "per_page": 15,
    "current_page": 1,
    "last_page": 5
  }
}
```

### Create Comment
```typescript
// Request
const response = await fetch('/api/client/comments', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    blog_id: 1,
    comment: "Great article! Thanks for sharing."
  })
});

// Response
{
  "status": true,
  "message": "Comment created successfully. It will be visible after approval.",
  "data": {
    "id": 25,
    "comment": "Great article! Thanks for sharing.",
    "is_approved": false,
    "created_at": "2024-01-20T10:15:00.000000Z",
    "user": {
      "id": 8,
      "name": "Current User"
    }
  }
}
```

---

## ⚡ React Hooks Quick Examples

### useBlogList Hook
```typescript
const {
  blogs,
  loading,
  error,
  pagination,
  setFilters,
  changePage
} = useBlogList({ category_id: 2 });

// Usage
<BlogList
  blogs={blogs}
  loading={loading}
  error={error}
  pagination={pagination}
  onPageChange={changePage}
  language="en"
/>
```

### useComments Hook
```typescript
const {
  comments,
  loading,
  submitComment,
  updateComment,
  deleteComment
} = useComments(blogId);

// Usage
<CommentThread
  blogId={blogId}
  comments={comments}
  currentUser={user}
  onCommentSubmit={submitComment}
  onCommentUpdate={updateComment}
  onCommentDelete={deleteComment}
/>
```

---

## 🎨 Component Examples

### BlogCard Component
```tsx
<BlogCard
  blog={blog}
  language="en"
  showCategory={true}
  showAuthor={true}
  showReadingTime={true}
/>
```

### CommentForm Component
```tsx
<CommentForm
  onSubmit={handleCommentSubmit}
  authenticated={!!user}
  onAuthRequired={() => setShowLogin(true)}
  placeholder="Write your comment..."
/>
```

### Search Filters
```tsx
<SearchFilters
  categories={categories}
  onFiltersChange={handleFiltersChange}
  language="en"
/>
```

---

## 🌐 Multilingual Support

### Language Toggle
```tsx
const [language, setLanguage] = useState<'en' | 'ar'>('en');

<LanguageToggle
  currentLanguage={language}
  onLanguageChange={setLanguage}
/>
```

### Content Display
```tsx
const title = language === 'ar' ? blog.title_ar : blog.title_en;
const content = language === 'ar' ? blog.content_ar : blog.content_en;

<div dir={language === 'ar' ? 'rtl' : 'ltr'}>
  <h1>{title}</h1>
  <div dangerouslySetInnerHTML={{ __html: content }} />
</div>
```

---

## 🔐 Authentication

### Headers for Authenticated Requests
```typescript
const headers = {
  'Authorization': `Bearer ${userToken}`,
  'Content-Type': 'application/json',
  'Accept': 'application/json'
};
```

### Check Authentication Status
```typescript
const isAuthenticated = !!userToken;

if (!isAuthenticated) {
  // Show login prompt
  onAuthRequired();
  return;
}
```

---

## 🚨 Error Handling

### API Error Types
```typescript
enum BlogErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  UNAUTHORIZED = 'UNAUTHORIZED',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  RATE_LIMITED = 'RATE_LIMITED',
}
```

### Error Handling Example
```typescript
try {
  const blog = await blogApi.getBlog(slug);
} catch (error) {
  if (error.type === BlogErrorType.NOT_FOUND) {
    // Show 404 page
  } else if (error.type === BlogErrorType.UNAUTHORIZED) {
    // Redirect to login
  } else {
    // Show generic error message
  }
}
```

---

## 📱 Responsive Design

### CSS Grid for Blog Cards
```css
.blog-list__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .blog-list__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .blog-list__grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

### Mobile-First Approach
```css
/* Mobile styles first */
.blog-card {
  padding: 1rem;
}

/* Then tablet and desktop */
@media (min-width: 768px) {
  .blog-card {
    padding: 1.5rem;
  }
}
```

---

## 🔍 SEO Implementation

### Meta Tags
```tsx
<Helmet>
  <title>{blog.meta_title}</title>
  <meta name="description" content={blog.meta_description} />
  <meta name="keywords" content={blog.keywords} />
  <meta property="og:title" content={blog.title_en} />
  <meta property="og:description" content={blog.meta_description} />
  <meta property="og:image" content={blog.featured_image} />
</Helmet>
```

### Structured Data
```tsx
const structuredData = {
  "@context": "https://schema.org",
  "@type": "BlogPosting",
  "headline": blog.title_en,
  "description": blog.meta_description,
  "author": { "@type": "Person", "name": blog.author.name },
  "datePublished": blog.published_at
};

<script
  type="application/ld+json"
  dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
/>
```

---

## 🎯 Performance Tips

1. **Lazy Loading**: Use `loading="lazy"` for images
2. **Caching**: Implement API response caching
3. **Pagination**: Use pagination instead of loading all blogs
4. **Image Optimization**: Use responsive images with `srcSet`
5. **Code Splitting**: Split blog components into separate chunks

---

## 📚 Additional Resources

- [Full API Documentation](./blog-system-frontend-guide.md)
- [TypeScript Definitions](./blog-system-types.ts)
- [Component Examples](./blog-system-frontend-guide.md#integration-examples)
- [Error Handling Guide](./blog-system-frontend-guide.md#technical-implementation-details)
