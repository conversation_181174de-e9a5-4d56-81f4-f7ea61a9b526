# Laravel Scout + Meilisearch Setup Guide for Ubuntu

## System Requirements
- Ubuntu 18.04+ (or compatible Linux distribution)
- PHP 8.2+
- Composer
- <PERSON><PERSON> 10+
- curl

## Step 1: Install Meilisearch Server

### Download and Install Meilisearch
```bash
# Download and install Meilisearch
curl -L https://install.meilisearch.com | sh

# Make executable (if needed)
chmod +x meilisearch

# Move to system path (optional)
sudo mv meilisearch /usr/local/bin/
```

### Start Meilisearch Server
```bash
# Start server (from project directory if not moved to system path)
./meilisearch

# Or if moved to system path
meilisearch

# Start with custom configuration
meilisearch --http-addr 127.0.0.1:7700 --env development
```

## Step 2: Install Laravel Scout Package

### Install Required Packages
```bash
# Navigate to your Laravel project
cd /path/to/your/laravel/project

# Install Laravel Scout and Meilisearch PHP client
composer require laravel/scout meilisearch/meilisearch-php
```

### Publish Scout Configuration
```bash
# Publish Scout config file
php artisan vendor:publish --provider="Lara<PERSON>\Scout\ScoutServiceProvider"
```

## Step 3: Configure Environment

### Update .env File
```env
# Scout Configuration
SCOUT_DRIVER=meilisearch

# Meilisearch Configuration
MEILISEARCH_HOST=http://127.0.0.1:7700
MEILISEARCH_KEY=

# Optional: Set index prefix
SCOUT_PREFIX=your_app_
```

### Update config/scout.php (Optional)
```php
'meilisearch' => [
    'host' => env('MEILISEARCH_HOST', 'http://localhost:7700'),
    'key' => env('MEILISEARCH_KEY'),
    'index-settings' => [
        'products' => [
            'filterableAttributes' => ['is_active', 'category_id'],
            'sortableAttributes' => ['created_at', 'price'],
            'searchableAttributes' => ['title_en', 'title_ar', 'system_sku'],
        ],
    ],
],
```

## Step 4: Prepare Your Model

### Add Searchable Trait to Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Laravel\Scout\Searchable;

class Product extends Model
{
    use Searchable;

    protected $fillable = [
        'title_en', 'title_ar', 'system_sku', 'vendor_sku', 
        'barcode', 'is_active', 'price'
    ];

    /**
     * Get the indexable data array for the model.
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'title_en' => $this->title_en,
            'title_ar' => $this->title_ar,
            'system_sku' => $this->system_sku,
            'vendor_sku' => $this->vendor_sku,
            'barcode' => $this->barcode,
            'is_active' => $this->is_active,
            'price' => $this->price,
        ];
    }

    /**
     * Determine if the model should be searchable.
     */
    public function shouldBeSearchable(): bool
    {
        return $this->is_active;
    }

    /**
     * Get the name of the index associated with the model.
     */
    public function searchableAs(): string
    {
        return 'products_index';
    }
}
```

## Step 5: Import Existing Data

### Import All Records
```bash
# Import all products to Meilisearch
php artisan scout:import "App\Models\Product"

# Import with progress bar (if available)
php artisan scout:import "App\Models\Product" --verbose
```

### Verify Import
```bash
# Check if data is indexed (using curl)
curl -X GET 'http://127.0.0.1:7700/indexes/products_index/documents' \
  -H 'Content-Type: application/json'
```

## Step 6: Test Search Functionality

### Basic Search Usage
```php
<?php

// In your controller or service
use App\Models\Product;

class SearchController extends Controller
{
    public function search(Request $request)
    {
        $query = $request->input('q');
        
        // Basic search
        $products = Product::search($query)->get();
        
        // Search with filters
        $products = Product::search($query)
            ->where('is_active', true)
            ->take(10)
            ->get();
            
        // Search with pagination
        $products = Product::search($query)
            ->where('is_active', true)
            ->paginate(15);
            
        return response()->json($products);
    }
}
```

### Advanced Search Features
```php
// Search with custom filters
$products = Product::search('apple')
    ->where('is_active', true)
    ->where('price', '>', 100)
    ->orderBy('price', 'asc')
    ->get();

// Get search results with IDs only
$productIds = Product::search('apple')->keys();

// Raw Meilisearch query
$results = Product::search('apple', function ($meilisearch, $query, $options) {
    $options['attributesToRetrieve'] = ['id', 'title_en', 'price'];
    $options['limit'] = 20;
    return $meilisearch->search($query, $options);
})->get();
```

## Step 7: Useful Commands

### Scout Commands
```bash
# Import all models
php artisan scout:import "App\Models\Product"

# Flush (delete) all records from index
php artisan scout:flush "App\Models\Product"

# Re-import all data (flush + import)
php artisan scout:flush "App\Models\Product"
php artisan scout:import "App\Models\Product"

# Import specific model instance
php artisan tinker
>>> App\Models\Product::find(1)->searchable();
```

### Meilisearch Server Commands
```bash
# Start server
./meilisearch

# Start with custom port
./meilisearch --http-addr 127.0.0.1:7701

# Start with master key (production)
./meilisearch --master-key your-master-key

# Start in production mode
./meilisearch --env production
```

## Step 8: Production Setup

### Create Systemd Service (Ubuntu)
```bash
# Create service file
sudo nano /etc/systemd/system/meilisearch.service
```

```ini
[Unit]
Description=Meilisearch
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
ExecStart=/usr/local/bin/meilisearch --env production --master-key YOUR_MASTER_KEY
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl enable meilisearch
sudo systemctl start meilisearch
sudo systemctl status meilisearch
```

### Production Environment Variables
```env
SCOUT_DRIVER=meilisearch
MEILISEARCH_HOST=http://127.0.0.1:7700
MEILISEARCH_KEY=your-master-key
SCOUT_QUEUE=true
```

## Step 9: Troubleshooting

### Common Issues
```bash
# Check if Meilisearch is running
curl http://127.0.0.1:7700/health

# Check indexes
curl http://127.0.0.1:7700/indexes

# Check specific index
curl http://127.0.0.1:7700/indexes/products_index

# View logs
tail -f storage/logs/laravel.log

# Check Meilisearch logs (if running as service)
sudo journalctl -u meilisearch -f
```

### Performance Optimization
```bash
# Queue search indexing (recommended for production)
# Add to .env
SCOUT_QUEUE=true

# Process search queue
php artisan queue:work --queue=scout
```

## Step 10: Testing

### Create Test Search
```php
// In routes/web.php or api.php
Route::get('/test-search', function () {
    $products = App\Models\Product::search('test')->get();
    return response()->json([
        'count' => $products->count(),
        'products' => $products->take(5)
    ]);
});
```

### Visit Test URL
```bash
# Test the search
curl http://your-app.com/test-search
```

## Additional Resources

- [Laravel Scout Documentation](https://laravel.com/docs/scout)
- [Meilisearch Documentation](https://docs.meilisearch.com/)
- [Meilisearch PHP SDK](https://github.com/meilisearch/meilisearch-php)

## Quick Reference

```bash
# Complete setup in one go
curl -L https://install.meilisearch.com | sh
./meilisearch &
composer require laravel/scout meilisearch/meilisearch-php
php artisan vendor:publish --provider="Laravel\Scout\ScoutServiceProvider"
# Add SCOUT_DRIVER=meilisearch to .env
php artisan scout:import "App\Models\Product"
```