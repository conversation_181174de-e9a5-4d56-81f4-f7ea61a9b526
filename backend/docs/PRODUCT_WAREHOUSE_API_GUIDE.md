# Product Warehouse API Implementation Guide

## Overview
Products are now assigned to warehouses through the inventory system, allowing better multi-warehouse support and inventory tracking.

## API Endpoints

### Get Active Warehouses
```http
GET /api/general/warehouses/active-list
```

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "name_en": "Main Warehouse",
      "name_ar": "المستودع الرئيسي",
      "code": "WH001",
      "status": "active"
    }
  ]
}
```

### Create Product with Warehouse
```http
POST /api/admin/products
```

**Request Body:**
```json
{
  "category_id": 1,
  "sub_category_id": 1,
  "class_id": 1,
  "brand_id": 1,
  "vendor_sku": "SKU-001",
  "model_number": "MODEL-001",
  "title_en": "Product Name",
  "regular_price": 99.99,
  
  // Inventory fields (all optional)
  "warehouse_id": 1,
  "stock": 100,
  "reserved": 5,
  "threshold": 10,
  "stock_status": "in_stock",
  "note": "Inventory notes"
}
```

### Update Product Warehouse
```http
PUT /api/admin/products/{id}
```

**Request Body:**
```json
{
  "title_en": "Updated Product Name",
  "warehouse_id": 2,
  "stock": 150,
  "threshold": 15
}
```

### Get Product with Warehouse Data
```http
GET /api/admin/products/{id}
```

**Response:**
```json
{
  "data": {
    "id": 1,
    "title_en": "Product Name",
    "regular_price": "99.99",
    "inventory": {
      "id": 1,
      "warehouse_id": 1,
      "stock": 100,
      "reserved": 5,
      "threshold": 10,
      "stock_status": "in_stock",
      "note": "Inventory notes",
      "warehouse": {
        "id": 1,
        "name_en": "Main Warehouse",
        "name_ar": "المستودع الرئيسي",
        "code": "WH001",
        "address": "123 Warehouse Street",
        "status": "active"
      }
    },
    "productVariants": [
      {
        "id": 1,
        "inventory": {
          "warehouse_id": 2,
          "stock": 50,
          "warehouse": {
            "id": 2,
            "name_en": "Secondary Warehouse"
          }
        }
      }
    ]
  }
}
```

## Request Fields

### Inventory Management Fields
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `warehouse_id` | integer | No | Warehouse ID (must exist in warehouses table) |
| `stock` | integer | No | Current stock quantity (≥ 0) |
| `reserved` | integer | No | Reserved stock quantity (≥ 0) |
| `threshold` | integer | No | Low stock alert threshold (≥ 0) |
| `stock_status` | string | No | `in_stock`, `out_of_stock`, `low_stock` |
| `note` | string | No | Inventory notes |

### Product Variants
For variant products, include inventory fields in variant data:
```json
{
  "product_variants": [
    {
      "attribute_id": 1,
      "attribute_value_id": 1,
      "regular_price": 99.99,
      "warehouse_id": 1,
      "stock": 50,
      "threshold": 5
    }
  ]
}
```

## Validation Rules

- `warehouse_id`: Must exist in warehouses table if provided
- `stock`, `reserved`, `threshold`: Non-negative integers
- `stock_status`: One of `in_stock`, `out_of_stock`, `low_stock`
- All inventory fields are optional

## Error Responses

**422 Validation Error:**
```json
{
  "message": "The given data was invalid.",
  "errors": {
    "warehouse_id": ["The selected warehouse id is invalid."],
    "stock": ["The stock must be at least 0."]
  }
}
```

## Key Changes

1. **Warehouse assignment moved from products to inventory table**
2. **Multiple warehouses supported per product (through variants)**
3. **All inventory fields are optional**
4. **Backward compatible - existing products work without changes**

## Migration Required

Run this migration to update your database:
```bash
php artisan migrate --path=database/migrations/2025_07_28_120000_rename_location_to_warehouse_id_in_inventories_table.php
```

## Frontend Integration

1. **Fetch warehouses** from `/api/general/warehouses/active-list`
2. **Add inventory fields** to product forms
3. **Display warehouse info** from `product.inventory.warehouse`
4. **Handle optional nature** - products may not have warehouse assigned
5. **Show stock calculations** - Available = Stock - Reserved

## Testing

- Create product without warehouse ✓
- Create product with warehouse ✓  
- Update warehouse assignment ✓
- Handle validation errors ✓
- Display warehouse information ✓
