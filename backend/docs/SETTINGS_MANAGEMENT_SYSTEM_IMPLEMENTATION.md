# Settings Management System Implementation

## Project Overview

This document outlines the comprehensive task breakdown for implementing a centralized Settings Management System for the multi-vendor eCommerce platform. The system provides a unified interface for managing all platform configurations through a flexible key-value storage approach with proper validation, caching, and access control.

## Current Project Context

### Existing Infrastructure
- **Framework**: Laravel with API-first architecture
- **Authentication**: Laravel Passport (API tokens)
- **Database**: MySQL with comprehensive migrations
- **Architecture**: Multi-vendor platform with User, Vendor, Product, and Cart systems
- **Existing Models**: User, Product, Vendor, ShoppingCart, CartItem, Customer
- **Middleware**: Authentication and authorization middleware
- **Services**: CartService, ProductService, UserService patterns
- **Caching**: Redis/File-based caching system

### Database Foundation
- **Settings Table**: New migration required for flexible key-value storage
- **Data Types**: Support for string, boolean, integer, float, json, array types
- **Environment Support**: Development, staging, production configurations
- **Caching Layer**: Automatic cache invalidation and refresh

## 🏗️ **CRITICAL: Architectural Patterns and Conventions**

**⚠️ MANDATORY REQUIREMENT**: All new controllers and services MUST follow the established patterns used in this Laravel project. Use `app/Http/Controllers/Admin/BannerController.php` as the reference pattern.

### Required Controller Structure Pattern

**Reference Controller**: `app/Http/Controllers/Admin/BannerController.php`

#### 1. **Controller Class Structure**
```php
<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Settings\StoreSettingRequest;
use App\Http\Requests\Settings\UpdateSettingRequest;
use App\Http\Requests\Settings\BulkUpdateSettingsRequest;
use App\Services\SettingService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SettingController extends Controller
{
    use HelperTrait;

    private SettingService $service;

    public function __construct(SettingService $service)
    {
        $this->service = $service;
    }

    // Standard CRUD methods following BannerController pattern
}
```

#### 2. **Required Method Patterns**

**Index Method Pattern**:
```php
public function index(Request $request): JsonResponse
{
    try {
        $settings = $this->service->index($request);

        return $this->successResponse($settings, 'Settings retrieved successfully!', Response::HTTP_OK);
    } catch (\Throwable $th) {
        return $this->errorResponse($th->getMessage(), 'Failed to retrieve settings', Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
```

**Store Method Pattern**:
```php
public function store(StoreSettingRequest $request): JsonResponse
{
    try {
        $setting = $this->service->store($request);

        return $this->successResponse($setting, 'Setting created successfully!', Response::HTTP_CREATED);
    } catch (\Exception $e) {
        return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
    } catch (\Throwable $th) {
        return $this->errorResponse($th->getMessage(), 'Failed to create setting', Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
```

**Show Method Pattern**:
```php
public function show(string $key): JsonResponse
{
    try {
        $setting = $this->service->show($key);

        return $this->successResponse($setting, 'Setting retrieved successfully!', Response::HTTP_OK);
    } catch (\Throwable $th) {
        return $this->errorResponse($th->getMessage(), 'Failed to retrieve setting', Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
```

**Update Method Pattern**:
```php
public function update(UpdateSettingRequest $request, string $key): JsonResponse
{
    try {
        $setting = $this->service->update($request, $key);

        return $this->successResponse($setting, 'Setting updated successfully!', Response::HTTP_OK);
    } catch (\Exception $e) {
        return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
    } catch (\Throwable $th) {
        return $this->errorResponse($th->getMessage(), 'Failed to update setting', Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
```

**Destroy Method Pattern**:
```php
public function destroy(string $key): JsonResponse
{
    try {
        $this->service->destroy($key);

        return $this->successResponse(null, 'Setting deleted successfully!', Response::HTTP_OK);
    } catch (\Throwable $th) {
        return $this->errorResponse($th->getMessage(), 'Failed to delete setting', Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
```

#### 3. **Service Class Structure Pattern**

**Reference Service**: `app/Services/BannerService.php`

```php
<?php

namespace App\Services;

use App\Models\Setting;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class SettingService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Setting::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Filtering
        $filters = [
            'category' => '=',
            'type' => '=',
            'is_public' => '='
        ];
        $this->applyFilters($query, $request, $filters);

        // Searching
        $searchKeys = ['key', 'display_name', 'description'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareSettingData($request);
        $setting = Setting::create($data);
        
        // Clear cache
        $this->clearSettingCache($setting->key);
        
        return $setting;
    }

    public function show(string $key): Setting
    {
        return Setting::where('key', $key)->firstOrFail();
    }

    public function update($request, string $key)
    {
        $setting = Setting::where('key', $key)->firstOrFail();
        $updateData = $this->prepareSettingData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $setting->update($updateData);
        
        // Clear cache
        $this->clearSettingCache($key);

        return $setting->fresh();
    }

    public function destroy(string $key): bool
    {
        $setting = Setting::where('key', $key)->firstOrFail();
        
        // Clear cache
        $this->clearSettingCache($key);
        
        return $setting->delete();
    }

    private function prepareSettingData(Request $request, bool $isStore = true): array
    {
        $data = [
            'key' => $request->input('key'),
            'value' => $this->formatSettingValue($request->input('value'), $request->input('type')),
            'type' => $request->input('type', 'string'),
            'category' => $request->input('category'),
            'display_name' => $request->input('display_name'),
            'description' => $request->input('description'),
            'is_public' => $request->boolean('is_public', false),
            'validation_rules' => $request->input('validation_rules'),
            'default_value' => $request->input('default_value'),
            'options' => $request->input('options'),
        ];

        if (!$isStore) {
            unset($data['key']); // Key should not be updated
        }

        return $data;
    }

    private function formatSettingValue($value, string $type)
    {
        return match ($type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'float' => (float) $value,
            'json', 'array' => is_string($value) ? json_decode($value, true) : $value,
            default => (string) $value,
        };
    }

    private function clearSettingCache(string $key): void
    {
        Cache::forget("setting.{$key}");
        Cache::forget('settings.all');
        Cache::forget('settings.public');
    }
}
```

## 📊 **Database Design**

### Settings Table Schema

**Migration File**: `database/migrations/2024_01_20_000000_create_settings_table.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique()->index();
            $table->longText('value')->nullable();
            $table->enum('type', ['string', 'boolean', 'integer', 'float', 'json', 'array', 'text'])->default('string');
            $table->string('category', 50)->index();
            $table->string('display_name');
            $table->text('description')->nullable();
            $table->boolean('is_public')->default(false)->index();
            $table->json('validation_rules')->nullable();
            $table->longText('default_value')->nullable();
            $table->json('options')->nullable(); // For select/radio options
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true)->index();
            $table->string('environment')->nullable()->index(); // dev, staging, production
            $table->timestamps();

            // Indexes for performance
            $table->index(['category', 'is_active']);
            $table->index(['is_public', 'is_active']);
            $table->index(['environment', 'is_active']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
```

## 🎯 **Implementation Tasks Breakdown**

### Phase 1: Core Infrastructure (HIGH Priority)

#### 1.1 Create Settings Migration and Model

**Priority**: CRITICAL
**Estimated Time**: 2 hours

**Task Details**:
- **Migration File**: `database/migrations/2024_01_20_000000_create_settings_table.php`
- **Model File**: `app/Models/Setting.php`
- **Features Required**:
  - Flexible key-value storage with type casting
  - Category-based organization
  - Public/private setting distinction
  - Environment-specific configurations
  - Validation rules storage
  - Default values and options support

**Model Implementation**:
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'category',
        'display_name',
        'description',
        'is_public',
        'validation_rules',
        'default_value',
        'options',
        'sort_order',
        'is_active',
        'environment',
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'is_active' => 'boolean',
        'validation_rules' => 'array',
        'options' => 'array',
        'sort_order' => 'integer',
    ];

    // Accessor for typed value
    public function getTypedValueAttribute()
    {
        return $this->castValue($this->value, $this->type);
    }

    // Helper method to cast values based on type
    private function castValue($value, string $type)
    {
        return match ($type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'float' => (float) $value,
            'json', 'array' => is_string($value) ? json_decode($value, true) : $value,
            default => (string) $value,
        };
    }

    // Static method to get setting value with caching
    public static function getValue(string $key, $default = null)
    {
        return Cache::remember("setting.{$key}", 3600, function () use ($key, $default) {
            $setting = static::where('key', $key)->where('is_active', true)->first();
            return $setting ? $setting->typed_value : $default;
        });
    }

    // Static method to set setting value
    public static function setValue(string $key, $value): bool
    {
        $setting = static::where('key', $key)->first();

        if ($setting) {
            $setting->update(['value' => $value]);
            Cache::forget("setting.{$key}");
            return true;
        }

        return false;
    }

    // Scope for public settings
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    // Scope for category
    public function scopeCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    // Scope for active settings
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
```

#### 1.2 Create SettingService

**Priority**: HIGH
**Estimated Time**: 3 hours

**Task Details**:
- **File**: `app/Services/SettingService.php`
- **Pattern**: MUST follow `BannerService.php` structure exactly
- **Features Required**:
  - CRUD operations with caching
  - Bulk update operations
  - Category-based retrieval
  - Type validation and casting
  - Cache management

**Service Implementation**:
```php
<?php

namespace App\Services;

use App\Models\Setting;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

class SettingService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Setting::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Filtering
        $filters = [
            'category' => '=',
            'type' => '=',
            'is_public' => '=',
            'is_active' => '=',
            'environment' => '='
        ];
        $this->applyFilters($query, $request, $filters);

        // Searching
        $searchKeys = ['key', 'display_name', 'description'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Default ordering
        $query->orderBy('category')->orderBy('sort_order')->orderBy('display_name');

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareSettingData($request);

        // Validate the setting value against its rules
        $this->validateSettingValue($data['value'], $data['validation_rules'] ?? null);

        $setting = Setting::create($data);

        // Clear cache
        $this->clearSettingCache($setting->key);

        return $setting;
    }

    public function show(string $key): Setting
    {
        return Setting::where('key', $key)->firstOrFail();
    }

    public function update($request, string $key)
    {
        $setting = Setting::where('key', $key)->firstOrFail();
        $updateData = $this->prepareSettingData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        // Validate the setting value against its rules
        if (isset($updateData['value'])) {
            $validationRules = $updateData['validation_rules'] ?? $setting->validation_rules;
            $this->validateSettingValue($updateData['value'], $validationRules);
        }

        $setting->update($updateData);

        // Clear cache
        $this->clearSettingCache($key);

        return $setting->fresh();
    }

    public function destroy(string $key): bool
    {
        $setting = Setting::where('key', $key)->firstOrFail();

        // Clear cache
        $this->clearSettingCache($key);

        return $setting->delete();
    }

    public function bulkUpdate(array $settings): array
    {
        $updated = [];

        foreach ($settings as $key => $value) {
            $setting = Setting::where('key', $key)->first();

            if ($setting) {
                // Validate the setting value
                $this->validateSettingValue($value, $setting->validation_rules);

                $setting->update(['value' => $value]);
                $this->clearSettingCache($key);
                $updated[] = $setting->fresh();
            }
        }

        return $updated;
    }

    public function getByCategory(string $category): Collection
    {
        return Cache::remember("settings.category.{$category}", 3600, function () use ($category) {
            return Setting::category($category)->active()->orderBy('sort_order')->get();
        });
    }

    public function getPublicSettings(): Collection
    {
        return Cache::remember('settings.public', 3600, function () {
            return Setting::public()->active()->orderBy('category')->orderBy('sort_order')->get();
        });
    }

    private function prepareSettingData(Request $request, bool $isStore = true): array
    {
        $data = [
            'key' => $request->input('key'),
            'value' => $this->formatSettingValue($request->input('value'), $request->input('type', 'string')),
            'type' => $request->input('type', 'string'),
            'category' => $request->input('category'),
            'display_name' => $request->input('display_name'),
            'description' => $request->input('description'),
            'is_public' => $request->boolean('is_public', false),
            'validation_rules' => $request->input('validation_rules'),
            'default_value' => $request->input('default_value'),
            'options' => $request->input('options'),
            'sort_order' => $request->integer('sort_order', 0),
            'is_active' => $request->boolean('is_active', true),
            'environment' => $request->input('environment'),
        ];

        if (!$isStore) {
            unset($data['key']); // Key should not be updated
        }

        return array_filter($data, function ($value) {
            return !is_null($value);
        });
    }

    private function formatSettingValue($value, string $type)
    {
        return match ($type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'float' => (float) $value,
            'json', 'array' => is_string($value) ? json_decode($value, true) : $value,
            default => (string) $value,
        };
    }

    private function validateSettingValue($value, $rules): void
    {
        if (!$rules) {
            return;
        }

        $validator = Validator::make(['value' => $value], ['value' => $rules]);

        if ($validator->fails()) {
            throw new \InvalidArgumentException('Setting value validation failed: ' . $validator->errors()->first());
        }
    }

    private function clearSettingCache(string $key): void
    {
        Cache::forget("setting.{$key}");
        Cache::forget('settings.all');
        Cache::forget('settings.public');

        // Clear category cache
        $setting = Setting::where('key', $key)->first();
        if ($setting) {
            Cache::forget("settings.category.{$setting->category}");
        }
    }
}
```

#### 1.3 Create SettingController

**Priority**: HIGH
**Estimated Time**: 2 hours

**Task Details**:
- **File**: `app/Http/Controllers/Admin/SettingController.php`
- **Pattern**: MUST follow `BannerController.php` structure exactly
- **Methods Required**:
  - Standard CRUD operations
  - Bulk update endpoint
  - Category-based retrieval
  - Public settings endpoint

#### 1.4 Create Request Validation Classes

**Priority**: MEDIUM
**Estimated Time**: 1.5 hours

**Task Details**:
- **Files**:
  - `app/Http/Requests/Settings/StoreSettingRequest.php`
  - `app/Http/Requests/Settings/UpdateSettingRequest.php`
  - `app/Http/Requests/Settings/BulkUpdateSettingsRequest.php`

### Phase 2: API Endpoints and Routes (MEDIUM Priority)

#### 2.1 Admin Settings Routes

**Priority**: MEDIUM
**Estimated Time**: 1 hour

**Route File**: `routes/admin.php`

```php
// Settings Management Routes
Route::prefix('settings')->name('settings.')->group(function () {
    Route::get('/', [SettingController::class, 'index'])->name('index');
    Route::post('/', [SettingController::class, 'store'])->name('store');
    Route::get('/category/{category}', [SettingController::class, 'getByCategory'])->name('category');
    Route::get('/{key}', [SettingController::class, 'show'])->name('show');
    Route::put('/{key}', [SettingController::class, 'update'])->name('update');
    Route::delete('/{key}', [SettingController::class, 'destroy'])->name('destroy');
    Route::post('/bulk-update', [SettingController::class, 'bulkUpdate'])->name('bulk-update');
});
```

#### 2.2 Client Settings Routes

**Priority**: MEDIUM
**Estimated Time**: 30 minutes

**Route File**: `routes/client.php`

```php
// Public Settings Routes
Route::prefix('settings')->name('settings.')->group(function () {
    Route::get('/public', [ClientSettingController::class, 'getPublicSettings'])->name('public');
    Route::get('/category/{category}/public', [ClientSettingController::class, 'getPublicByCategory'])->name('category.public');
});
```

### Phase 3: Frontend Integration (MEDIUM Priority)

#### 3.1 Settings Categories Configuration

**Priority**: MEDIUM
**Estimated Time**: 2 hours

**Configuration File**: `config/settings.php`

```php
<?php

return [
    'categories' => [
        'system_backup' => [
            'name' => 'System & Backup',
            'description' => 'System configuration and backup settings',
            'icon' => 'server',
            'order' => 1,
        ],
        'payment_financial' => [
            'name' => 'Payment & Financial',
            'description' => 'Payment gateway and financial configurations',
            'icon' => 'credit-card',
            'order' => 2,
        ],
        'communication' => [
            'name' => 'Communication',
            'description' => 'Email, SMS, and notification settings',
            'icon' => 'mail',
            'order' => 3,
        ],
        'homepage_management' => [
            'name' => 'Homepage Management',
            'description' => 'Homepage content and layout settings',
            'icon' => 'home',
            'order' => 4,
        ],
        'branding_ui' => [
            'name' => 'Branding & UI',
            'description' => 'Brand identity and user interface settings',
            'icon' => 'palette',
            'order' => 5,
        ],
        'security_performance' => [
            'name' => 'Security & Performance',
            'description' => 'Security and performance optimization settings',
            'icon' => 'shield',
            'order' => 6,
        ],
        'vendor_management' => [
            'name' => 'Vendor Management',
            'description' => 'Vendor-related configurations',
            'icon' => 'users',
            'order' => 7,
        ],
        'order_management' => [
            'name' => 'Order Management',
            'description' => 'Order processing and fulfillment settings',
            'icon' => 'shopping-cart',
            'order' => 8,
        ],
        'customer_management' => [
            'name' => 'Customer Management',
            'description' => 'Customer registration and verification settings',
            'icon' => 'user',
            'order' => 9,
        ],
        'seo_settings' => [
            'name' => 'SEO Settings',
            'description' => 'Search engine optimization configurations',
            'icon' => 'search',
            'order' => 10,
        ],
    ],

    'field_types' => [
        'string' => 'Text Input',
        'text' => 'Textarea',
        'boolean' => 'Toggle Switch',
        'integer' => 'Number Input',
        'float' => 'Decimal Input',
        'json' => 'JSON Editor',
        'array' => 'Array Input',
    ],

    'cache_ttl' => 3600, // 1 hour
];
```

### Phase 4: Additional Features (LOW Priority)

#### 4.1 Settings Seeder

**Priority**: LOW
**Estimated Time**: 2 hours

**Task Details**:
- Create comprehensive seeder with all default settings
- Include all categories with proper default values
- Support for environment-specific settings

#### 4.2 Settings Helper Class

**Priority**: LOW
**Estimated Time**: 1 hour

**Helper File**: `app/Helpers/SettingsHelper.php`

```php
<?php

namespace App\Helpers;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class SettingsHelper
{
    public static function get(string $key, $default = null)
    {
        return Setting::getValue($key, $default);
    }

    public static function set(string $key, $value): bool
    {
        return Setting::setValue($key, $value);
    }

    public static function getByCategory(string $category): array
    {
        $settings = Cache::remember("settings.category.{$category}", 3600, function () use ($category) {
            return Setting::category($category)->active()->get();
        });

        return $settings->pluck('typed_value', 'key')->toArray();
    }

    public static function getPublic(): array
    {
        $settings = Cache::remember('settings.public', 3600, function () {
            return Setting::public()->active()->get();
        });

        return $settings->pluck('typed_value', 'key')->toArray();
    }

    public static function clearCache(string $key = null): void
    {
        if ($key) {
            Cache::forget("setting.{$key}");
        } else {
            Cache::flush();
        }
    }
}
```

## 🔒 **Security Considerations**

### Access Control
- Admin-only access for sensitive settings
- Public settings API for frontend consumption
- Environment-specific setting isolation
- Audit logging for setting changes

### Validation
- Type-specific validation rules
- Custom validation for complex settings
- Sanitization of input values
- Prevention of code injection

### Caching Security
- Cache key namespacing
- Automatic cache invalidation
- Secure cache storage
- Cache poisoning prevention

## 🧪 **Testing Strategy**

### Unit Tests
- Setting model functionality
- Service layer methods
- Helper class methods
- Validation rules

### Feature Tests
- API endpoint functionality
- Authentication and authorization
- Bulk operations
- Cache behavior

### Integration Tests
- Frontend integration
- Database operations
- Cache integration
- Environment-specific behavior

## 📚 **API Documentation**

### Endpoints Summary

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/admin/settings` | List all settings | Yes (Admin) |
| POST | `/admin/settings` | Create new setting | Yes (Admin) |
| GET | `/admin/settings/{key}` | Get specific setting | Yes (Admin) |
| PUT | `/admin/settings/{key}` | Update setting | Yes (Admin) |
| DELETE | `/admin/settings/{key}` | Delete setting | Yes (Admin) |
| GET | `/admin/settings/category/{category}` | Get settings by category | Yes (Admin) |
| POST | `/admin/settings/bulk-update` | Bulk update settings | Yes (Admin) |
| GET | `/client/settings/public` | Get public settings | No |
| GET | `/client/settings/category/{category}/public` | Get public settings by category | No |

### Request/Response Examples

**Create Setting Request**:
```json
{
  "key": "payment.stripe_enabled",
  "value": true,
  "type": "boolean",
  "category": "payment_financial",
  "display_name": "Stripe Payment Gateway",
  "description": "Enable Stripe payment processing",
  "is_public": false,
  "validation_rules": ["boolean"],
  "sort_order": 1
}
```

**Bulk Update Request**:
```json
{
  "settings": {
    "payment.stripe_enabled": true,
    "payment.paypal_enabled": false,
    "system.cache_enabled": true
  }
}
```

**Public Settings Response**:
```json
{
  "status": true,
  "message": "Public settings retrieved successfully",
  "data": {
    "payment.default_currency": "USD",
    "payment.tax_rate": 0.15,
    "homepage.hot_deals_enabled": true,
    "branding.site_logo": "/images/logo.png"
  }
}
```

This comprehensive Settings Management System provides a flexible, secure, and scalable solution for managing all platform configurations with proper caching, validation, and access control.

### Setting Categories and Default Values

**Seeder File**: `database/seeders/SettingsSeeder.php`

```php
<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    public function run(): void
    {
        $settings = [
            // System & Backup Settings
            [
                'key' => 'system.auto_backup_enabled',
                'value' => true,
                'type' => 'boolean',
                'category' => 'system_backup',
                'display_name' => 'Auto Database Backup',
                'description' => 'Enable automatic database backups',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'system.backup_frequency',
                'value' => 'daily',
                'type' => 'string',
                'category' => 'system_backup',
                'display_name' => 'Backup Frequency',
                'description' => 'How often to perform automatic backups',
                'is_public' => false,
                'options' => ['hourly', 'daily', 'weekly', 'monthly'],
                'sort_order' => 2,
            ],
            [
                'key' => 'system.cache_enabled',
                'value' => true,
                'type' => 'boolean',
                'category' => 'system_backup',
                'display_name' => 'Cache Management',
                'description' => 'Enable application caching',
                'is_public' => false,
                'sort_order' => 3,
            ],

            // Payment & Financial Settings
            [
                'key' => 'payment.stripe_enabled',
                'value' => false,
                'type' => 'boolean',
                'category' => 'payment_financial',
                'display_name' => 'Stripe Payment Gateway',
                'description' => 'Enable Stripe payment processing',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'payment.stripe_public_key',
                'value' => '',
                'type' => 'string',
                'category' => 'payment_financial',
                'display_name' => 'Stripe Public Key',
                'description' => 'Stripe publishable key',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'payment.paypal_enabled',
                'value' => false,
                'type' => 'boolean',
                'category' => 'payment_financial',
                'display_name' => 'PayPal Payment Gateway',
                'description' => 'Enable PayPal payment processing',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'payment.default_currency',
                'value' => 'USD',
                'type' => 'string',
                'category' => 'payment_financial',
                'display_name' => 'Default Currency',
                'description' => 'Default platform currency',
                'is_public' => true,
                'options' => ['USD', 'EUR', 'GBP', 'SAR', 'AED'],
                'sort_order' => 4,
            ],
            [
                'key' => 'payment.tax_rate',
                'value' => 0.15,
                'type' => 'float',
                'category' => 'payment_financial',
                'display_name' => 'Default Tax Rate',
                'description' => 'Default tax rate (as decimal)',
                'is_public' => true,
                'sort_order' => 5,
            ],

            // Communication Settings
            [
                'key' => 'communication.smtp_enabled',
                'value' => false,
                'type' => 'boolean',
                'category' => 'communication',
                'display_name' => 'SMTP Email',
                'description' => 'Enable SMTP email sending',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'communication.smtp_host',
                'value' => '',
                'type' => 'string',
                'category' => 'communication',
                'display_name' => 'SMTP Host',
                'description' => 'SMTP server hostname',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'communication.sms_enabled',
                'value' => false,
                'type' => 'boolean',
                'category' => 'communication',
                'display_name' => 'SMS Gateway',
                'description' => 'Enable SMS notifications',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'communication.notification_preferences',
                'value' => ['email' => true, 'sms' => false, 'push' => true],
                'type' => 'json',
                'category' => 'communication',
                'display_name' => 'Notification Preferences',
                'description' => 'Default notification channel preferences',
                'is_public' => false,
                'sort_order' => 4,
            ],

            // Homepage Management Settings
            [
                'key' => 'homepage.featured_categories',
                'value' => [],
                'type' => 'array',
                'category' => 'homepage_management',
                'display_name' => 'Featured Categories',
                'description' => 'Categories to display on homepage',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'homepage.hot_deals_enabled',
                'value' => true,
                'type' => 'boolean',
                'category' => 'homepage_management',
                'display_name' => 'Hot Deals Section',
                'description' => 'Show hot deals on homepage',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'homepage.promotional_banners',
                'value' => [],
                'type' => 'json',
                'category' => 'homepage_management',
                'display_name' => 'Promotional Banners',
                'description' => 'Homepage promotional banner configuration',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'homepage.app_download_links',
                'value' => ['ios' => '', 'android' => ''],
                'type' => 'json',
                'category' => 'homepage_management',
                'display_name' => 'App Download Links',
                'description' => 'Mobile app download links',
                'is_public' => true,
                'sort_order' => 4,
            ],
            [
                'key' => 'homepage.review_system_enabled',
                'value' => true,
                'type' => 'boolean',
                'category' => 'homepage_management',
                'display_name' => 'Review System Toggle',
                'description' => 'Enable/disable product review system',
                'is_public' => true,
                'sort_order' => 5,
            ],

            // Branding & UI Settings
            [
                'key' => 'branding.site_logo',
                'value' => '',
                'type' => 'string',
                'category' => 'branding_ui',
                'display_name' => 'Site Logo',
                'description' => 'Main site logo URL',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'branding.support_contact',
                'value' => '',
                'type' => 'string',
                'category' => 'branding_ui',
                'display_name' => 'Support Contact',
                'description' => 'Customer support contact information',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'branding.support_email',
                'value' => '',
                'type' => 'string',
                'category' => 'branding_ui',
                'display_name' => 'Support Email',
                'description' => 'Customer support email address',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'branding.footer_address',
                'value' => '',
                'type' => 'text',
                'category' => 'branding_ui',
                'display_name' => 'Footer Address',
                'description' => 'Company address for footer',
                'is_public' => true,
                'sort_order' => 4,
            ],
            [
                'key' => 'branding.social_links',
                'value' => ['facebook' => '', 'twitter' => '', 'instagram' => '', 'linkedin' => ''],
                'type' => 'json',
                'category' => 'branding_ui',
                'display_name' => 'Social Media Links',
                'description' => 'Social media profile links',
                'is_public' => true,
                'sort_order' => 5,
            ],
            [
                'key' => 'branding.payment_icons',
                'value' => ['visa', 'mastercard', 'paypal'],
                'type' => 'array',
                'category' => 'branding_ui',
                'display_name' => 'Payment Icons',
                'description' => 'Payment method icons to display',
                'is_public' => true,
                'sort_order' => 6,
            ],
            [
                'key' => 'branding.partner_logos',
                'value' => [],
                'type' => 'array',
                'category' => 'branding_ui',
                'display_name' => 'Partner Logos',
                'description' => 'Partner/sponsor logos',
                'is_public' => true,
                'sort_order' => 7,
            ],

            // Security & Performance Settings
            [
                'key' => 'security.rate_limiting_enabled',
                'value' => true,
                'type' => 'boolean',
                'category' => 'security_performance',
                'display_name' => 'Rate Limiting',
                'description' => 'Enable API rate limiting',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'security.max_requests_per_minute',
                'value' => 60,
                'type' => 'integer',
                'category' => 'security_performance',
                'display_name' => 'Max Requests Per Minute',
                'description' => 'Maximum API requests per minute per user',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'security.session_timeout',
                'value' => 120,
                'type' => 'integer',
                'category' => 'security_performance',
                'display_name' => 'Session Timeout (minutes)',
                'description' => 'User session timeout in minutes',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'security.api_throttling_enabled',
                'value' => true,
                'type' => 'boolean',
                'category' => 'security_performance',
                'display_name' => 'API Throttling',
                'description' => 'Enable API request throttling',
                'is_public' => false,
                'sort_order' => 4,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
```
