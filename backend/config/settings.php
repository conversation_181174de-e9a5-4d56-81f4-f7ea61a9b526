<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Settings Categories Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration defines the available setting categories and their
    | display properties for the admin interface.
    |
    */

    'categories' => [
        'system_backup' => [
            'name' => 'System & Backup',
            'description' => 'System configuration and backup settings',
            'icon' => 'server',
            'order' => 1,
        ],
        'payment_financial' => [
            'name' => 'Payment & Financial',
            'description' => 'Payment gateway and financial configurations',
            'icon' => 'credit-card',
            'order' => 2,
        ],
        'communication' => [
            'name' => 'Communication',
            'description' => 'Email, SMS, and notification settings',
            'icon' => 'mail',
            'order' => 3,
        ],
        'homepage_management' => [
            'name' => 'Homepage Management',
            'description' => 'Homepage content and layout settings',
            'icon' => 'home',
            'order' => 4,
        ],
        'branding_ui' => [
            'name' => 'Branding & UI',
            'description' => 'Brand identity and user interface settings',
            'icon' => 'palette',
            'order' => 5,
        ],
        'security_performance' => [
            'name' => 'Security & Performance',
            'description' => 'Security and performance optimization settings',
            'icon' => 'shield',
            'order' => 6,
        ],
        'vendor_management' => [
            'name' => 'Vendor Management',
            'description' => 'Vendor-related configurations',
            'icon' => 'users',
            'order' => 7,
        ],
        'order_management' => [
            'name' => 'Order Management',
            'description' => 'Order processing and fulfillment settings',
            'icon' => 'shopping-cart',
            'order' => 8,
        ],
        'customer_management' => [
            'name' => 'Customer Management',
            'description' => 'Customer registration and verification settings',
            'icon' => 'user',
            'order' => 9,
        ],
        'seo_settings' => [
            'name' => 'SEO Settings',
            'description' => 'Search engine optimization configurations',
            'icon' => 'search',
            'order' => 10,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Field Types Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration defines the available field types for settings
    | and their display names for the admin interface.
    |
    */

    'field_types' => [
        'string' => 'Text Input',
        'text' => 'Textarea',
        'boolean' => 'Toggle Switch',
        'integer' => 'Number Input',
        'float' => 'Decimal Input',
        'json' => 'JSON Editor',
        'array' => 'Array Input',
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    |
    | Settings cache configuration for performance optimization.
    |
    */

    'cache' => [
        'ttl' => 3600, // 1 hour
        'prefix' => 'setting.',
        'tags' => ['settings'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Rules
    |--------------------------------------------------------------------------
    |
    | Common validation rules for different setting types.
    |
    */

    'validation_rules' => [
        'email' => ['email'],
        'url' => ['url'],
        'phone' => ['regex:/^[\+]?[1-9][\d]{0,15}$/'],
        'currency' => ['regex:/^[A-Z]{3}$/'],
        'percentage' => ['numeric', 'min:0', 'max:100'],
        'decimal_percentage' => ['numeric', 'min:0', 'max:1'],
        'positive_integer' => ['integer', 'min:1'],
        'non_negative_integer' => ['integer', 'min:0'],
        'positive_float' => ['numeric', 'min:0.01'],
        'non_negative_float' => ['numeric', 'min:0'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Environment Specific Settings
    |--------------------------------------------------------------------------
    |
    | Settings that should be environment specific.
    |
    */

    'environment_specific' => [
        'system.debug_enabled',
        'system.maintenance_mode',
        'payment.stripe_secret_key',
        'payment.paypal_secret',
        'communication.smtp_password',
        'security.api_keys',
    ],

    /*
    |--------------------------------------------------------------------------
    | Public Settings
    |--------------------------------------------------------------------------
    |
    | Settings that are safe to expose to the frontend/client side.
    |
    */

    'public_settings' => [
        'payment.default_currency',
        'payment.tax_rate',
        'payment.currency_conversion_enabled',
        'homepage.featured_categories',
        'homepage.hot_deals_enabled',
        'homepage.promotional_banners',
        'homepage.app_download_links',
        'homepage.review_system_enabled',
        'branding.site_logo',
        'branding.site_name',
        'branding.support_contact',
        'branding.support_email',
        'branding.footer_address',
        'branding.social_links',
        'branding.payment_icons',
        'branding.partner_logos',
        'order.cancellation_window_hours',
        'customer.guest_checkout_enabled',
        'customer.loyalty_program_enabled',
        'seo.site_title',
        'seo.meta_description',
        'seo.meta_keywords',
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Values
    |--------------------------------------------------------------------------
    |
    | Default values for common settings.
    |
    */

    'defaults' => [
        'cache_ttl' => 3600,
        'pagination_limit' => 15,
        'max_file_size' => 10240, // KB
        'session_timeout' => 120, // minutes
        'rate_limit' => 60, // requests per minute
    ],
];
