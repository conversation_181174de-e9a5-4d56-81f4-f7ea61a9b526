<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Real-time Notifications Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the real-time notification
    | system including feature flags, channel settings, and queue options.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    |
    | These flags allow you to enable/disable specific notification features
    | without affecting the core functionality.
    |
    */

    'realtime_enabled' => env('NOTIFICATIONS_REALTIME_ENABLED', true),
    'database_enabled' => env('NOTIFICATIONS_DATABASE_ENABLED', true),
    'mail_enabled' => env('NOTIFICATIONS_MAIL_ENABLED', true),
    'push_enabled' => env('NOTIFICATIONS_PUSH_ENABLED', false),

    /*
    |--------------------------------------------------------------------------
    | Broadcasting Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for real-time broadcasting of notifications.
    |
    */

    'broadcasting' => [
        'enabled' => env('NOTIFICATIONS_BROADCASTING_ENABLED', true),
        'connection' => env('BROADCAST_CONNECTION', 'reverb'),
        'queue' => env('NOTIFICATIONS_QUEUE', 'default'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Channel Configuration
    |--------------------------------------------------------------------------
    |
    | Define the available notification channels and their settings.
    |
    */

    'channels' => [
        'user' => [
            'prefix' => 'private-user',
            'enabled' => true,
        ],
        'vendor' => [
            'prefix' => 'private-vendor',
            'enabled' => true,
        ],
        'admin' => [
            'prefix' => 'private-admin',
            'enabled' => true,
        ],
        'order' => [
            'prefix' => 'private-order',
            'enabled' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Categories
    |--------------------------------------------------------------------------
    |
    | Define the available notification categories and their settings.
    |
    */

    'categories' => [
        'order' => [
            'name' => 'Order Updates',
            'description' => 'Notifications related to order status changes',
            'icon' => 'shopping-cart',
            'color' => '#3B82F6',
        ],
        'refund' => [
            'name' => 'Refunds',
            'description' => 'Refund processing and status updates',
            'icon' => 'credit-card',
            'color' => '#EF4444',
        ],
        'system' => [
            'name' => 'System',
            'description' => 'System-wide announcements and updates',
            'icon' => 'cog',
            'color' => '#6B7280',
        ],
        'promotion' => [
            'name' => 'Promotions',
            'description' => 'Special offers and promotional content',
            'icon' => 'tag',
            'color' => '#10B981',
        ],
        'inventory' => [
            'name' => 'Inventory',
            'description' => 'Stock levels and inventory alerts',
            'icon' => 'cube',
            'color' => '#F59E0B',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Queue Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for queuing notification jobs.
    |
    */

    'queue' => [
        'connection' => env('NOTIFICATIONS_QUEUE_CONNECTION', 'redis'),
        'queue' => env('NOTIFICATIONS_QUEUE_NAME', 'notifications'),
        'retry_after' => env('NOTIFICATIONS_RETRY_AFTER', 90),
        'max_tries' => env('NOTIFICATIONS_MAX_TRIES', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Rate limiting configuration for notification endpoints.
    |
    */

    'rate_limiting' => [
        'read_operations' => env('NOTIFICATIONS_READ_RATE_LIMIT', '100,1'),
        'write_operations' => env('NOTIFICATIONS_WRITE_RATE_LIMIT', '60,1'),
        'device_registration' => env('NOTIFICATIONS_DEVICE_RATE_LIMIT', '10,1'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cleanup Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for automatic cleanup of old notifications.
    |
    */

    'cleanup' => [
        'enabled' => env('NOTIFICATIONS_CLEANUP_ENABLED', true),
        'keep_days' => env('NOTIFICATIONS_KEEP_DAYS', 90),
        'batch_size' => env('NOTIFICATIONS_CLEANUP_BATCH_SIZE', 1000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Push Notification Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for mobile push notifications (FCM/APNs).
    |
    */

    'push' => [
        'fcm' => [
            'enabled' => env('FCM_ENABLED', false),
            'server_key' => env('FCM_SERVER_KEY'),
            'sender_id' => env('FCM_SENDER_ID'),
        ],
        'apns' => [
            'enabled' => env('APNS_ENABLED', false),
            'certificate_path' => env('APNS_CERTIFICATE_PATH'),
            'certificate_passphrase' => env('APNS_CERTIFICATE_PASSPHRASE'),
            'production' => env('APNS_PRODUCTION', false),
        ],
    ],

];
