<?php

/**
 * Manual Cart API Test Script
 * Tests all cart endpoints with the new vendors structure
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Product;
use App\Models\Vendor;
use App\Models\ShoppingCart;
use App\Models\User;
use Illuminate\Support\Facades\Http;

echo "=== CART API INTEGRATION TEST ===\n";
echo "Testing all cart endpoints with new vendors structure\n\n";

// Configuration
$baseUrl = 'http://localhost:8000'; // Adjust as needed
$apiUrl = $baseUrl . '/api/client/cart';

// Step 1: Check if we have test data
echo "1. Checking for test data...\n";

$products = Product::with('vendor')
    ->where('is_active', true)
    ->whereIn('status', ['active', 'submitted'])
    ->limit(3)
    ->get();

if ($products->count() < 2) {
    echo "❌ Not enough active products found. Please run: php artisan db:seed --class=ProductSeeder\n";
    exit(1);
}

$product1 = $products[0];
$product2 = $products[1];
$vendor1 = $product1->vendor;

echo "✓ Found test products:\n";
echo "  - Product 1: {$product1->title_en} (Vendor: {$vendor1->name_tl_en})\n";
echo "  - Product 2: {$product2->title_en} (Vendor: {$product2->vendor->name_tl_en})\n\n";

// Step 2: Create a cart
echo "2. Creating new cart...\n";

$createCartData = [
    'currency' => 'AED',
    'notes' => 'API Integration Test Cart'
];

$createResponse = makeApiRequest('POST', $apiUrl, $createCartData);

if (!$createResponse || !isset($createResponse['data']['uuid'])) {
    echo "❌ Failed to create cart\n";
    exit(1);
}

$cartUuid = $createResponse['data']['uuid'];
$cartToken = $createResponse['data']['cart_token'] ?? null;

echo "✓ Cart created successfully\n";
echo "  - UUID: {$cartUuid}\n";
echo "  - Token: {$cartToken}\n";

// Validate initial structure
validateCartStructure($createResponse['data'], 'CREATE CART');

// Step 3: Add items to cart
echo "\n3. Adding items to cart...\n";

// Add first item
$addItem1Data = [
    'product_id' => $product1->id,
    'quantity' => 2,
    'cart_token' => $cartToken
];

$addItem1Response = makeApiRequest('POST', "{$apiUrl}/{$cartUuid}/items", $addItem1Data);

if (!$addItem1Response) {
    echo "❌ Failed to add first item\n";
    exit(1);
}

echo "✓ Added first item: 2x {$product1->title_en}\n";
validateCartStructure($addItem1Response['data'], 'ADD ITEM 1');

// Add second item
$addItem2Data = [
    'product_id' => $product2->id,
    'quantity' => 1,
    'cart_token' => $cartToken
];

$addItem2Response = makeApiRequest('POST', "{$apiUrl}/{$cartUuid}/items", $addItem2Data);

if (!$addItem2Response) {
    echo "❌ Failed to add second item\n";
    exit(1);
}

echo "✓ Added second item: 1x {$product2->title_en}\n";
validateCartStructure($addItem2Response['data'], 'ADD ITEM 2');

// Step 4: Get cart
echo "\n4. Retrieving cart...\n";

$getCartResponse = makeApiRequest('GET', "{$apiUrl}/{$cartUuid}?cart_token={$cartToken}");

if (!$getCartResponse) {
    echo "❌ Failed to get cart\n";
    exit(1);
}

echo "✓ Retrieved cart successfully\n";
validateCartStructure($getCartResponse['data'], 'GET CART');

// Step 5: Update item
echo "\n5. Updating item quantity...\n";

$cartData = $getCartResponse['data'];
if (empty($cartData['vendors'])) {
    echo "❌ No vendors found in cart\n";
    exit(1);
}

$firstVendor = $cartData['vendors'][0];
$firstItem = $firstVendor['items'][0];

$updateItemData = [
    'quantity' => 3,
    'cart_token' => $cartToken
];

$updateResponse = makeApiRequest('PUT', "{$apiUrl}/{$cartUuid}/items/{$firstItem['id']}", $updateItemData);

if (!$updateResponse) {
    echo "❌ Failed to update item\n";
    exit(1);
}

echo "✓ Updated item quantity to 3\n";
validateCartStructure($updateResponse['data'], 'UPDATE ITEM');

// Step 6: Remove item
echo "\n6. Removing item...\n";

$cartData = $updateResponse['data'];
$firstVendor = $cartData['vendors'][0];

if (count($firstVendor['items']) > 1) {
    $itemToRemove = $firstVendor['items'][1];
    
    $removeResponse = makeApiRequest('DELETE', "{$apiUrl}/{$cartUuid}/items/{$itemToRemove['id']}?cart_token={$cartToken}");
    
    if (!$removeResponse) {
        echo "❌ Failed to remove item\n";
        exit(1);
    }
    
    echo "✓ Removed item successfully\n";
    validateCartStructure($removeResponse['data'], 'REMOVE ITEM');
} else {
    echo "⚠ Only one item in cart, skipping removal test\n";
}

echo "\n=== ALL TESTS COMPLETED SUCCESSFULLY ===\n";
echo "✅ New vendors structure is working correctly across all endpoints!\n\n";

// Helper functions
function makeApiRequest($method, $url, $data = null)
{
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json',
        ],
        CURLOPT_TIMEOUT => 30,
    ]);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($response === false) {
        echo "❌ cURL error: " . curl_error($ch) . "\n";
        return null;
    }
    
    $decoded = json_decode($response, true);
    
    if ($httpCode >= 400) {
        echo "❌ HTTP Error {$httpCode}: " . ($decoded['message'] ?? 'Unknown error') . "\n";
        if (isset($decoded['errors'])) {
            print_r($decoded['errors']);
        }
        return null;
    }
    
    return $decoded;
}

function validateCartStructure($cartData, $operation)
{
    echo "  → Validating structure for {$operation}...\n";
    
    // Check for new vendors structure
    if (!isset($cartData['vendors'])) {
        echo "    ❌ Missing 'vendors' key\n";
        return false;
    }
    
    if (isset($cartData['vendor_groups'])) {
        echo "    ❌ Found old 'vendor_groups' key - should be removed\n";
        return false;
    }
    
    if (empty($cartData['vendors'])) {
        echo "    ✓ Empty cart - no vendors\n";
        return true;
    }
    
    echo "    ✓ Found " . count($cartData['vendors']) . " vendor(s)\n";
    
    foreach ($cartData['vendors'] as $index => $vendor) {
        // Check vendor structure
        if (!isset($vendor['id']) || !isset($vendor['name']) || !isset($vendor['items'])) {
            echo "    ❌ Vendor {$index} missing required fields\n";
            return false;
        }
        
        if (isset($vendor['vendor_id']) || isset($vendor['vendor'])) {
            echo "    ❌ Vendor {$index} has redundant vendor data\n";
            return false;
        }
        
        echo "    ✓ Vendor {$vendor['id']}: {$vendor['name']} with " . count($vendor['items']) . " item(s)\n";
        
        // Check items
        foreach ($vendor['items'] as $itemIndex => $item) {
            if (isset($item['vendor'])) {
                echo "    ❌ Item {$itemIndex} contains redundant vendor data\n";
                return false;
            }
            
            if (!isset($item['id']) || !isset($item['product_id']) || !isset($item['quantity'])) {
                echo "    ❌ Item {$itemIndex} missing required fields\n";
                return false;
            }
        }
    }
    
    echo "    ✅ Structure validation passed for {$operation}\n";
    return true;
}
