<?php

/**
 * Complete Cart Functionality Test Script
 * 
 * This script tests the entire cart workflow from product selection to checkout readiness.
 * Run with: php test_cart_complete.php
 */

require_once __DIR__ . '/vendor/autoload.php';

class CartFunctionalityTester
{
    private $baseUrl = 'http://localhost:8000/api/client';
    private $guestCartId;
    private $guestCartToken;
    private $userToken;
    private $userCartId;
    private $testResults = [];

    public function __construct()
    {
        echo "🧪 COMPLETE CART FUNCTIONALITY TEST\n";
        echo "===================================\n\n";
    }

    public function runAllTests()
    {
        try {
            // Phase 1: Guest Cart Operations
            $this->testGuestCartOperations();
            
            // Phase 2: User Authentication & Migration
            $this->testUserAuthenticationAndMigration();
            
            // Phase 3: User Cart Operations
            $this->testUserCartOperations();
            
            // Phase 4: Advanced Features
            $this->testAdvancedFeatures();
            
            // Phase 5: Error <PERSON>enarios
            $this->testErrorScenarios();
            
            $this->printSummary();
            
        } catch (Exception $e) {
            echo "❌ Test failed with error: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        }
    }

    private function testGuestCartOperations()
    {
        echo "🎯 PHASE 1: GUEST CART OPERATIONS\n";
        echo "---------------------------------\n";

        // Test 1: Create Guest Cart
        echo "1️⃣ Testing: Create Guest Cart\n";
        $response = $this->makeRequest('POST', '/cart/', [
            'currency' => 'USD'
        ]);
        
        if ($response['success'] && isset($response['data']['id'])) {
            $this->guestCartId = $response['data']['id'];
            $this->guestCartToken = $response['data']['cart_token'];
            $this->logSuccess("Guest cart created: {$this->guestCartId}");
            $this->logSuccess("Cart token: " . substr($this->guestCartToken, 0, 20) . "...");
        } else {
            $this->logError("Failed to create guest cart", $response);
            return;
        }

        // Test 2: Get Available Products
        echo "\n2️⃣ Testing: Get Available Products\n";
        $products = $this->getTestProducts();
        if (count($products) > 0) {
            $this->logSuccess("Found " . count($products) . " test products");
        } else {
            $this->logError("No products available for testing");
            return;
        }

        // Test 3: Add Items to Cart
        echo "\n3️⃣ Testing: Add Items to Cart\n";
        $product1 = $products[0];
        $response = $this->makeRequest('POST', "/cart/{$this->guestCartId}/items", [
            'product_id' => $product1['id'],
            'quantity' => 2
        ], [
            'X-Cart-Token' => $this->guestCartToken
        ]);
        
        if ($response['success']) {
            $this->logSuccess("Added product {$product1['id']} (qty: 2)");
        } else {
            $this->logError("Failed to add item to cart", $response);
        }

        // Add second item
        if (count($products) > 1) {
            $product2 = $products[1];
            $response = $this->makeRequest('POST', "/cart/{$this->guestCartId}/items", [
                'product_id' => $product2['id'],
                'quantity' => 1
            ], [
                'X-Cart-Token' => $this->guestCartToken
            ]);
            
            if ($response['success']) {
                $this->logSuccess("Added product {$product2['id']} (qty: 1)");
            }
        }

        // Test 4: Get Cart Details
        echo "\n4️⃣ Testing: Get Cart Details\n";
        $response = $this->makeRequest('GET', "/cart/{$this->guestCartId}", [], [
            'X-Cart-Token' => $this->guestCartToken
        ]);
        
        if ($response['success'] && isset($response['data']['items'])) {
            $itemCount = count($response['data']['items']);
            $totalAmount = $response['data']['totals']['total_amount'] ?? 0;
            $this->logSuccess("Cart retrieved with {$itemCount} items");
            $this->logSuccess("Total amount: $" . number_format($totalAmount, 2));
        } else {
            $this->logError("Failed to get cart details", $response);
        }

        // Test 5: Update Cart Item
        echo "\n5️⃣ Testing: Update Cart Item\n";
        if (isset($response['data']['items'][0])) {
            $firstItem = $response['data']['items'][0];
            $updateResponse = $this->makeRequest('PUT', "/cart/{$this->guestCartId}/items/{$firstItem['id']}", [
                'quantity' => 3
            ], [
                'X-Cart-Token' => $this->guestCartToken
            ]);
            
            if ($updateResponse['success']) {
                $this->logSuccess("Updated item quantity to 3");
            } else {
                $this->logError("Failed to update item", $updateResponse);
            }
        }

        echo "\n";
    }

    private function testUserAuthenticationAndMigration()
    {
        echo "🎯 PHASE 2: USER AUTHENTICATION & MIGRATION\n";
        echo "--------------------------------------------\n";

        // Test 6: Get User Token (Simulate Login)
        echo "6️⃣ Testing: User Authentication\n";
        $this->userToken = $this->getUserToken();
        
        if ($this->userToken) {
            $this->logSuccess("User authenticated successfully");
            $this->logSuccess("JWT token: " . substr($this->userToken, 0, 30) . "...");
        } else {
            $this->logError("Failed to authenticate user");
            return;
        }

        // Test 7: Cart Migration
        echo "\n7️⃣ Testing: Cart Migration\n";
        $response = $this->makeRequest('POST', '/my-cart/migrate', [
            'guest_cart_id' => $this->guestCartId,
            'guest_cart_token' => $this->guestCartToken,
            'merge_strategy' => 'merge',
            'clear_guest_cart' => true
        ], [
            'Authorization' => "Bearer {$this->userToken}"
        ]);
        
        if ($response['status'] && isset($response['data']['uuid'])) {
            $this->userCartId = $response['data']['uuid'];
            $itemsCount = $response['data']['items_count'] ?? 0;
            $this->logSuccess("Cart migrated successfully");
            $this->logSuccess("User cart UUID: {$this->userCartId}");
            $this->logSuccess("Items migrated: {$itemsCount}");
        } else {
            $this->logError("Failed to migrate cart", $response);
        }

        echo "\n";
    }

    private function testUserCartOperations()
    {
        echo "🎯 PHASE 3: USER CART OPERATIONS\n";
        echo "--------------------------------\n";

        // Test 8: Get User Cart
        echo "8️⃣ Testing: Get User Cart\n";
        $response = $this->makeRequest('GET', '/my-cart/', [], [
            'Authorization' => "Bearer {$this->userToken}"
        ]);
        
        if ($response['success'] && isset($response['data']['items'])) {
            $itemCount = count($response['data']['items']);
            $totalAmount = $response['data']['totals']['total_amount'] ?? 0;
            $this->logSuccess("User cart retrieved");
            $this->logSuccess("Items in cart: {$itemCount}");
            $this->logSuccess("Total amount: $" . number_format($totalAmount, 2));
        } else {
            $this->logError("Failed to get user cart", $response);
        }

        // Test 9: Save Items for Later
        echo "\n9️⃣ Testing: Save Items for Later\n";
        if (isset($response['data']['items'][0])) {
            $firstItem = $response['data']['items'][0];
            $saveResponse = $this->makeRequest('POST', '/my-cart/save-for-later', [
                'item_ids' => [$firstItem['id']]
            ], [
                'Authorization' => "Bearer {$this->userToken}"
            ]);
            
            if ($saveResponse['success']) {
                $this->logSuccess("Saved 1 item for later");
            } else {
                $this->logError("Failed to save item for later", $saveResponse);
            }
        }

        echo "\n";
    }

    private function testAdvancedFeatures()
    {
        echo "🎯 PHASE 4: ADVANCED FEATURES\n";
        echo "-----------------------------\n";

        // Test 10: Cart Validation
        echo "🔟 Testing: Cart Validation\n";
        $response = $this->makeRequest('POST', "/cart/{$this->userCartId}/validate", [], [
            'Authorization' => "Bearer {$this->userToken}"
        ]);
        
        if ($response !== null) {
            if (isset($response['data']['is_valid'])) {
                $isValid = $response['data']['is_valid'] ? 'Yes' : 'No';
                $this->logSuccess("Cart validation completed");
                $this->logSuccess("Cart is valid: {$isValid}");
            } else {
                $this->logSuccess("Cart validation endpoint responded");
            }
        } else {
            $this->logWarning("Cart validation endpoint not implemented");
        }

        // Test 11: Get Cart Statistics
        echo "\n1️⃣1️⃣ Testing: Get Cart Statistics\n";
        $response = $this->makeRequest('GET', '/my-cart/statistics', [], [
            'Authorization' => "Bearer {$this->userToken}"
        ]);
        
        if ($response !== null && $response['success']) {
            $this->logSuccess("Cart statistics retrieved");
        } else {
            $this->logWarning("Cart statistics endpoint not implemented");
        }

        echo "\n";
    }

    private function testErrorScenarios()
    {
        echo "🎯 PHASE 5: ERROR SCENARIOS\n";
        echo "---------------------------\n";

        // Test 12: Invalid Cart Token
        echo "1️⃣2️⃣ Testing: Invalid Cart Token\n";
        $response = $this->makeRequest('GET', '/cart/invalid-cart-id', [], [
            'X-Cart-Token' => 'invalid-token'
        ]);
        
        if ($response === null || !$response['success']) {
            $this->logSuccess("Invalid token properly rejected");
        } else {
            $this->logError("Invalid token was accepted (security issue!)");
        }

        // Test 13: Unauthorized Access
        echo "\n1️⃣3️⃣ Testing: Unauthorized Access\n";
        $response = $this->makeRequest('GET', '/my-cart/');
        
        if ($response === null || !$response['success']) {
            $this->logSuccess("Unauthorized access properly blocked");
        } else {
            $this->logError("Unauthorized access was allowed (security issue!)");
        }

        echo "\n";
    }

    private function makeRequest($method, $endpoint, $data = [], $headers = [])
    {
        $url = $this->baseUrl . $endpoint;
        $ch = curl_init();
        
        $defaultHeaders = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        foreach ($headers as $key => $value) {
            $defaultHeaders[] = "{$key}: {$value}";
        }
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $defaultHeaders,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10
        ]);
        
        if (!empty($data) && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "   ❌ CURL Error: {$error}\n";
            return null;
        }
        
        $decodedResponse = json_decode($response, true);
        
        if ($httpCode >= 200 && $httpCode < 300) {
            return $decodedResponse;
        } else {
            echo "   ❌ HTTP {$httpCode}: " . ($decodedResponse['message'] ?? 'Unknown error') . "\n";
            return $decodedResponse;
        }
    }

    private function getTestProducts()
    {
        // Simulate getting products from database
        return [
            ['id' => 1, 'name' => 'Test Product 1', 'price' => 29.99],
            ['id' => 2, 'name' => 'Test Product 2', 'price' => 39.99],
            ['id' => 3, 'name' => 'Test Product 3', 'price' => 19.99]
        ];
    }

    private function getUserToken()
    {
        // This would normally be obtained through login API
        // For testing, we'll use a pre-generated token or create one via Tinker
        return 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.test-token-here';
    }

    private function logSuccess($message)
    {
        echo "   ✅ {$message}\n";
        $this->testResults[] = ['status' => 'success', 'message' => $message];
    }

    private function logError($message, $details = null)
    {
        echo "   ❌ {$message}\n";
        if ($details) {
            echo "      Details: " . json_encode($details, JSON_PRETTY_PRINT) . "\n";
        }
        $this->testResults[] = ['status' => 'error', 'message' => $message, 'details' => $details];
    }

    private function logWarning($message)
    {
        echo "   ⚠️ {$message}\n";
        $this->testResults[] = ['status' => 'warning', 'message' => $message];
    }

    private function printSummary()
    {
        echo "\n📊 TEST SUMMARY\n";
        echo "===============\n";
        
        $successCount = count(array_filter($this->testResults, fn($r) => $r['status'] === 'success'));
        $errorCount = count(array_filter($this->testResults, fn($r) => $r['status'] === 'error'));
        $warningCount = count(array_filter($this->testResults, fn($r) => $r['status'] === 'warning'));
        
        echo "✅ Successful tests: {$successCount}\n";
        echo "❌ Failed tests: {$errorCount}\n";
        echo "⚠️ Warnings: {$warningCount}\n";
        echo "📋 Total tests: " . count($this->testResults) . "\n\n";
        
        if ($errorCount === 0) {
            echo "🎉 ALL TESTS PASSED! Cart functionality is working correctly.\n";
        } else {
            echo "🚨 Some tests failed. Please check the errors above.\n";
        }
    }
}

// Run the tests
$tester = new CartFunctionalityTester();
$tester->runAllTests();
