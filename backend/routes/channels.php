<?php

use Illuminate\Support\Facades\Broadcast;
use App\Models\User;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

// User-specific notifications
Broadcast::channel('private-user.{userId}', function (User $user, int $userId) {
    return (int) $user->id === $userId;
});

// Vendor-specific notifications
Broadcast::channel('private-vendor.{vendorId}', function (User $user, int $vendorId) {
    // Check if user belongs to this vendor or has admin role
    return $user->vendor_id === $vendorId || $user->hasRole(['admin', 'super-admin']);
});

// Admin-wide notifications
Broadcast::channel('private-admin.global', function (User $user) {
    return $user->hasRole(['admin', 'super-admin']);
});

// Order-specific notifications (accessible by buyer, seller, admin)
Broadcast::channel('private-order.{orderId}', function (User $user, int $orderId) {
    // Use the OrderPolicy to check if user can view this order
    return $user->can('viewOrder', \App\Models\Order::class, $orderId);
});

// Legacy Laravel notification channel for backward compatibility
Broadcast::channel('App.Models.User.{id}', function (User $user, int $id) {
    return (int) $user->id === $id;
});
