# Vitamins.ae Blog SEO and Performance Guidelines

## SEO Optimization Strategy

### URL Structure
```
Primary Structure:
/blog/                              # Blog landing page
/blog/[category]/                   # Category archives
/blog/[category]/[article-slug]     # Individual articles
/blog/experts/[expert-slug]         # Expert profiles
/blog/tags/[tag-slug]              # Tag archives
/blog/search                        # Search results

SEO-Friendly Patterns:
✅ /blog/nutrition/best-vitamin-d-supplements-2024
✅ /blog/fitness/post-workout-recovery-guide
✅ /blog/experts/dr-sarah-johnson-nutritionist
❌ /blog/article/123456
❌ /blog/p/nutrition-tips
```

### Next.js Metadata Implementation
```typescript
// app/blog/[category]/[slug]/page.tsx
import { Metadata } from 'next';

export async function generateMetadata({ params }): Promise<Metadata> {
  const article = await getArticle(params.slug);

  return {
    title: article.metaTitle || `${article.title} | Vitamins.ae Blog`,
    description: article.metaDescription || article.excerpt,
    
    // Open Graph
    openGraph: {
      title: article.title,
      description: article.excerpt,
      url: `https://vitamins.ae/blog/${params.category}/${params.slug}`,
      siteName: 'Vitamins.ae Blog',
      images: [
        {
          url: article.coverImage?.url || '/images/wellness-og-default.jpg',
          width: 1200,
          height: 630,
          alt: article.title,
        }
      ],
      locale: 'en_US',
      type: 'article',
      publishedTime: article.publishedAt,
      modifiedTime: article.updatedAt,
      authors: [article.author.name],
      section: article.category.name,
      tags: article.tags.map(tag => tag.name),
    },
    
    // Twitter Card
    twitter: {
      card: 'summary_large_image',
      title: article.title,
      description: article.excerpt,
      images: [article.coverImage?.url || '/images/wellness-twitter-default.jpg'],
      creator: `@${article.author.twitterHandle || 'VitaminsAE'}`,
    },
    
    // Additional Meta Tags
    keywords: article.tags.map(tag => tag.name).join(', '),
    authors: [{ name: article.author.name, url: `/blog/experts/${article.author.slug}` }],
    creator: article.author.name,
    publisher: 'Vitamins.ae',

    // Canonical URL
    alternates: {
      canonical: article.canonicalUrl || `https://vitamins.ae/blog/${params.category}/${params.slug}`,
    },
    
    // Robots
    robots: {
      index: article.status === 'published',
      follow: true,
      googleBot: {
        index: article.status === 'published',
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}
```

### Structured Data Implementation
```typescript
// components/wellness/StructuredData.tsx
export function ArticleStructuredData({ article }: { article: Article }) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: article.title,
    description: article.excerpt,
    image: article.coverImage?.url,
    datePublished: article.publishedAt,
    dateModified: article.updatedAt,
    author: {
      '@type': 'Person',
      name: article.author.name,
      url: `https://vitamins.ae/blog/experts/${article.author.slug}`,
      jobTitle: article.author.credentials.join(', '),
      knowsAbout: article.author.specialties.map(s => s.name),
    },
    publisher: {
      '@type': 'Organization',
      name: 'Vitamins.ae',
      logo: {
        '@type': 'ImageObject',
        url: 'https://vitamins.ae/images/logo-structured-data.png',
      },
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `https://vitamins.ae/blog/${article.category.slug}/${article.slug}`,
    },
    articleSection: article.category.name,
    keywords: article.tags.map(tag => tag.name),
    wordCount: calculateWordCount(article.content),
    timeRequired: `PT${article.readTime}M`,
    
    // Medical content specific
    ...(article.medicalDisclaimer && {
      about: {
        '@type': 'MedicalCondition',
        name: article.category.name,
      },
      audience: {
        '@type': 'PeopleAudience',
        audienceType: 'health-conscious consumers',
      },
    }),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

export function BreadcrumbStructuredData({ breadcrumbs }: { breadcrumbs: BreadcrumbItem[] }) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.label,
      item: `https://vitamins.ae${item.href}`,
    })),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}
```

### XML Sitemap Generation
```typescript
// app/blog/sitemap.xml/route.ts
import { MetadataRoute } from 'next';

export async function GET(): Promise<Response> {
  const articles = await getPublishedArticles();
  const categories = await getActiveCategories();
  const experts = await getActiveExperts();

  const sitemap: MetadataRoute.Sitemap = [
    // Static pages
    {
      url: 'https://vitamins.ae/blog',
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
    },

    // Category pages
    ...categories.map(category => ({
      url: `https://vitamins.ae/blog/${category.slug}`,
      lastModified: category.updatedAt,
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    })),

    // Article pages
    ...articles.map(article => ({
      url: `https://vitamins.ae/blog/${article.category.slug}/${article.slug}`,
      lastModified: article.updatedAt,
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    })),

    // Expert pages
    ...experts.map(expert => ({
      url: `https://vitamins.ae/blog/experts/${expert.slug}`,
      lastModified: expert.updatedAt,
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    })),
  ];

  const xml = generateSitemapXML(sitemap);
  
  return new Response(xml, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600',
    },
  });
}
```

### RSS Feed Implementation
```typescript
// app/blog/feed.xml/route.ts
export async function GET(): Promise<Response> {
  const articles = await getRecentArticles(50);

  const rss = `<?xml version="1.0" encoding="UTF-8"?>
    <rss version="2.0" xmlns:content="http://purl.org/rss/1.0/modules/content/">
      <channel>
        <title>Vitamins.ae Blog</title>
        <description>Evidence-based wellness insights and expert health advice</description>
        <link>https://vitamins.ae/blog</link>
        <language>en-us</language>
        <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
        
        ${articles.map(article => `
          <item>
            <title><![CDATA[${article.title}]]></title>
            <description><![CDATA[${article.excerpt}]]></description>
            <link>https://vitamins.ae/blog/${article.category.slug}/${article.slug}</link>
            <guid>https://vitamins.ae/blog/${article.category.slug}/${article.slug}</guid>
            <pubDate>${new Date(article.publishedAt).toUTCString()}</pubDate>
            <author>${article.author.email} (${article.author.name})</author>
            <category>${article.category.name}</category>
            ${article.coverImage ? `<enclosure url="${article.coverImage.url}" type="image/jpeg"/>` : ''}
          </item>
        `).join('')}
      </channel>
    </rss>`;

  return new Response(rss, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600',
    },
  });
}
```

## Performance Optimization

### Core Web Vitals Targets
```typescript
interface PerformanceTargets {
  LCP: '< 2.5s';  // Largest Contentful Paint
  FID: '< 100ms'; // First Input Delay
  CLS: '< 0.1';   // Cumulative Layout Shift
  
  // Additional Metrics
  TTFB: '< 600ms'; // Time to First Byte
  FCP: '< 1.8s';   // First Contentful Paint
  TTI: '< 3.8s';   // Time to Interactive
}
```

### Image Optimization Strategy
```typescript
// components/wellness/OptimizedImage.tsx
import Image from 'next/image';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width: number;
  height: number;
  priority?: boolean;
  className?: string;
}

export function OptimizedImage({ 
  src, 
  alt, 
  width, 
  height, 
  priority = false,
  className 
}: OptimizedImageProps) {
  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      priority={priority}
      loading={priority ? 'eager' : 'lazy'}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      className={className}
      style={{
        objectFit: 'cover',
      }}
    />
  );
}

// Usage in components
<OptimizedImage
  src={article.coverImage.url}
  alt={article.coverImage.alt}
  width={800}
  height={450}
  priority={isFeatured}
  className="rounded-2xl"
/>
```

### Code Splitting and Lazy Loading
```typescript
// Dynamic imports for heavy components
const VideoPlayer = dynamic(() => import('./VideoPlayer'), {
  loading: () => <div className="animate-pulse bg-gray-200 aspect-video rounded-lg" />,
  ssr: false,
});

const CommentSection = dynamic(() => import('./CommentSection'), {
  loading: () => <CommentSkeleton />,
});

const ShareModal = dynamic(() => import('./ShareModal'));

// Lazy load below-the-fold content
const RelatedArticles = dynamic(() => import('./RelatedArticles'), {
  loading: () => <RelatedArticlesSkeleton />,
});
```

### Font Optimization
```typescript
// app/layout.tsx
import { Inter, Merriweather } from 'next/font/google';

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
  preload: true,
});

const merriweather = Merriweather({
  subsets: ['latin'],
  weight: ['300', '400', '700'],
  display: 'swap',
  variable: '--font-merriweather',
  preload: false, // Only load when needed for headings
});

export default function RootLayout({ children }) {
  return (
    <html lang="en" className={`${inter.variable} ${merriweather.variable}`}>
      <body className="font-sans">{children}</body>
    </html>
  );
}
```

### Caching Strategy
```typescript
// next.config.js
const nextConfig = {
  images: {
    domains: ['cdn.vitamins.ae'],
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 31536000, // 1 year
  },
  
  // Static page caching
  experimental: {
    staleTimes: {
      dynamic: 30,     // 30 seconds for dynamic pages
      static: 180,     // 3 minutes for static pages
    },
  },
  
  // Headers for caching
  async headers() {
    return [
      {
        source: '/blog/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=300, s-maxage=300, stale-while-revalidate=86400',
          },
        ],
      },
      {
        source: '/images/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};
```

### ISR Configuration
```typescript
// app/blog/[category]/[slug]/page.tsx
export const revalidate = 300; // 5 minutes

export async function generateStaticParams() {
  const articles = await getPopularArticles(100);
  
  return articles.map((article) => ({
    category: article.category.slug,
    slug: article.slug,
  }));
}

// Incremental regeneration for category pages
export const dynamic = 'force-static';
export const dynamicParams = true;
```

### Bundle Size Optimization
```typescript
// webpack-bundle-analyzer integration
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

module.exports = withBundleAnalyzer({
  // Bundle size targets
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },
  
  // Tree shaking
  webpack: (config) => {
    config.optimization.usedExports = true;
    return config;
  },
});

// Target bundle sizes:
// - Article pages: ≤ 120KB gzipped
// - Category pages: ≤ 100KB gzipped
// - Landing page: ≤ 150KB gzipped
```

## Technical SEO Implementation

### Robots.txt Configuration
```
# /public/robots.txt
User-agent: *
Allow: /blog/
Disallow: /blog/search
Disallow: /blog/preview/
Disallow: /api/

# Sitemap
Sitemap: https://vitamins.ae/blog/sitemap.xml
Sitemap: https://vitamins.ae/sitemap.xml

# Crawl delay for respectful crawling
Crawl-delay: 1
```

### Internal Linking Strategy
```typescript
// components/wellness/InternalLinks.tsx
export function SmartInternalLinks({ content }: { content: string }) {
  const processedContent = useMemo(() => {
    return content.replace(
      /\b(vitamin d|omega 3|probiotics|protein powder)\b/gi,
      (match) => {
        const productLink = getProductLinkForKeyword(match);
        const articleLink = getArticleLinkForKeyword(match);
        
        return `<a href="${articleLink}" class="text-rico-primary hover:underline">${match}</a>`;
      }
    );
  }, [content]);
  
  return <div dangerouslySetInnerHTML={{ __html: processedContent }} />;
}
```

### Page Speed Monitoring
```typescript
// lib/performance.ts
export function trackWebVitals(metric: any) {
  // Send to analytics
  gtag('event', metric.name, {
    event_category: 'Web Vitals',
    value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
    event_label: metric.id,
    non_interaction: true,
  });
  
  // Send to performance monitoring service
  if (process.env.NODE_ENV === 'production') {
    fetch('/api/analytics/web-vitals', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(metric),
    });
  }
}

// app/layout.tsx
import { reportWebVitals } from 'next/web-vitals';

export default function RootLayout({ children }) {
  useEffect(() => {
    reportWebVitals(trackWebVitals);
  }, []);
  
  return children;
}
```

## E-E-A-T Optimization (Expertise, Experience, Authoritativeness, Trustworthiness)

### Author Authority Signals
```typescript
// components/wellness/AuthorCredentials.tsx
export function AuthorCredentials({ author }: { author: Author }) {
  return (
    <div className="bg-gray-50 rounded-xl p-6 mt-8">
      <div className="flex items-start gap-4">
        <Image
          src={author.avatar}
          alt={author.name}
          width={80}
          height={80}
          className="rounded-full"
        />
        <div>
          <h3 className="font-semibold text-lg">{author.name}</h3>
          <div className="flex flex-wrap gap-2 mb-2">
            {author.credentials.map(credential => (
              <Badge key={credential.type} variant="outline">
                {credential.type}
              </Badge>
            ))}
          </div>
          <p className="text-gray-600 text-sm mb-3">{author.bio}</p>
          
          {/* Authority indicators */}
          <div className="flex items-center gap-4 text-xs text-gray-500">
            <span>{author.articleCount} articles published</span>
            <span>{author.yearsExperience} years experience</span>
            {author.isVerified && (
              <span className="flex items-center gap-1 text-green-600">
                <CheckCircle size={12} />
                Verified Expert
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
```

### Content Quality Signals
```typescript
// components/wellness/QualityIndicators.tsx
export function QualityIndicators({ article }: { article: Article }) {
  return (
    <div className="border-t border-gray-200 pt-6 mt-8">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
        <div>
          <div className="text-2xl font-bold text-rico-primary">
            {article.references.length}
          </div>
          <div className="text-sm text-gray-600">Scientific References</div>
        </div>
        
        <div>
          <div className="text-2xl font-bold text-rico-primary">
            {formatDate(article.lastMedicalReview)}
          </div>
          <div className="text-sm text-gray-600">Last Medical Review</div>
        </div>
        
        <div>
          <div className="text-2xl font-bold text-rico-primary">
            {article.readTime}min
          </div>
          <div className="text-sm text-gray-600">Read Time</div>
        </div>
        
        <div>
          <div className="text-2xl font-bold text-rico-primary">
            {formatNumber(article.viewCount)}
          </div>
          <div className="text-sm text-gray-600">Readers</div>
        </div>
      </div>
    </div>
  );
}
```

## Analytics and Monitoring

### SEO Performance Tracking
```typescript
// lib/seo-analytics.ts
export function trackSEOMetrics() {
  // Track organic traffic
  gtag('event', 'organic_traffic', {
    event_category: 'SEO',
    event_label: window.location.pathname,
  });
  
  // Track search rankings (server-side)
  fetch('/api/seo/track-rankings', {
    method: 'POST',
    body: JSON.stringify({
      url: window.location.href,
      keywords: getPageKeywords(),
    }),
  });
}

// Monitor Core Web Vitals
export function monitorCoreWebVitals() {
  new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'largest-contentful-paint') {
        trackMetric('LCP', entry.startTime);
      }
    }
  }).observe({ entryTypes: ['largest-contentful-paint'] });
}
```

### A/B Testing for SEO
```typescript
// lib/seo-testing.ts
export function runSEOTest(testName: string, variants: string[]) {
  const variant = getVariantForUser(testName);
  
  // Track which variant is shown
  gtag('event', 'seo_test_view', {
    event_category: 'SEO Testing',
    event_label: `${testName}_${variant}`,
  });
  
  return variant;
}

// Usage in components
const titleVariant = runSEOTest('article_title_format', [
  'standard',
  'question_format',
  'benefit_focused'
]);
```
