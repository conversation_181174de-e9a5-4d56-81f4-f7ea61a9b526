# Vitamins.ae Blog Implementation Guide

## URL Structure Changes Summary

All URLs have been updated from `/wellness/` to `/blog/` as requested:

### Primary Routes
- **Blog Landing**: `/blog/` (was `/wellness/`)
- **Category Archives**: `/blog/[category]/` (was `/wellness/[category]/`)
- **Article Pages**: `/blog/[category]/[article-slug]` (was `/wellness/[category]/[article-slug]`)
- **Expert Directory**: `/blog/experts/` (was `/wellness/experts/`)
- **Expert Profiles**: `/blog/experts/[expert-slug]` (was `/wellness/experts/[expert-slug]`)
- **Search Results**: `/blog/search` (was `/wellness/search`)
- **Tag Archives**: `/blog/tags/[tag-slug]` (was `/wellness/tags/[tag-slug]`)
- **Video Hub**: `/blog/videos/` (was `/wellness/videos/`)

### API Endpoints
- **Articles**: `/api/blog/articles` (was `/api/wellness/articles`)
- **Experts**: `/api/blog/experts` (was `/api/wellness/experts`)
- **Categories**: `/api/blog/categories` (was `/api/wellness/categories`)
- **Tags**: `/api/blog/tags` (was `/api/wellness/tags`)
- **Analytics**: `/api/blog/analytics` (was `/api/wellness/analytics`)
- **Search**: `/api/blog/search` (was `/api/wellness/search`)

## Implementation Steps

### Phase 1: Project Setup (Week 1)

#### 1.1 Create Directory Structure
```bash
mkdir -p customer/app/\(main\)/blog/{experts,search,tags,videos}
mkdir -p customer/components/blog/{layout,article,navigation,cards,widgets}
mkdir -p customer/hooks/blog
mkdir -p customer/lib/blog
```

#### 1.2 Install Additional Dependencies
```bash
cd customer
npm install @mdx-js/loader @mdx-js/react gray-matter reading-time
npm install @next/mdx remark-gfm rehype-highlight
npm install framer-motion # for animations
npm install @headlessui/react # for accessible components
```

#### 1.3 Update Tailwind Configuration
```javascript
// customer/tailwind.config.js - Add blog-specific utilities
module.exports = {
  // ... existing config
  theme: {
    extend: {
      // ... existing extensions
      typography: {
        DEFAULT: {
          css: {
            maxWidth: 'none',
            color: 'var(--color-rico-text-icon-black)',
            a: {
              color: 'var(--color-rico-primary)',
              '&:hover': {
                color: 'var(--color-rico-secondary-dark-4)',
              },
            },
          },
        },
      },
    },
  },
  plugins: [
    // ... existing plugins
    require('@tailwindcss/typography'),
  ],
}
```

### Phase 2: Core Components (Week 2)

#### 2.1 Create Base Layout Components
```typescript
// customer/components/blog/layout/BlogLayout.tsx
import { ReactNode } from 'react';
import BlogNavigation from './BlogNavigation';
import BlogFooter from './BlogFooter';

interface BlogLayoutProps {
  children: ReactNode;
  showNavigation?: boolean;
}

export default function BlogLayout({ 
  children, 
  showNavigation = true 
}: BlogLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      {showNavigation && <BlogNavigation />}
      <main>{children}</main>
      <BlogFooter />
    </div>
  );
}
```

#### 2.2 Create Article Components
```typescript
// customer/components/blog/article/ArticleCard.tsx
import { BlogCard } from '../cards/BlogCard';
import { Article } from '@/types/blog';

interface ArticleCardProps {
  article: Article;
  variant?: 'default' | 'compact' | 'wide' | 'featured';
  showBookmark?: boolean;
  showShare?: boolean;
}

export default function ArticleCard({
  article,
  variant = 'default',
  showBookmark = true,
  showShare = true
}: ArticleCardProps) {
  return (
    <BlogCard
      variant={variant}
      article={article}
      showBookmark={showBookmark}
      showShare={showShare}
    />
  );
}
```

#### 2.3 Create Navigation Components
```typescript
// customer/components/blog/navigation/BlogNavigation.tsx
import Link from 'next/link';
import { useCategories } from '@/hooks/blog/useCategories';
import BlogSearchBar from './BlogSearchBar';

export default function BlogNavigation() {
  const { categories } = useCategories();

  return (
    <nav className="bg-white border-b border-gray-200 sticky top-[120px] z-40">
      <div className="container">
        <div className="flex items-center justify-between py-4">
          <div className="flex items-center space-x-6">
            <Link href="/blog" className="font-semibold text-rico-primary">
              Blog
            </Link>
            <div className="hidden md:flex items-center space-x-4">
              {categories?.map(category => (
                <Link 
                  key={category.slug}
                  href={`/blog/${category.slug}`}
                  className="text-gray-600 hover:text-rico-primary transition-colors"
                >
                  {category.name}
                </Link>
              ))}
            </div>
          </div>
          <BlogSearchBar />
        </div>
      </div>
    </nav>
  );
}
```

### Phase 3: Page Implementation (Week 3)

#### 3.1 Blog Landing Page
```typescript
// customer/app/(main)/blog/page.tsx
import BlogLayout from '@/components/blog/layout/BlogLayout';
import HeroSection from '@/components/blog/sections/HeroSection';
import FeaturedArticles from '@/components/blog/sections/FeaturedArticles';
import PopularArticles from '@/components/blog/sections/PopularArticles';
import ExpertShowcase from '@/components/blog/sections/ExpertShowcase';
import TopicGrid from '@/components/blog/sections/TopicGrid';
import NewsletterCTA from '@/components/blog/widgets/NewsletterCTA';
import Disclaimer from '@/components/blog/widgets/Disclaimer';

export default async function BlogPage() {
  const [featuredArticles, popularArticles, experts, topics] = await Promise.all([
    getFeaturedArticles(),
    getPopularArticles(),
    getFeaturedExperts(),
    getWellnessTopics(),
  ]);

  return (
    <BlogLayout>
      <HeroSection />
      <FeaturedArticles articles={featuredArticles} />
      <PopularArticles articles={popularArticles} />
      <ExpertShowcase experts={experts} />
      <TopicGrid topics={topics} />
      <NewsletterCTA variant="inline" />
      <Disclaimer type="medical" />
    </BlogLayout>
  );
}
```

#### 3.2 Article Detail Page
```typescript
// customer/app/(main)/blog/[category]/[slug]/page.tsx
import { Metadata } from 'next';
import BlogLayout from '@/components/blog/layout/BlogLayout';
import ArticleHeader from '@/components/blog/article/ArticleHeader';
import ArticleContent from '@/components/blog/article/ArticleContent';
import ArticleSidebar from '@/components/blog/article/ArticleSidebar';
import RelatedArticles from '@/components/blog/sections/RelatedArticles';
import { getArticle, getRelatedArticles } from '@/lib/blog/articles';

interface ArticlePageProps {
  params: { category: string; slug: string };
}

export async function generateMetadata({ params }: ArticlePageProps): Promise<Metadata> {
  const article = await getArticle(params.slug);
  
  return {
    title: article.metaTitle || `${article.title} | Vitamins.ae Blog`,
    description: article.metaDescription || article.excerpt,
    openGraph: {
      title: article.title,
      description: article.excerpt,
      url: `https://vitamins.ae/blog/${params.category}/${params.slug}`,
      siteName: 'Vitamins.ae Blog',
      images: [{ url: article.coverImage?.url || '/images/blog-og-default.jpg' }],
      type: 'article',
    },
  };
}

export default async function ArticlePage({ params }: ArticlePageProps) {
  const [article, relatedArticles] = await Promise.all([
    getArticle(params.slug),
    getRelatedArticles(params.slug),
  ]);

  return (
    <BlogLayout>
      <div className="container max-w-7xl mx-auto py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <div className="lg:col-span-3">
            <ArticleHeader article={article} />
            <ArticleContent article={article} />
          </div>
          <aside className="lg:col-span-1">
            <ArticleSidebar article={article} />
          </aside>
        </div>
        <RelatedArticles articles={relatedArticles} />
      </div>
    </BlogLayout>
  );
}
```

### Phase 4: Data Layer (Week 4)

#### 4.1 Create Data Fetching Hooks
```typescript
// customer/hooks/blog/useArticles.ts
import useSWR from 'swr';
import { Article } from '@/types/blog';

interface UseArticlesOptions {
  category?: string;
  tag?: string;
  author?: string;
  featured?: boolean;
  limit?: number;
  sort?: 'newest' | 'popular' | 'trending';
}

export function useArticles(options: UseArticlesOptions = {}) {
  const params = new URLSearchParams();
  Object.entries(options).forEach(([key, value]) => {
    if (value !== undefined) {
      params.append(key, value.toString());
    }
  });

  const { data, error, isLoading } = useSWR<{
    articles: Article[];
    total: number;
    hasMore: boolean;
  }>(`/api/blog/articles?${params.toString()}`);

  return {
    articles: data?.articles || [],
    total: data?.total || 0,
    hasMore: data?.hasMore || false,
    isLoading,
    error,
  };
}
```

#### 4.2 Create API Routes
```typescript
// customer/app/api/blog/articles/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getArticles } from '@/lib/blog/articles';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  
  const filters = {
    category: searchParams.get('category'),
    tag: searchParams.get('tag'),
    author: searchParams.get('author'),
    featured: searchParams.get('featured') === 'true',
    limit: parseInt(searchParams.get('limit') || '20'),
    offset: parseInt(searchParams.get('offset') || '0'),
    sort: searchParams.get('sort') || 'newest',
  };

  try {
    const result = await getArticles(filters);
    
    return NextResponse.json(result, {
      headers: {
        'Cache-Control': 'public, max-age=300, s-maxage=300',
      },
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch articles' },
      { status: 500 }
    );
  }
}
```

### Phase 5: Integration with Existing System (Week 5)

#### 5.1 Update Header Navigation
```typescript
// customer/components/layout/Header.js - Add blog link
// Around line 193 (after existing links)
<Link href="/blog" className="border border-white rounded-md px-4 py-2 text-sm hover:bg-white hover:text-teal-600 transition-colors">
  Blog
</Link>
```

#### 5.2 Create Product Integration Components
```typescript
// customer/components/blog/widgets/ProductRecommendation.tsx
import { ProductInlineCard } from '@/components/ui/product-card';
import { Product } from '@/types/product';

interface ProductRecommendationProps {
  products: Product[];
  context: 'article' | 'sidebar';
  title?: string;
}

export default function ProductRecommendation({
  products,
  context,
  title = "Recommended Products"
}: ProductRecommendationProps) {
  return (
    <section className="bg-gray-50 rounded-2xl p-6 mt-8">
      <h3 className="font-semibold text-lg mb-4">{title}</h3>
      <div className={`grid gap-4 ${
        context === 'article' ? 'grid-cols-1 md:grid-cols-2' : 'grid-cols-1'
      }`}>
        {products.map(product => (
          <ProductInlineCard
            key={product.id}
            product={product}
            context={context}
            utmSource="blog_recommendation"
          />
        ))}
      </div>
    </section>
  );
}
```

### Phase 6: SEO and Performance (Week 6)

#### 6.1 Implement Structured Data
```typescript
// customer/components/blog/seo/StructuredData.tsx
import { Article } from '@/types/blog';

export function ArticleStructuredData({ article }: { article: Article }) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: article.title,
    description: article.excerpt,
    image: article.coverImage?.url,
    datePublished: article.publishedAt,
    dateModified: article.updatedAt,
    author: {
      '@type': 'Person',
      name: article.author.name,
      url: `https://vitamins.ae/blog/experts/${article.author.slug}`,
    },
    publisher: {
      '@type': 'Organization',
      name: 'Vitamins.ae',
      logo: {
        '@type': 'ImageObject',
        url: 'https://vitamins.ae/images/logo.png',
      },
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}
```

#### 6.2 Create Sitemap Generation
```typescript
// customer/app/blog/sitemap.xml/route.ts
import { MetadataRoute } from 'next';
import { getPublishedArticles, getActiveCategories, getActiveExperts } from '@/lib/blog';

export async function GET(): Promise<Response> {
  const [articles, categories, experts] = await Promise.all([
    getPublishedArticles(),
    getActiveCategories(),
    getActiveExperts(),
  ]);
  
  const sitemap: MetadataRoute.Sitemap = [
    {
      url: 'https://vitamins.ae/blog',
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
    },
    ...categories.map(category => ({
      url: `https://vitamins.ae/blog/${category.slug}`,
      lastModified: category.updatedAt,
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    })),
    ...articles.map(article => ({
      url: `https://vitamins.ae/blog/${article.category.slug}/${article.slug}`,
      lastModified: article.updatedAt,
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    })),
    ...experts.map(expert => ({
      url: `https://vitamins.ae/blog/experts/${expert.slug}`,
      lastModified: expert.updatedAt,
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    })),
  ];

  const xml = `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      ${sitemap.map(item => `
        <url>
          <loc>${item.url}</loc>
          <lastmod>${item.lastModified.toISOString()}</lastmod>
          <changefreq>${item.changeFrequency}</changefreq>
          <priority>${item.priority}</priority>
        </url>
      `).join('')}
    </urlset>`;

  return new Response(xml, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600',
    },
  });
}
```

## Testing Strategy

### Unit Tests
```typescript
// customer/__tests__/blog/components/ArticleCard.test.tsx
import { render, screen } from '@testing-library/react';
import ArticleCard from '@/components/blog/article/ArticleCard';
import { mockArticle } from '@/test-utils/mocks';

describe('ArticleCard', () => {
  it('renders article title and excerpt', () => {
    render(<ArticleCard article={mockArticle} />);
    
    expect(screen.getByText(mockArticle.title)).toBeInTheDocument();
    expect(screen.getByText(mockArticle.excerpt)).toBeInTheDocument();
  });

  it('displays author information', () => {
    render(<ArticleCard article={mockArticle} />);
    
    expect(screen.getByText(mockArticle.author.name)).toBeInTheDocument();
  });
});
```

### Integration Tests
```typescript
// customer/__tests__/blog/pages/blog.test.tsx
import { render, screen } from '@testing-library/react';
import BlogPage from '@/app/(main)/blog/page';

// Mock the data fetching functions
jest.mock('@/lib/blog/articles', () => ({
  getFeaturedArticles: jest.fn(() => Promise.resolve([])),
  getPopularArticles: jest.fn(() => Promise.resolve([])),
}));

describe('Blog Landing Page', () => {
  it('renders the main heading', async () => {
    render(await BlogPage());
    
    expect(screen.getByText('Welcome to the Vitamins.ae Blog')).toBeInTheDocument();
  });
});
```

### E2E Tests
```typescript
// customer/e2e/blog.spec.ts
import { test, expect } from '@playwright/test';

test('blog navigation and article reading flow', async ({ page }) => {
  // Navigate to blog
  await page.goto('/blog');
  await expect(page.locator('h1')).toContainText('Welcome to the Vitamins.ae Blog');

  // Click on first article
  await page.locator('[data-testid="article-card"]').first().click();
  
  // Verify article page loads
  await expect(page.locator('article')).toBeVisible();
  await expect(page.locator('[data-testid="article-title"]')).toBeVisible();
});
```

## Deployment Checklist

### Pre-deployment
- [ ] All components tested and working
- [ ] SEO metadata implemented
- [ ] Performance optimizations applied
- [ ] Accessibility compliance verified
- [ ] Mobile responsiveness tested
- [ ] Cross-browser compatibility checked

### Deployment
- [ ] Database migrations run
- [ ] Static assets optimized
- [ ] CDN cache cleared
- [ ] Sitemap submitted to search engines
- [ ] Analytics tracking verified
- [ ] Error monitoring configured

### Post-deployment
- [ ] Core Web Vitals monitored
- [ ] Search console errors checked
- [ ] User feedback collected
- [ ] Performance metrics tracked
- [ ] A/B tests initiated

## Maintenance and Updates

### Content Management
- Weekly content calendar review
- Monthly SEO performance analysis
- Quarterly expert content audit
- Bi-annual design system updates

### Technical Maintenance
- Monthly dependency updates
- Quarterly performance audits
- Bi-annual security reviews
- Annual accessibility audits

This implementation guide provides a comprehensive roadmap for building the Vitamins.ae blog system with the updated `/blog/` URL structure.
