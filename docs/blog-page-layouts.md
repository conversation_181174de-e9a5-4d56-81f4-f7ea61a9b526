# Vitamins.ae Blog System - Integrated Layout Specifications

## Integration Overview

The blog system integrates seamlessly within the existing site structure, preserving all current navigation, header, footer, and design patterns. The blog content renders within the existing `<main>` container using the site's established layout system.

## Site Integration Requirements

### Existing Site Structure Preservation
```jsx
// Existing Layout Structure (UNCHANGED)
<LayoutClient>
  <Header />  {/* Existing header with all current navigation */}
  <main>
    {/* Blog content renders here within existing main container */}
    <BlogContent />
  </main>
  <Footer />  {/* Existing footer unchanged */}
</LayoutClient>
```

### Header Integration
```jsx
// Add to existing Header.js navigation bar (line ~181)
// Insert between existing links in the navigation section
<Link href="/blog" className="px-4 py-3 text-sm hover:bg-teal-700 transition-colors border-r border-teal-500">
  Wellness Hub
</Link>
```

### Container and Styling Integration
- **Use existing `container` class** for all blog content
- **Follow existing Rico theme colors** from globals.css
- **Maintain existing responsive breakpoints** (sm, md, lg, xl, 2xl)
- **Use existing card components** and design patterns

## 1. Blog Landing Page - Three-Column Integrated Layout

### Layout Structure
```jsx
const BlogLandingPage = () => (
  // Renders within existing LayoutClient > main container
  <div className="container py-6">
    {/* Page Header */}
    <div className="mb-8">
      <h1 className="text-3xl md:text-4xl font-bold text-rico-secondary-dark-1 mb-4">
        iHerb Wellness Hub
      </h1>
      <p className="text-lg text-gray-600 max-w-3xl">
        Your go-to place for wellness information curated by our experts.
      </p>
    </div>

    {/* Three-Column Layout */}
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">

      {/* Left Sidebar - Navigation & Filters */}
      <aside className="lg:col-span-1">
        <div className="sticky top-24 space-y-6">
          <BlogSidebar />
        </div>
      </aside>

      {/* Main Content Area */}
      <main className="lg:col-span-3">
        <BlogMainContent />
      </main>
    </div>
  </div>
);
```

### Left Sidebar Component
```jsx
const BlogSidebar = () => (
  <div className="space-y-6">
    {/* Categories Section */}
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h3 className="font-semibold text-rico-secondary-dark-1 mb-4 text-sm uppercase tracking-wide">
        Categories
      </h3>
      <nav className="space-y-2">
        <SidebarLink href="/blog" active>Most Popular</SidebarLink>
        <SidebarLink href="/blog/wellness">Wellness</SidebarLink>
        <SidebarLink href="/blog/fitness">Fitness</SidebarLink>
        <SidebarLink href="/blog/nutrition">Nutrition</SidebarLink>
        <SidebarLink href="/blog/beauty">Beauty</SidebarLink>
      </nav>
    </div>

    {/* Content Types Section */}
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h3 className="font-semibold text-rico-secondary-dark-1 mb-4 text-sm uppercase tracking-wide">
        Content Types
      </h3>
      <nav className="space-y-2">
        <SidebarLink href="/blog?type=articles">Articles</SidebarLink>
        <SidebarLink href="/blog?type=videos">Videos</SidebarLink>
      </nav>
    </div>

    {/* Wellness Experts Section */}
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h3 className="font-semibold text-rico-secondary-dark-1 mb-4 text-sm uppercase tracking-wide">
        Wellness Experts
      </h3>
      <div className="space-y-3">
        {featuredExperts.map(expert => (
          <ExpertMiniCard key={expert.id} expert={expert} />
        ))}
      </div>
      <Button variant="outline" size="sm" className="w-full mt-4">
        View All
      </Button>
    </div>
  </div>
);
```

### Main Content Component
```jsx
const BlogMainContent = () => (
  <div className="space-y-8">
    {/* Search Bar */}
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <BlogSearchBar />
    </div>

    {/* What's New Section */}
    <section>
      <SectionHeader title="What's New" />
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
        {latestArticles.map(article => (
          <BlogCard key={article.id} article={article} variant="compact" />
        ))}
      </div>
    </section>

    {/* Popular Articles Section */}
    <section>
      <SectionHeader title="Popular Articles" />
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
        {popularArticles.map(article => (
          <BlogCard key={article.id} article={article} variant="compact" />
        ))}
      </div>
    </section>

    {/* Featured Authors Section */}
    <section>
      <SectionHeader title="Featured Authors" />
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
        {featuredAuthors.map(author => (
          <AuthorCard key={author.id} author={author} />
        ))}
      </div>
    </section>

    {/* Explore Topics Section */}
    <section>
      <SectionHeader title="Explore" />
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {topics.map(topic => (
          <TopicCard key={topic.id} topic={topic} />
        ))}
      </div>
    </section>

    {/* Pagination */}
    <div className="flex justify-center">
      <BlogPagination currentPage={1} totalPages={10} />
    </div>
  </div>
);
```

### Responsive Behavior
- **Mobile (320-767px)**: Sidebar collapses to top, single column content
- **Tablet (768-1023px)**: Sidebar remains, 2-column content grid
- **Desktop (1024px+)**: Full three-column layout with sticky sidebar

## 2. Article Detail Page - Integrated Layout

### Layout Structure
```jsx
const ArticleDetailPage = ({ article }) => (
  // Renders within existing LayoutClient > main container
  <div className="container py-6">
    {/* Breadcrumbs */}
    <div className="mb-6">
      <Breadcrumbs items={breadcrumbItems} />
    </div>

    {/* Three-Column Layout for Article */}
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">

      {/* Left Sidebar - Same as blog landing */}
      <aside className="lg:col-span-1">
        <div className="sticky top-24 space-y-6">
          <BlogSidebar />
        </div>
      </aside>

      {/* Article Content */}
      <article className="lg:col-span-3">
        <div className="bg-white rounded-lg border border-gray-200 p-6 md:p-8">
          {/* Article Header */}
          <header className="mb-8">
            <CategoryChip category={article.category} className="mb-4" />
            <h1 className="text-3xl md:text-4xl font-bold text-rico-secondary-dark-1 mb-4">
              {article.title}
            </h1>
            {article.excerpt && (
              <p className="text-lg text-gray-600 mb-6">{article.excerpt}</p>
            )}

            <div className="flex flex-wrap items-center gap-4 mb-6">
              <AuthorBadge author={article.author} showCredentials />
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <time>{formatDate(article.publishedAt)}</time>
                <span>•</span>
                <span>{article.readTime} min read</span>
                <span>•</span>
                <span>{article.viewCount} views</span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {article.tags.map(tag => (
                  <Badge key={tag} variant="secondary">{tag}</Badge>
                ))}
              </div>
              <div className="flex items-center gap-2">
                <BookmarkButton articleId={article.id} />
                <ShareMenu
                  url={article.url}
                  title={article.title}
                  platforms={['facebook', 'twitter', 'linkedin', 'copy']}
                />
              </div>
            </div>
          </header>

          {/* Cover Image */}
          {article.coverImage && (
            <div className="mb-8">
              <Image
                src={article.coverImage}
                alt={article.title}
                width={800}
                height={450}
                className="w-full rounded-lg"
              />
            </div>
          )}

          {/* Article Content */}
          <div className="prose prose-lg max-w-none mb-8">
            <MDXContent content={article.content} />
          </div>

          {/* Product Recommendations */}
          {article.productRecommendations && (
            <section className="mt-8 p-6 bg-rico-secondary-light-4 rounded-lg">
              <h3 className="font-semibold mb-4 text-rico-secondary-dark-1">
                Scientifically Referenced Supplements
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {article.productRecommendations.map(product => (
                  <ProductInlineCard
                    key={product.id}
                    product={product}
                    context="article"
                    utmSource="blog_article"
                  />
                ))}
              </div>
            </section>
          )}

          {/* Author Bio */}
          <AuthorBioSection author={article.author} />

          {/* References */}
          {article.references && (
            <section className="mt-8 p-6 bg-gray-50 rounded-lg">
              <h3 className="font-semibold mb-4">Scientific References</h3>
              <ol className="space-y-2 text-sm">
                {article.references.map((ref, index) => (
                  <li key={index} className="text-gray-600">
                    {index + 1}. {ref}
                  </li>
                ))}
              </ol>
            </section>
          )}

          {/* Related Articles */}
          <section className="mt-12">
            <h3 className="font-semibold mb-6 text-rico-secondary-dark-1">Related Articles</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {relatedArticles.map(article => (
                <BlogCard key={article.id} article={article} variant="compact" />
              ))}
            </div>
          </section>
        </div>
      </article>
    </div>
  </div>
);
```

## 3. Component Specifications

### SidebarLink Component
```jsx
const SidebarLink = ({ href, children, active = false, count }) => (
  <Link
    href={href}
    className={`flex items-center justify-between px-3 py-2 rounded-md text-sm transition-colors ${
      active
        ? 'bg-rico-secondary-light-3 text-rico-secondary-dark-4 font-medium'
        : 'text-gray-600 hover:bg-gray-50 hover:text-rico-secondary-dark-4'
    }`}
  >
    <span>{children}</span>
    {count && (
      <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
        {count}
      </span>
    )}
  </Link>
);
```

### BlogCard Component (Integrated with existing Card patterns)
```jsx
const BlogCard = ({ article, variant = 'compact' }) => (
  <div className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
    {article.coverImage && (
      <div className="aspect-video overflow-hidden">
        <Image
          src={article.coverImage}
          alt={article.title}
          width={400}
          height={225}
          className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
        />
      </div>
    )}
    <div className="p-4">
      <CategoryChip category={article.category} size="sm" className="mb-2" />
      <h3 className="font-semibold text-rico-secondary-dark-1 mb-2 line-clamp-2 hover:text-rico-primary transition-colors">
        <Link href={`/blog/${article.category.slug}/${article.slug}`}>
          {article.title}
        </Link>
      </h3>
      <p className="text-gray-600 text-sm line-clamp-2 mb-3">{article.excerpt}</p>
      <div className="flex items-center justify-between text-xs text-gray-500">
        <AuthorBadge author={article.author} size="sm" />
        <div className="flex items-center gap-2">
          <span>{article.readTime} min read</span>
          <span>•</span>
          <span>{formatDate(article.publishedAt)}</span>
        </div>
      </div>
    </div>
  </div>
);
```

### BlogSearchBar Component
```jsx
const BlogSearchBar = () => (
  <div className="relative">
    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
    <input
      type="text"
      placeholder="Search articles, topics, or experts..."
      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rico-primary focus:border-transparent"
    />
  </div>
);
```

## 4. Required API Endpoints

### Additional Endpoints Needed
Based on the integrated layout requirements, these additional endpoints are recommended:

```php
// Add to existing blog routes in client.php

// Categories with post counts for sidebar
Route::get('/blog-categories', [\App\Http\Controllers\Client\BlogController::class, 'getCategories']);

// Popular/trending posts for sidebar and main content
Route::get('/blogs/popular', [\App\Http\Controllers\Client\BlogController::class, 'getPopular']);

// Blog statistics for navigation badges
Route::get('/blogs/stats', [\App\Http\Controllers\Client\BlogController::class, 'getStats']);
```

### Controller Methods to Add
```php
// In BlogController.php

/**
 * Get categories with article counts for sidebar navigation
 */
public function getCategories(): JsonResponse
{
    try {
        $categories = $this->service->getCategories();
        return $this->successResponse($categories, 'Categories retrieved successfully!');
    } catch (\Throwable $th) {
        return $this->errorResponse($th->getMessage(), 'Failed to retrieve categories');
    }
}

/**
 * Get popular/trending articles
 */
public function getPopular(Request $request): JsonResponse
{
    try {
        $articles = $this->service->getPopular($request);
        return $this->successResponse($articles, 'Popular articles retrieved successfully!');
    } catch (\Throwable $th) {
        return $this->errorResponse($th->getMessage(), 'Failed to retrieve popular articles');
    }
}

/**
 * Get blog statistics for navigation
 */
public function getStats(): JsonResponse
{
    try {
        $stats = $this->service->getStats();
        return $this->successResponse($stats, 'Blog stats retrieved successfully!');
    } catch (\Throwable $th) {
        return $this->errorResponse($th->getMessage(), 'Failed to retrieve blog stats');
    }
}
```

## 5. Category Archive Page - Integrated Layout

### Layout Structure
```jsx
const CategoryArchivePage = ({ category, articles }) => (
  // Renders within existing LayoutClient > main container
  <div className="container py-6">
    {/* Breadcrumbs */}
    <div className="mb-6">
      <Breadcrumbs items={breadcrumbItems} />
    </div>

    {/* Category Header */}
    <div className="mb-8">
      <h1 className="text-3xl md:text-4xl font-bold text-rico-secondary-dark-1 mb-4">
        {category.name}
      </h1>
      <p className="text-lg text-gray-600">{category.description}</p>
    </div>

    {/* Same Three-Column Layout */}
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">

      {/* Left Sidebar - Same as blog landing */}
      <aside className="lg:col-span-1">
        <div className="sticky top-24 space-y-6">
          <BlogSidebar activeCategory={category.slug} />
        </div>
      </aside>

      {/* Articles Grid */}
      <main className="lg:col-span-3">
        <div className="space-y-6">
          {/* Sort and Filter Controls */}
          <div className="flex items-center justify-between bg-white rounded-lg border border-gray-200 p-4">
            <p className="text-gray-600">{articles.length} articles found</p>
            <SortDropdown />
          </div>

          {/* Articles Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
            {articles.map(article => (
              <BlogCard
                key={article.id}
                variant="compact"
                article={article}
              />
            ))}
          </div>

          {/* Pagination */}
          <div className="flex justify-center">
            <BlogPagination
              currentPage={currentPage}
              totalPages={totalPages}
            />
          </div>
        </div>
      </main>
    </div>
  </div>
);
```

## 6. Integration Guidelines

### Existing Site CSS Classes to Reuse
```css
/* Container and Layout */
.container                    /* Main container with responsive padding */

/* Rico Theme Colors (from globals.css) */
.text-rico-primary           /* #40C4B6 */
.text-rico-secondary-dark-1  /* #082B2B */
.text-rico-secondary-dark-4  /* #196C67 */
.bg-rico-secondary-light-3   /* #D1F6EF */
.bg-rico-secondary-light-4   /* #F1FCF9 */

/* Existing Component Classes */
.rounded-lg                  /* Consistent border radius */
.border-gray-200            /* Standard border color */
.shadow-md                  /* Standard shadow */
.hover:shadow-lg            /* Hover shadow effect */
```

### Routing Integration
```jsx
// Blog routes should be added to existing app/(main) structure
app/(main)/
├── blog/
│   ├── page.js                    // Blog landing page
│   ├── [category]/
│   │   ├── page.js                // Category archive
│   │   └── [slug]/page.js         // Article detail
│   └── search/page.js             // Search results

// URL structure examples:
// /blog                           // Blog landing
// /blog/wellness                  // Wellness category
// /blog/wellness/vitamin-d-guide  // Article detail
// /blog/search?q=protein          // Search results
```

### Component Integration Patterns
```jsx
// Use existing site patterns for consistency

// 1. Use existing Card component as base
import { Card, CardContent, CardHeader } from '@/components/ui/card';

// 2. Follow existing grid patterns
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-3 sm:gap-4">

// 3. Use existing Button component
import { Button } from '@/components/ui/button';

// 4. Follow existing loading states
import { Skeleton } from '@/components/ui/skeleton';
```

## 7. Responsive Design Specifications

### Breakpoint Behavior
```css
/* Mobile First Approach - Following existing site patterns */

/* Mobile (320-767px) */
.blog-mobile {
  @apply grid-cols-1 px-4;
}

/* Tablet (768-1023px) */
@media (min-width: 768px) {
  .blog-tablet {
    @apply grid-cols-2 px-6;
  }

  /* Sidebar remains visible */
  .blog-sidebar {
    @apply block;
  }
}

/* Desktop (1024px+) */
@media (min-width: 1024px) {
  .blog-desktop {
    @apply grid-cols-3 px-8;
  }

  /* Full three-column layout */
  .blog-layout {
    @apply lg:grid-cols-4;
  }

  /* Sticky sidebar */
  .blog-sidebar {
    @apply sticky top-24;
  }
}
```

### Mobile Optimizations
```jsx
// Mobile sidebar behavior
const BlogSidebar = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      {/* Mobile toggle button */}
      <div className="lg:hidden mb-4">
        <Button
          variant="outline"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full"
        >
          <Menu className="w-4 h-4 mr-2" />
          Categories & Filters
        </Button>
      </div>

      {/* Sidebar content */}
      <div className={`${isOpen ? 'block' : 'hidden'} lg:block`}>
        <SidebarContent />
      </div>
    </>
  );
};
```

## 8. Implementation Summary

### Key Integration Points

1. **Preserve Existing Site Structure**
   - All blog content renders within existing `<LayoutClient>` wrapper
   - Header and footer remain completely unchanged
   - Use existing `container` class for all content

2. **Three-Column Layout Pattern**
   - **Left Sidebar**: Categories, content types, featured experts (sticky)
   - **Main Content**: Blog posts in responsive grid (2-3 columns)
   - **Consistent across all blog pages**: landing, category, article detail

3. **Existing Design System Integration**
   - Use Rico theme colors from `globals.css`
   - Follow existing card and grid patterns
   - Maintain current responsive breakpoints
   - Reuse existing Button, Card, and other UI components

4. **Required API Endpoints**
   ```php
   // Additional endpoints needed beyond existing blog API:
   GET /blog-categories     // Categories with post counts
   GET /blogs/popular      // Popular/trending posts
   GET /blogs/stats        // Blog statistics for navigation
   ```

5. **Routing Structure**
   ```
   /blog                    // Blog landing (three-column layout)
   /blog/[category]         // Category archive (same layout)
   /blog/[category]/[slug]  // Article detail (integrated layout)
   /blog/search            // Search results (same layout)
   ```

### Mobile Responsiveness
- **Mobile**: Sidebar collapses to toggle menu, single column content
- **Tablet**: Sidebar visible, 2-column content grid
- **Desktop**: Full three-column layout with sticky sidebar

This integrated approach maintains the existing site's design consistency while providing a comprehensive blog experience that feels native to the current platform.


