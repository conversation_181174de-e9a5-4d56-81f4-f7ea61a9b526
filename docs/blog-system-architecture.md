# Vitamins.ae Blog System Architecture Plan

## Overview
The Vitamins.ae Wellness Hub is a comprehensive blog system designed to integrate seamlessly with the existing e-commerce platform. It serves as a content-driven traffic generator while maintaining editorial integrity and providing contextual product recommendations.

## Current Codebase Analysis

### Existing Design System
- **Color Palette**: Rico theme with primary teal (#40C4B6), secondary dark shades, and light variants
- **Typography**: Modern sans-serif with clear hierarchy
- **Component Architecture**: Radix UI + Tailwind CSS with shadcn/ui patterns
- **Layout System**: Container-based with responsive breakpoints
- **State Management**: Zustand for cart, auth, and language state

### Key Patterns Identified
1. **Card Components**: Consistent rounded corners, shadow elevation, hover effects
2. **Button Variants**: Primary, secondary, ghost, outline with Rico theme colors
3. **Navigation**: Sticky header with mega menu, mobile-responsive
4. **Product Cards**: Standardized layout with image, title, price, CTA
5. **Responsive Design**: Mobile-first approach with container system

## Blog System Architecture

### Route Structure
```
/blog/                          # Wellness Hub landing page
/blog/[category]/               # Category archive pages
/blog/[category]/[article-slug] # Individual article pages
/blog/experts/                  # Expert directory
/blog/experts/[expert-slug]     # Expert profile pages
/blog/search                    # Search results page
/blog/tags/[tag-slug]          # Tag archive pages
/blog/videos/                   # Video content hub
```

### Integration Points

#### 1. Header Navigation
- Add "Blog" link to existing navigation bar
- Integrate blog search with global search functionality
- Maintain existing user authentication state

#### 2. Product Integration
- Contextual product recommendations within articles
- UTM tracking from blog to product pages
- Shopping cart state preservation across blog navigation

#### 3. User Experience
- Consistent styling with existing e-commerce pages
- Shared components (header, footer, cart, user menu)
- Unified authentication and user preferences

## Technical Implementation

### Framework Alignment
- **Next.js App Router**: Following existing pattern in `/app/(main)/`
- **Tailwind CSS**: Extend existing theme configuration
- **Component Library**: Build on existing shadcn/ui components
- **State Management**: Integrate with existing Zustand stores

### File Structure
```
customer/
├── app/(main)/blog/
│   ├── layout.js                   # Blog-specific layout
│   ├── page.js                     # Blog landing page
│   ├── [category]/
│   │   ├── page.js                 # Category archive
│   │   └── [slug]/page.js          # Article detail
│   ├── experts/
│   │   ├── page.js                 # Expert directory
│   │   └── [slug]/page.js          # Expert profile
│   ├── search/page.js              # Search results
│   ├── tags/[slug]/page.js         # Tag archive
│   └── videos/page.js              # Video hub
├── components/blog/
│   ├── layout/                     # Blog-specific layouts
│   ├── article/                    # Article components
│   ├── navigation/                 # Blog navigation
│   ├── cards/                      # Content cards
│   └── widgets/                    # Sidebar widgets
└── hooks/
    ├── useArticles.js              # Article data fetching
    ├── useExperts.js               # Expert data fetching
    └── useBlogSearch.js            # Blog search functionality
```

### Data Flow
1. **Content Management**: Headless CMS or API-driven content
2. **Static Generation**: ISR for articles with 60-300 second revalidation
3. **Dynamic Routes**: Category and tag-based routing
4. **Search Integration**: Unified search across products and content

## Performance Considerations

### Optimization Strategy
- **Image Optimization**: Next.js Image component with AVIF/WebP
- **Code Splitting**: Route-based splitting for blog components
- **Caching**: ISR for content, client-side caching for navigation
- **Bundle Size**: Target ≤120KB gzipped for article pages

### Core Web Vitals Targets
- **LCP**: <2.5 seconds on mid-tier 4G
- **CLS**: <0.1
- **FID**: <100ms

## Accessibility Standards
- **WCAG 2.2 AA Compliance**: Following existing patterns
- **Semantic HTML**: Proper heading hierarchy and landmarks
- **Focus Management**: Keyboard navigation support
- **Screen Reader**: ARIA labels and descriptions

## SEO Implementation
- **Metadata API**: Next.js metadata for each route
- **Structured Data**: BlogPosting, Person, and Breadcrumb schemas
- **URL Structure**: SEO-friendly slugs and hierarchy
- **Sitemap**: Dynamic XML sitemap generation

## Content Strategy Integration
- **Editorial Calendar**: Content scheduling and publishing workflow
- **Author Management**: Expert profiles and content attribution
- **Category Taxonomy**: Wellness-focused content organization
- **Product Recommendations**: Contextual and non-intrusive placement

## Analytics and Testing
- **Event Tracking**: Reading depth, engagement metrics, CTA clicks
- **A/B Testing**: Layout variations and content optimization
- **Performance Monitoring**: Core Web Vitals and user experience metrics

## Security and Compliance
- **Content Security**: XSS prevention and input sanitization
- **Privacy**: GDPR compliance for analytics and user data
- **Medical Disclaimers**: Required legal disclaimers for health content

## Internationalization Preparation
- **i18n Infrastructure**: Extend existing language support
- **RTL Support**: Arabic language consideration
- **Content Localization**: Multi-language content management

## Next Steps
1. Component library specification
2. Page layout designs
3. Content model definition
4. Implementation timeline
5. Testing strategy
