# Vitamins.ae Blog Content Management Model

## Data Models

### 1. Article Model
```typescript
interface Article {
  id: string;
  uuid: string;
  slug: string;
  title: string;
  excerpt: string;
  content: string; // MDX content
  coverImage?: {
    url: string;
    alt: string;
    caption?: string;
    credit?: string;
  };
  
  // Metadata
  status: 'draft' | 'published' | 'archived';
  publishedAt?: Date;
  lastUpdated: Date;
  createdAt: Date;
  
  // Content Classification
  category: Category;
  tags: Tag[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  contentType: 'article' | 'guide' | 'research' | 'opinion';
  
  // Author Information
  author: Author;
  reviewedBy?: Author[];
  
  // Engagement Metrics
  readTime: number; // in minutes
  viewCount: number;
  likeCount: number;
  shareCount: number;
  bookmarkCount: number;
  
  // SEO
  metaTitle?: string;
  metaDescription?: string;
  canonicalUrl?: string;
  openGraphImage?: string;
  
  // Content Features
  badges: Badge[];
  isFeatured: boolean;
  isEditorsPick: boolean;
  isTrending: boolean;
  
  // Product Integration
  productRecommendations: ProductRecommendation[];
  affiliateDisclosure: boolean;
  
  // Scientific Content
  references: Reference[];
  medicalDisclaimer: boolean;
  lastMedicalReview?: Date;
  
  // Related Content
  relatedArticles: string[]; // Article IDs
  relatedExperts: string[]; // Expert IDs
  
  // Internationalization
  language: 'en' | 'ar';
  translations?: {
    [key: string]: string; // language code -> article ID
  };
}
```

### 2. Author/Expert Model
```typescript
interface Author {
  id: string;
  uuid: string;
  slug: string;
  name: string;
  bio: string;
  avatar: string;
  
  // Professional Information
  credentials: Credential[];
  specialties: Specialty[];
  yearsExperience: number;
  education: Education[];
  certifications: Certification[];
  
  // Contact & Social
  email?: string;
  website?: string;
  socialLinks: SocialLink[];
  
  // Content Stats
  articleCount: number;
  totalViews: number;
  followerCount: number;
  
  // Verification
  isVerified: boolean;
  verificationDate?: Date;
  
  // SEO
  metaTitle?: string;
  metaDescription?: string;
  
  // Settings
  isActive: boolean;
  allowComments: boolean;
  newsletterEnabled: boolean;
  
  createdAt: Date;
  updatedAt: Date;
}

interface Credential {
  type: 'RDN' | 'MD' | 'ND' | 'PhD' | 'MS' | 'BS' | 'CPT' | 'Other';
  title: string;
  institution: string;
  year: number;
  isVerified: boolean;
}

interface Specialty {
  id: string;
  name: string;
  category: 'nutrition' | 'fitness' | 'wellness' | 'medical' | 'beauty';
}
```

### 3. Category Model
```typescript
interface Category {
  id: string;
  slug: string;
  name: string;
  description: string;
  coverImage?: string;
  icon?: string;
  color?: string;
  
  // Hierarchy
  parentId?: string;
  children?: Category[];
  level: number;
  
  // Content
  articleCount: number;
  isActive: boolean;
  
  // SEO
  metaTitle?: string;
  metaDescription?: string;
  
  // Display
  displayOrder: number;
  showInNavigation: boolean;
  
  createdAt: Date;
  updatedAt: Date;
}
```

### 4. Tag Model
```typescript
interface Tag {
  id: string;
  slug: string;
  name: string;
  description?: string;
  color?: string;
  
  // Usage Stats
  articleCount: number;
  
  // Related
  relatedTags: string[]; // Tag IDs
  
  // SEO
  metaTitle?: string;
  metaDescription?: string;
  
  createdAt: Date;
  updatedAt: Date;
}
```

### 5. Product Recommendation Model
```typescript
interface ProductRecommendation {
  id: string;
  productId: string;
  position: number; // Order in article
  context: 'inline' | 'sidebar' | 'footer';
  reasoning?: string; // Why this product is recommended
  
  // Tracking
  utmSource: string;
  utmMedium: string;
  utmCampaign: string;
  
  // Display
  customTitle?: string;
  customDescription?: string;
  
  createdAt: Date;
}
```

### 6. Reference Model
```typescript
interface Reference {
  id: string;
  title: string;
  authors: string[];
  publication: string;
  year: number;
  url?: string;
  doi?: string;
  pmid?: string; // PubMed ID
  type: 'journal' | 'book' | 'website' | 'study' | 'report';
  
  // Quality Indicators
  peerReviewed: boolean;
  impactFactor?: number;
  
  createdAt: Date;
}
```

### 7. Video Content Model
```typescript
interface VideoContent {
  id: string;
  slug: string;
  title: string;
  description: string;
  
  // Video Details
  videoUrl: string;
  thumbnailUrl: string;
  duration: number; // in seconds
  quality: '720p' | '1080p' | '4K';
  
  // Content
  transcript?: string;
  chapters: VideoChapter[];
  
  // Same metadata as Article
  author: Author;
  category: Category;
  tags: Tag[];
  publishedAt: Date;
  viewCount: number;
  
  // Video-specific
  playCount: number;
  averageWatchTime: number;
  
  createdAt: Date;
  updatedAt: Date;
}

interface VideoChapter {
  title: string;
  startTime: number; // in seconds
  endTime: number;
}
```

## API Endpoints

### Article Endpoints
```typescript
// Get articles with filtering and pagination
GET /api/blog/articles
Query Parameters:
- category?: string
- tag?: string
- author?: string
- status?: 'published' | 'draft'
- featured?: boolean
- limit?: number (default: 20)
- offset?: number
- sort?: 'newest' | 'popular' | 'trending' | 'read-time'

// Get single article
GET /api/blog/articles/:slug
Includes: author, category, tags, related articles, product recommendations

// Get article analytics
GET /api/blog/articles/:id/analytics
Returns: views, engagement metrics, referrer data

// Search articles
GET /api/blog/search
Query Parameters:
- q: string (search query)
- type?: 'article' | 'expert' | 'video'
- category?: string
- limit?: number
```

### Author/Expert Endpoints
```typescript
// Get experts
GET /api/blog/experts
Query Parameters:
- specialty?: string
- credential?: string
- limit?: number
- sort?: 'name' | 'articles' | 'followers'

// Get expert profile
GET /api/blog/experts/:slug
Includes: articles, specialties, credentials, social links

// Get expert articles
GET /api/blog/experts/:id/articles
Query Parameters:
- limit?: number
- offset?: number
```

### Category & Tag Endpoints
```typescript
// Get categories
GET /api/blog/categories
Returns: hierarchical category tree with article counts

// Get category articles
GET /api/blog/categories/:slug/articles
Query Parameters: same as article filtering

// Get tags
GET /api/blog/tags
Query Parameters:
- popular?: boolean (most used tags)
- limit?: number

// Get tag articles
GET /api/blog/tags/:slug/articles
```

### Analytics Endpoints
```typescript
// Track article view
POST /api/blog/analytics/view
Body: { articleId: string, referrer?: string }

// Track engagement
POST /api/blog/analytics/engagement
Body: {
  articleId: string,
  action: 'like' | 'share' | 'bookmark',
  platform?: string
}

// Get popular content
GET /api/blog/analytics/popular
Query Parameters:
- timeframe?: 'day' | 'week' | 'month'
- type?: 'article' | 'video'
- limit?: number
```

## Content Workflow

### Editorial Process
```typescript
interface ContentWorkflow {
  stages: [
    'draft',           // Author writing
    'review',          // Editorial review
    'medical_review',  // Medical fact-checking (if applicable)
    'seo_review',      // SEO optimization
    'approved',        // Ready for publishing
    'published',       // Live on site
    'archived'         // No longer active
  ];
  
  permissions: {
    author: ['create', 'edit_own', 'submit_for_review'];
    editor: ['edit_any', 'approve', 'publish', 'archive'];
    medical_reviewer: ['medical_review', 'flag_concerns'];
    admin: ['all_permissions'];
  };
}
```

### Content Scheduling
```typescript
interface PublishingSchedule {
  articleId: string;
  scheduledDate: Date;
  timezone: string;
  autoPublish: boolean;
  socialMediaPosts: SocialMediaPost[];
  emailNewsletter: boolean;
}
```

## Database Schema (PostgreSQL)

### Core Tables
```sql
-- Articles table
CREATE TABLE articles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  slug VARCHAR(255) UNIQUE NOT NULL,
  title VARCHAR(500) NOT NULL,
  excerpt TEXT,
  content TEXT, -- MDX content
  cover_image JSONB,
  status VARCHAR(20) DEFAULT 'draft',
  published_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  author_id UUID REFERENCES authors(id),
  category_id UUID REFERENCES categories(id),
  read_time INTEGER,
  view_count INTEGER DEFAULT 0,
  like_count INTEGER DEFAULT 0,
  share_count INTEGER DEFAULT 0,
  bookmark_count INTEGER DEFAULT 0,
  is_featured BOOLEAN DEFAULT FALSE,
  is_editors_pick BOOLEAN DEFAULT FALSE,
  meta_title VARCHAR(255),
  meta_description TEXT,
  canonical_url VARCHAR(500),
  language VARCHAR(5) DEFAULT 'en'
);

-- Authors table
CREATE TABLE authors (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  slug VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  bio TEXT,
  avatar VARCHAR(500),
  email VARCHAR(255),
  website VARCHAR(500),
  is_verified BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  article_count INTEGER DEFAULT 0,
  total_views INTEGER DEFAULT 0,
  follower_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Categories table
CREATE TABLE categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  slug VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  cover_image VARCHAR(500),
  parent_id UUID REFERENCES categories(id),
  level INTEGER DEFAULT 0,
  display_order INTEGER DEFAULT 0,
  article_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  show_in_navigation BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Tags table
CREATE TABLE tags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  slug VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  color VARCHAR(7), -- Hex color
  article_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Junction tables
CREATE TABLE article_tags (
  article_id UUID REFERENCES articles(id) ON DELETE CASCADE,
  tag_id UUID REFERENCES tags(id) ON DELETE CASCADE,
  PRIMARY KEY (article_id, tag_id)
);

CREATE TABLE article_product_recommendations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  article_id UUID REFERENCES articles(id) ON DELETE CASCADE,
  product_id VARCHAR(255) NOT NULL,
  position INTEGER,
  context VARCHAR(20),
  reasoning TEXT,
  utm_source VARCHAR(100),
  utm_medium VARCHAR(100),
  utm_campaign VARCHAR(100),
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE article_references (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  article_id UUID REFERENCES articles(id) ON DELETE CASCADE,
  title VARCHAR(500) NOT NULL,
  authors TEXT[],
  publication VARCHAR(255),
  year INTEGER,
  url VARCHAR(500),
  doi VARCHAR(100),
  pmid VARCHAR(20),
  type VARCHAR(20),
  peer_reviewed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### Indexes for Performance
```sql
-- Article indexes
CREATE INDEX idx_articles_status_published ON articles(status, published_at DESC);
CREATE INDEX idx_articles_category ON articles(category_id);
CREATE INDEX idx_articles_author ON articles(author_id);
CREATE INDEX idx_articles_featured ON articles(is_featured, published_at DESC);
CREATE INDEX idx_articles_slug ON articles(slug);

-- Search indexes
CREATE INDEX idx_articles_search ON articles USING gin(to_tsvector('english', title || ' ' || excerpt));

-- Analytics indexes
CREATE INDEX idx_articles_views ON articles(view_count DESC);
CREATE INDEX idx_articles_engagement ON articles((like_count + share_count + bookmark_count) DESC);
```

## Content Migration Strategy

### Existing Content Integration
```typescript
interface MigrationPlan {
  phases: [
    'content_audit',      // Inventory existing content
    'taxonomy_mapping',   // Map to new category structure
    'content_migration',  // Move content to new system
    'url_redirects',      // Maintain SEO value
    'testing',           // Validate migration
    'go_live'            // Switch to new system
  ];
  
  redirects: {
    '/wellness/*': '/blog/*';
    '/health-tips/*': '/blog/wellness/*';
    '/nutrition/*': '/blog/nutrition/*';
  };
}
```

## Performance Considerations

### Caching Strategy
```typescript
interface CacheStrategy {
  articles: {
    ttl: 300; // 5 minutes for published articles
    tags: ['article', 'category', 'author'];
  };
  
  categories: {
    ttl: 3600; // 1 hour for category data
    tags: ['category'];
  };
  
  popular_content: {
    ttl: 900; // 15 minutes for trending/popular
    tags: ['analytics', 'popular'];
  };
}
```

### Search Implementation
```typescript
interface SearchConfig {
  engine: 'postgresql_fts' | 'elasticsearch' | 'algolia';
  
  indexedFields: [
    'title',
    'excerpt', 
    'content',
    'author.name',
    'category.name',
    'tags.name'
  ];
  
  features: [
    'autocomplete',
    'fuzzy_matching',
    'faceted_search',
    'result_highlighting'
  ];
}
```
