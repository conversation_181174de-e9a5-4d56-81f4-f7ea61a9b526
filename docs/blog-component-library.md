# Vitamins.ae Blog Component Library Specification

## Design System Foundation

### Color Palette (Extending Existing Rico Theme)
```css
/* Primary Colors */
--color-rico-primary: #40C4B6;
--color-rico-secondary-dark-4: #196C67;
--color-rico-secondary-light-3: #D1F6EF;

/* Blog-Specific Extensions */
--color-blog-accent: #F1B0C4;
--color-blog-success: #11A701;
--color-blog-warning: #FF8C00;
--color-blog-error: #D0005D;
--color-blog-neutral: #525252;
```

### Typography Scale
```css
/* Heading Hierarchy */
.blog-h1 { @apply text-3xl md:text-4xl lg:text-5xl font-bold leading-tight; }
.blog-h2 { @apply text-2xl md:text-3xl font-semibold leading-tight; }
.blog-h3 { @apply text-xl md:text-2xl font-semibold leading-snug; }
.blog-h4 { @apply text-lg md:text-xl font-medium leading-snug; }

/* Body Text */
.blog-lead { @apply text-lg md:text-xl leading-relaxed text-gray-700; }
.blog-body { @apply text-base leading-relaxed text-gray-800; }
.blog-caption { @apply text-sm text-gray-600; }
.blog-meta { @apply text-xs text-gray-500; }
```

### Spacing Scale
```css
/* Blog-Specific Spacing */
.blog-section-gap { @apply space-y-8 md:space-y-12; }
.blog-content-gap { @apply space-y-6; }
.blog-element-gap { @apply space-y-4; }
```

## Core Components

### 1. BlogCard Component
```typescript
interface BlogCardProps {
  variant: 'default' | 'compact' | 'wide' | 'featured';
  article: {
    id: string;
    slug: string;
    title: string;
    excerpt: string;
    coverImage: string;
    category: string;
    author: Author;
    publishedAt: string;
    readTime: number;
    viewCount: number;
    badges?: string[];
  };
  showBookmark?: boolean;
  showShare?: boolean;
}
```

**Styling Classes:**
```css
.blog-card {
  @apply bg-white rounded-2xl border border-gray-200 overflow-hidden 
         hover:shadow-lg hover:scale-[1.01] transition-all duration-300;
}

.blog-card-image {
  @apply aspect-video w-full object-cover;
}

.blog-card-content {
  @apply p-4 md:p-6;
}

.blog-card-title {
  @apply font-semibold text-gray-900 line-clamp-2 hover:text-rico-primary 
         transition-colors;
}

.blog-card-excerpt {
  @apply text-gray-600 line-clamp-2 text-sm md:text-base;
}
```

### 2. AuthorBadge Component
```typescript
interface AuthorBadgeProps {
  author: {
    name: string;
    avatar: string;
    credentials: string[];
    slug: string;
  };
  showCredentials?: boolean;
  size: 'sm' | 'md' | 'lg';
}
```

**Styling Classes:**
```css
.author-badge {
  @apply flex items-center gap-3;
}

.author-avatar {
  @apply rounded-full border-2 border-white shadow-sm;
}

.author-avatar-sm { @apply w-8 h-8; }
.author-avatar-md { @apply w-10 h-10; }
.author-avatar-lg { @apply w-12 h-12; }

.author-credentials {
  @apply text-xs text-rico-secondary-dark-4 font-medium;
}
```

### 3. CategoryChip Component
```typescript
interface CategoryChipProps {
  category: string;
  variant: 'default' | 'outline' | 'subtle';
  size: 'sm' | 'md';
  href?: string;
}
```

**Styling Classes:**
```css
.category-chip {
  @apply inline-flex items-center rounded-full font-medium transition-colors;
}

.category-chip-default {
  @apply bg-rico-secondary-light-3 text-rico-secondary-dark-4 
         hover:bg-rico-secondary-light-2;
}

.category-chip-outline {
  @apply border border-rico-secondary-dark-4 text-rico-secondary-dark-4 
         hover:bg-rico-secondary-light-3;
}

.category-chip-sm { @apply px-3 py-1 text-xs; }
.category-chip-md { @apply px-4 py-2 text-sm; }
```

### 4. TableOfContents Component
```typescript
interface TableOfContentsProps {
  headings: {
    id: string;
    text: string;
    level: number;
  }[];
  activeId?: string;
  className?: string;
}
```

**Styling Classes:**
```css
.toc-container {
  @apply sticky top-24 bg-white rounded-2xl border border-gray-200 p-6 
         max-h-[calc(100vh-8rem)] overflow-y-auto;
}

.toc-heading {
  @apply font-semibold text-gray-900 mb-4 text-sm uppercase tracking-wide;
}

.toc-list {
  @apply space-y-2;
}

.toc-item {
  @apply text-sm text-gray-600 hover:text-rico-primary transition-colors 
         cursor-pointer border-l-2 border-transparent hover:border-rico-primary 
         pl-3 py-1;
}

.toc-item-active {
  @apply text-rico-primary border-rico-primary font-medium;
}

.toc-item-level-2 { @apply ml-4; }
.toc-item-level-3 { @apply ml-8; }
```

### 5. ProductInlineCard Component
```typescript
interface ProductInlineCardProps {
  product: {
    id: string;
    name: string;
    price: number;
    originalPrice?: number;
    image: string;
    rating: number;
    slug: string;
  };
  context: 'article' | 'sidebar';
  utmSource: string;
}
```

**Styling Classes:**
```css
.product-inline-card {
  @apply bg-gray-50 rounded-xl border border-gray-200 p-4 
         hover:shadow-md transition-all duration-300;
}

.product-inline-image {
  @apply w-16 h-16 rounded-lg object-cover;
}

.product-inline-content {
  @apply flex-1 min-w-0;
}

.product-inline-title {
  @apply font-medium text-gray-900 line-clamp-2 text-sm;
}

.product-inline-price {
  @apply font-bold text-rico-secondary-dark-4;
}

.product-inline-cta {
  @apply bg-rico-secondary-light-3 text-rico-secondary-dark-4 
         hover:bg-rico-secondary-light-2 px-3 py-1.5 rounded-lg text-xs 
         font-medium transition-colors;
}
```

### 6. ShareMenu Component
```typescript
interface ShareMenuProps {
  url: string;
  title: string;
  description?: string;
  platforms: ('facebook' | 'twitter' | 'linkedin' | 'whatsapp' | 'copy')[];
}
```

### 7. BookmarkButton Component
```typescript
interface BookmarkButtonProps {
  articleId: string;
  isBookmarked?: boolean;
  onToggle?: (bookmarked: boolean) => void;
  size: 'sm' | 'md';
}
```

### 8. ReadingProgress Component
```typescript
interface ReadingProgressProps {
  target: string; // CSS selector for content container
  className?: string;
}
```

**Styling Classes:**
```css
.reading-progress {
  @apply fixed top-0 left-0 h-1 bg-rico-primary z-50 transition-all duration-150;
}
```

### 9. NewsletterCTA Component
```typescript
interface NewsletterCTAProps {
  variant: 'inline' | 'sidebar' | 'modal';
  title?: string;
  description?: string;
  placeholder?: string;
}
```

**Styling Classes:**
```css
.newsletter-cta {
  @apply bg-gradient-to-r from-rico-secondary-light-4 to-rico-secondary-light-3 
         rounded-2xl p-6 md:p-8;
}

.newsletter-input {
  @apply w-full px-4 py-3 rounded-lg border border-gray-300 
         focus:border-rico-primary focus:ring-2 focus:ring-rico-primary/20 
         focus:outline-none;
}

.newsletter-button {
  @apply bg-rico-secondary-dark-4 text-white px-6 py-3 rounded-lg 
         hover:bg-rico-secondary-dark-3 transition-colors font-medium;
}
```

### 10. Disclaimer Component
```typescript
interface DisclaimerProps {
  type: 'medical' | 'affiliate' | 'general';
  content?: string;
  className?: string;
}
```

**Styling Classes:**
```css
.disclaimer {
  @apply bg-yellow-50 border border-yellow-200 rounded-xl p-4 text-sm 
         text-yellow-800;
}

.disclaimer-icon {
  @apply text-yellow-600 flex-shrink-0;
}
```

## Layout Components

### 1. BlogLayout Component
```typescript
interface BlogLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  breadcrumbs?: BreadcrumbItem[];
  showTableOfContents?: boolean;
}
```

### 2. ArticleHeader Component
```typescript
interface ArticleHeaderProps {
  title: string;
  excerpt?: string;
  author: Author;
  publishedAt: string;
  lastUpdated?: string;
  readTime: number;
  category: string;
  tags: string[];
  coverImage?: string;
}
```

### 3. RelatedArticles Component
```typescript
interface RelatedArticlesProps {
  articles: Article[];
  title?: string;
  maxItems?: number;
  layout: 'grid' | 'carousel';
}
```

## Animation and Interaction Patterns

### Micro-interactions
```css
/* Hover Effects */
.blog-hover-lift {
  @apply hover:scale-[1.01] transition-transform duration-300;
}

.blog-hover-shadow {
  @apply hover:shadow-lg transition-shadow duration-300;
}

/* Loading States */
.blog-skeleton {
  @apply animate-pulse bg-gray-200 rounded;
}

/* Focus States */
.blog-focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-rico-primary/50 
         focus:ring-offset-2;
}
```

### Stagger Animations
```css
.blog-stagger-children > * {
  animation: slideUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

.blog-stagger-children > *:nth-child(1) { animation-delay: 0ms; }
.blog-stagger-children > *:nth-child(2) { animation-delay: 100ms; }
.blog-stagger-children > *:nth-child(3) { animation-delay: 200ms; }
```

## Responsive Design Patterns

### Breakpoint System
```css
/* Following existing Tailwind breakpoints */
/* sm: 640px, md: 768px, lg: 1024px, xl: 1280px, 2xl: 1536px */

.blog-grid-responsive {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
}

.blog-sidebar-layout {
  @apply grid grid-cols-1 lg:grid-cols-3 gap-8;
}

.blog-sidebar-content {
  @apply lg:col-span-2;
}

.blog-sidebar-widgets {
  @apply lg:col-span-1 space-y-6;
}
```

### Mobile Optimizations
```css
.blog-mobile-stack {
  @apply flex flex-col md:flex-row md:items-center gap-4;
}

.blog-mobile-scroll {
  @apply flex overflow-x-auto snap-x snap-mandatory gap-4 pb-4 
         scrollbar-hide md:grid md:grid-cols-3 md:overflow-visible;
}

.blog-mobile-scroll > * {
  @apply flex-shrink-0 snap-start w-80 md:w-auto;
}
```

## Accessibility Features

### Screen Reader Support
```css
.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap 
         border-0;
}

.blog-skip-link {
  @apply absolute top-0 left-0 bg-rico-primary text-white px-4 py-2 
         transform -translate-y-full focus:translate-y-0 transition-transform 
         z-50;
}
```

### Focus Management
```css
.blog-focus-trap {
  @apply focus:outline-none focus:ring-2 focus:ring-rico-primary 
         focus:ring-offset-2 rounded;
}
```

## Dark Mode Support

### Color Overrides
```css
.dark .blog-card {
  @apply bg-gray-800 border-gray-700 text-white;
}

.dark .blog-card-title {
  @apply text-gray-100 hover:text-rico-secondary-light-1;
}

.dark .blog-card-excerpt {
  @apply text-gray-300;
}
```

## Performance Optimizations

### Image Loading
```typescript
// Optimized image component usage
<Image
  src={coverImage}
  alt={title}
  width={800}
  height={450}
  className="blog-card-image"
  loading="lazy"
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>
```

### Code Splitting
```typescript
// Lazy load heavy components
const VideoPlayer = dynamic(() => import('./VideoPlayer'), {
  loading: () => <div className="blog-skeleton h-64" />,
  ssr: false
});
```

## Component Usage Examples

### Basic Article Card
```jsx
<BlogCard
  variant="default"
  article={article}
  showBookmark={true}
  showShare={true}
/>
```

### Featured Article Layout
```jsx
<BlogCard
  variant="featured"
  article={featuredArticle}
  className="col-span-full"
/>
```

### Sidebar Product Recommendation
```jsx
<ProductInlineCard
  product={recommendedProduct}
  context="sidebar"
  utmSource="blog_sidebar"
/>
```
