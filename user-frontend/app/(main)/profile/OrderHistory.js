'use client';

import { useState } from 'react';
import { Search } from 'lucide-react';
import DirhamSymbol from '@/components/ui/dirham-symbol';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

export default function OrderHistory() {
  const [orders, setOrders] = useState([
    {
      id: 1,
      orderId: 'ord123456789',
      totalPrice: 480,
      status: 'Pending'
    },
    {
      id: 2,
      orderId: 'ord123456789',
      totalPrice: 480,
      status: 'Completed'
    },
    {
      id: 3,
      orderId: 'ord123456789',
      totalPrice: 480,
      status: 'Cancelled'
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOrders, setSelectedOrders] = useState([]);

  const filteredOrders = orders.filter(order =>
    order.orderId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectOrder = (orderId) => {
    if (selectedOrders.includes(orderId)) {
      setSelectedOrders(selectedOrders.filter(id => id !== orderId));
    } else {
      setSelectedOrders([...selectedOrders, orderId]);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800 border-[#fec831]';
      case 'Completed':
        return 'bg-[#e2fff9] text-green-800 border-[#41c4b6]';
      case 'Cancelled':
        return 'bg-[#ffebf1] text-red-800 border-[#f1b0c4]';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center w-full">
          <h2 className="text-xl font-semibold text-[#196c67] mr-4">Order History</h2>
          <div className="flex-1 h-px bg-gray-300"></div>
        </div>
        <div className="relative ml-4">
          <input
            type="text"
            placeholder="Search"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-4 pr-10 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-teal-500 min-w-[200px]"
          />
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#196c67]" />
        </div>
      </div>

      {/* Orders Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <Table>
          <TableHeader className="bg-[#f1fcf9]">
            <TableRow>
              <TableHead>Order ID</TableHead>
              <TableHead>Total Price</TableHead>
              <TableHead className="text-right">Order Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredOrders.map((order) => (
              <TableRow key={order.id}>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={selectedOrders.includes(order.id)}
                      onChange={() => handleSelectOrder(order.id)}
                      className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                    />
                    <span className="text-sm text-gray-800 font-medium">
                      {order.orderId}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <span className="text-sm text-gray-800 font-medium flex items-center">
                    <DirhamSymbol className="w-4 h-4 mr-1" />
                    {order.totalPrice}
                  </span>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end space-x-3">
                    <button className="text-sm text-gray-600 border border-[#5d5d5d] px-3 py-1 rounded-full bg-[#f1fcf9] transition-colors">
                      Order Again
                    </button>
                    <span className={`text-xs px-3 py-1 rounded-full border ${getStatusColor(order.status)}`}>
                      {order.status}
                    </span>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </>
  );
}

