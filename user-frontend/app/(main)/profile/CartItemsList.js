'use client';

import Image from 'next/image';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Heart, Trash2 } from 'lucide-react';
import DirhamSymbol from '@/components/ui/dirham-symbol';

const CartItemsList = ({ 
  cartItems = [], 
  selectedItems = [], 
  handleSelectItem, 
  handleQuantityChange, 
  handleRemoveItem 
}) => {
  const groupedByVendor = cartItems.reduce((acc, item) => {
    const vendor = item.vendor || 'Default Vendor';
    if (!acc[vendor]) acc[vendor] = [];
    acc[vendor].push(item);
    return acc;
  }, {});

  return (
    <div className="space-y-4">
      {Object.entries(groupedByVendor).map(([vendor, items]) => (
        <div key={vendor} className="bg-white rounded-lg border">
          {/* Vendor Header */}
          <div className="flex items-center p-3 md:p-4 bg-[#f4f4f4] rounded-t-lg border-b">
            <Checkbox />
            <span className="ml-3 font-medium text-gray-800 text-sm md:text-base">{vendor}</span>
          </div>

          {/* Vendor Items */}
          <div className="divide-y">
            {items.map((item) => (
              <div key={item.id} className="p-3 md:p-4 bg-[#f8fffd]">
                {/* Mobile Layout */}
                <div className="block md:hidden">
                  <div className="flex items-start space-x-3">
                    <Checkbox 
                      checked={selectedItems.includes(item.id)}
                      onCheckedChange={() => handleSelectItem(item.id)}
                      className="mt-1"
                    />
                    
                    <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        src={item.image}
                        alt={item.name}
                        width={64}
                        height={64}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-medium text-gray-800 mb-1">{item.name}</h3>
                      <div className="flex items-center mb-2">
                        <DirhamSymbol className="w-3 h-3 mr-1" />
                        <span className="text-sm text-gray-600">{item.price}</span>
                      </div>
                      
                      {/* Price and Sale Info */}
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="icon" className="w-8 h-8 bg-gray-100 hover:bg-red-50 rounded-lg">
                            <Heart className="w-4 h-4 text-red-500" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            className="w-8 h-8 bg-gray-100 hover:bg-red-50 rounded-lg"
                            onClick={() => handleRemoveItem(item.id)}
                          >
                            <Trash2 className="w-4 h-4 text-red-500" />
                          </Button>
                        </div>
                        
                        <div className="text-right">
                          {item.surpriseSale && (
                            <p className="text-xs text-red-500 font-medium">Surprise Sale !!!</p>
                          )}
                          <div className="flex items-center justify-end">
                            <DirhamSymbol className="w-4 h-4 mr-1" />
                            <span className="font-semibold">{item.price * item.quantity}</span>
                          </div>
                          {item.originalPrice && (
                            <div className="flex items-center justify-end text-gray-400 line-through text-sm">
                              <DirhamSymbol className="w-3 h-3 mr-1" />
                              <span>{item.originalPrice}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      {/* Quantity Controls - Center */}
                      <div className="flex justify-center">
                        <div className="flex items-center bg-white border border-gray-200 rounded-full overflow-hidden">
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            className="w-10 h-10 rounded-none hover:bg-gray-200"
                            onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                          >
                            <span className="text-gray-600 font-medium">−</span>
                          </Button>
                          <div className="px-4 py-2 bg-white border-l border-r border-gray-200 min-w-[50px] text-center">
                            <span className="text-sm font-medium">{item.quantity}</span>
                          </div>
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            className="w-10 h-10 rounded-none hover:bg-gray-200"
                            onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          >
                            <span className="text-gray-600 font-medium">+</span>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Desktop Layout */}
                <div className="hidden md:flex items-center">
                  <Checkbox 
                    checked={selectedItems.includes(item.id)}
                    onCheckedChange={() => handleSelectItem(item.id)}
                  />
                  
                  <div className="w-25 h-25 ml-4 bg-gray-100 rounded-lg overflow-hidden">
                    <Image
                      src={item.image}
                      alt={item.name}
                      width={64}
                      height={64}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  <div className="flex-1 ml-4">
                    <h3 className="text-sm font-medium text-gray-800">{item.name}</h3>
                    <div className="flex items-center mt-1">
                      <DirhamSymbol className="w-3 h-3 mr-1" />
                      <span className="text-sm text-gray-600">{item.price}</span>
                    </div>
                    <div className="flex items-center mt-2 space-x-2">
                      <Button variant="ghost" size="icon" className="w-8 h-8 bg-gray-100 hover:bg-red-50 rounded-lg shadow-sm">
                        <Heart className="w-4 h-4 text-red-500" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="w-8 h-8 bg-gray-100 hover:bg-red-50 rounded-lg shadow-sm"
                        onClick={() => handleRemoveItem(item.id)}
                      >
                        <Trash2 className="w-4 h-4 text-red-500" />
                      </Button>
                    </div>
                  </div>

                  {/* Quantity Controls - Centered */}
                  <div className="flex items-center justify-center flex-3">
                    <div className="flex items-center bg-white border-2 border-gray-200 rounded-full overflow-hidden">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="w-10 h-10 rounded-none bg-[#f4f4f4]"
                        onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                      >
                        <span className="text-gray-600 font-medium">−</span>
                      </Button>
                      <div className="px-4 py-2 bg-white border-l border-r border-gray-200 min-w-[50px] text-center">
                        <span className="text-sm font-medium">{item.quantity}</span>
                      </div>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="w-10 h-10 rounded-none bg-[#f4f4f4]"
                        onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                      >
                        <span className="text-gray-600 font-medium">+</span>
                      </Button>
                    </div>
                  </div>

                  {/* Price */}
                  <div className="text-right min-w-[80px]">
                    {item.surpriseSale && (
                      <p className="text-xs text-red-500 font-medium mb-5">Surprise Sale !!!</p>
                    )}
                     {item.originalPrice && (
                      <div className="flex items-center justify-end text-red-400 line-through text-sm">
                        <DirhamSymbol className="w-3 h-3 mr-1" />
                        <span>{item.originalPrice}</span>
                      </div>
                    )}
                    <div className="flex items-center justify-end">
                      <DirhamSymbol className="w-4 h-4 mr-1" />
                      <span className="font-semibold">{item.price * item.quantity}</span>
                    </div>
                   
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default CartItemsList;