"use client";

import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import { toast } from 'sonner';

export default function SupportTicketPage() {
  const [formData, setFormData] = useState({
    orderNumber: '',
    subject: '',
    category: '',
    topic: '',
    description: '',
    image: null
  });

  const [loading, setLoading] = useState(false);
  const [showList, setShowList] = useState(false);
  const [activeTab, setActiveTab] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [generatedTicketId, setGeneratedTicketId] = useState('');

  const tickets = [
    { orderId: 'TIC123456789', ticketId: 'ord123456789', status: 'Pending' },
    { orderId: 'TIC123456790', ticketId: 'ord123456789', status: 'Reviewed' },
    { orderId: 'TIC123456791', ticketId: 'ord123456789', status: 'Canceled' }
  ];

  const getTabColor = (tab) => {
    switch (tab) {
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'Resolved':
        return 'bg-[#e2fff9] text-[#196c67] border-[#40c4b6]';
      case 'Cancelled':
        return 'bg-[#ffebf1] text-[#ec6566] border-[#f1b0c4]';
      case 'All':
        return 'bg-teal-50 text-teal-600 border-teal-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const doesStatusMatchTab = (status) => {
    if (activeTab === 'All') return true;
    if (activeTab === 'Resolved') return status === 'Resolved' || status === 'Reviewed';
    if (activeTab === 'Cancelled') return status === 'Cancelled' || status === 'Canceled';
    return status === activeTab;
  };

  const filteredTickets = tickets.filter((t) =>
    doesStatusMatchTab(t.status) &&
    (t.orderId.toLowerCase().includes(searchQuery.toLowerCase()) ||
      t.ticketId.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData(prev => ({
        ...prev,
        image: file
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.orderNumber || !formData.description || !formData.category || !formData.subject) {
      toast.error('Please fill in all required fields');
      return;
    }

    setLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('Support ticket submitted successfully!');
      const newId = `TIC${Math.floor(100000000 + Math.random()*900000000)}`;
      setGeneratedTicketId(newId);
      setIsModalOpen(true);
      setShowList(true);
      setFormData({
        orderNumber: '',
        subject: '',
        category: '',
        topic: '',
        description: '',
        image: null
      });
    } catch (error) {
      toast.error('Failed to submit ticket. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
              <div className="bg-gray-50 min-h-screen p-0">
		{/* Support Ticket Form */}
		<div className="bg-white rounded-xl border-2 border-dashed border-[#40c4b6] p-8">
				<div className="mb-6">
					<div className="flex items-center gap-3">
						<h2 className="text-xl font-semibold text-teal-600">Raise a Support Ticket</h2>
						<div className="w-190 h-0.5 bg-gray-200"></div>

					</div>
					
				</div>

				<form onSubmit={handleSubmit} className="space-y-6">
					{/* First Row - Order Number and Subject */}
					<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
						{/* Order Number - Short */}
						<div className="lg:col-span-1">
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Order Number
							</label>
							<div className="relative">
								<select
									name="orderNumber"
									value={formData.orderNumber}
									onChange={handleInputChange}
									className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 appearance-none bg-white"
									required
								>
									<option value="">Select Order Number</option>
									<option value="ORD-001">ORD-001</option>
									<option value="ORD-002">ORD-002</option>
									<option value="ORD-003">ORD-003</option>
								</select>
								<Icon 
									icon="mdi:chevron-down" 
									className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"
								/>
							</div>
						</div>
						{/* Subject - Large, positioned more to the left */}
						<div className="lg:col-span-2">
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Subject
							</label>
							<input
								type="text"
								name="subject"
								value={formData.subject}
								onChange={handleInputChange}
								className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
								placeholder="Brief subject of your issue"
								required
							/>
						</div>
					</div>

					{/* Second Row - Category, Topic, and Upload Image */}
					<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
						{/* Select Category */}
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Select Category
							</label>
							<div className="relative">
								<select
									name="category"
									value={formData.category}
									onChange={handleInputChange}
									className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 appearance-none bg-white"
									required
								>
									<option value="">Select Category</option>
									<option value="order-issue">Order Issue</option>
									<option value="delivery">Delivery</option>
									<option value="product-quality">Product Quality</option>
									<option value="refund">Refund</option>
									<option value="technical">Technical Support</option>
									<option value="other">Other</option>
								</select>
								<Icon 
									icon="mdi:chevron-down" 
									className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"
								/>
							</div>
						</div>

						{/* Select Topic */}
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Select Topic
							</label>
							<input
								type="text"
								name="topic"
								value={formData.topic}
								onChange={handleInputChange}
								className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
								placeholder="Select topic"
							/>
						</div>

						{/* Upload Image */}
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Upload Image
							</label>
							<input
								id="image-upload"
								type="file"
								name="image"
								accept="image/*"
								onChange={handleFileChange}
								className="hidden"
							/>
							<label
								htmlFor="image-upload"
								className="flex items-center justify-end w-full h-12 border border-gray-300 rounded-lg px-4 cursor-pointer hover:bg-gray-50 transition"
								aria-label="Upload Image"
							>
								<Icon icon="mdi:upload" className="text-gray-600" />
							</label>
							{formData.image && (
								<p className="text-sm text-gray-500 mt-1">
									Selected: {formData.image.name}
								</p>
							)}
						</div>
					</div>

					{/* Third Row - Description (Full Width) */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-2">
							Description
						</label>
						<textarea
							name="description"
							value={formData.description}
							onChange={handleInputChange}
							rows={1}
							className="w-full px-4 py-3  border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 resize-none"
							placeholder="Please describe your issue in detail..."
							required
						/>
					</div>

					{/* Submit Button */}
					<div className="flex justify-end pt-4">
						<button
							type="submit"
							disabled={loading}
							className="px-8 py-3 bg-teal-700 text-white font-semibold rounded-lg hover:bg-teal-800 focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
						>
							{loading ? (
								<div className="flex items-center space-x-2">
									<Icon icon="mdi:loading" className="animate-spin" />
									<span>Submitting...</span>
								</div>
							) : (
								'Submit'
							)}
						</button>
					</div>
				</form>
			</div>

		{/* Support Ticket List */}

		{showList && (
		<div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200">
			<div className="px-4 pt-4">
				<div className="flex items-center gap-3">
					<h2 className="text-sm font-semibold text-teal-700">Support Ticket List</h2>
					<div className="h-px bg-gray-200 flex-1"></div>
				</div>
			</div>
			<div className="flex flex-col lg:flex-row lg:items-center lg:justify-between p-4 space-y-4 lg:space-y-0 relative">
                <div className="flex flex-wrap gap-2 lg:space-x-6 mb-0 mt-0 relative">
                    {['All','Pending','Resolved','Cancelled'].map((tab) => (
                        <button
                            key={tab}
                            onClick={() => setActiveTab(tab)}
                            className={`px-3 py-2 text-xs lg:text-sm font-medium transition-colors rounded-t-lg border-t border-l border-r relative ${activeTab === tab ? `${getTabColor(tab)} border-b-white z-10` : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50 border-transparent'}`}
                            style={activeTab === tab ? { marginBottom: '-19px', borderBottomWidth: '2px', borderBottomColor: 'white' } : {}}
                        >
                            {tab}
                        </button>
                    ))}
                </div>
				<div className="relative w-full lg:w-64">
					<input
						type="text"
						placeholder="Search"
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
						className="pl-4 pr-10 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-teal-500 w-full"
					/>
					<Icon icon="mdi:magnify" className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-teal-600" />
				</div>
			</div>

			<div className="w-full border-b-2 border-[#40c4b6]"></div>

			<div className="hidden lg:grid grid-cols-4 gap-4 p-4 bg-[#f1fcf9] text-sm font-medium text-gray-700">
				<div>Order ID</div>
				<div>Ticket ID</div>
				<div>Request Status</div>
				<div>Action</div>
			</div>

			<div className="divide-y divide-gray-200">
				{filteredTickets.map((t, idx) => (
					<div key={idx} className="p-4 hover:bg-gray-50">
						{/* Desktop Layout */}
						<div className="hidden lg:grid grid-cols-4 gap-4 items-center">
							<div className="flex items-center space-x-3">
								<input type="checkbox" className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500" />
								<span className="text-sm text-gray-800 font-medium">{t.orderId}</span>
							</div>
							<div className="text-sm text-gray-800 font-medium">{t.ticketId}</div>
							<div>
								<span className={`${t.status==='Pending' ? 'inline-flex px-3 py-1 rounded-full border text-xs font-medium border-amber-300 text-amber-700 bg-amber-50' : t.status==='Reviewed' ? 'inline-flex px-3 py-1 rounded-full border text-xs font-medium border-emerald-300 text-emerald-700 bg-emerald-50' : 'inline-flex px-3 py-1 rounded-full border text-xs font-medium border-rose-300 text-rose-700 bg-rose-50'}`}>{t.status}</span>
							</div>
							<div className="flex justify-left">
								<button className="px-2 py-1 text-xs border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition-colors">See Details</button>
							</div>
						</div>
					</div>
				))}
			</div>

			<div className="p-4 border-t border-gray-200 flex justify-end">
				<span className="text-sm text-gray-500">1-{filteredTickets.length} of {filteredTickets.length}</span>
			</div>
		</div>
		)}

		{/* Success Modal */}
		{isModalOpen && (
			<div className="fixed inset-0 z-50 flex items-center justify-center">
				<div className="absolute inset-0 bg-black/40" onClick={() => setIsModalOpen(false)}></div>
				<div className="relative bg-white rounded-xl shadow-xl w-[460px] overflow-visible">
					{/* Top badge image overlapping card */}
					<div className="absolute -top-16 left-1/2 -translate-x-1/2">
						<img src="/images/profile/Simplification.svg" alt="success" className="w-32 h-32" />
					</div>
					<div className="pt-20 px-8 pb-6 text-center shadow-md">
						<h3 className="text-4xl font-semibold text-teal-600">Success</h3>
						<p className="mt-3 text-lg text-teal-600">Your Successfully submited the Ticket</p>
						<p className="mt-3 text-xl text-gray-500">Your Ticket ID is : <span className="font-semibold">{generatedTicketId}</span></p>
					</div>
					<div className="bg-[#e8f8f5] p-6 flex justify-center shadow-xl">
						<button onClick={() => setIsModalOpen(false)} className="px-10 py-3 rounded-xl bg-[#2b8f86] text-white text-lg">Ok</button>
					</div>
				</div>
			</div>
		)}

      
      </div>
    
  );
}
