'use client';

import { useState } from 'react';
import { Plus, Edit, Trash2 } from 'lucide-react';

export default function Address() {
  const [addresses, setAddresses] = useState([
    {
      id: 1,
      title: 'Shipping Address 1',
      type: 'Home/Work/School',
      flatVilla: '#4556asdfg',
      street: 'sdf456',
      area: 'Acd',
      state: '###XXXXXXXXX',
      landmark: 'Besides ABC Shop',
      country: '<EMAIL>'
    },
    {
      id: 2,
      title: 'Shipping Address 2',
      type: 'Home/Work/School',
      flatVilla: '#4556asdfg',
      street: 'sdf456',
      area: 'Acd',
      state: '###XXXXXXXXX',
      landmark: 'Besides ABC Shop',
      country: '<EMAIL>'
    },
    {
      id: 3,
      title: 'Address',
      type: 'Home/Work/School',
      flatVilla: '#4556asdfg',
      street: 'sdf456',
      area: 'Acd',
      state: '(+880) 13XXXXXXXX',
      landmark: '<EMAIL>',
      country: 'BAngladesh'
    }
  ]);

  return (
    <>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center w-full">
          <h2 className="text-xl font-semibold text-[#196c67] mr-4">Address</h2>
          <div className="flex-1 h-px bg-gray-300"></div>
        </div>
        <button className="bg-teal-100 text-teal-700 px-4 py-2 rounded-lg hover:bg-teal-200 transition-colors text-sm ml-4">
          Add Additional Address
        </button>
      </div>

      {/* Add Address Cards */}
      <div className="flex gap-6 mb-6">
        {/* Add Billing Address */}
        <div className="bg-[#f1fcf9] border-2 border-gray-300 rounded-lg p-8 flex flex-col items-center justify-center min-h-[200px] w-[200px] hover:border-teal-400 transition-colors cursor-pointer shadow-sm">
          <div className="w-10 h-10 border-2 border-teal-600 rounded-lg flex items-center justify-center mb-4">
            <Plus className="w-8 h-8 text-teal-600" />
          </div>
          <span className="text-teal-600 font-medium text-center">Add Billing Address</span>
        </div>

        {/* Add Shipping Address */}
        <div className="bg-[#f1fcf9] border-2 border-gray-300 rounded-lg p-8 flex flex-col items-center justify-center min-h-[200px] w-[200px] hover:border-teal-400 transition-colors cursor-pointer shadow-sm">
          <div className="w-10 h-10 border-2 border-teal-600 rounded-lg flex items-center justify-center mb-4">
            <Plus className="w-8 h-8 text-teal-600" />
          </div>
          <span className="text-teal-600 font-medium text-center">Add Shipping Address</span>
        </div>
      </div>

      {/* Address List */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {addresses.map((address) => (
          <div key={address.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
            {/* Address Header */}
            <div className="bg-[#f1fffc] flex items-center justify-between p-6 mb-0">
              <h3 className="text-lg font-semibold text-teal-600">{address.title}</h3>
              <div className="flex items-center space-x-2">
                <button className="w-8 h-8 bg-white rounded flex items-center justify-center text-teal-600 hover:text-teal-700 shadow-sm">
                  <Edit className="w-4 h-4" />
                </button>
                <button className="w-8 h-8 bg-white rounded flex items-center justify-center text-red-500 hover:text-red-700 shadow-sm">
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Address Details */}
            <div className="p-6 pt-4 space-y-4 text-sm">
              <div className="flex items-start">
                <span className="text-gray-600 w-40 flex-shrink-0">Address Type</span>
                <span className="mx-3 text-gray-600">:</span>
                <span className="text-teal-600 font-medium">{address.type}</span>
              </div>
              
              <div className="flex items-start">
                <span className="text-gray-600 w-40 flex-shrink-0">Flat/Villa Number,</span>
                <span className="mx-3 text-gray-600">:</span>
                <span className="text-teal-600 font-medium">{address.flatVilla}</span>
              </div>
              
              <div className="flex items-start">
                <span className="text-gray-600 w-40 flex-shrink-0">Street Address:</span>
                <span className="mx-3 text-gray-600">:</span>
                <span className="text-teal-600 font-medium">{address.street}</span>
              </div>
              
              <div className="flex items-start">
                <span className="text-gray-600 w-40 flex-shrink-0">Area/Zone/Locality</span>
                <span className="mx-3 text-gray-600">:</span>
                <span className="text-teal-600 font-medium">{address.area}</span>
              </div>
              
              <div className="flex items-start">
                <span className="text-gray-600 w-40 flex-shrink-0">State/Province/Region</span>
                <span className="mx-3 text-gray-600">:</span>
                <span className="text-teal-600 font-medium">{address.state}</span>
              </div>
              
              <div className="flex items-start">
                <span className="text-gray-600 w-40 flex-shrink-0">Nearest LandMark</span>
                <span className="mx-3 text-gray-600">:</span>
                <span className="text-teal-600 font-medium">{address.landmark}</span>
              </div>
              
              <div className="flex items-start">
                <span className="text-gray-600 w-40 flex-shrink-0">Country</span>
                <span className="mx-3 text-gray-600">:</span>
                <span className="text-teal-600 font-medium">{address.country}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </>
  );
}






