'use client';

import useAuthStore from '@/store/authStore';
import { Edit } from 'lucide-react';

export default function BasicInfo() {
  const { user } = useAuthStore();

  return (
    <>
      {/* Header */}
      <div className="flex items-center mb-6">
        <h2 className="text-xl font-semibold text-[#196c67] mr-4">Basic Info</h2>
        <div className="flex-1 h-px bg-gray-300"></div>
      </div>
      
      {/* Profile Info Form */}
      <div className="space-y-4">
        <div className="flex items-center justify-between py-3  border-gray-100">
          <div className="flex items-center">
            <span className="text-gray-600 w-32 font-semibold">Name</span>
            <span className="mx-4">:</span>
            <span className="text-teal-600 font-medium">{user?.name || 'Full Name'}</span>
          </div>
          <button className="text-teal-600 hover:text-teal-700">
            <Edit className="w-4 h-4" />
          </button>
        </div>
        
        <div className="flex items-center py-3 border-gray-100">
          <span className="text-gray-600 w-32 font-semibold">Phone</span>
          <span className="mx-4">:</span>
          <span className="text-teal-600 font-medium">{user?.phone || '(880) 13XXXXXXXX'}</span>
        </div>
        
        <div className="flex items-center py-3  border-gray-100">
          <span className="text-gray-600 w-32 font-semibold">Email</span>
          <span className="mx-4">:</span>
          <span className="text-teal-600 font-medium">{user?.email || '<EMAIL>'}</span>
        </div>
        
        <div className="flex items-center py-3  border-gray-100">
          <span className="text-gray-600 w-32 font-semibold">Gender</span>
          <span className="mx-4">:</span>
          <span className="text-teal-400">----</span>
        </div>
        
        <div className="flex items-center py-3 border-gray-100">
          <span className="text-gray-600 w-32 font-semibold">Occupation</span>
          <span className="mx-4">:</span>
          <span className="text-teal-400">----</span>
        </div>
        
        <div className="flex items-center py-3 border-gray-100">
          <span className="text-gray-600 w-32 font-semibold">Company Name</span>
          <span className="mx-4">:</span>
          <span className="text-teal-400">----</span>
        </div>
        
        <div className="flex items-center py-3">
          <span className="text-gray-600 w-32 font-semibold">Designation</span>
          <span className="mx-4">:</span>
          <span className="text-teal-400">----</span>
        </div>
      </div>
    </>
  );
}