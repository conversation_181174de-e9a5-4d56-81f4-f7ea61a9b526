'use client';

import { useState, useMemo, useEffect } from 'react';
import { Search, Trash2, Heart } from 'lucide-react';
import Image from 'next/image';
import DirhamSymbol from '@/components/ui/dirham-symbol';
import { useSWRAxios } from '@/lib/useSWR';
import api from '@/lib/axios';
import { toast } from 'sonner';

export default function WishList() {
  const { data: wishlistData, error, isLoading, mutate } = useSWRAxios('/client/wishlist');
  
  // Refresh data when component mounts
  useEffect(() => {
    mutate();
  }, []);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [selectAll, setSelectAll] = useState(false);
  const [selectedItems, setSelectedItems] = useState([]);
  const [loadingItems, setLoadingItems] = useState(new Set());
  const [bulkLoading, setBulkLoading] = useState(false);

  // Transform API data to match component structure
  const wishlistItems = useMemo(() => {
    if (!wishlistData?.data?.data || !Array.isArray(wishlistData.data.data)) return [];
    
    return wishlistData.data.data.map(item => ({
      id: item.id,
      productId: item.product?.id,
      name: item.product?.title_en || item.product?.name || 'Product Name',
      price: parseFloat(item.product?.offer_price || item.product?.regular_price || 0),
      rating: item.product?.rating || 5,
      image: item.product?.main_image_url || item.product?.image || '/images/brand/product.jpg'
    }));
  }, [wishlistData]);

  const filteredItems = wishlistItems.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredItems.map(item => item.id));
    }
    setSelectAll(!selectAll);
  };

  const handleSelectItem = (itemId) => {
    if (selectedItems.includes(itemId)) {
      setSelectedItems(selectedItems.filter(id => id !== itemId));
    } else {
      setSelectedItems([...selectedItems, itemId]);
    }
  };

  const handleAddToCart = async (itemId) => {
    if (loadingItems.has(itemId)) return;
    
    setLoadingItems(prev => new Set(prev).add(itemId));
    
    try {
      const response = await api.post(`/client/wishlist/${itemId}/move-to-cart`);
      
      if (response.data.status) {
        toast.success('Item moved to cart successfully!');
        // Remove from selected items if it was selected
        setSelectedItems(prev => prev.filter(id => id !== itemId));
        // Refresh wishlist data
        mutate();
      } else {
        toast.error(response.data.message || 'Failed to move item to cart');
      }
    } catch (error) {
      console.error('Failed to move item to cart:', error);
      const errorMessage = error.response?.data?.message || 'Failed to move item to cart';
      toast.error(errorMessage);
    } finally {
      setLoadingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });
    }
  };

  const handleRemoveFromWishlist = async (itemId) => {
    try {
      const response = await api.delete(`/client/wishlist/${itemId}`);
      
      if (response.data.status) {
        toast.success('Item removed from wishlist');
        setSelectedItems(selectedItems.filter(id => id !== itemId));
        mutate();
      } else {
        toast.error(response.data.message || 'Failed to remove item');
      }
    } catch (error) {
      toast.error('Failed to remove item from wishlist');
      console.error('Remove from wishlist error:', error);
    }
  };

  const handleAddItemsToCart = async () => {
    if (selectedItems.length === 0) {
      toast.error('Please select items to add to cart');
      return;
    }
    
    if (bulkLoading) return;
    setBulkLoading(true);
    
    try {
      const response = await api.post('/client/wishlist/bulk-move-to-cart', {
        wishlist_ids: selectedItems
      });
      
      if (response.data.status) {
        toast.success('Items moved to cart successfully!');
        setSelectedItems([]);
        setSelectAll(false);
        mutate();
      } else {
        toast.error(response.data.message || 'Failed to move items to cart');
      }
    } catch (error) {
      console.error('Bulk add to cart error:', error);
      toast.error('Failed to add items to cart');
    } finally {
      setBulkLoading(false);
    }
  };

  const handleClearList = async () => {
    if (selectedItems.length === 0) {
      toast.error('Please select items to delete');
      return;
    }
    
    try {
      const response = await api.post('/client/wishlist/bulk-delete', {
        wishlist_ids: selectedItems
      });
      
      if (response.data.status) {
        toast.success('Selected items removed from wishlist');
        setSelectedItems([]);
        setSelectAll(false);
        mutate();
      } else {
        toast.error(response.data.message || 'Failed to remove items');
      }
    } catch (error) {
      console.error('Failed to clear wishlist:', error);
      toast.error('Failed to remove items from wishlist');
    }
  };

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <span key={index} className={`text-sm ${index < rating ? 'text-yellow-400' : 'text-gray-300'}`}>
        ★
      </span>
    ));
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="flex items-center mb-6">
          <div className="h-6 bg-gray-200 rounded w-32"></div>
          <div className="flex-1 h-px bg-gray-300 ml-4"></div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm mb-1">
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
              <div className="flex space-x-4">
                <div className="w-4 h-4 bg-gray-200 rounded"></div>
                <div className="w-32 h-32 bg-gray-200 rounded"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }



  return (
    <>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center w-full">
          <h2 className="text-xl font-semibold text-[#196c67] mr-4">My Wish List</h2>
          <div className="flex-1 h-px bg-gray-300"></div>
        </div>
      </div>

      {/* Controls Card */}
      <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm mb-1 ">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={selectAll}
                onChange={handleSelectAll}
                className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
              />
              <span className="text-sm font-medium text-gray-700">All</span>
            </div>
            <div className="relative">
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 min-w-[200px]"
              />
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#196c67]" />
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button 
              onClick={handleAddItemsToCart}
              disabled={bulkLoading || selectedItems.length === 0}
              className="bg-teal-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-teal-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {bulkLoading ? 'Moving Items...' : 'Add Items to Cart'}
            </button>
            <button 
              onClick={handleClearList}
              disabled={selectedItems.length === 0}
              className="bg-red-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Delete Selected
            </button>
          </div>
        </div>
      </div>

      {/* Wishlist Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {filteredItems.map((item) => (
          <div key={item.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
            <div className="flex space-x-4">
              {/* Checkbox */}
              <div className="flex-shrink-0 pt-2">
                <input
                  type="checkbox"
                  checked={selectedItems.includes(item.id)}
                  onChange={() => handleSelectItem(item.id)}
                  className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                />
              </div>

              {/* Product Image */}
              <div className="flex-shrink-0">
                <div className="w-32 h-32 bg-gray-100  overflow-hidden">
                  <Image
                    src={item.image}
                    alt={item.name}
                    width={80}
                    height={80}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>

              {/* Product Details */}
              <div className="flex-1">
                <h3 className="text-sm font-medium text-gray-800 mb-1 line-clamp-2">
                  {item.name}
                </h3>
                <div className="flex items-center mb-2">
                  {renderStars(item.rating)}
                </div>
                <div className="flex items-center mb-3">
                  <DirhamSymbol className="w-4 h-4 mr-1" />
                  <span className="text-lg font-semibold text-gray-800">{item.price}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleAddToCart(item.id)}
                    disabled={loadingItems.has(item.id)}
                    className="bg-teal-600 text-white px-4 py-2 rounded text-sm font-medium hover:bg-teal-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loadingItems.has(item.id) ? 'Moving...' : 'Add to Cart'}
                  </button>
                  <button
                    onClick={() => handleRemoveFromWishlist(item.id)}
                    className="bg-white border border-gray-200 rounded-lg p-2 text-red-500 hover:text-red-700 hover:bg-red-50 transition-colors shadow-sm"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredItems.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-500 mb-2">Your wishlist is empty</h3>
          <p className="text-gray-400">Add some products to your wishlist to see them here.</p>
        </div>
      )}
    </>
  );
}




