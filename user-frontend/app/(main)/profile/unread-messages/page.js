'use client';

import { useState } from 'react';
import { Search, CheckCheck } from 'lucide-react';

export default function UnreadMessages() {
  const [messages, setMessages] = useState([
    {
      id: 1,
      subject: 'Password Change',
      date: '7/7/2025',
      time: '12:45 p.m',
      isRead: false
    },
    {
      id: 2,
      subject: 'Password Change',
      date: '7/7/2025',
      time: '12:45 p.m',
      isRead: false
    },
    {
      id: 3,
      subject: 'Password Change',
      date: '7/7/2025',
      time: '12:45 p.m',
      isRead: false
    },
    {
      id: 4,
      subject: 'Password Change',
      date: '7/7/2025',
      time: '12:45 p.m',
      isRead: false
    },
    {
      id: 5,
      subject: 'Welcome to Vitamim.ae',
      date: '7/7/2025',
      time: '12:45 p.m',
      isRead: false
    },
    {
      id: 6,
      subject: 'Welcome to Vitamim.ae',
      date: '7/7/2025',
      time: '12:45 p.m',
      isRead: false
    },
    {
      id: 7,
      subject: 'Welcome to Vitamim.ae',
      date: '7/7/2025',
      time: '12:45 p.m',
      isRead: false
    },
    {
      id: 8,
      subject: 'Welcome to Vitamim.ae',
      date: '7/7/2025',
      time: '12:45 p.m',
      isRead: false
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectAll, setSelectAll] = useState(false);
  const [selectedMessages, setSelectedMessages] = useState([]);

  const filteredMessages = messages.filter(message =>
    message.subject.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedMessages([]);
    } else {
      setSelectedMessages(filteredMessages.map(m => m.id));
    }
    setSelectAll(!selectAll);
  };

  const handleSelectMessage = (messageId) => {
    if (selectedMessages.includes(messageId)) {
      setSelectedMessages(selectedMessages.filter(id => id !== messageId));
    } else {
      setSelectedMessages([...selectedMessages, messageId]);
    }
  };

  const markAllAsRead = () => {
    setMessages(messages.map(m => ({ ...m, isRead: true })));
  };

  return (
    <>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center w-full">
          <h2 className="text-xl font-semibold text-[#196c67] mr-4">Unread Messages</h2>
          <div className="flex-1 h-px bg-gray-300"></div>
        </div>
      </div>

      {/* Search and Mark All Read */}
      <div className="flex items-center justify-between mb-6 gap-4">
        <div className="relative flex-1 max-w-md">
          <input
            type="text"
            placeholder="Search"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
          />
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#196c67]" />
        </div>
        <button 
          onClick={markAllAsRead}
          className="bg-teal-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-teal-700 transition-colors"
        >
          Mark All as Read
        </button>
      </div>

      {/* Messages Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        {/* Table Header */}
        <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <input
                type="checkbox"
                checked={selectAll}
                onChange={handleSelectAll}
                className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
              />
              <span className="text-sm font-medium text-gray-700">All</span>
              <span className="text-sm font-medium text-gray-700 ml-8">Subject</span>
            </div>
            <span className="text-sm font-medium text-gray-700">Date | Time</span>
          </div>
        </div>

        {/* Messages List */}
        <div className="divide-y divide-gray-200">
          {filteredMessages.map((message) => (
            <div 
              key={message.id}
              className="px-6 py-4 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <input
                    type="checkbox"
                    checked={selectedMessages.includes(message.id)}
                    onChange={() => handleSelectMessage(message.id)}
                    className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                  />
                  <span className="text-sm text-gray-800 font-medium">
                    {message.subject}
                  </span>
                </div>
                <span className="text-sm text-gray-600">
                  {message.date} | {message.time}
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className="bg-gray-100 px-6 py-3 border-t border-gray-200">
          <div className="flex justify-between items-center text-sm text-gray-600">
            <span>1-8 of 8</span>
          </div>
        </div>
      </div>
    </>
  );
}