'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON>, Check, CheckCheck } from 'lucide-react';

export default function Notification() {
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      message: 'Your writing assessment result has been arrived.',
      time: '10 minutes ago',
      isRead: false
    },
    {
      id: 2,
      message: 'Your writing assessment result has been arrived.',
      time: '10 minutes ago',
      isRead: false
    },
    {
      id: 3,
      message: 'Your writing assessment result has been arrived.',
      time: '10 minutes ago',
      isRead: false
    },
    {
      id: 4,
      message: 'Your writing assessment result has been arrived.',
      time: '10 minutes ago',
      isRead: false
    },
    {
      id: 5,
      message: 'Your writing assessment result has been arrived.',
      time: '10 minutes ago',
      isRead: false
    },
    {
      id: 6,
      message: 'Your writing assessment result has been arrived.',
      time: '10 minutes ago',
      isRead: false
    },
    {
      id: 7,
      message: 'Your writing assessment result has been arrived.',
      time: '10 minutes ago',
      isRead: false
    },
    {
      id: 8,
      message: 'Your writing assessment result has been arrived.',
      time: '10 minutes ago',
      isRead: false
    }
  ]);

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const markAllAsRead = () => {
    setNotifications(notifications.map(n => ({ ...n, isRead: true })));
  };

  return (
    <>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center w-full">
          <h2 className="text-xl font-semibold text-[#196c67] mr-4">Notification</h2>
          <div className="flex-1 h-px bg-gray-300"></div>
        </div>
      </div>

      {/* Unread Count and Mark All Read */}
      <div className="flex items-center justify-between mb-6">
        <div className="text-gray-600">
          <span className="font-medium">Unread ({unreadCount})</span>
        </div>
        <button 
          onClick={markAllAsRead}
          className="flex items-center text-gray-600 hover:text-teal-600 text-sm"
        >
          <CheckCheck className="w-4 h-4 mr-1" />
          Mark all read
        </button>
      </div>

      {/* Notifications List */}
      <div className="space-y-4">
        {notifications.map((notification) => (
          <div 
            key={notification.id} 
            className={`flex items-start space-x-4 p-4 rounded-lg border ${
              notification.isRead ? 'bg-white border-gray-200' : 'bg-teal-50 border-teal-200'
            }`}
          >
            {/* Notification Icon */}
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center">
                <Bell className="w-4 h-4 text-teal-600" />
              </div>
            </div>

            {/* Notification Content */}
            <div className="flex-1">
              <p className="text-gray-800 text-sm font-medium mb-1">
                {notification.message}
              </p>
              <div className="flex items-center text-gray-500 text-xs">
                <Clock className="w-3 h-3 mr-1" />
                <span>{notification.time}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </>
  );
}
