'use client';

import { useState } from 'react';
import { Plus, Edit, Trash2 } from 'lucide-react';

export default function PaymentInfo() {
  const [showAddForm, setShowAddForm] = useState(false);
  const [cards, setCards] = useState([
    {
      id: 1,
      nickName: 'Card Nick Name',
      nameOnCard: 'Name of Card',
      cardNumber: '•••• •••• •••• 6555',
      holderName: 'Mr <PERSON><PERSON> a <PERSON>',
      expiry: 'Nov 2025',
      cardType: 'mastercard',
      isDefault: false
    },
    {
      id: 2,
      nickName: 'Card Nick Name',
      cardType: 'visa',
      nameOnCard: 'Name of Card',
      cardNumber: '•••• •••• •••• 6555',
      holderName: 'Mr <PERSON><PERSON> a <PERSON>',
      expiry: 'Nov 2025',
      isDefault: false
    },
    {
      id: 3,
      nickName: 'Card Nick Name',
      cardType: 'visa',
      nameOnCard: 'Name of Card',
      cardNumber: '•••• •••• •••• 6555',
      holderName: 'Mr <PERSON><PERSON> a <PERSON>',
      expiry: 'Nov 2025',
      isDefault: false
    },
    {
      id: 4,
      nickName: '$50 + 172 Dhm',
      cardType: 'mastercard',
      nameOnCard: 'Name of Card',
      cardNumber: '•••• •••• •••• 6555',
      holderName: 'Mr Reyet a Desh',
      expiry: 'Nov 2025',
      isDefault: true
    },
    {
      id: 5,
      nickName: 'Card Nick Name',
      cardType: 'mastercard',
      nameOnCard: 'Name of Card',
      cardNumber: '•••• •••• •••• 6555',
      holderName: 'Mr Reyet a Desh',
      expiry: 'Nov 2025',
      isDefault: false
    }
  ]);

  const getCardIcon = (type) => {
    if (type === 'visa') {
      return (
        <img 
          src="/images/profile/visaCard.png" 
          alt="Visa Card" 
          className="w-18 h-11 object-contain"
        />
      );
    } else {
      return (
        <img 
          src="/images/profile/masterCard.png" 
          alt="Master Card" 
          className="w-18 h-11 object-contain"
        />
      );
    }
  };

  const handleDeleteCard = (cardId) => {
    setCards(cards.filter(card => card.id !== cardId));
  };

  const handleAddCard = (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const newCard = {
      id: Date.now(),
      nickName: formData.get('nickName'),
      nameOnCard: formData.get('nameOnCard'),
      cardNumber: '•••• •••• •••• ' + formData.get('cardNumber').slice(-4),
      holderName: formData.get('holderName'),
      expiry: formData.get('expiry'),
      cardType: formData.get('cardType'),
      isDefault: false
    };
    setCards([...cards, newCard]);
    setShowAddForm(false);
  };

  return (
    <>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center w-full">
          <h2 className="text-xl font-semibold text-[#196c67] mr-4">Card Details</h2>
          <div className="flex-1 h-px bg-gray-300"></div>
        </div>
      </div>

      {/* Add Card */}
      <div className="mb-6">
        {!showAddForm ? (
          <div 
            onClick={() => setShowAddForm(true)}
            className="bg-[#f1fcf9] border-2 border-gray-300 rounded-lg p-8 flex flex-col items-center justify-center min-h-[120px] w-[200px] hover:border-teal-400 transition-colors cursor-pointer shadow-sm"
          >
            <div className="w-10 h-10 border-2 border-teal-600 rounded-lg flex items-center justify-center mb-4">
              <Plus className="w-6 h-6 text-teal-600" />
            </div>
            <span className="text-teal-600 font-medium text-center">Add Card</span>
          </div>
        ) : (
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm max-w-md">
            <h3 className="text-lg font-semibold mb-4">Add New Card</h3>
            <form onSubmit={handleAddCard} className="space-y-4">
              <input name="nickName" placeholder="Card Nick Name" className="w-full p-2 border rounded" required />
              <input name="cardNumber" placeholder="Card Number" className="w-full p-2 border rounded" required />
              <input name="holderName" placeholder="Card Holder Name" className="w-full p-2 border rounded" required />
              <select name="cardType" className="w-full p-2 border rounded" required>
                <option value="">Select Card Type</option>
                <option value="mastercard">Mastercard</option>
                <option value="visa">Visa</option>
              </select>
              <div className="flex gap-2">
                <button type="submit" className="bg-teal-600 text-white px-4 py-2 rounded hover:bg-teal-700">Add Card</button>
                <button type="button" onClick={() => setShowAddForm(false)} className="bg-gray-300 px-4 py-2 rounded hover:bg-gray-400">Cancel</button>
              </div>
            </form>
          </div>
        )}
      </div>

      {/* Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {cards.map((card, index) => (
          <div key={card.id} className="space-y-3">
            {/* Card Header - Outside the card */}
            <div className="flex items-center justify-between">
              <span className="text-base font-semibold text-[#1e293b]">Card {index + 1}</span>
              <button 
                onClick={() => handleDeleteCard(card.id)}
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                Delete
              </button>
            </div>

            {/* Card Body */}
            <div className="bg-white border border-gray-200 rounded-lg p-10 shadow-sm">
              {/* Card Content */}
              <div className="flex items-start space-x-4">
                {/* Card Icon */}
                <div className="flex-shrink-0">
                  {getCardIcon(card.cardType)}
                  {card.cardType === 'mastercard' && (
                    <div className="text-xs text-gray-600 font-semibold mt-1 capitalize">{card.cardType}</div>
                  )}
                </div>
                
                {/* Card Details */}
                <div className="flex-1">
                  <div className="text-base font-semibold text-[#1e293b] mb-2">{card.nickName}</div>
                  <div className="text-sm text-gray-800 font-medium">{card.cardNumber}</div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </>
  );
}








