'use client';

import { useState } from 'react';
import useAuthStore from '@/store/authStore';
import { useSWRAxios } from '@/lib/useSWR';
import { Edit, X, Check } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import api from '@/lib/axios';

export default function BasicInfo() {
  const { user } = useAuthStore();
  const { data: profileData, error, isLoading, mutate } = useSWRAxios('/client/profile');
  const [isEditing, setIsEditing] = useState(false);

  const profile = profileData?.data;
  const customer = profileData?.data?.customer;

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue
  } = useForm();

  const handleEditClick = () => {
    // Pre-populate form with current values
    setValue('name', profile?.name || user?.name || '');
    setValue('phone', profile?.phone || user?.phone || '');
    setValue('email', profile?.email || user?.email || '');
    // setValue('gender', customer?.gender || '');
    // setValue('occupation', customer?.occupation || '');
    // setValue('company_name', customer?.company_name || '');
    // setValue('designation', customer?.designation || '');
    
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    reset();
  };

  const onSubmit = async (data) => {
    try {
      const response = await api.post('/client/update-profile', {
        name: data.name,
        phone: data.phone,
        email: data.email,
        // gender: data.gender,
        // occupation: data.occupation,
        // company_name: data.company_name,
        // designation: data.designation,
        status: profile?.status || customer?.status || 'active'
      });

      if (response.data.status) {
        toast.success('Profile updated successfully!');
        setIsEditing(false);
        mutate(); // Refresh profile data
      } else {
        toast.error(response.data.message || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Profile update error:', error);
      const errorMessage = error.response?.data?.message || 'Failed to update profile';
      toast.error(errorMessage);
    }
  };

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="flex items-center mb-6">
          <h2 className="text-xl font-semibold text-[#196c67] mr-4">Basic Info</h2>
          <div className="flex-1 h-px bg-gray-300"></div>
        </div>
        <div className="space-y-4">
          {[...Array(7)].map((_, i) => (
            <div key={i} className="flex items-center py-3">
              <div className="w-32 h-4 bg-gray-200 rounded"></div>
              <div className="mx-4 w-2 h-4 bg-gray-200 rounded"></div>
              <div className="w-40 h-4 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500">Failed to load profile information.</div>;
  }

  if (isEditing) {
    return (
      <>
        {/* Header */}
        <div className="flex items-center mb-6">
          <h2 className="text-xl font-semibold text-[#196c67] mr-4">Basic Info</h2>
          <div className="flex-1 h-px bg-gray-300"></div>
        </div>

        {/* Edit Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
              <input
                type="text"
                {...register('name', { required: 'Name is required' })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent outline-none ${
                  errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>}
            </div>

            {/* Phone */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
              <input
                type="tel"
                {...register('phone', { required: 'Phone is required' })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent outline-none ${
                  errors.phone ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.phone && <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>}
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
              <input
                type="email"
                {...register('email', { 
                  required: 'Email is required',
                  pattern: {
                    value: /^\S+@\S+$/i,
                    message: 'Invalid email address'
                  }
                })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent outline-none ${
                  errors.email ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>}
            </div>

            {/* Gender */}
            {/* <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Gender</label>
              <select
                {...register('gender')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent outline-none"
              >
                <option value="">Select Gender</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
              </select>
            </div> */}

            {/* Occupation */}
            {/* <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Occupation</label>
              <input
                type="text"
                {...register('occupation')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent outline-none"
              />
            </div> */}

            {/* Company Name */}
            {/* <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
              <input
                type="text"
                {...register('company_name')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent outline-none"
              />
            </div> */}

            {/* Designation */}
            {/* <div className="md:col-span-3">
              <label className="block text-sm font-medium text-gray-700 mb-2">Designation</label>
              <input
                type="text"
                {...register('designation')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent outline-none"
              />
            </div> */}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-teal-600 text-white px-6 py-2 rounded-lg hover:bg-teal-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Updating...' : 'Update'}
            </button>
          </div>
        </form>
      </>
    );
  }

  return (
    <>
      {/* Header */}
      <div className="flex items-center mb-6">
        <h2 className="text-xl font-semibold text-[#196c67] mr-4">Basic Info</h2>
        <div className="flex-1 h-px bg-gray-300"></div>
      </div>
      
      {/* Profile Info Form */}
      <div className="space-y-4">
        <div className="flex items-center justify-between py-3 border-gray-100">
          <div className="flex items-center">
            <span className="text-gray-600 w-32 font-semibold">Name</span>
            <span className="mx-4">:</span>
            <span className="text-teal-600 font-medium">{profile?.name || user?.name || 'Full Name'}</span>
          </div>
          <button 
            onClick={handleEditClick}
            className="text-teal-600 hover:text-teal-700"
          >
            <Edit className="w-4 h-4" />
          </button>
        </div>
        
        <div className="flex items-center py-3 border-gray-100">
          <span className="text-gray-600 w-32 font-semibold">Phone</span>
          <span className="mx-4">:</span>
          <span className="text-teal-600 font-medium">{profile?.phone || user?.phone || '(880) 13XXXXXXXX'}</span>
        </div>
        
        <div className="flex items-center py-3 border-gray-100">
          <span className="text-gray-600 w-32 font-semibold">Email</span>
          <span className="mx-4">:</span>
          <span className="text-teal-600 font-medium">{profile?.email || user?.email || '<EMAIL>'}</span>
        </div>
        
        {/* <div className="flex items-center py-3 border-gray-100">
          <span className="text-gray-600 w-32 font-semibold">Gender</span>
          <span className="mx-4">:</span>
          <span className="text-teal-600">{customer?.gender || '----'}</span>
        </div>
        
        <div className="flex items-center py-3 border-gray-100">
          <span className="text-gray-600 w-32 font-semibold">Occupation</span>
          <span className="mx-4">:</span>
          <span className="text-teal-600">{customer?.occupation || '----'}</span>
        </div>
        
        <div className="flex items-center py-3 border-gray-100">
          <span className="text-gray-600 w-32 font-semibold">Company Name</span>
          <span className="mx-4">:</span>
          <span className="text-teal-600">{customer?.company_name || '----'}</span>
        </div>
        
        <div className="flex items-center py-3">
          <span className="text-gray-600 w-32 font-semibold">Designation</span>
          <span className="mx-4">:</span>
          <span className="text-teal-600">{customer?.designation || '----'}</span>
        </div> */}
      </div>
    </>
  );
}
