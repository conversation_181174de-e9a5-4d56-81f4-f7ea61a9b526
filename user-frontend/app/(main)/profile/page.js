'use client';

import Image from 'next/image';
import SimilarProductsSlider from '../../../components/ui/SimilarProductsSlider';
import DirhamSymbol from '@/components/ui/dirham-symbol';

export default function ProfilePage() {
  return (
    <>
      {/* Header */}
      <div className="flex items-center mb-6">
        <h2 className="text-xl font-semibold text-[#196c67] mr-4">Address</h2>
        <div className="flex-1 h-px bg-gray-300"></div>
      </div>
  
      {/* First Row - Large Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {/* Total Available Reward Card */}
        <div className="bg-[#fcf8ef] shadow rounded-lg p-4 relative">
          <div className="absolute top-0 right-0 w-16 h-13 flex items-center justify-center">
            <div className="relative w-16 h-16 flex items-center justify-center">
              <Image
                src="/images/profile/Vector.png"
                alt="Reward icon background"
                width={64}
                height={64}
                className="object-contain"
              />
              <Image
                src="/images/profile/Simplification.png"
                alt="Reward icon"
                width={32}
                height={32}
                className="object-contain absolute"
              />
            </div>
          </div>
          <div>
            <div className="text-3xl font-bold text-gray-700 mb-2 flex items-center gap-2">
              <DirhamSymbol className="w-8 h-8" />
              <DirhamSymbol className="w-8 h-8" />
            </div>
            <p className="text-md text-[#194846] font-bold mb-3">Total Available Reward</p>
            <button className="bg-[#196c67] text-white px-4 py-2 rounded-lg text-sm font-semibold">
              Shop Now
            </button>
            <p className="text-xs text-[#194846] mt-2 text-right">Validity: 05/08/2025</p>
          </div>
        </div>

        {/* My Offers Card */}
        <div className="bg-[#f6fff5] shadow rounded-lg p-4 relative">
          <div className="absolute top-0 right-0 w-16 h-13 flex items-center justify-center">
            <div className="relative w-16 h-16 flex items-center justify-center">
              <Image
                src="/images/profile/Vector.png"
                alt="Reward icon background"
                width={64}
                height={64}
                className="object-contain"
              />
              <Image
                src="/images/profile/Simplification (1).png"
                alt="Reward icon"
                width={32}
                height={32}
                className="object-contain absolute"
              />
            </div>
          </div>

          <div className="pt-2">
            <h3 className="text-lg font-semibold text-[#36605d] mb-2">My Offers</h3>
            <p className="text-sm text-[#36605d]">Unlock the best deals as a vitamin.ae member!</p>
          </div>
        </div>

        {/* My Wish-list Card */}
        <div className="bg-[#fff9fb] shadow rounded-lg p-4 relative">
          <div className="absolute top-0 right-0 w-16 h-13 flex items-center justify-center">
            <div className="relative w-16 h-16 flex items-center justify-center">
              <Image
                src="/images/profile/Vector.png"
                alt="Reward icon background"
                width={64}
                height={64}
                className="object-contain"
              />
              <Image
                src="/images/profile/Simplification (2).png"
                alt="Reward icon"
                width={32}
                height={32}
                className="object-contain absolute"
              />
            </div>
          </div>
          <div className="pt-2">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">My Wish-list</h3>
            <p className="text-sm text-gray-600">Track orders, request returns, reorder, or write reviews.</p>
          </div>
        </div>
      </div>

      {/* Separator */}
      <div className="border-t-3 border-dashed border-gray-300 mb-6"></div>

      {/* Second Row - Small Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {/* My Order Card */}
        <div className="bg-[#f1fcfa] rounded-lg p-4 relative min-h-[100px] overflow-hidden shadow">
          
        <div className="absolute top-0 right-0 w-16 h-13 flex items-center justify-center">
          <div className="relative w-16 h-16 flex items-center justify-center">
            <Image
              src="/images/profile/Vector (1).png"
              alt="Reward icon background"
              width={64}
              height={64}
              className="object-contain"
            />
            <Image
              src="/images/profile/Vector (2).png"
              alt="Reward icon"
              width={26}
              height={26}
              className="object-contain absolute"
            />
          </div>
        </div>
        <div className="pr-12">
          <h3 className="text-base font-semibold text-gray-800 mb-2">My order</h3>
          <p className="text-sm text-gray-600 leading-relaxed">Track orders, request returns, reorder, or write reviews.</p>
        </div>
      </div>

      {/* Redeemed Points Card */}
      <div className="bg-[#f1fcfa] rounded-lg p-4 shadow relative min-h-[100px]">
        <div className="absolute top-0 right-0 w-16 h-13 flex items-center justify-center">
          <div className="relative w-16 h-16 flex items-center justify-center">
            <Image
              src="/images/profile/Vector (1).png"
              alt="Reward icon background"
              width={64}
              height={64}
              className="object-contain"
            />
            <Image
              src="/images/profile/Simplification (3).png"
              alt="Reward icon"
              width={26}
              height={26}
              className="object-contain absolute"
            />
          </div>
        </div>
        <div className="pr-12">
          <h3 className="text-base font-semibold text-gray-800 mb-2">Redeemed Points</h3>
          <p className="text-sm text-gray-600 leading-relaxed">Point Use to Purchase product</p>
        </div>
      </div>

      {/* My Address Card */}
      <div className="bg-[#f1fcfa] rounded-lg p-4 shadow relative min-h-[100px]">
        <div className="absolute top-0 right-0 w-16 h-13 flex items-center justify-center">
          <div className="relative w-16 h-16 flex items-center justify-center">
            <Image
              src="/images/profile/Vector (1).png"
              alt="Reward icon background"
              width={64}
              height={64}
              className="object-contain"
            />
            <Image
              src="/images/profile/Simplification (4).png"
              alt="Reward icon"
              width={26}
              height={26}
              className="object-contain absolute"
            />
          </div>
        </div>
        <div className="pr-12">
          <h3 className="text-base font-semibold text-gray-800 mb-2">My Address</h3>
          <p className="text-sm text-gray-600 leading-relaxed">Point Use to Purchase product</p>
        </div>
      </div>

      {/* Payment History Card */}
      <div className="bg-[#f1fcfa] rounded-lg p-4 shadow relative min-h-[100px]">
        <div className="absolute top-0 right-0 w-16 h-13 flex items-center justify-center">
          <div className="relative w-16 h-16 flex items-center justify-center">
            <Image
              src="/images/profile/Vector (1).png"
              alt="Reward icon background"
              width={64}
              height={64}
              className="object-contain"
            />
            <Image
              src="/images/profile/Simplification (5).png"
              alt="Reward icon"
              width={26}
              height={26}
              className="object-contain absolute"
            />
          </div>
        </div>
        <div className="pr-12">
          <h3 className="text-base font-semibold text-gray-800 mb-2">Payment History</h3>
          <p className="text-sm text-gray-600 leading-relaxed">Point Use to Purchase product</p>
        </div>
      </div>
    </div>
     <div className="border-t-3 border-dashed border-gray-300 mb-2"></div>
    <div className="border-t-3 border-dashed border-gray-300 mb-6"></div>


    {/* Newsletter Subscription */}
    <div className="rounded-lg p-6 text-white relative overflow-hidden" style={{backgroundImage: 'url(/images/profile/bg1.svg)', backgroundSize: 'cover', backgroundPosition: 'center', backgroundRepeat: 'no-repeat'}}>
      <div className="absolute top-0 left-0 w-25 h-15 flex items-center justify-center">
      </div>
      <div className="relative z-10">
        <h3 className="text-xl font-medium mb-4 text-center mr-30">Discover the latest exclusive deals!</h3>
       <div className="bg-[#e2fff9] rounded-[9px] p-2 max-w-md mx-auto">
          <div className="flex items-center gap-2">
            <input
              type="email"
              placeholder="Email address"
              className="bg-white flex-1 px-3 sm:px-4 py-2 sm:py-3 text-gray-500 text-sm border-0 focus:outline-none rounded-[5px] min-w-0"
            />
            <button className="bg-[#196c67] text-white px-4 sm:px-6 py-2 sm:py-3 rounded-[50px] font-medium hover:bg-[#0f5a56] transition-colors whitespace-nowrap text-sm">
              Subscribe
            </button>
          </div>
        </div>
      </div>
    </div>
    </>
  );
}

