'use client';

import { useState } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import useAuthStore from '@/store/authStore';
import { 
  User, 
  MapPin, 
  Package, 
  Heart, 
  CreditCard, 
  Settings,
  Bell,
  MessageCircle,
  HelpCircle,
  Gift,
  ShoppingCart,
  Star,
  Calendar,
  Truck,
  RefreshCw
} from 'lucide-react';
import { Breadcrumb } from '@/components/ui/breadcrumb';

export default function ProfileLayout({ children }) {
  const { user, isAuthenticated } = useAuthStore();
  const pathname = usePathname();
  const [expandedSections, setExpandedSections] = useState({
    profile: true,
    messageCenter: false,
    orderTracking: false,
    loyaltyRPS: false,
    returnRequest: false
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Please Login</h2>
          <Link href="/login" className="bg-[#196c67] text-white px-6 py-2 rounded-lg">
            Login
          </Link>
        </div>
      </div>
    );
  }

  const sidebarSections = [
    {
      id: 'profile',
      title: 'Profile',
      expandable: true,
      items: [
        { id: 'dashboard', label: 'Dashboard', href: '/profile' },
        { id: 'profile-info', label: 'Profile Info', href: '/profile/profile-info' },
        { id: 'change-password', label: 'Change Password', href: '/profile/change-password' },
        { id: 'address', label: 'Address', href: '/profile/address' },
        { id: 'payment-info', label: 'Payment Info', href: '/profile/payment-info' }
      ]
    },
    {
      id: 'messageCenter',
      title: 'Message Center',
      expandable: true,
      items: [
        { id: 'notification', label: 'Notification & Alerts', href: '/profile/notification' },
        { id: 'unread-messages', label: 'Unread Messages', href: '/profile/unread-messages' },
        { id: 'support-ticket', label: 'Raise a Support Ticket', href: '/profile/support-ticket' },
        { id: 'review-support', label: 'Review Support Ticket' }
      ]
    },
    {
      id: 'orderTracking',
      title: 'Order Tracking',
      expandable: true,
      items: [
        { id: 'order-history', label: 'Order History', href: '/profile/order-history' },
        { id: 'buy-again', label: 'Buy Again' }
      ]
    },
    {
      id: 'viewCart',
      title: 'View Cart',
      expandable: false,
      href: '/cart'
    },
    {
      id: 'buyLater',
      title: 'Buy Later',
      expandable: false,
    },
    {
      id: 'wishList',
      title: 'Wish List',
      expandable: false,
      href: '/profile/wishlist'
    },
    {
      id: 'suggestProduct',
      title: 'Suggest a Product',
      expandable: false,
    },
    {
      id: 'loyaltyRPS',
      title: 'Loyalty RPS Point',
      expandable: true,
      items: [
        { id: 'statement', label: 'Statement'},
        { id: 'redeem-points', label: 'Redeem Points'}
      ]
    },
    {
      id: 'returnRequest',
      title: 'Return Request',
      expandable: true,
      items: [
        { id: 'requested', label: 'Requested',},
        { id: 'approved', label: 'Approved',},
        { id: 'rejected', label: 'Rejected',}
      ]
    },
    {
      id: 'reviewProduct',
      title: 'Review a Product',
      expandable: false,
    }
  ];

  const getBreadcrumbItems = (currentPath) => {
    const baseItems = [
      { label: 'Home', href: '/' }
    ];

    for (const section of sidebarSections) {
      if (section.items) {
        for (const item of section.items) {
          if (item.href === currentPath) {
            return [
              ...baseItems,
              { label: section.title },
              { label: item.label }
            ];
          }
        }
      } else {
        if (section.href === currentPath) {
          return [
            ...baseItems,
            { label: section.title }
          ];
        }
      }
    }
    
    if (currentPath === '/profile') {
        return [
            ...baseItems,
            { label: 'Profile' }
        ];
    }

    return baseItems;
  };

  const breadcrumbItems = getBreadcrumbItems(pathname);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-6">
        <div className="mb-4">
          <Breadcrumb items={breadcrumbItems} />
        </div>

        {pathname !== '/cart' && (
          <div 
            className="rounded-lg p-6 mb-6 relative border-2 border-gray-100 shadow"
            style={{ background: 'linear-gradient(106.3deg, rgba(254, 225, 254, 0.6) 8%, rgba(236, 247, 255, 0.6) 40%, rgba(255, 235, 222, 0.6) 66%, rgba(239, 254, 255, 0.6) 91%)' }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center">
                    <User className="text-gray-600" size={32} />
                  </div>
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center border border-gray-200">
                    <svg className="w-3 h-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">{user?.name}</h2>
                  <div className="flex items-center space-x-2 mt-1">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">verified</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white border border-gray-200 text-gray-900 px-4 py-3 rounded-lg shadow-sm">
                  <button className="bg-teal-600 text-white px-4 py-2 rounded-lg text-sm font-semibold w-25 mb-2">
                    Earn Point
                  </button>
                  <div className="text-xs text-gray-600 leading-tight font-bold">
                    Earn Rewards point by sharing vitamin.ae with others
                  </div>
                </div>
            </div>
          </div>
        )}

        <div className="flex flex-col lg:flex-row gap-6">
          {pathname !== '/cart' && (
            <div className="lg:w-64 bg-[#feffff] rounded-lg shadow-xl border-17 border-[#f6fffd]">
            <nav className="p-0">
              <ul className="space-y-1 ">
                {sidebarSections.map((section) => (
                  <li key={section.id} className="border-b border-[#f6fffd] last:border-b-0">
                    {section.expandable ? (
                      <div>
                        <button
                          onClick={() => toggleSection(section.id)}
                          className="w-full flex items-center justify-between px-4 py-3 text-sm text-[#196c67]"
                        >
                          <span className="font-medium">{section.title}</span>
                          <svg
                            className={`w-4 h-4 transition-transform ${
                              expandedSections[section.id] ? 'rotate-180' : ''
                            }`}
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </button>
                        {expandedSections[section.id] && section.items && (
                          <ul className="bg-gray-50 shadow-inner">
                            {section.items.map((item) => (
                              <li key={item.id}>
                                {item.href ? (
                                  <Link href={item.href} className={`block w-full text-left px-6 py-2.5 text-sm transition-colors border-l-4 ${
                                      pathname === item.href
                                        ? 'border-[#40c4b6] bg-[#f1fcf9] text-gray-700 font-medium '
                                        : 'border-transparent text-gray-600 hover:bg-gray-100'
                                    }`}>
                                      {item.label}
                                  </Link>
                                ) : (
                                  <span className="block w-full text-left px-6 py-2.5 text-sm text-gray-400  border-l-4 border-transparent">
                                    {item.label}
                                  </span>
                                )}
                              </li>
                            ))}
                          </ul>
                        )}
                      </div>
                    ) : (
                      section.href ? (
                        <Link href={section.href} className={`block w-full text-center px-4 py-3 text-sm  rounded-lg border border-gray-200 bg-white  ${
                            pathname === section.href
                              ? 'bg-[#196c67] text-gray-700 font-medium border-[#196c67]'
                              : 'text-gray-500'
                          }`}>
                            <span className="font-medium">{section.title}</span>
                        </Link>
                      ) : (
                        <div className="block w-full text-center px-4 py-3 text-sm rounded-lg border border-gray-200 bg-white text-gray-400 cursor-not-allowed">
                          <span className="font-medium">{section.title}</span>
                        </div>
                      )
                    )}
                  </li>
                ))}
              </ul>
            </nav>
            </div>
          )}

          <div className="flex-1 bg-white rounded-lg shadow-lg p-6">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}
