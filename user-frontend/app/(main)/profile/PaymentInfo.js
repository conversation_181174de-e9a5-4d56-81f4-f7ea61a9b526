'use client';

import { useState } from 'react';
import { Plus, Edit } from 'lucide-react';

export default function PaymentInfo() {
  const [cards, setCards] = useState([
    {
      id: 1,
      nickName: 'Card Nick Name',
      nameOnCard: 'Name of Card',
      cardNumber: '•••• •••• •••• 6555',
      holderName: 'Mr <PERSON><PERSON> a <PERSON>',
      expiry: 'Nov 2025',
      isDefault: false
    },
    {
      id: 2,
      nickName: 'Card Nick Name',
      cardType: 'visa',
      nameOnCard: 'Name of Card',
      cardNumber: '•••• •••• •••• 6555',
      holderName: 'Mr <PERSON><PERSON> a <PERSON>',
      expiry: 'Nov 2025',
      isDefault: false
    },
    {
      id: 3,
      nickName: 'Card Nick Name',
      cardType: 'visa',
      nameOnCard: 'Name of Card',
      cardNumber: '•••• •••• •••• 6555',
      holderName: 'Mr <PERSON><PERSON> a <PERSON>',
      expiry: 'Nov 2025',
      isDefault: false
    },
    {
      id: 4,
      nickName: '$50 + 172 Dhm',
      cardType: 'mastercard',
      nameOnCard: 'Name of Card',
      cardNumber: '•••• •••• •••• 6555',
      holderName: 'Mr Reyet a Desh',
      expiry: 'Nov 2025',
      isDefault: true
    },
    {
      id: 5,
      nickName: 'Card Nick Name',
      cardType: 'mastercard',
      nameOnCard: 'Name of Card',
      cardNumber: '•••• •••• •••• 6555',
      holderName: 'Mr Reyet a Desh',
      expiry: 'Nov 2025',
      isDefault: false
    }
  ]);

  const getCardIcon = (type) => {
    if (type === 'visa') {
      return (
        <div className="flex flex-col items-center">
          <img 
            src="/images/profile/visaCard.png" 
            alt="Visa Card" 
            className="w-25 h-25 object-contain"
          />
        </div>
      );
    } else {
      return (
        <div className="flex flex-col items-center">
          <img 
            src="/images/profile/masterCard.png" 
            alt="Master Card" 
            className="w-25 h-25 object-contain"
          />
        </div>
      );
    }
  };

  return (
    <>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center w-full">
          <h2 className="text-xl font-semibold text-[#196c67] mr-4">Card Details</h2>
          <div className="flex-1 h-px bg-gray-300"></div>
        </div>
      </div>

      {/* Add Card */}
      <div className="mb-6">
        <div className="bg-[#f1fcf9] border-2 border-gray-300 rounded-lg p-8 flex flex-col items-center justify-center min-h-[120px] w-[200px] hover:border-teal-400 transition-colors cursor-pointer shadow-sm">
          <div className="w-10 h-10 border-2 border-teal-600 rounded-lg flex items-center justify-center mb-4">
            <Plus className="w-6 h-6 text-teal-600" />
          </div>
          <span className="text-teal-600 font-medium text-center">Add Card</span>
        </div>
      </div>

      {/* Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {cards.map((card) => (
          <div key={card.id} className="space-y-2">
            {/* Card Header - Outside the card */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Card Nick Name</span>
              <button className="text-blue-500 hover:text-blue-700 text-sm font-medium">
                Edit
              </button>
            </div>

            {/* Card Body */}
            <div 
              className="border border-gray-200 rounded-lg p-4 shadow-sm"
              style={{ background: 'linear-gradient(106.3deg, rgba(254, 225, 254, 0.6) 8%, rgba(236, 247, 255, 0.6) 40%, rgba(255, 235, 222, 0.6) 66%, rgba(239, 254, 255, 0.6) 91%)' }}
            >
              {/* Card Content */}
              <div className="flex items-start space-x-4">
                {/* Card Icon */}
                <div className="flex-shrink-0">
                  {getCardIcon(card.cardType)}
                </div>
                
                {/* Card Details */}
                <div className="flex-1">
                  <div className="text-base font-medium text-gray-800 mb-1">{card.nameOnCard}</div>
                  <div className="text-base text-gray-800 font-medium mb-2">{card.cardNumber}</div>
                  <div className="text-sm text-gray-600 mb-3">{card.holderName}</div>
                  
                  {/* Bottom section */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">
                      Expire in : {card.expiry}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
 
   </>
  );
}








