'use client';

import { useState } from 'react';
import { Search, Trash2 } from 'lucide-react';
import Image from 'next/image';
import DirhamSymbol from '@/components/ui/dirham-symbol';

export default function WishList() {
  const [wishlistItems, setWishlistItems] = useState([
    {
      id: 1,
      name: 'Product Name Product Name',
      price: 480,
      rating: 5,
      image: '/images/brand/product.jpg'
    },
    {
      id: 2,
      name: 'Product Name Product Name',
      price: 480,
      rating: 5,
      image: '/images/brand/product.jpg'
    },
    {
      id: 3,
      name: 'Product Name Product Name',
      price: 480,
      rating: 5,
      image: '/images/brand/product.jpg'
    },
    {
      id: 4,
      name: 'Product Name Product Name',
      price: 480,
      rating: 5,
      image: '/images/brand/product.jpg'
    },
    {
      id: 5,
      name: 'Product Name Product Name',
      price: 480,
      rating: 5,
      image: '/images/brand/product.jpg'
    },
    {
      id: 6,
      name: 'Product Name Product Name',
      price: 480,
      rating: 5,
      image: '/images/brand/product.jpg'
    },
    {
      id: 7,
      name: 'Product Name Product Name',
      price: 480,
      rating: 5,
      image: '/images/brand/product.jpg'
    },
    {
      id: 8,
      name: 'Product Name Product Name',
      price: 480,
      rating: 5,
      image: '/images/brand/product.jpg'
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectAll, setSelectAll] = useState(false);
  const [selectedItems, setSelectedItems] = useState([]);

  const filteredItems = wishlistItems.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredItems.map(item => item.id));
    }
    setSelectAll(!selectAll);
  };

  const handleSelectItem = (itemId) => {
    if (selectedItems.includes(itemId)) {
      setSelectedItems(selectedItems.filter(id => id !== itemId));
    } else {
      setSelectedItems([...selectedItems, itemId]);
    }
  };

  const handleAddToCart = (itemId) => {
    // Add to cart logic
    console.log('Add to cart:', itemId);
  };

  const handleRemoveFromWishlist = (itemId) => {
    setWishlistItems(wishlistItems.filter(item => item.id !== itemId));
    setSelectedItems(selectedItems.filter(id => id !== itemId));
  };

  const handleAddItemsToCart = () => {
    // Add selected items to cart
    console.log('Add selected items to cart:', selectedItems);
  };

  const handleClearList = () => {
    setWishlistItems([]);
    setSelectedItems([]);
    setSelectAll(false);
  };

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <span key={index} className={`text-sm ${index < rating ? 'text-yellow-400' : 'text-gray-300'}`}>
        ★
      </span>
    ));
  };

  return (
    <>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center w-full">
          <h2 className="text-xl font-semibold text-[#196c67] mr-4">My Wish List</h2>
          <div className="flex-1 h-px bg-gray-300"></div>
        </div>
      </div>

      {/* Controls Card */}
      <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm mb-1 ">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={selectAll}
                onChange={handleSelectAll}
                className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
              />
              <span className="text-sm font-medium text-gray-700">All</span>
            </div>
            <div className="relative">
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 min-w-[200px]"
              />
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#196c67]" />
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button 
              onClick={handleAddItemsToCart}
              className="bg-teal-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-teal-700 transition-colors"
            >
              Add Items to Cart
            </button>
            <button 
              onClick={handleClearList}
              className="bg-red-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-red-600 transition-colors"
            >
              Clear List
            </button>
          </div>
        </div>
      </div>

      {/* Wishlist Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {filteredItems.map((item) => (
          <div key={item.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
            <div className="flex space-x-4">
              {/* Checkbox */}
              <div className="flex-shrink-0 pt-2">
                <input
                  type="checkbox"
                  checked={selectedItems.includes(item.id)}
                  onChange={() => handleSelectItem(item.id)}
                  className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                />
              </div>

              {/* Product Image */}
              <div className="flex-shrink-0">
                <div className="w-32 h-32 bg-gray-100  overflow-hidden">
                  <Image
                    src={item.image}
                    alt={item.name}
                    width={80}
                    height={80}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>

              {/* Product Details */}
              <div className="flex-1">
                <h3 className="text-sm font-medium text-gray-800 mb-1 line-clamp-2">
                  {item.name}
                </h3>
                <div className="flex items-center mb-2">
                  {renderStars(item.rating)}
                </div>
                <div className="flex items-center mb-3">
                  <DirhamSymbol className="w-4 h-4 mr-1" />
                  <span className="text-lg font-semibold text-gray-800">{item.price}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleAddToCart(item.id)}
                    className="bg-teal-600 text-white px-4 py-2 rounded text-sm font-medium hover:bg-teal-700 transition-colors"
                  >
                    Add to Cart
                  </button>
                  <button
                    onClick={() => handleRemoveFromWishlist(item.id)}
                    className="bg-white border border-gray-200 rounded-lg p-2 text-red-500 hover:text-red-700 hover:bg-red-50 transition-colors shadow-sm"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredItems.length === 0 && (
        <div className="text-center py-12">
          <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-500 mb-2">Your wishlist is empty</h3>
          <p className="text-gray-400">Add some products to your wishlist to see them here.</p>
        </div>
      )}
    </>
  );
}




