'use client';

import { useState } from 'react';
import { Plus, Edit, Trash2, X } from 'lucide-react';
import { useSWRAxios } from '@/lib/useSWR';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import api from '@/lib/axios';

export default function Address() {
  const { data: addressData, error, isLoading, mutate } = useSWRAxios('/client/user-addresses');
  const [deletingId, setDeletingId] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedAddressType, setSelectedAddressType] = useState('');
  const [editingAddress, setEditingAddress] = useState(null);

  // Handle different possible API response structures
  const addresses = Array.isArray(addressData?.data?.data) 
    ? addressData.data.data 
    : Array.isArray(addressData?.data) 
    ? addressData.data 
    : Array.isArray(addressData) 
    ? addressData 
    : [];

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue
  } = useForm({
    defaultValues: {
      address_type: '',
      flat_or_villa_number: '',
      building_name: '',
      address_line_1: '',
      address_line_2: '',
      city: '',
      state: '',
      postal_code: '',
      country: 'UAE',
      is_default: false,
      is_shipping: false,
      is_billing: false
    }
  });

  const handleAddAddress = (type) => {
    setSelectedAddressType(type);
    // Map UI types to valid database values
    if (type === 'billing') {
      setValue('address_type', 'home'); // Use 'home' as default for billing
    } else if (type === 'shipping') {
      setValue('address_type', 'home'); // Use 'home' as default for shipping
    }
    setShowAddForm(true);
  };

  const handleEditAddress = async (addressId) => {
    try {
      const response = await api.get(`/client/user-addresses/${addressId}`);
      const addressData = response.data.data || response.data;
      
      setEditingAddress(addressData);
      setSelectedAddressType(addressData.address_type);
      
      // Populate form with existing data
      Object.keys(addressData).forEach(key => {
        setValue(key, addressData[key]);
      });
      
      setShowAddForm(true);
    } catch (error) {
      toast.error('Failed to load address details');
      console.error('Edit address error:', error);
    }
  };

  const handleDeleteAddress = async (addressId) => {
    if (!confirm('Are you sure you want to delete this address?')) return;
    
    setDeletingId(addressId);
    try {
      await api.delete(`/client/user-addresses/${addressId}`);
      toast.success('Address deleted successfully');
      mutate(); // Refresh the data
    } catch (error) {
      toast.error('Failed to delete address');
      console.error('Delete address error:', error);
    } finally {
      setDeletingId(null);
    }
  };

  const onSubmit = async (data) => {
    try {
      // Set address type flags based on selected type
      const submissionData = {
        ...data,
        address_type: 'home', // Always use 'home' as the database value
        is_default: false,
        is_shipping: selectedAddressType === 'shipping' || data.is_shipping || false,
        is_billing: selectedAddressType === 'billing' || data.is_billing || false
      };
      
      let response;
      if (editingAddress) {
        response = await api.put(`/client/user-addresses/${editingAddress.id}`, submissionData);
      } else {
        response = await api.post('/client/user-addresses', submissionData);
      }
      
      if (response.data.status) {
        toast.success(`Address ${editingAddress ? 'updated' : 'added'} successfully!`);
        setShowAddForm(false);
        setEditingAddress(null);
        reset();
        mutate(); // Refresh the data
      } else {
        toast.error(response.data.message || `Failed to ${editingAddress ? 'update' : 'add'} address`);
      }
    } catch (error) {
      console.error(`${editingAddress ? 'Update' : 'Add'} address error:`, error);
      const errorMessage = error.response?.data?.message || `Failed to ${editingAddress ? 'update' : 'add'} address`;
      toast.error(errorMessage);
    }
  };

  const handleCancelAdd = () => {
    setShowAddForm(false);
    setSelectedAddressType('');
    setEditingAddress(null);
    reset();
  };

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Failed to load addresses. Please try again.</p>
        <button 
          onClick={() => mutate()} 
          className="mt-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700"
        >
          Retry
        </button>
      </div>
    );
  }

  if (showAddForm) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="mb-6 bg-white rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-[#196c67]">
              {editingAddress ? '' : 'Add'} {selectedAddressType} Address
            </h3>
            <button 
              onClick={handleCancelAdd}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
          
                     <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
             {/* First Row - Address Type and Country */}
             <div className="grid grid-cols-3 gap-4">
               <div>
                 <label className="block text-sm font-medium text-gray-700 mb-1">
                   Address Type <span className="text-red-500">*</span>
                 </label>
                 <select
                   {...register('address_type', { required: 'Address type is required' })}
                   className="w-68 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                 >
                   <option value="">Select address type</option>
                   <option value="home">Home Address</option>
                   <option value="work">Work Address</option>
                   <option value="other">Other Address</option>
                 </select>
                 {errors.address_type && (
                   <p className="text-red-500 text-xs mt-1">{errors.address_type.message}</p>
                 )}
               </div>
               <div className="col-span-2">
                 <label className="block text-sm font-medium text-gray-700 mb-1">
                   Country <span className="text-red-500">*</span>
                 </label>
                 <select
                   {...register('country', { required: 'Country is required' })}
                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                 >
                   <option value="UAE">UAE</option>
                   <option value="Saudi Arabia">Saudi Arabia</option>
                   <option value="Kuwait">Kuwait</option>
                   <option value="Qatar">Qatar</option>
                   <option value="Bahrain">Bahrain</option>
                   <option value="Oman">Oman</option>
                 </select>
                 {errors.country && (
                   <p className="text-red-500 text-xs mt-1">{errors.country.message}</p>
                 )}
               </div>
             </div>

             {/* Second Row - Flat/Villa Number, Street Address, Area/Zone/Locality */}
             <div className="grid grid-cols-3 gap-4">
               <div>
                 <label className="block text-sm font-medium text-gray-700 mb-1">
                   Flat/Villa Number <span className="text-red-500">*</span>
                 </label>
                 <input
                   {...register('flat_or_villa_number', { required: 'Flat/Villa number is required' })}
                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                   placeholder="e.g., Apt 123, Villa 45"
                 />
                 {errors.flat_or_villa_number && (
                   <p className="text-red-500 text-xs mt-1">{errors.flat_or_villa_number.message}</p>
                 )}
               </div>
               <div>
                 <label className="block text-sm font-medium text-gray-700 mb-1">
                   Street Address <span className="text-red-500">*</span>
                 </label>
                 <input
                   {...register('address_line_1', { required: 'Street address is required' })}
                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                   placeholder="Enter street address"
                 />
                 {errors.address_line_1 && (
                   <p className="text-red-500 text-xs mt-1">{errors.address_line_1.message}</p>
                 )}
               </div>
               <div>
                 <label className="block text-sm font-medium text-gray-700 mb-1">
                   Area/Zone/Locality <span className="text-red-500">*</span>
                 </label>
                 <input
                   {...register('address_line_2', { required: 'Area/Zone/Locality is required' })}
                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                   placeholder="Enter area/zone/locality"
                 />
                 {errors.address_line_2 && (
                   <p className="text-red-500 text-xs mt-1">{errors.address_line_2.message}</p>
                 )}
               </div>
             </div>

             {/* Third Row - City, State/Province/Region, Zip/Postal Code */}
             <div className="grid grid-cols-3 gap-4">
               <div>
                 <label className="block text-sm font-medium text-gray-700 mb-1">
                   City <span className="text-red-500">*</span>
                 </label>
                 <input
                   {...register('city', { required: 'City is required' })}
                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                   placeholder="Enter city"
                 />
                 {errors.city && (
                   <p className="text-red-500 text-xs mt-1">{errors.city.message}</p>
                 )}
               </div>
               <div>
                 <label className="block text-sm font-medium text-gray-700 mb-1">
                   State/Province/Region <span className="text-red-500">*</span>
                 </label>
                 <input
                   {...register('state', { required: 'State/Province/Region is required' })}
                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                   placeholder="Enter state/province/region"
                 />
                 {errors.state && (
                   <p className="text-red-500 text-xs mt-1">{errors.state.message}</p>
                 )}
               </div>
               <div>
                 <label className="block text-sm font-medium text-gray-700 mb-1">
                   Zip/Postal Code <span className="text-red-500">*</span>
                 </label>
                 <input
                   {...register('postal_code', { required: 'Zip/Postal code is required' })}
                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                   placeholder="Enter zip/postal code"
                 />
                 {errors.postal_code && (
                   <p className="text-red-500 text-xs mt-1">{errors.postal_code.message}</p>
                 )}
               </div>
             </div>

             {/* Fourth Row - Nearest Landmark */}
             <div className="grid grid-cols-1 gap-4">
               <div>
                 <label className="block text-sm font-medium text-gray-700 mb-1">
                   Nearest Landmark
                 </label>
                 <input
                   {...register('building_name')}
                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                   placeholder="Enter nearest landmark"
                 />
               </div>
             </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleCancelAdd}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-6 py-2 bg-[#196c67] text-white rounded-md hover:bg-[#145a56] disabled:opacity-50"
              >
                {isSubmitting ? (editingAddress ? 'Updating...' : 'Adding...') : (editingAddress ? 'Update' : 'Add Address')}
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center w-full">
          <h2 className="text-xl font-semibold text-[#196c67] mr-4">Address</h2>
          <div className="flex-1 h-px bg-gray-300"></div>
        </div>
        <button 
          onClick={() => handleAddAddress('other')}
          className="bg-teal-100 text-teal-700 px-4 py-2 rounded-lg hover:bg-teal-200 transition-colors text-sm ml-4"
        >
          Add Additional Address
        </button>
      </div>

      {/* Inline Add Address Form - This section is now removed since it's handled above */}
      {false && (
        <div className="mb-6 bg-white border-2 border-dashed border-[#196c67] rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-[#196c67]">
              {editingAddress ? 'Edit' : 'Add'} {selectedAddressType} Address
            </h3>
            <button 
              onClick={handleCancelAdd}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
          
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address Type <span className="text-red-500">*</span>
                </label>
                <select
                  {...register('address_type', { required: 'Address type is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                >
                  <option value="">Select address type</option>
                  <option value="home">Home Address</option>
                  <option value="work">Work Address</option>
                  <option value="other">Other Address</option>
                </select>
                {errors.address_type && (
                  <p className="text-red-500 text-xs mt-1">{errors.address_type.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Flat/Villa Number <span className="text-red-500">*</span>
                </label>
                <input
                  {...register('flat_or_villa_number', { required: 'Flat/Villa number is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  placeholder="e.g., Apt 123, Villa 45"
                />
                {errors.flat_or_villa_number && (
                  <p className="text-red-500 text-xs mt-1">{errors.flat_or_villa_number.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Building Name <span className="text-red-500">*</span>
                </label>
                <input
                  {...register('building_name', { required: 'Building name is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  placeholder="Enter building name"
                />
                {errors.building_name && (
                  <p className="text-red-500 text-xs mt-1">{errors.building_name.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 1 <span className="text-red-500">*</span>
                </label>
                <input
                  {...register('address_line_1', { required: 'Address line 1 is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  placeholder="Enter address line 1"
                />
                {errors.address_line_1 && (
                  <p className="text-red-500 text-xs mt-1">{errors.address_line_1.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 2 <span className="text-red-500">*</span>
                </label>
                <input
                  {...register('address_line_2', { required: 'Address line 2 is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  placeholder="Enter address line 2"
                />
                {errors.address_line_2 && (
                  <p className="text-red-500 text-xs mt-1">{errors.address_line_2.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  City <span className="text-red-500">*</span>
                </label>
                <input
                  {...register('city', { required: 'City is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  placeholder="Enter city"
                />
                {errors.city && (
                  <p className="text-red-500 text-xs mt-1">{errors.city.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  State <span className="text-red-500">*</span>
                </label>
                <input
                  {...register('state', { required: 'State is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  placeholder="Enter state"
                />
                {errors.state && (
                  <p className="text-red-500 text-xs mt-1">{errors.state.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Postal Code <span className="text-red-500">*</span>
                </label>
                <input
                  {...register('postal_code', { required: 'Postal code is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  placeholder="Enter postal code"
                />
                {errors.postal_code && (
                  <p className="text-red-500 text-xs mt-1">{errors.postal_code.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Country <span className="text-red-500">*</span>
                </label>
                <select
                  {...register('country', { required: 'Country is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                >
                  <option value="UAE">UAE</option>
                  <option value="Saudi Arabia">Saudi Arabia</option>
                  <option value="Kuwait">Kuwait</option>
                  <option value="Qatar">Qatar</option>
                  <option value="Bahrain">Bahrain</option>
                  <option value="Oman">Oman</option>
                </select>
                {errors.country && (
                  <p className="text-red-500 text-xs mt-1">{errors.country.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  ZIP/Postal Code
                </label>
                <input
                  {...register('zip_code')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  placeholder="Enter ZIP code"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleCancelAdd}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-6 py-2 bg-[#196c67] text-white rounded-md hover:bg-[#145a56] disabled:opacity-50"
              >
                {isSubmitting ? (editingAddress ? 'Updating...' : 'Adding...') : (editingAddress ? 'Update Address' : 'Add Address')}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Add Address Cards */}
      <div className="flex gap-6 mb-6 justify-center">
        {/* Add Billing Address */}
        <div 
          onClick={() => handleAddAddress('billing')}
          className="bg-[#f1fcf9] border-2 border-gray-300 rounded-lg p-8 flex flex-col items-center justify-center h-35 w-70 hover:border-teal-400 transition-colors cursor-pointer shadow-xl"
        >
          <div className="w-10 h-10 border-2 border-teal-600 rounded-lg flex items-center justify-center mb-4">
            <Plus className="w-8 h-8 text-teal-600" />
          </div>
          <span className="text-teal-600 font-medium text-center">Add Billing Address</span>
        </div>

        {/* Add Shipping Address */}
        <div 
          onClick={() => handleAddAddress('shipping')}
          className="bg-[#f1fcf9] border-2 border-gray-300 rounded-lg p-8 flex flex-col items-center justify-center h-35 w-70 hover:border-teal-400 transition-colors cursor-pointer shadow-xl"
        >
          <div className="w-10 h-10 border-2 border-teal-600 rounded-lg flex items-center justify-center mb-4">
            <Plus className="w-8 h-8 text-teal-600" />
          </div>
          <span className="text-teal-600 font-medium text-center">Add Shipping Address</span>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm animate-pulse">
              <div className="bg-gray-200 h-16"></div>
              <div className="p-6 space-y-4">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Address List */}
      {!isLoading && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {addresses.length === 0 ? (
            <div className="col-span-full text-center py-8 text-gray-500">
              No addresses found. Add your first address using the buttons above.
            </div>
          ) : (
            addresses.map((address) => (
              <div key={address.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                {/* Address Header */}
                <div className="bg-[#f1fffc] flex items-center justify-between p-6 mb-0">
                  <h3 className="text-lg font-semibold text-teal-600">
                    {address.is_billing ? 'Billing Address' : address.is_shipping ? 'Shipping Address' : address.address_type || 'Address'}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <button 
                      onClick={() => handleEditAddress(address.id)}
                      className="w-8 h-8 bg-white rounded flex items-center justify-center text-teal-600 hover:text-teal-700 shadow-sm"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button 
                      onClick={() => handleDeleteAddress(address.id)}
                      disabled={deletingId === address.id}
                      className="w-8 h-8 bg-white rounded flex items-center justify-center text-red-500 hover:text-red-700 shadow-sm disabled:opacity-50"
                    >
                      {deletingId === address.id ? (
                        <div className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <Trash2 className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                </div>

                {/* Address Details */}
                <div className="p-6 pt-4 space-y-4 text-sm">
                  <div className="flex items-start">
                    <span className="text-gray-600 w-40 flex-shrink-0">Address Type</span>
                    <span className="mx-3 text-gray-600">:</span>
                    <span className="text-teal-600 font-medium">{address.is_billing ? 'Billing Address' : address.is_shipping ? 'Shipping Address' : address.address_type || 'N/A'}</span>
                  </div>
                  
                  <div className="flex items-start">
                    <span className="text-gray-600 w-40 flex-shrink-0">Flat/Villa Number</span>
                    <span className="mx-3 text-gray-600">:</span>
                    <span className="text-teal-600 font-medium">{address.flat_or_villa_number || 'N/A'}</span>
                  </div>
                  
                  <div className="flex items-start">
                    <span className="text-gray-600 w-40 flex-shrink-0">Building Name</span>
                    <span className="mx-3 text-gray-600">:</span>
                    <span className="text-teal-600 font-medium">{address.building_name || 'N/A'}</span>
                  </div>
                  
                  <div className="flex items-start">
                    <span className="text-gray-600 w-40 flex-shrink-0">Address Line 1</span>
                    <span className="mx-3 text-gray-600">:</span>
                    <span className="text-teal-600 font-medium">{address.address_line_1 || 'N/A'}</span>
                  </div>
                  
                  <div className="flex items-start">
                    <span className="text-gray-600 w-40 flex-shrink-0">Address Line 2</span>
                    <span className="mx-3 text-gray-600">:</span>
                    <span className="text-teal-600 font-medium">{address.address_line_2 || 'N/A'}</span>
                  </div>
                  
                  <div className="flex items-start">
                    <span className="text-gray-600 w-40 flex-shrink-0">City</span>
                    <span className="mx-3 text-gray-600">:</span>
                    <span className="text-teal-600 font-medium">{address.city || 'N/A'}</span>
                  </div>
                  
                  <div className="flex items-start">
                    <span className="text-gray-600 w-40 flex-shrink-0">State</span>
                    <span className="mx-3 text-gray-600">:</span>
                    <span className="text-teal-600 font-medium">{address.state || 'N/A'}</span>
                  </div>
                  
                  <div className="flex items-start">
                    <span className="text-gray-600 w-40 flex-shrink-0">Postal Code</span>
                    <span className="mx-3 text-gray-600">:</span>
                    <span className="text-teal-600 font-medium">{address.postal_code || 'N/A'}</span>
                  </div>
                  
                  <div className="flex items-start">
                    <span className="text-gray-600 w-40 flex-shrink-0">Country</span>
                    <span className="mx-3 text-gray-600">:</span>
                    <span className="text-teal-600 font-medium">{address.country || 'N/A'}</span>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      )}
    </>
  );
}






