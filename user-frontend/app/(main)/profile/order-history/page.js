'use client';

import { useState, useEffect } from 'react';
import { Search } from 'lucide-react';
import DirhamSymbol from '@/components/ui/dirham-symbol';
import { useSWRAxios } from '@/lib/useSWR';

export default function OrderHistory() {
  const { data, error } = useSWRAxios('/client/orders');
  const orders = data?.data?.data || [];

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [activeFilter, setActiveFilter] = useState('All');

  const filterTabs = ['All', 'Shipped', 'Pending', 'Received', 'Cancelled'];

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.order_number.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = activeFilter === 'All' || order.status.fulfillment_display === activeFilter;
    return matchesSearch && matchesFilter;
  });

  const handleSelectOrder = (orderId) => {
    if (selectedOrders.includes(orderId)) {
      setSelectedOrders(selectedOrders.filter(id => id !== orderId));
    } else {
      setSelectedOrders([...selectedOrders, orderId]);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800 border-[#fec831]';
      case 'Shipped':
        return 'bg-blue-100 text-blue-800 border-[#e2eeff] shadow';
      case 'Received':
        return 'bg-[#e2fff9] text-[#196c67] border-[#40c4b6]';
        return 'bg-[#e2fff9] text-green-800 border-[#40c4b6]';
      case 'Cancelled':
        return 'bg-[#ffebf1] text-[#ec6566] border-[#f1b0c4]';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTabColor = (tab) => {
    switch (tab) {
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'Shipped':
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'Received':
        return 'bg-[#e2fff9] text-[#196c67] border-[#40c4b6]';
      case 'Cancelled':
        return 'bg-[#ffebf1] text-[#ec6566] border-[#f1b0c4]';
      case 'All':
        return 'bg-teal-50 text-teal-600 border-teal-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="bg-gray-50 min-h-screen p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-teal-600 mb-6">Order History</h1>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between p-4 space-y-4 lg:space-y-0 relative">
            <div className="flex flex-wrap gap-2 lg:space-x-6 mb-0 mt-0 relative">
              {filterTabs.map((tab, index) => (
                <button
                  key={tab}
                  onClick={() => setActiveFilter(tab)}
                  className={`px-3 py-2 text-xs lg:text-sm font-medium transition-colors rounded-t-lg border-t border-l border-r relative ${
                    activeFilter === tab
                      ? `${getTabColor(tab)} border-b-white z-10`
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50 border-transparent'
                  }`}
                  style={activeFilter === tab ? {
                    marginBottom: '-19px',
                    borderBottomWidth: '2px',
                    borderBottomColor: 'white'
                  } : {}}
                >
                  {tab}
                </button>
              ))}
            </div>
            
            <div className="relative w-full lg:w-64">
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-4 pr-10 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-teal-500 w-full"
              />
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-teal-600" />
            </div>
          </div>
          
          <div className=" w-237 border-b-2 border-teal-500 ml-5"></div>

          <div className="hidden lg:grid grid-cols-4 gap-4 p-4 bg-[#f1fcf9] text-sm font-medium text-gray-700">
            <div>Order ID</div>
            <div>Total Price</div>
            <div>Status</div>
            <div>Action</div>
          </div>

          {error && <p className="p-4 text-red-500">Failed to load orders.</p>}
          {!data && !error && <p className="p-4 text-gray-500">Loading orders...</p>}

          <div className="divide-y divide-gray-200">
            {filteredOrders.map((order) => (
              <div key={order.id} className="p-4 hover:bg-gray-50">
                <div className="lg:hidden space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={selectedOrders.includes(order.id)}
                        onChange={() => handleSelectOrder(order.id)}
                        className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                      />
                      <span className="text-sm text-gray-800 font-medium">
                        {order.order_number}
                      </span>
                    </div>
                    <span className={`inline-flex px-2 py-1 rounded-full border text-xs font-medium ${getStatusColor(order.status.fulfillment_display)}`}>
                      {order.status.fulfillment_display}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <DirhamSymbol className="w-4 h-4 mr-1" />
                      <span className="text-sm text-gray-800 font-medium">
                        {order.pricing.total}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex flex-nowrap space-x-2 overflow-x-auto">
                    <button className="px-4 py-2 text-sm border-2 border-teal-500 text-teal-500 rounded-full hover:bg-teal-50 transition-colors whitespace-nowrap flex-shrink-0">
                      Track Order
                    </button>
                    <button className="px-4 py-2 text-sm border-2 border-gray-400 text-gray-600 rounded-full hover:bg-gray-50 transition-colors whitespace-nowrap flex-shrink-0">
                      Order Again
                    </button>
                    <button className="px-4 py-2 text-sm border-2 border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition-colors whitespace-nowrap flex-shrink-0">
                      See Details
                    </button>
                  </div>
                </div>

                <div className="hidden lg:grid grid-cols-4 gap-4 items-center">
                  <div className="flex items-center space-x-3">
                    <span className="text-sm text-gray-800 font-medium">
                      {order.order_number}
                    </span>
                  </div>
                  
                  <div className="flex items-center">
                    <DirhamSymbol className="w-4 h-4 mr-1" />
                    <span className="text-sm text-gray-800 font-medium">
                      {order.pricing.total}
                    </span>
                  </div>
                  
                  <div>
                    <span className={`inline-flex px-3 py-1 rounded-full border text-xs font-medium ${getStatusColor(order.status.fulfillment_display)}`}>
                      {order.status.fulfillment_display}
                    </span>
                  </div>
                  
                  <div className="flex flex-nowrap space-x-1 overflow-x-auto">
                    <button className="px-2 py-1 text-xs border border-teal-500 text-teal-500 rounded-full bg-teal-50 transition-colors whitespace-nowrap flex-shrink-0">
                      Track Order
                    </button>
                    <button className="px-2 py-1 text-xs border border-gray-400 text-gray-600 rounded-full bg-teal-50 transition-colors whitespace-nowrap flex-shrink-0">
                      Order Again
                    </button>
                    <button className="px-2 py-1 text-xs border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition-colors whitespace-nowrap flex-shrink-0">
                      Details
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="p-4 border-t border-gray-200 flex justify-end">
            <span className="text-sm text-gray-500">
              {`1-${filteredOrders.length} of ${filteredOrders.length}`}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

