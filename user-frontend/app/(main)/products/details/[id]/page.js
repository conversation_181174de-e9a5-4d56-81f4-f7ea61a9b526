"use client";

import { useParams } from 'next/navigation';
import useSWR<PERSON>ustom from '@/lib/useSWR';
import Breadcrumb from '@/components/ui/breadcrumb';
import ProductDetailsClient from '@/components/ui/ProductDetailsClient';
import Faq from '@/components/ui/Faq';
import ProductDetailsPageSkeleton from '@/components/skeletons/ProductDetailsPageSkeleton';

export default function ProductDetailsPage() {
  const { id } = useParams();
  const { data: product, error, isLoading } = useSWRCustom(`/client/products/${id}`);

  if (error) return <div>Failed to load product details.</div>;
  if (isLoading) return <ProductDetailsPageSkeleton />;

  const p = product?.data;
  if (!p) return <div>Product not found.</div>;

  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: p.category?.name_en || 'Category', href: `/products/${p.category?.slug}` },
    { label: p.sub_category?.name_en || 'Sub-category', href: `/products/${p.category?.slug}?sub_category=${p.sub_category?.slug}` },
    { label: p.title_en, href: `/products/details/${p.id}` },
  ];
  
  const productData = {
    id: p.id,
    uuid: p.uuid,
    name: p.title_en,
    title_ar: p.title_ar,
    short_name: p.short_name,
    short_description: p.short_description_en,
    short_description_ar: p.short_description_ar,
    image: p.main_image_url,
    description: p.description_en,
    description_ar: p.description_ar,
    price: parseFloat(p.offer_price || p.regular_price),
    oldPrice: p.offer_price ? parseFloat(p.regular_price) : null,
    regular_price: parseFloat(p.regular_price),
    offer_price: p.offer_price ? parseFloat(p.offer_price) : null,
    discount: p.offer_price ? Math.round(((p.regular_price - p.offer_price) / p.regular_price) * 100) + '%' : null,
    vat_tax: p.vat_tax,
    discount_start_date: p.discount_start_date,
    discount_end_date: p.discount_end_date,
    quantity: 1,
    net_weight: p.net_weight,
    net_weight_unit: p.net_weight_unit,
    servings: p.servings,
    seller: p.vendor?.name_tl_en || p.brand?.name_en || 'Unknown',
    vendor: p.vendor,
    brand: p.brand,
    category: p.category,
    sub_category: p.sub_category,
    expectedDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString(),
    deliveryType: 'Standard delivery available',
    returnPolicy: p.return_policy?.value_en || 'No return policy',
    warranty: p.warranty?.value_en || 'No warranty',
    is_returnable: p.is_returnable,
    storage_conditions: p.storage_conditions,
    country_of_origin: p.country_of_origin,
    bbe_date: p.bbe_date,
    is_vegan: p.is_vegan,
    is_vegetarian: p.is_vegetarian,
    is_halal: p.is_halal,
    user_group: p.user_group,
    package_length: p.package_length,
    package_width: p.package_width,
    package_height: p.package_height,
    package_weight: p.package_weight,
    vendor_sku: p.vendor_sku,
    system_sku: p.system_sku,
    barcode: p.barcode,
    model_number: p.model_number,
    key_ingredients: p.key_ingredients,
    usage_instruction_en: p.usage_instruction_en,
    usage_instruction_ar: p.usage_instruction_ar,
    supplement_image_url: p.supplement_image_url,
    variant_setup: p.variant_setup,
    product_seo: p.product_seo,
    fulfillment: p.fulfillment,
    formulation: p.formulation,
    flavour: p.flavour,
    product_variants: p.product_variants || [],
    inventory: p.inventory,
    product_class: p.product_class,
    sub_class: p.sub_class,
    dietary_need_ids: p.dietary_need_ids,
    allergen_info_ids: p.allergen_info_ids,
    regulatory_product_registration: p.regulatory_product_registration,
    is_variant: p.is_variant,
    is_active: p.is_active,
    is_approved: p.is_approved,
    status: p.status,
    created_at: p.created_at,
    updated_at: p.updated_at,
    images: p.product_media?.map(media => media.path_url) || [p.main_image_url],
    product_media: p.product_media || [],
    related_products: p.related_products || []
  };
  


  return (
    <>
      <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8 py-[24px]">
        <Breadcrumb items={breadcrumbItems} />
        <ProductDetailsClient product={productData} />
      </div>
      <div className="mt-8">
        <Faq
          title="Frequently Asking Question (FAQ)"
          items={p.product_faqs?.length > 0 ? p.product_faqs.map(faq => ({
            question: faq.question_en,
            answer: faq.answer_en
          })) : [
            {
              question: "Do You Provide Cash On Delivery?",
              answer: "Yes, we offer Cash on Delivery (COD) for orders within certain areas. Please check the delivery options during checkout to see if COD is available for your location."
            },
            {
              question: "What is the return policy for this product?",
              answer: p.return_policy?.value_en || "Please contact customer service for return policy details."
            },
            {
              question: "Is this product authentic?",
              answer: "Yes, we guarantee that all products sold on our platform are 100% authentic and sourced directly from authorized distributors or manufacturers."
            }
          ]}
          className="text-sm"
        />
      </div>
    </>
  );
}

