'use client';

import { Suspense } from 'react';
import { useParams } from 'next/navigation';
import CategoryPageSkeleton from '@/components/skeletons/CategoryPageSkeleton';
import ProductListingContent from '@/components/products/ProductListingContent';
import { useCategoryData } from '@/hooks/useCategoryData';

function CategoryProductsPageContent() {
  const params = useParams();
  const categorySlug = params.category;
  const { loading, error } = useCategoryData(categorySlug);

  if (loading) {
    return <CategoryPageSkeleton />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container py-4 flex items-center justify-center">
          <div className="text-center">
            <div className="text-red-500 text-xl mb-4">Error loading category</div>
            <p className="text-gray-600">{error}</p>
            <button 
              onClick={() => window.location.reload()} 
              className="mt-4 px-4 py-2 bg-[#196c67] text-white rounded-lg hover:bg-[#155a56]"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return <ProductListingContent pageType="category" slug={categorySlug} />;
}

export default function CategoryProductsPage() {
  return (
    <Suspense fallback={<CategoryPageSkeleton />}>
      <CategoryProductsPageContent />
    </Suspense>
  );
}