'use client';

import { Suspense } from 'react';
import { useParams } from 'next/navigation';
import ProductListingContent from '@/components/products/ProductListingContent';
import BrandPageSkeleton from '@/components/skeletons/BrandPageSkeleton';

function BrandProductPage() {
  const params = useParams();
  const slug = params.slug;

  return (
    <Suspense fallback={<BrandPageSkeleton />}>
      <ProductListingContent pageType="brand" slug={slug} />
    </Suspense>
  );
}

export default BrandProductPage;