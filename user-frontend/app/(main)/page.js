'use client';

import useS<PERSON>Custom from '@/lib/useSWR';
import HeroSection from '@/components/home/<USER>';
import CategoriesSection from '@/components/home/<USER>';
import DealsSection from '@/components/home/<USER>';
import NewArrivals from '@/components/home/<USER>';
import DiscountedSection from '@/components/home/<USER>';
import BestSellers from '@/components/home/<USER>';
import TrendingProducts from '@/components/home/<USER>';
import Brands from '@/components/home/<USER>';
import RecentlyViewed from '@/components/home/<USER>';
import { Skeleton } from '@/components/ui/skeleton';

export default function Home() {
  const { data: result, error } = useSWRCustom('/client');

  const data = result?.data;

  if (error) {
    console.error('Failed to fetch initial data:', error);
    return <div>Failed to load data.</div>;
  }

  if (!data) {
    return (
      <div className="container">
        <Skeleton className="h-[400px] w-full mb-8" />
        <Skeleton className="h-[300px] w-full mb-8" />
        <Skeleton className="h-[300px] w-full mb-8" />
      </div>
    );
  }

  return (
    <>
      <div className="container">
        <HeroSection banners={data?.banners} />
        <NewArrivals latest_products={data?.latest_products} />
        <DealsSection offer_and_deals={data?.offer_and_deals} />
        <CategoriesSection categories={data?.categories} />
        <BestSellers best_sellers={data?.best_sellers} />
        <DiscountedSection
          discount_products={data?.discount_products}
          discounted_product_banners={data?.discounted_product_banners}
        />
        <TrendingProducts trending_products={data?.trending_products} />
      </div>
      <Brands brands={data?.brands} />
      <div className="container">
        <RecentlyViewed
          recently_viewed_products={data?.recently_viewed_products}
        />
      </div>
    </>
  );
}