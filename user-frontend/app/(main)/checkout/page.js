'use client';

import { useState, useEffect } from 'react';
import { ChevronDown, Plus } from 'lucide-react';
import ShippingInformation from '@/components/checkout/ShippingInformation';
import ShippingMethod from '@/components/checkout/ShippingMethod';
import PaymentInformation from '@/components/checkout/PaymentInformation';
import CheckoutProductList from '@/components/checkout/CheckoutProductList';
import OrderSummary from '@/components/cart/OrderSummary';
import CouponCodeInput from '@/components/cart/CouponCodeInput';
import AddressForm from '@/components/checkout/AddressForm';
import useAuth from '@/hooks/useAuth';
import useCart from '@/hooks/useCart';
import { useSWRAxios } from '@/lib/useSWR';
import api from '@/lib/axios';
import { toast } from 'sonner';

export default function CheckoutPage() {
  const [openSection, setOpenSection] = useState('shipping');
  const [orderPlaced, setOrderPlaced] = useState(false);
  const { user } = useAuth();
  const { cart } = useCart();
  const { data: checkoutData, mutate, error, isLoading } = useSWRAxios(cart ? `/client/checkout/initialize/${cart?.uuid}` : null);
  const [cards, setCards] = useState([]);
  const [selectedAddress, setSelectedAddress] = useState(null);
  const [selectedShippingMethod, setSelectedShippingMethod] = useState(null);
  const [shippingMethods, setShippingMethods] = useState([
    { id: 1, name: 'Standard Shipping', price: 10 },
    { id: 2, name: 'Express Shipping', price: 25 },
  ]);
  const [selectedCard, setSelectedCard] = useState(null);
  const [newCard, setNewCard] = useState(null);
  const [billingAddress, setBillingAddress] = useState(null);
  const [showAddressForm, setShowAddressForm] = useState(false);
  const [isCashOnDelivery, setIsCashOnDelivery] = useState(false);

  // Handle different possible API response structures for addresses
  const addresses = checkoutData?.data?.user_addresses?.shipping || [];
  const availablePaymentMethods = checkoutData?.data?.available_payment_methods || [];

  useEffect(() => {
    if (error) {
      toast.error('Failed to initialize checkout. Please try again later.');
    }
  }, [error]);

  useEffect(() => {
    if(error) return
    if (user) {
      setCards(user.cards || []);
    }
    if (checkoutData && !error && addresses.length === 0) {
      setShowAddressForm(true);
    }
  }, [user, checkoutData, addresses, error]);

  const toggleSection = (section) => {
    setOpenSection(openSection === section ? null : section);
  };

  const handleSelect = (section) => {
    setOpenSection(section);
  };

  const handlePlaceOrder = async () => {
    if (!selectedAddress) {
      toast.error('Please select a shipping address.');
      setOpenSection('shipping');
      return;
    }

    if (!selectedShippingMethod) {
      toast.error('Please select a shipping method.');
      setOpenSection('shippingMethod');
      return;
    }

    if (!selectedCard && !isCashOnDelivery) {
      toast.error('Please select a payment method.');
      setOpenSection('payment');
      return;
    }

    const validationData = {
      payment_method_id: selectedCard?.id,
      shipping_address_id: selectedAddress?.id,
      user_card_id: selectedCard?.id,
      use_shipping_for_billing: true,
      terms_accepted: true
    };

    try {
      const validationResponse = await api.post(`/client/checkout/validate/${cart?.uuid}`, validationData);
      if (validationResponse.data.data.is_valid) {
        const processData = {
          ...validationData,
          terms_accepted: true,
          customer_note: '',
        };
        await api.post(`/client/checkout/process/${cart?.uuid}`, processData);
        setOrderPlaced(true);
      } else {
        toast.error('Checkout validation failed: ' + JSON.stringify(validationResponse.data.data.errors));
      }
    } catch (error) {
      console.error('Error placing order:', error);
      toast.error('An error occurred while placing your order.');
    }
  };

  const handleSaveCard = (card) => {
    setNewCard(card);
  }

  const handleSaveAddress = async (address) => {
    try {
      await api.post('/client/checkout/addresses', address);
      mutate(); // Re-fetch checkout data to get the new address
      setShowAddressForm(false);
    } catch (error) {
      console.error('Error saving address:', error);
      toast.error('An error occurred while saving your address.');
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Checkout</h1>

      {orderPlaced ? (
        <div className="text-center py-20">
          <img src="images/profile/smiley-icon.png" alt="Thank you" className="mx-auto mb-4" width="100" height="100" />
          <h2 className="text-4xl font-bold mb-2">Thank You!</h2>
          <p className="text-gray-600 mb-6">Thank you for shopping with vitamin.ae!</p>
          <a href="/" className="bg-rico-secondary-dark-4 text-white py-2 px-4 rounded-lg hover:bg-teal-700">
            Back to Homepage
          </a>
        </div>
      ) : (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="md:col-span-2">
          {/* Shipping Information */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-4">
              <button
                className="flex-1 flex justify-between items-center p-4 hover:bg-gray-200"
                onClick={() => toggleSection('shipping')}
              >
                <h2 className={`text-lg font-semibold ${openSection !== 'shipping' && selectedAddress ? 'text-rico-secondary-dark-4' : ''}`}>Shipping Information</h2>
                {openSection !== 'shipping' && <div className="flex-grow border-t border-gray-300 ml-4"></div>}
                {openSection !== 'shipping' && selectedAddress && (
                  <ChevronDown className="h-5 w-5 text-rico-secondary-dark-4" />
                )}
              </button>
              {addresses.length > 0 && (
                <div className="flex gap-2 ml-4">
                  <button 
                    className="border border-rico-secondary-light-2 bg-[#949494] text-white hover:bg-[#196c67] px-4 py-2 rounded-lg transition-colors"
                    onClick={() => setShowAddressForm(true)}
                  >
                    Add a New Shipping Address
                  </button>
                </div>
              )}
            </div>
            {openSection === 'shipping' && (
              <div className="p-4">
                {addresses.length === 0 ? (
                  showAddressForm ? (
                    <AddressForm onSave={handleSaveAddress} type="shipping" />
                  ) : (
                    <div className="text-center py-8">
                      <button 
                        className="flex items-center text-teal-600 hover:text-teal-800"
                        onClick={() => setShowAddressForm(true)}
                      >
                        <Plus size={20} className="mr-2" />
                        Add New Address
                      </button>
                    </div>
                  )
                ) : (
                  showAddressForm ? (
                    <AddressForm onSave={handleSaveAddress} type="shipping" />
                  ) : (
                    <ShippingInformation
                      addresses={addresses}
                      onSelectAddress={(address) => {
                        setSelectedAddress(address);
                        handleSelect('shippingMethod');
                      }}
                    />
                  )
                )}
              </div>
            )}
          </div>

          {/* Shipping Method */}
          <div className="mb-6">
            <button
              className="w-full flex justify-between items-center p-4 hover:bg-gray-200"
              onClick={() => toggleSection('shippingMethod')}
            >
              <h2 className={`text-lg font-semibold ${openSection !== 'shippingMethod' && selectedShippingMethod ? 'text-rico-secondary-dark-4' : ''}`}>Shipping Method</h2>
              {openSection !== 'shippingMethod' && <div className="flex-grow border-t border-gray-300 ml-4"></div>}
              {openSection !== 'shippingMethod' && selectedShippingMethod && (
                <ChevronDown className="h-5 w-5 text-rico-secondary-dark-4" />
              )}
            </button>
            {openSection === 'shippingMethod' && (
              <div className="p-4">
                <ShippingMethod
                  methods={shippingMethods}
                  onSelectMethod={(method) => {
                    setSelectedShippingMethod(method);
                    handleSelect('payment');
                  }}
                />
              </div>
            )}
          </div>

          {/* Payment Method */}
          <div className="mb-6">
            <button
              className="w-full flex justify-between items-center p-4 hover:bg-gray-200"
              onClick={() => toggleSection('payment')}
            >
              <h2 className={`text-lg font-semibold ${openSection !== 'payment' && (selectedCard || isCashOnDelivery) ? 'text-rico-secondary-dark-4' : ''}`}>Payment Method</h2>
              {openSection !== 'payment' && <div className="flex-grow border-t border-gray-300 ml-4"></div>}
              {openSection !== 'payment' && (selectedCard || isCashOnDelivery) && (
                <ChevronDown className="h-5 w-5 text-rico-secondary-dark-4" />
              )}
            </button>
            {openSection === 'payment' && (
              <div className="p-4">
                <PaymentInformation
                  cards={availablePaymentMethods}
                  onSelectCard={setSelectedCard}
                  onSaveCard={handleSaveCard}
                  onSaveAddress={handleSaveAddress}
                  onCashOnDelivery={setIsCashOnDelivery}
                />
              </div>
            )}
          </div>
          {/* Product List */}
          <CheckoutProductList cart={cart} />
        </div>
        <div className="md:col-span-1">
          <div className="mb-6">
            <CouponCodeInput page="checkout" />
          </div>
          <OrderSummary isCheckout={true} onPlaceOrder={handlePlaceOrder} isCashOnDelivery={isCashOnDelivery} />
        </div>
      </div>
      )}
    </div>
  );
}