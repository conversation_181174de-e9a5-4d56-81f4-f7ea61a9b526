'use client';

import { Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import BlogSearchContent from '@/components/blog/BlogSearchContent';
import BlogPageSkeleton from '@/components/blog/BlogPageSkeleton';

function BlogSearchPageContent() {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';

  return <BlogSearchContent searchQuery={query} />;
}

export default function BlogSearchPage() {
  return (
    <Suspense fallback={<BlogPageSkeleton />}>
      <BlogSearchPageContent />
    </Suspense>
  );
}
