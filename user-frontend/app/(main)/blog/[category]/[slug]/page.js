'use client';

import { Suspense } from 'react';
import { useParams } from 'next/navigation';
import BlogPostContent from '@/components/blog/BlogPostContent';
import BlogPageSkeleton from '@/components/blog/BlogPageSkeleton';

export default function BlogPostPage() {
  const params = useParams();
  const { category, slug } = params;

  return (
    <Suspense fallback={<BlogPageSkeleton />}>
      <BlogPostContent category={category} slug={slug} />
    </Suspense>
  );
}
