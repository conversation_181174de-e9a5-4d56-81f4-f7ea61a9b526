'use client';

import { Suspense } from 'react';
import { useParams } from 'next/navigation';
import BlogCategoryContent from '@/components/blog/BlogCategoryContent';
import BlogPageSkeleton from '@/components/blog/BlogPageSkeleton';

export default function BlogCategoryPage() {
  const params = useParams();
  const { category } = params;

  return (
    <Suspense fallback={<BlogPageSkeleton />}>
      <BlogCategoryContent categorySlug={category} />
    </Suspense>
  );
}
