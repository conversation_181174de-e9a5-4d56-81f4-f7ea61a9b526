'use client';
import React from 'react';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import CartProductList from '@/components/cart/CartProductList';
import OrderSummary from '@/components/cart/OrderSummary';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import CouponCodeInput from '@/components/cart/CouponCodeInput';
import SimilarProductsSlider from '@/components/ui/SimilarProductsSlider';
import ViewCart from './ViewCart';

const breadcrumbItems = [
  { label: 'Home', href: '/' },
  { label: 'My Cart' },
];


const similarProducts = [
  {
    id: 1,
    name: 'Product Name',
    rating: 4.5,
    reviews: 120,
    price: 480,
    image: '/images/deals/bottle.png',
    unit: '30 Unit',
  },
  {
    id: 2,
    name: 'Product Name',
    rating: 4.5,
    reviews: 120,
    price: 480,
    image: '/images/deals/makeup.png',
    unit: '30 Unit',
  },
  {
    id: 3,
    name: 'Product Name',
    rating: 4.5,
    reviews: 120,
    price: 480,
    image: '/images/deals/pills.png',
    unit: '30 Unit',
  },
  {
    id: 4,
    name: 'Product Name',
    rating: 4.5,
    reviews: 120,
    price: 480,
    image: '/images/deals/bottle.png',
    unit: '30 Unit',
  },
  {
    id: 5,
    name: 'Product Name',
    rating: 4.5,
    reviews: 120,
    price: 480,
    image: '/images/deals/makeup.png',
    unit: '30 Unit',
  },
];

import useCart from '@/hooks/useCart';
import useCartStore from '@/store/cartStore';


const CartPage = () => {
  const { cart, loading, error, clearCart } = useCart();

  const handleRemoveAll = () => {
    // This needs to be implemented in the store
    // For now, we can just clear the cart
    if (window.confirm('Are you sure you want to remove all items from your cart?')) {
      // A `clearCart` function that iterates and removes all items would be needed here.
      // This is a placeholder for that functionality.
      console.log("Clearing cart...");
    }
  };

  return (
    <div className="bg-gray-50 py-2">
      <div className="container mx-auto px-0 sm:px-0 lg:px-0">
        <Breadcrumb items={breadcrumbItems} />
        <div className="flex items-center gap-4 my-2">
          <h2 className="text-md text-rico-secondary-dark-4 font-semibold whitespace-nowrap">My Cart</h2>
          <hr className="w-full" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 items-start">
          <div className="lg:col-span-3">
            <div className="flex items-center justify-between p-3 bg-rico-secondary-light-4 rounded-lg shadow-md mb-2 border">
              <div className="flex items-center">
                <Checkbox id="select-all" className="ml-1" />
                <label
                  htmlFor="select-all"
                  className="ml-3 text-lg font-semibold text-rico-secondary-dark-4"
                >
                  Select All ({cart?.items_count || 0} items)
                </label>
              </div>
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  className="border-rico-secondary-dark-4 text-rico-secondary-dark-4 text-md hover:bg-emerald-50"
                >
                  Back to Shopping
                </Button>
                <Button
                  variant="outline"
                  className="text-[#FF0000] border-[#FF0000] text-md hover:bg-red-50 hover:text-red-700"
                  onClick={handleRemoveAll}
                  disabled={loading || !cart || !cart.vendors || Object.keys(cart.vendors).length === 0}
                >
                  Remove All
                </Button>
              </div>
            </div>
            <CartProductList />
          </div>
          <div className="sticky top-24">
            <CouponCodeInput page="cart" />
            <OrderSummary />
          </div>
        </div>
        <div className="mt-16">
          <SimilarProductsSlider products={similarProducts} />
        </div>
      </div>
    </div>
  );
};

export default CartPage;