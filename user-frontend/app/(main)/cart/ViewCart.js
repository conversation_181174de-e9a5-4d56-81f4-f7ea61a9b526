'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Heart, Trash2 } from 'lucide-react';
import DirhamSymbol from '@/components/ui/dirham-symbol';
import ShippingForm from '@/components/cart/ShippingForm';
import CartItemsList from '@/app/(main)/profile/CartItemsList';

const ViewCart = () => {
  const [cartItems, setCartItems] = useState([
    {
      id: 1,
      vendor: 'Vitamin.ae',
      name: 'Product Name Product Name',
      price: 480,
      originalPrice: 580,
      quantity: 2,
      image: '/images/deals/bottle.png',
      surpriseSale: true
    },
    {
      id: 2,
      vendor: 'Vitamin.ae',
      name: 'Product Name Product Name',
      price: 480,
      quantity: 1,
      image: '/images/deals/bottle.png',
    },
    {
      id: 3,
      vendor: 'Vitamin.ae',
      name: 'Product Name Product Name',
      price: 480,
      originalPrice: 580,
      quantity: 1,
      image: '/images/deals/bottle.png',
      surpriseSale: true
    },
    {
      id: 4,
      vendor: 'Vendor shop name',
      name: 'Product Name Product Name',
      price: 480,
      originalPrice: 580,
      quantity: 1,
      image: '/images/deals/makeup.png',
      surpriseSale: true
    },
    {
      id: 5,
      vendor: 'Vendor shop name',
      name: 'Product Name Product Name',
      price: 480,
      quantity: 1,
      image: '/images/deals/makeup.png',
    },
    {
      id: 6,
      vendor: 'Vendor shop name',
      name: 'Product Name Product Name',
      price: 480,
      originalPrice: 580,
      quantity: 1,
      image: '/images/deals/makeup.png',
      surpriseSale: true
    },
    {
      id: 7,
      vendor: 'Vendor shop name',
      name: 'Product Name Product Name',
      price: 480,
      quantity: 1,
      image: '/images/deals/makeup.png',
    },
    {
      id: 8,
      vendor: 'Vendor shop name',
      name: 'Product Name Product Name',
      price: 480,
      originalPrice: 580,
      quantity: 1,
      image: '/images/deals/makeup.png',
      surpriseSale: true
    }
  ]);

  const [selectedItems, setSelectedItems] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [couponCode, setCouponCode] = useState('');
  const [showShippingForm, setShowShippingForm] = useState(false);
  const [shippingData, setShippingData] = useState(null);

  const groupedByVendor = cartItems.reduce((acc, item) => {
    const vendor = item.vendor || 'Default Vendor';
    if (!acc[vendor]) {
      acc[vendor] = [];
    }
    acc[vendor].push(item);
    return acc;
  }, {});

  const totalItems = cartItems.length;
  const itemsPrice = 480;
  const discount = 180;
  const subtotal = 300;
  const shippingCharge = 80;
  const rewardRedeemed = 0;
  const payableTotal = 480;

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedItems([]);
    } else {
      setSelectedItems(cartItems.map(item => item.id));
    }
    setSelectAll(!selectAll);
  };

  const handleSelectItem = (itemId) => {
    if (selectedItems.includes(itemId)) {
      setSelectedItems(selectedItems.filter(id => id !== itemId));
    } else {
      setSelectedItems([...selectedItems, itemId]);
    }
  };

  const handleQuantityChange = (itemId, newQuantity) => {
    if (newQuantity < 1) return;
    setCartItems(cartItems.map(item => 
      item.id === itemId ? { ...item, quantity: newQuantity } : item
    ));
  };

  const handleRemoveItem = (itemId) => {
    setCartItems(cartItems.filter(item => item.id !== itemId));
    setSelectedItems(selectedItems.filter(id => id !== itemId));
  };

  const handleRemoveAll = () => {
    setCartItems([]);
    setSelectedItems([]);
    setSelectAll(false);
  };

  const handleApplyCoupon = () => {
    console.log('Apply coupon:', couponCode);
  };

  const handleProceedToCheckout = () => {
    setShowShippingForm(true);
  };

  const handleBackToCart = () => {
    setShowShippingForm(false);
  };

  const handleShippingContinue = (data) => {
    setShippingData(data);
    console.log('Shipping data:', data);
    // Here you would typically navigate to payment page or show payment form
    alert('Shipping information saved! Proceeding to payment...');
  };

  // Show shipping form if checkout is initiated
  if (showShippingForm) {
    return (
      <ShippingForm 
        onBack={handleBackToCart}
        onContinue={handleShippingContinue}
        cartItems={cartItems}
        selectedItems={selectedItems}
        handleSelectItem={handleSelectItem}
        handleQuantityChange={handleQuantityChange}
        handleRemoveItem={handleRemoveItem}
        selectAll={selectAll}
        handleSelectAll={handleSelectAll}
        handleRemoveAll={handleRemoveAll}
      />
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen py-4 md:py-6">
      <div className="container mx-auto px-2 md:px-4">
        {/* Header */}
        <div className="flex items-center gap-4 mb-4 md:mb-6">
          <h2 className="text-lg md:text-xl font-semibold text-[#196c67]">My Cart</h2>
          <hr className="flex-1" />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            {/* Controls */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between p-3 md:p-4 bg-[#f1fcf9] rounded-lg mb-1  border gap-3 sm:gap-0 shadow-md">
              <div className="flex items-center">
                <Checkbox 
                  checked={selectAll}
                  onCheckedChange={handleSelectAll}
                />
                <label className="ml-3 text-sm font-medium text-[#196c67]">
                  Select All ({totalItems} items)
                </label>
              </div>
              <div className="flex items-center space-x-2 md:space-x-3">
                <Button variant="outline" size="sm" className="border-[#196c67] text-[#196c67] hover:bg-[#196c67] hover:text-white text-xs md:text-sm">
                  Back to Shopping
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  className="text-red-500 border-red-500 hover:bg-red-50 text-xs md:text-sm"
                  onClick={handleRemoveAll}
                >
                  Remove All
                </Button>
              </div>
            </div>

            {/* Cart Items by Vendor */}
            <CartItemsList 
              cartItems={cartItems}
              selectedItems={selectedItems}
              handleSelectItem={handleSelectItem}
              handleQuantityChange={handleQuantityChange}
              handleRemoveItem={handleRemoveItem}
            />
          </div>

          {/* Order Summary - Sticky on mobile */}
          <div className="lg:sticky lg:top-24 space-y-4">
            {/* Coupon Code */}
            <div className="bg-white p-3 md:p-4 rounded-lg border">
              <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
                <Input
                  type="text"
                  placeholder="Enter your coupon code"
                  value={couponCode}
                  onChange={(e) => setCouponCode(e.target.value)}
                  className="flex-1 text-sm"
                />
                <Button onClick={handleApplyCoupon} className="bg-teal-600 hover:bg-teal-700 text-sm">
                  Apply
                </Button>
              </div>
              <p className="text-xs text-gray-400 mt-1">(if any)</p>
            </div>

            {/* Order Summary */}
            <div className="bg-white p-3 md:p-4 rounded-lg border">
              <h3 className="text-base md:text-lg font-semibold mb-4 text-[#666666]">Order Summary</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between text-gray-500">
                  <span>Total Items ({totalItems})</span>
                  <span className="flex items-center text-black">
                    <DirhamSymbol className="w-3 h-3 mr-1" />
                    {itemsPrice}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Discount</span>
                  <span className="flex items-center text-red-500">
                    -<DirhamSymbol className="w-3 h-3 mr-1" />
                    {discount}
                  </span>
                </div>
                <hr className="border-dashed" />
                <div className="flex justify-between font-medium text-gray-500">
                  <span>Subtotal</span>
                  <span className="flex items-center text-black">
                    <DirhamSymbol className="w-3 h-3 mr-1" />
                    {subtotal}
                  </span>
                </div>
                <div className="flex justify-between text-gray-500">
                  <span>Shipping Charge</span>
                  <span className="flex items-center text-black">
                    <DirhamSymbol className="w-3 h-3 mr-1" />
                    {shippingCharge}
                  </span>
                </div>
                <div className="flex justify-between text-gray-500">
                  <span>Reward Redeemed</span>
                  <span className="flex items-center text-black">
                    <DirhamSymbol className="w-3 h-3 mr-1" />
                    {rewardRedeemed}
                  </span>
                </div>
                <hr className="border-dashed" />
                <div className="flex justify-between text-base md:text-lg font-bold">
                  <span className="text-[#194846]">Payable Total</span>
                  <span className="flex items-center text-gray-500">
                    <DirhamSymbol className="w-4 h-4 mr-1" />
                    {payableTotal}
                  </span>
                </div>
              </div>
              
              <div className="mt-4 p-3 bg-white rounded-lg text-xs border shadow-sm relative">
                <button className="absolute top-2 right-2 text-[#d0025e]">
                  ✕
                </button>
                <div className="flex items-start space-x-3 pr-6">
                  <div className="text-[#FF00FF]">
                    <Image
                      src="/images/profile/brightness_alert.svg"
                      alt="Alert"
                      width={20}
                      height={20}
                    />
                  </div>
                  <div className="flex-1">
                    <span className="text-[#d90136]">
                      Purchase over AED 100 to enjoy free delivery charges.
                    </span>
                    <br />
                    <a href="#" className="text-[#196c67] hover:text-[#007A6E]">
                      Continue Shopping
                    </a>
                  </div>
                </div>
              </div>

              <Button 
                className="w-full mt-4 bg-teal-600 hover:bg-teal-700"
                onClick={handleProceedToCheckout}
              >
                Proceed to Checkout
              </Button>
              <p className="text-center text-xs text-gray-500 mt-2">
              Estimated Delivery Time will show here
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewCart;












