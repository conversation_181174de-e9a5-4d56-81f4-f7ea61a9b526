'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Eye, EyeOff } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import useAuthStore from '../../../store/authStore';
import api from '../../../lib/axios';
import AuthLayout from '../../../components/layout/AuthLayout';

export default function LoginForm() {
  const [showPassword, setShowPassword] = useState(false);
  const { login, isAuthenticated, initializeAuth } = useAuthStore();
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectUrl = searchParams.get('redirectUrl');

  // Initialize auth and redirect if already authenticated
  useEffect(() => {
    initializeAuth();
    if (isAuthenticated) {
      router.push(redirectUrl || '/');
    }
  }, [isAuthenticated, router, initializeAuth, redirectUrl]);
  
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch
  } = useForm({
    defaultValues: {
      email: '',
      password: '',
      keepLoggedIn: false,
      agreeToTerms: true
    }
  });

  const onSubmit = async (data) => {
    try {
      const response = await api.post('/login', {
        email_or_phone: data.email,
        password: data.password,
      });

      const result = response.data;

      if (result.status && result.data) {
        // Store keepLoggedIn preference
        localStorage.setItem('keepLoggedIn', data.keepLoggedIn);
        
        // Use Zustand login method
        const loginSuccess = login(result.data, result.data.token);
        
        if (loginSuccess) {
          console.log('Login successful:', result.message);
          toast.success('Login successful! Welcome back!');
          // Redirect to dashboard
          router.push(redirectUrl || '/');
        } else {
          toast.error('Login failed: Unable to store user data');
        }
      } else {
        console.error('Login failed:', result.message || 'Unknown error');
        toast.error('Login failed: ' + (result.message || 'Please check your credentials'));
      }
    } catch (error) {
      // Prevent the error from bubbling up and showing in console
      console.log('Login error caught:', error.response?.status, error.response?.data?.message);
      
      // Handle specific error cases first
      if (error.response?.status === 403) {
        const errorData = error.response.data;
        
        // Check for account not verified error
        if (errorData?.message === 'Account not verified' || 
            errorData?.errors?.includes('not verified') ||
            errorData?.message?.includes('not verified')) {
          
          toast.error('Account not verified. Redirecting to OTP verification...');
          
          // Redirect to OTP verification with the email/phone
          const contact = data.email;
          setTimeout(() => {
            router.push(`/otp-verification?contact=${encodeURIComponent(contact)}&type=login`);
          }, 2000);
          return;
        }
        
        // Other 403 errors
        toast.error('Access denied: ' + (errorData?.message || 'Please check your credentials'));
        return;
      }
      
      // Handle other error status codes
      const errorMessage = error.response?.data?.message || error.message || 'Network error. Please try again.';
      toast.error('Login failed: ' + errorMessage);
    }
  };

  return (
    <AuthLayout
      title="Welcome to vitamin.ae"
      subtitle=""
    >
                <h2 className="text-xl font-semibold text-center mb-6 text-gray-800">Login</h2>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                  {/* Email Input */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      {...register('email', {
                        required: 'Email is required'
                      })}
                      placeholder="Enter your email..."
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent outline-none transition-all ${
                        errors.email ? 'border-red-500' : 'border-gray-300'
                      }`}
                    />
                    {errors.email && (
                      <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                    )}
                  </div>

                  {/* Password Input */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Password
                    </label>
                    <div className="relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        {...register('password', {
                          required: 'Password is required',
                          minLength: {
                            value: 6,
                            message: 'Password must be at least 6 characters'
                          }
                        })}
                        placeholder="Write Password..."
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent outline-none transition-all pr-10 ${
                          errors.password ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                      >
                        {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                    {errors.password && (
                      <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
                    )}
                  </div>

                  {/* Keep me logged in */}
                  <div className="flex items-center justify-between">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        {...register('keepLoggedIn')}
                        className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                      />
                      <span className="ml-2 text-sm text-gray-600">Keep me logged in</span>
                    </label>
                    <Link href="/auth/forgot-password" className="text-sm text-teal-600 hover:text-teal-700">
                      Forgot Password?
                    </Link>
                  </div>

                  {/* Login Button */}
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full text-white bg-rico-secondary-dark-5 font-semibold py-3 px-4 rounded-lg transition-colors disabled:opacity-50"
                    style={isSubmitting ? { backgroundColor: '#8D8D8D' } : undefined}
                  >
                    {isSubmitting ? 'Logging in...' : 'Login'}
                  </button>
                </form>

                {/* Divider */}
                <div className="my-6 text-center">
                  <span className="text-gray-500 text-sm">OR</span>
                </div>

                {/* Social Login */}
                <div className="text-center mb-4">
                  <p className="text-sm text-gray-600 mb-4">Sign in Using</p>
                  <div className="flex justify-center space-x-4">
                    <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                      <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                      </svg>
                      <span className="text-sm">Google</span>
                    </button>
                    <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                      <svg className="w-5 h-5 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                      </svg>
                      <span className="text-sm">Facebook</span>
                    </button>
                  </div>
                </div>

                {/* Terms and Privacy */}
                {/* <div className="text-center mb-4">
                  <label className="flex items-center justify-center">
                    <input 
                      type="checkbox" 
                      {...register('agreeToTerms', {
                        required: 'You must agree to the terms and privacy policy'
                      })}
                      className={`w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500 ${
                        errors.agreeToTerms ? 'border-red-500' : ''
                      }`}
                    />
                    <span className="ml-2 text-xs text-gray-600">
                      I'm agree to the <Link href="/terms" className="text-teal-600 hover:text-teal-700">Terms of Service</Link> and <Link href="/privacy" className="text-teal-600 hover:text-teal-700">Privacy Policy</Link>
                    </span>
                  </label>
                  {errors.agreeToTerms && (
                    <p className="mt-1 text-sm text-red-600 text-center">{errors.agreeToTerms.message}</p>
                  )}
                </div> */}

                {/* Sign Up Link */}
                <div className="text-center">
                  <p className="text-sm text-gray-600">
                    Do Not have an account? <Link href="/signup" className="text-teal-600 hover:text-teal-700 font-semibold">Create Account !</Link>
                  </p>
                </div>
    </AuthLayout>
  );
}