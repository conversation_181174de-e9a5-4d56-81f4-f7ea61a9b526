'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ArrowLeft, Eye, EyeOff } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import api from '../../../lib/axios';
import AuthLayout from '../../../components/layout/AuthLayout';

export default function SignupPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch
  } = useForm({
    defaultValues: {
      firstName: '',
      email_or_phone: '',
      password: '',
      password_confirmation: '',
      agreeToTerms: false
    }
  });

  const onSubmit = async (data) => {
    try {
      // Prepare data for API
      const apiData = {
        name: data.firstName.trim(),
        email_or_phone: data.email_or_phone.trim(),
        password: data.password,
        password_confirmation: data.password_confirmation
      };

      const response = await api.post('/register', apiData);
      const result = response.data;

      if (result.status) {
        console.log('Registration successful:', result.message);
        
        // Show success message
        toast.success(result.message || 'Registration successful! An OTP has been sent to your provided contact.');
        
        // Redirect to OTP verification page with user contact info
        const contactInfo = data.email_or_phone.trim();
        router.push(`/otp-verification?contact=${encodeURIComponent(contactInfo)}&type=registration`);
      } else {
        console.error('Registration failed:', result.message || 'Unknown error');
        toast.error('Registration failed: ' + (result.message || 'Please try again'));
      }
    } catch (error) {
      console.error('Registration error:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Network error. Please try again.';
      toast.error('Registration failed: ' + errorMessage);
    }
  };

  return (
    <AuthLayout
      title="Welcome to vitamin.ae"
      subtitle=""
    >
                <h2 className="text-xl font-semibold text-center mb-6" style={{color: '#196C67'}}>Create Account</h2>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                  {/* Full Name Field */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      {...register('firstName', {
                        required: 'Full name is required'
                      })}
                      placeholder="Enter your full name"
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent outline-none transition-all ${
                        errors.firstName ? 'border-red-500' : 'border-gray-300'
                      }`}
                    />
                    {errors.firstName && (
                      <p className="mt-1 text-sm text-red-600">{errors.firstName.message}</p>
                    )}
                  </div>

                  {/* Email or Phone Input */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email or Phone Number *
                    </label>
                    <input
                      type="text"
                      {...register('email_or_phone', {
                        required: 'Email or phone number is required'
                      })}
                      placeholder="Enter your email or phone number"
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent outline-none transition-all ${
                        errors.email_or_phone ? 'border-red-500' : 'border-gray-300'
                      }`}
                    />
                    {errors.email_or_phone && (
                      <p className="mt-1 text-sm text-red-600">{errors.email_or_phone.message}</p>
                    )}
                  </div>



                  {/* Password Input */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Password *
                    </label>
                    <div className="relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        {...register('password', {
                          required: 'Password is required',
                          minLength: {
                            value: 6,
                            message: 'Password must be at least 6 characters'
                          }
                        })}
                        placeholder="Create Password..."
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent outline-none transition-all pr-10 ${
                          errors.password ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                      >
                        {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                    {errors.password && (
                      <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
                    )}
                  </div>

                  {/* Confirm Password Input */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Confirm Password *
                    </label>
                    <div className="relative">
                      <input
                        type={showConfirmPassword ? 'text' : 'password'}
                        {...register('password_confirmation', {
                          required: 'Please confirm your password',
                          validate: (value) => {
                            const password = watch('password');
                            return password === value || 'Passwords do not match';
                          }
                        })}
                        placeholder="Confirm Password..."
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent outline-none transition-all pr-10 ${
                          errors.password_confirmation ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                      >
                        {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                    {errors.password_confirmation && (
                      <p className="mt-1 text-sm text-red-600">{errors.password_confirmation.message}</p>
                    )}
                  </div>

                  {/* Terms and Privacy */}
                  <div className="flex items-start">
                    <input
                      type="checkbox"
                      {...register('agreeToTerms', {
                        required: 'You must agree to the Terms of Service and Privacy Policy'
                      })}
                      className={`w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500 mt-1 ${
                        errors.agreeToTerms ? 'border-red-500' : ''
                      }`}
                    />
                    <span className="ml-2 text-sm text-gray-600">
                      I agree to the <Link href="/terms" className="text-teal-600 hover:text-teal-700">Terms of Service</Link> and <Link href="/privacy" className="text-teal-600 hover:text-teal-700">Privacy Policy</Link>
                    </span>
                  </div>
                  {errors.agreeToTerms && (
                    <p className="mt-1 text-sm text-red-600">{errors.agreeToTerms.message}</p>
                  )}

                  {/* Signup Button */}
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors disabled:opacity-50"
                  >
                    {isSubmitting ? 'Creating Account...' : 'Create Account'}
                  </button>
                </form>

                {/* Divider */}
                <div className="my-6 text-center">
                  <span className="text-gray-500 text-sm">OR</span>
                </div>

                {/* Social Signup */}
                <div className="text-center mb-4">
                  <p className="text-sm text-gray-600 mb-4">Sign up Using</p>
                  <div className="flex justify-center space-x-4">
                    <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                      <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                      </svg>
                      <span className="text-sm">Google</span>
                    </button>
                    <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                      <svg className="w-5 h-5 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                      </svg>
                      <span className="text-sm">Facebook</span>
                    </button>
                  </div>
                </div>

                {/* Login Link */}
                <div className="text-center">
                  <p className="text-sm text-gray-600">
                    Already have an account? <Link href="/login" className="text-teal-600 hover:text-teal-700 font-semibold">Sign In !</Link>
                  </p>
                </div>
    </AuthLayout>
  );
}