'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import api from '../../../lib/axios';
import useAuthStore from '../../../store/authStore';
import AuthLayout from '../../../components/layout/AuthLayout';

function OTPVerificationContent() {
  const [resendCooldown, setResendCooldown] = useState(0);
  const router = useRouter();
  const searchParams = useSearchParams();
  const contact = searchParams.get('contact');
  const type = searchParams.get('type'); // 'registration' or 'login'
  const { login } = useAuthStore();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setValue,
    getValues
  } = useForm({
    defaultValues: {
      otp1: '',
      otp2: '',
      otp3: '',
      otp4: '',
      otp5: '',
      otp6: ''
    }
  });

  // Countdown timer for resend OTP
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => setResendCooldown(resendCooldown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const onSubmit = async (data) => {
    try {
      const otp = `${data.otp1}${data.otp2}${data.otp3}${data.otp4}${data.otp5}${data.otp6}`;
      const response = await api.post('/verify-otp', {
        otp: otp,
        email_or_phone: contact
      });

      const result = response.data;

      if (result.status) {
        console.log('OTP verification successful:', result.message);
        
        if (type === 'registration') {
          // Registration OTP verified - user can now login
          toast.success('Account verified successfully! Please login with your credentials.');
          router.push('/login');
        } else {
          // Handle other OTP verification types (like login OTP)
          if (type === 'login') {
            // For login OTP, after verification user should be able to login
            toast.success('Account verified successfully! You can now login.');
            router.push('/login');
          } else {
            // For other types, check if we get token data in response
            if (result.data && result.data.token) {
              const userData = result.data.user || {};
              const tokenData = result.data.token;
              
              // Login user with received token
              const loginSuccess = login(userData, tokenData);
              
              if (loginSuccess) {
                toast.success('Login successful! Welcome!');
                router.push('/dashboard');
              } else {
                toast.error('Login failed. Please try again.');
                router.push('/login');
              }
            } else {
              toast.success('OTP verified successfully!');
              router.push('/dashboard');
            }
          }
        }
      } else {
        console.error('OTP verification failed:', result.message || 'Unknown error');
        toast.error('OTP verification failed: ' + (result.message || 'Invalid OTP'));
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Network error. Please try again.';
      toast.error('OTP verification failed: ' + errorMessage);
    }
  };

  const handleResendOTP = async () => {
    try {
      setResendCooldown(60); // 60 seconds cooldown
      
      const response = await api.post('/resend-otp', {
        email_or_phone: contact
      });

      const result = response.data;

      if (result.status) {
        toast.success('OTP has been resent to your contact.');
      } else {
        toast.error('Failed to resend OTP: ' + (result.message || 'Please try again'));
        setResendCooldown(0); // Reset cooldown on failure
      }
    } catch (error) {
      console.error('Resend OTP error:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Network error. Please try again.';
      toast.error('Failed to resend OTP: ' + errorMessage);
      setResendCooldown(0); // Reset cooldown on failure
    }
  };

  return (
    <AuthLayout
      title="Welcome to vitamin.ae"
      subtitle=""
    >
              <div className="text-center mb-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-2">Verify Your Account</h2>
                <p className="text-sm text-gray-600">
                  We've sent a verification code to
                </p>
                <p className="text-sm font-medium text-gray-800">{contact}</p>
              </div>

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* OTP Input */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 text-center">
                    Enter Verification Code
                  </label>
                  <div className="flex justify-center space-x-3 mb-4">
                    {[1, 2, 3, 4, 5, 6].map((index) => (
                      <input
                        key={index}
                        type="text"
                        {...register(`otp${index}`, {
                          required: 'Required',
                          pattern: {
                            value: /^\d$/,
                            message: 'Must be a digit'
                          }
                        })}
                        maxLength="1"
                        className={`w-12 h-12 text-center text-lg font-semibold border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent outline-none transition-all ${
                          errors[`otp${index}`] ? 'border-red-500' : 'border-gray-300'
                        }`}
                        onInput={(e) => {
                          const value = e.target.value;
                          if (value && /^\d$/.test(value)) {
                            // Move to next input
                            const nextInput = document.querySelector(`input[name="otp${index + 1}"]`);
                            if (nextInput) {
                              nextInput.focus();
                            }
                          }
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Backspace' && !e.target.value) {
                            // Move to previous input on backspace
                            const prevInput = document.querySelector(`input[name="otp${index - 1}"]`);
                            if (prevInput) {
                              prevInput.focus();
                            }
                          }
                        }}
                      />
                    ))}
                  </div>
                  {(errors.otp1 || errors.otp2 || errors.otp3 || errors.otp4 || errors.otp5 || errors.otp6) && (
                    <p className="mt-1 text-sm text-red-600 text-center">Please enter all 6 digits</p>
                  )}
                </div>

                {/* Verify Button */}
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors disabled:opacity-50"
                >
                  {isSubmitting ? 'Verifying...' : 'Verify Account'}
                </button>
              </form>

              {/* Resend OTP */}
              <div className="mt-6 text-center">
                <p className="text-sm text-gray-600 mb-2">
                  Didn't receive the code?
                </p>
                {resendCooldown > 0 ? (
                  <p className="text-sm text-gray-500">
                    Resend OTP in {resendCooldown} seconds
                  </p>
                ) : (
                  <button
                    onClick={handleResendOTP}
                    className="text-sm text-teal-600 hover:text-teal-700 font-semibold"
                  >
                    Resend OTP
                  </button>
                )}
              </div>

    </AuthLayout>
  );
}

// Loading component for Suspense fallback
function OTPVerificationLoading() {
  return (
    <AuthLayout
      title="Welcome to vitamin.ae"
      subtitle=""
    >
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Verify Your Account</h2>
        <p className="text-sm text-gray-600">Loading...</p>
      </div>
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
          <div className="h-12 bg-gray-200 rounded"></div>
        </div>
        <div className="h-12 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </AuthLayout>
  );
}

// Main page component with Suspense boundary
export default function OTPVerificationPage() {
  return (
    <Suspense fallback={<OTPVerificationLoading />}>
      <OTPVerificationContent />
    </Suspense>
  );
}