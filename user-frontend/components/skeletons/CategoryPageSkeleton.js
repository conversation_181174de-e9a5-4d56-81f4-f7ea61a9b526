'use client';

export default function CategoryPageSkeleton() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container py-4">
        {/* Breadcrumb Skeleton */}
        <div className="mb-6">
          <div className="inline-flex items-end gap-px p-3 bg-[#f1fcf9] rounded-3xl shadow-[0px_2px_8px_2px_#0000001a] border-none">
            <div className="flex items-center space-x-2">
              <div className="h-4 bg-gray-300 rounded w-12 animate-pulse"></div>
              <div className="h-4 bg-gray-300 rounded w-1 animate-pulse"></div>
              <div className="h-4 bg-gray-300 rounded w-20 animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Banner Skeleton */}
        <div className="relative overflow-visible mb-8">
          <div className="relative w-full h-48 lg:h-64 bg-gray-300 rounded-lg animate-pulse"></div>
        </div>

        {/* Category Pills Skeleton */}
        <div className="mb-8">
          <div className="text-center mb-4">
            <div className="h-8 bg-gray-300 rounded w-64 mx-auto mb-2 animate-pulse"></div>
            <div className="h-4 bg-gray-300 rounded w-48 mx-auto animate-pulse"></div>
          </div>
          <div className="flex flex-wrap items-start justify-center gap-3 p-4 bg-white rounded-lg border border-solid border-[#f4f4f4] shadow-[0px_2px_4px_#0000001a]">
            {Array(8).fill(0).map((_, index) => (
              <div key={index} className="h-10 lg:h-12 bg-gray-300 rounded-full w-24 lg:w-32 animate-pulse"></div>
            ))}
          </div>
        </div>

        {/* Gradient Divider */}
        <div className="w-full h-2.5 mb-8 bg-gradient-to-r from-pink-400 via-orange-400 to-cyan-300 rounded-full"></div>

        {/* Category Header Skeleton */}
        <div className="w-full flex flex-col lg:flex-row items-start lg:items-center justify-between mb-8 gap-4">
          <div className="flex items-center">
            <div className="bg-gray-300 px-6 py-3 rounded-lg h-12 w-48 animate-pulse"></div>
          </div>
          <div className="bg-gray-300 rounded-lg h-10 w-32 animate-pulse"></div>
        </div>

        <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
          {/* Left Sidebar - Filters Skeleton */}
          <div className="w-full lg:w-80 xl:w-72 flex-shrink-0">
            <div className="bg-white rounded-xl border border-[#e9f0f4] overflow-hidden shadow-sm">
              {/* Filter Header */}
              <div className="flex items-center justify-between px-4 py-3 bg-[#f1fcf9] border-b border-[#e9f0f4] mb-2">
                <div className="h-4 bg-gray-300 rounded w-16 animate-pulse"></div>
                <div className="h-4 bg-gray-300 rounded w-20 animate-pulse"></div>
              </div>

              {/* Filter Sections Skeleton */}
              <div className="divide-y divide-[#f2f6f8]">
                {Array(5).fill(0).map((_, sectionIndex) => (
                  <div key={sectionIndex} className="p-4">
                    <div className="h-5 bg-gray-300 rounded w-24 mb-3 animate-pulse"></div>
                    <div className="space-y-2">
                      {Array(4).fill(0).map((_, itemIndex) => (
                        <div key={itemIndex} className="flex items-center space-x-2">
                          <div className="w-4 h-4 bg-gray-300 rounded animate-pulse"></div>
                          <div className="h-4 bg-gray-300 rounded flex-1 animate-pulse"></div>
                          <div className="h-4 bg-gray-300 rounded w-8 animate-pulse"></div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Side - Products Grid Skeleton */}
          <div className="flex-1 min-w-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-3 sm:gap-4">
              {Array(8).fill(0).map((_, index) => (
                <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 animate-pulse">
                  <div className="bg-gray-300 h-48 rounded-lg mb-4"></div>
                  <div className="bg-gray-300 h-4 rounded mb-2"></div>
                  <div className="bg-gray-300 h-4 rounded w-3/4 mb-2"></div>
                  <div className="bg-gray-300 h-6 rounded w-1/2 mb-2"></div>
                  <div className="bg-gray-300 h-8 rounded w-full"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}