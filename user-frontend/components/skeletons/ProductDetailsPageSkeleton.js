import { Skeleton } from "@/components/ui/skeleton";

export default function ProductDetailsPageSkeleton() {
  return (
    <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8 py-[24px]">
      {/* Breadcrumb skeleton */}
      <div className="flex items-center space-x-2 mb-4">
        <Skeleton className="h-4 w-[50px]" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-[80px]" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-[100px]" />
      </div>

      <div className="md:grid grid-cols-12 gap-4 pt-[12px]">
        {/* Image Gallery Skeleton */}
        <div className="col-span-4">
          <Skeleton className="h-[400px] w-full rounded-lg" />
          <div className="flex space-x-2 mt-2">
            <Skeleton className="h-20 w-20 rounded-lg" />
            <Skeleton className="h-20 w-20 rounded-lg" />
            <Skeleton className="h-20 w-20 rounded-lg" />
            <Skeleton className="h-20 w-20 rounded-lg" />
          </div>
        </div>

        {/* Product Details Skeleton */}
        <div className="col-span-5">
          <Skeleton className="h-8 w-3/4 mb-4" />
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-5/6 mb-6" />
          <Skeleton className="h-6 w-1/4 mb-4" />
        </div>

        {/* Price and Info Skeleton */}
        <div className="col-span-3 space-y-4">
          <div className="p-4 border rounded-lg">
            <Skeleton className="h-8 w-1/2 mb-2" />
            <Skeleton className="h-6 w-1/4 mb-4" />
            <Skeleton className="h-10 w-full mb-2" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="p-4 border rounded-lg">
            <Skeleton className="h-5 w-full mb-2" />
            <Skeleton className="h-5 w-3/4" />
          </div>
          <div className="p-4 border rounded-lg">
            <Skeleton className="h-5 w-full mb-2" />
            <Skeleton className="h-5 w-3/4" />
          </div>
          <div className="p-4 border rounded-lg">
            <Skeleton className="h-5 w-1/2 mb-2" />
            <Skeleton className="h-8 w-full" />
          </div>
        </div>
      </div>

      {/* Similar Products, Info, and Reviews Sections Skeleton */}
      <div className="col-span-12 mt-10">
        <Skeleton className="h-8 w-1/4 mb-4" />
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <Skeleton className="h-48 w-full" />
          <Skeleton className="h-48 w-full" />
          <Skeleton className="h-48 w-full" />
          <Skeleton className="h-48 w-full" />
          <Skeleton className="h-48 w-full" />
          <Skeleton className="h-48 w-full" />
        </div>
      </div>
      <div className="col-span-12 mt-10">
        <Skeleton className="h-40 w-full" />
      </div>
      <div className="col-span-12 mt-10">
        <Skeleton className="h-40 w-full" />
      </div>
    </div>
  );
} 
