'use client';

export default function FiltersSkeleton() {
  return (
    <div className="w-full lg:w-80 xl:w-72 flex-shrink-0">
      <div className="bg-white rounded-xl border border-[#e9f0f4] overflow-hidden shadow-sm">
        {/* Filter Header */}
        <div className="flex items-center justify-between px-4 py-3 bg-[#f1fcf9] border-b border-[#e9f0f4] mb-2">
          <div className="h-4 bg-gray-300 rounded w-12 animate-pulse"></div>
          <div className="h-4 bg-gray-300 rounded w-16 animate-pulse"></div>
        </div>

        {/* Loading state */}
        <div className="px-4 py-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#196c67] mx-auto mb-2"></div>
          <div className="h-4 bg-gray-300 rounded w-24 mx-auto animate-pulse"></div>
        </div>
      </div>
    </div>
  );
}