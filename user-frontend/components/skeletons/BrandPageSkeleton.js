export default function BrandPageSkeleton() {
  return (
    <div className="bg-white animate-pulse">
      <div className="container py-4">
        <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="flex flex-wrap gap-2 mb-8">
          {Array.from({ length: 28 }).map((_, index) => (
            <div key={index} className="h-8 bg-gray-200 rounded w-12"></div>
          ))}
        </div>

        <div>
          <div className="h-6 bg-gray-200 rounded w-1/6 mb-4"></div>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {Array.from({ length: 12 }).map((_, index) => (
              <div key={index} className="flex flex-col items-center">
                <div className="w-24 h-24 bg-gray-200 rounded-md mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-20"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}