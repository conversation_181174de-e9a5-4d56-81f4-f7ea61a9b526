'use client';

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
export default function CategoryBanner({ mainCategory, bannerData }) {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [failedImages, setFailedImages] = useState(new Set());
  
  // Use API banner data and filter active slides
  const slides = bannerData?.items?.filter(slide => 
    slide.is_active && 
    slide.media_path_url && 
    !failedImages.has(slide.id)
  ) || [];

  const handleImageError = (slideId) => {
    console.error('Image failed to load for slide ID:', slideId);
    setFailedImages(prev => new Set([...prev, slideId]));
  };

  // Auto-slide functionality
  useEffect(() => {
    if (slides.length > 1) {
      const timer = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % slides.length);
      }, 5000); // Change slide every 5 seconds

      return () => clearInterval(timer);
    }
  }, [slides.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  // Don't render if no slides
  if (!slides || slides.length === 0) {
    return (
      <div className="relative w-full h-48 lg:h-64 mb-8 bg-gradient-to-r from-[#196c67] to-[#40c4b6] rounded-lg flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl lg:text-3xl font-bold mb-2">{mainCategory}</h2>
          <p className="text-lg opacity-90">Explore our premium collection</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-48 lg:h-64 mb-2">
      {/* Slider Images */}
      <div className="relative w-full h-full rounded-lg overflow-hidden">
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 transition-opacity duration-500 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <Image
              className="w-full h-full object-cover"
              alt={slide.alt_text || slide.title_en || 'Banner image'}
              src={slide.media_path_url}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 100vw, 100vw"
              onError={() => handleImageError(slide.id)}
            />
          </div>
        ))}
      </div>

      {/* Navigation Arrows - only show if more than 1 slide */}
      {slides.length > 1 && (
        <>
          <Button
            variant="ghost"
            size="icon"
            onClick={prevSlide}
            className="absolute -left-7 top-1/2 transform -translate-y-1/2 bg-[#F1FCF9] shadow-lg hover:bg-[#e2fff9] text-[#196c67] rounded-full transition-all duration-300 w-14 h-14 z-20 p-4"
          >
            <ChevronLeft className="w-6 h-6" />
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={nextSlide}
            className="absolute -right-7 top-1/2 transform -translate-y-1/2 bg-[#F1FCF9] shadow-lg hover:bg-[#e2fff9] text-[#196c67] rounded-full transition-all duration-300 w-14 h-14 z-20 p-4"
          >
            <ChevronRight className="w-6 h-6" />
          </Button>

          {/* Slide Indicators */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide
                    ? 'bg-white scale-110'
                    : 'bg-white/50 hover:bg-white/75'
                }`}
              />
            ))}
          </div>
        </>
      )}
    </div>
  );
}