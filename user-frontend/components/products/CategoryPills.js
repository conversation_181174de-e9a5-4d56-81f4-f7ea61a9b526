'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp } from 'lucide-react';
import CategoryBanner from './CategoryBanner';
export default function CategoryPills({ categories, selectedCategory, onCategorySelect, onSubcategorySelect, mainCategory = "Vitamins, Minerals, Herbs & Supplements", bannerData }) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const handleCategoryClick = (categoryKey, categoryId) => {
    const newCategory = selectedCategory === categoryKey ? '' : categoryKey;
    onCategorySelect(prev => ({
      ...prev,
      category: newCategory,
      priceRange: prev?.priceRange || [0, 1000],
      rating: prev?.rating || 0,
      brands: prev?.brands || [],
      userGroups: prev?.userGroups || [],
      offers: prev?.offers || [],
      countries: prev?.countries || [],
      storage: prev?.storage || [],
      priceRangeId: prev?.priceRangeId || '',
      inStock: prev?.inStock || false,
      onSale: prev?.onSale || false,
      booleanFilters: prev?.booleanFilters || { vegan: false, vegetarian: false, halal: false }
    }));
    
    if (onSubcategorySelect && categoryId && newCategory) {
      onSubcategorySelect(categoryId);
    }
  };

  return (
    <div className="mb-8">
       <div className="relative overflow-visible">
         <CategoryBanner
           mainCategory={mainCategory}
           bannerData={bannerData}
         />
       </div>
      {/* Main Category Title */}
      <div className="text-left mb-4">
        <div className="flex items-center">
          <div className="inline-block bg-[#1D6F6C] text-white font-semibold px-8 py-2 tracking-wide hover:bg-[#155a58] focus:outline-none focus:ring-2 focus:ring-[#1D6F6C] focus:ring-opacity-50 min-w-[280px] text-center" 
               style={{
                 clipPath: 'polygon(5% 0%, 95% 0%, 100% 50%, 95% 100%, 5% 100%, 0% 50%)'
               }}>
            <h2 className="text-lg md:text-xl font-semibold">
              {mainCategory}
            </h2>
          </div>
          <div className="flex-1 h-0.5 ml-4 border-dashed border-t-2 border-[#40C4B6]"></div>
        </div>
      </div>

      {/* Subcategory Pills */}
      <div className="flex flex-wrap items-start justify-start gap-3 p-4 bg-white rounded-lg border border-solid border-[#f4f4f4] shadow-[0px_2px_4px_#0000001a]">
        {categories?.slice(0, isExpanded ? categories.length : 12).map((category) => (
          <Button
            key={category.id}
            variant="ghost"
            onClick={() => handleCategoryClick(category.key, category.id)}
            className={`h-10 lg:h-12 px-3 lg:px-5 py-2 rounded-full transition-all flex items-center gap-2 ${
              selectedCategory === category.key
                ? "bg-[#e2fff9] border border-solid border-[#40c4b6] shadow-[0px_2px_4px_#00000014] text-[#196c67] font-medium"
                : "bg-[#feffff] shadow-[0px_2px_4px_#0000001a] text-neutral-600 font-normal hover:bg-[#f8f9fa]"
            }`}
          >
            <span className="text-xs lg:text-sm">{category.name}</span>
          </Button>
        ))}
        
        {/* Show More/Less Pill */}
        {categories && categories.length > 12 && (
          <Button
            variant="ghost"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-10 lg:h-12 px-3 lg:px-5 py-2 rounded-full transition-all flex items-center gap-2 bg-[#f0f9ff] border border-solid border-[#3b82f6] shadow-[0px_2px_4px_#00000014] text-[#1d4ed8] font-medium hover:bg-[#dbeafe]"
          >
            {isExpanded ? (
              <>
                <span className="text-xs lg:text-sm">Show Less</span>
                <ChevronUp className="h-3 w-3" />
              </>
            ) : (
              <>
                <span className="text-xs lg:text-sm">+{categories.length - 12} More</span>
                <ChevronDown className="h-3 w-3" />
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  );
}