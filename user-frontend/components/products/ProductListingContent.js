'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import ProductFilters from '@/components/products/ProductFilters';
import ProductGrid from '@/components/products/ProductGrid';
import ProductHeader from '@/components/products/ProductHeader';
import CategoryPills from '@/components/products/CategoryPills';
import ClassPills from '@/components/products/ClassPills';
import Breadcrumb from '@/components/ui/breadcrumb';
import ProductGridSkeleton from '@/components/skeletons/ProductGridSkeleton';
import SubClassPills from '@/components/products/SubClassPills';
import { useCategoryData } from '@/hooks/useCategoryData';
import { useSubcategoryData } from '@/hooks/useSubcategoryData';
import { useClassData } from '@/hooks/useClassData';
import { useBrandProductData } from '@/hooks/useBrandProductData';
import { useProductFilters } from '@/hooks/useProductFilters';
import { useProducts } from '@/hooks/useProducts';
import { useURLManager } from '@/hooks/useURLManager';

export default function ProductListingContent({ pageType, slug }) {
  const searchParams = useSearchParams();
  const sortBy = searchParams.get('sortBy') || 'name';

  // Custom hooks
  const { categoryData, loading: categoryLoading, error: categoryError } = useCategoryData(pageType === 'category' ? slug : null);
  const { brandData, loading: brandLoading, error: brandError } = useBrandProductData(pageType === 'brand' ? slug : null);

  const {
    subcategoryData,
    subcategoryLoading,
    fetchSubcategoryData,
    clearSubcategoryData
  } = useSubcategoryData();
  const {
    classData,
    classLoading,
    fetchClassData,
    clearClassData
  } = useClassData();

  const {
    filters,
    setFilters,
    dynamicFilters,
    filtersLoading,
    selectedSubcategoryId,
    setSelectedSubcategoryId,
    shouldFetchProducts,
    setShouldFetchProducts,
    fetchProductFilters
  } = useProductFilters();

  const {
    products,
    productsLoading,
    totalProducts,
    currentPage,
    setCurrentPage,
    fetchProducts,
    buildFilterParams
  } = useProducts();

  const { updateURL } = useURLManager();

  // State for selected class
  const [selectedClass, setSelectedClass] = useState(filters.class || '');
  const [selectedClassId, setSelectedClassId] = useState(null);
  const [selectedSubClass, setSelectedSubClass] = useState('');
  const [selectedSubClassId, setSelectedSubClassId] = useState(null);

  // Transform subcategories for CategoryPills component
  const transformSubcategories = (subcats) => {
    return subcats.map(subcat => ({
      id: subcat.id,
      name: subcat.name_en,
      key: subcat.slug,
      icon: subcat.icon_url || '/images/categories/vitamins.svg',
    }));
  };

  // Handle subcategory selection
  const handleSubcategorySelect = (subcategoryId) => {
    setSelectedSubcategoryId(subcategoryId);
    setSelectedClass('');
    setSelectedClassId(null);
    if (categoryData?.data?.category?.id) {
      fetchProductFilters('category', categoryData.data.category.id, subcategoryId);
      const selectedSubcat = transformedSubcategories.find(sub => sub.id === subcategoryId);
      if (selectedSubcat) {
        fetchSubcategoryData(selectedSubcat.key);
        const newFilters = { ...filters, subcategory: selectedSubcat.key, class: '' };
        setFilters(newFilters);
        updateURL(newFilters, sortBy, dynamicFilters);
      }
    }
  };

  // Handle class selection
  const handleClassSelect = (classSlug, classId) => {
    setSelectedClass(classSlug);
    setSelectedClassId(classId);
    fetchClassData(classSlug);
    const newFilters = { ...filters, class: classSlug };
    setFilters(newFilters);
    updateURL(newFilters, sortBy, dynamicFilters);
  };

  const handleSubClassSelect = (subClassSlug, subClassId) => {
    setSelectedSubClass(subClassSlug);
    setSelectedSubClassId(subClassId);
    const newFilters = { ...filters, subclass: subClassSlug };
    setFilters(newFilters);
    updateURL(newFilters, sortBy, dynamicFilters);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    updateURL({ ...newFilters, subcategory: filters.subcategory, class: filters.class }, sortBy, dynamicFilters);
  };

  // Handle sort changes
  const handleSortChange = (newSortBy) => {
    updateURL(filters, newSortBy, dynamicFilters);
  };

  // Clear all filters
  const clearFilters = () => {
    const clearedFilters = {
      category: '',
      priceRange: [0, 1000],
      rating: 0,
      brands: [],
      userGroups: [],
      offers: [],
      countries: [],
      storage: [],
      priceRangeId: '',
      inStock: false,
      onSale: false,
      booleanFilters: { vegan: false, vegetarian: false, halal: false }
    };
    setFilters(clearedFilters);
    if (pageType === 'category') {
        setSelectedClass('');
        setSelectedClassId(null);
        clearSubcategoryData();
        clearClassData();
    }
    updateURL(clearedFilters, 'name', dynamicFilters);
  };

  // Fetch initial filters
  useEffect(() => {
    if (pageType === 'category' && categoryData?.data?.category?.id) {
      fetchProductFilters('category', categoryData.data.category.id, selectedSubcategoryId);
    } else if (pageType === 'brand' && brandData?.data?.id) {
      fetchProductFilters('brand', brandData.data.id);
    }
  }, [categoryData, brandData, selectedSubcategoryId, pageType]);

  // Effects for category page
  useEffect(() => {
    if (pageType === 'category') {
        if (filters.class) {
            fetchClassData(filters.class);
            clearSubcategoryData();
        } else if (filters.subcategory) {
            const subcategory = categoryData?.data?.subcategories.find(sub => sub.slug === filters.subcategory);
            if (subcategory) {
                setSelectedSubcategoryId(subcategory.id);
                fetchSubcategoryData(filters.subcategory);
            }
            clearClassData();
        } else {
            clearSubcategoryData();
            clearClassData();
        }
        setSelectedClass(filters.class || '');
        setSelectedSubClass(filters.subclass || '');
    }
  }, [filters.subcategory, filters.class, filters.subclass, categoryData, pageType]);

  useEffect(() => {
    if (pageType === 'category' && subcategoryData) {
      setSelectedSubcategoryId(subcategoryData.data.category.id);
    }
  }, [subcategoryData, pageType]);

  useEffect(() => {
    if (pageType === 'category' && classData) {
      setSelectedClassId(classData.data.class.id);
    }
  }, [classData, pageType]);

  // Fetch products
  useEffect(() => {
    const entityId = pageType === 'category' ? categoryData?.data?.category?.id : brandData?.data?.id;
    if (entityId && dynamicFilters && (shouldFetchProducts || filters || sortBy || currentPage)) {
      const filterParams = buildFilterParams({
        pageType,
        entityId,
        subcategoryId: selectedSubcategoryId,
        classId: selectedClassId,
        subClassId: selectedSubClassId,
        filters,
        sortBy,
        dynamicFilters,
      });
      fetchProducts(filterParams);
      if (shouldFetchProducts) setShouldFetchProducts(false);
    }
  }, [shouldFetchProducts, filters, sortBy, currentPage, categoryData, brandData, dynamicFilters, selectedSubcategoryId, pageType, selectedClassId, selectedSubClassId]);

  const loading = pageType === 'category' ? categoryLoading : brandLoading;
  const error = pageType === 'category' ? categoryError : brandError;
  const data = pageType === 'category' ? categoryData?.data : brandData?.data;

  if (loading || error || !data) {
    return null; // Parent component handles these states
  }

  const breadcrumb = data.breadcrumb;
  const category = pageType === 'category' ? data.category : null;
  const subcategories = pageType === 'category' ? data.subcategories : [];
  const transformedSubcategories = pageType === 'category' ? transformSubcategories(subcategories) : [];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container py-4">
        <div className="mb-2">
          <div className="inline-flex items-end bg-[#f1fcf9] rounded-3xl shadow-[0px_2px_8px_2px_#0000001a] border-none">
            <Breadcrumb items={breadcrumb?.map(item => ({
              label: item.name_en,
              href: item.url
            })) || []} />
          </div>
        </div>

        {pageType === 'category' && (
          <>
            {filters.class ? (
              <SubClassPills
                subClasses={classData?.data?.class?.sub_classes || []}
                selectedSubClass={selectedSubClass}
                onSubClassSelect={handleSubClassSelect}
                className={classData?.data?.class?.name_en}
                loading={classLoading}
              />
            ) : filters.subcategory ? (
              <ClassPills
                classes={category.classes || subcategoryData?.data?.category?.classes || []}
                selectedClass={selectedClass}
                onClassSelect={handleClassSelect}
                subcategoryName={subcategoryData?.data?.category?.name_en || category.name_en}
                loading={subcategoryLoading}
              />
            ) : (
              <CategoryPills
                categories={transformedSubcategories}
                selectedCategory={filters.category}
                onCategorySelect={handleFilterChange}
                onSubcategorySelect={handleSubcategorySelect}
                mainCategory={category.name_en}
                bannerData={category.banner}
              />
            )}
          </>
        )}

        <div className="w-full h-2.5 mb-8 bg-gradient-to-r from-pink-400 via-orange-400 to-cyan-300 rounded-full" />

        <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
          <ProductFilters 
            filters={filters}
            onFilterChange={handleFilterChange}
            dynamicFilters={dynamicFilters}
            filtersLoading={filtersLoading}
          />

          <div className="flex-1 min-w-0">
            <ProductHeader 
              totalProducts={totalProducts}
              sortBy={sortBy}
              setSortBy={handleSortChange}
              onClearFilters={clearFilters}
              compact={true}
              currentPage={currentPage}
              perPage={10}
            />
            {productsLoading ? (
              <ProductGridSkeleton count={6} />
            ) : products.length > 0 ? (
              <ProductGrid 
                products={products}
                totalProducts={totalProducts}
                currentPage={currentPage}
                totalPages={Math.ceil(totalProducts / 10)}
                onPageChange={setCurrentPage}
              />
            ) : (
              <div className="text-center py-12">
                <div className="text-gray-500 text-xl mb-4">No products found</div>
                <p className="text-gray-600">Try adjusting your filters to see more products.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}