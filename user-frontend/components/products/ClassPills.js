'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp } from 'lucide-react';

export default function ClassPills({ 
  classes, 
  selectedClass, 
  onClassSelect, 
  subcategoryName = "Classes",
  loading = false 
}) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const handleClassClick = (classKey, classId) => {
    // Only allow one class to be selected at a time
    const newClass = selectedClass === classKey ? '' : classKey;
    onClassSelect(newClass, classId);
  };

  if (loading) {
    return (
      <div className="mb-8">
        <div className="text-center mb-4">
          <div className="h-6 bg-gray-300 rounded w-48 mx-auto mb-2 animate-pulse"></div>
          <div className="h-4 bg-gray-300 rounded w-32 mx-auto animate-pulse"></div>
        </div>
        <div className="flex flex-wrap items-start justify-center gap-3 p-4 bg-white rounded-lg border border-solid border-[#f4f4f4] shadow-[0px_2px_4px_#0000001a]">
          {Array(6).fill(0).map((_, index) => (
            <div key={index} className="h-10 lg:h-12 bg-gray-300 rounded-full w-24 lg:w-32 animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!classes || classes.length === 0) {
    return null;
  }

  return (
    <div className="mb-8">
      {/* Class Banner with Centered Title */}
      <div className="relative w-full h-48 mb-8 rounded-lg overflow-hidden">
        <Image
          src="/images/hero-1.png"
          alt="Classes Banner"
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 flex items-center justify-center bg-black/30">
          <h2 className="text-3xl font-bold text-white drop-shadow-lg">
            {subcategoryName}
          </h2>
        </div>
      </div>

      {/* Class Pills */}
      <div className="flex flex-wrap items-start justify-start gap-3 p-4 bg-white rounded-lg border border-solid border-[#f4f4f4] shadow-[0px_2px_4px_#0000001a]">
        {classes?.slice(0, isExpanded ? classes.length : 12).map((classItem) => (
          <Button
            key={classItem.id}
            variant="ghost"
            onClick={() => handleClassClick(classItem.slug, classItem.id)}
            className={`h-10 lg:h-12 px-3 lg:px-5 py-2 rounded-full transition-all flex items-center gap-2 ${
              selectedClass === classItem.slug
                ? "bg-[#e2fff9] border border-solid border-[#40c4b6] shadow-[0px_2px_4px_#00000014] text-[#196c67] font-medium"
                : "bg-[#feffff] shadow-[0px_2px_4px_#0000001a] text-neutral-600 font-normal hover:bg-[#f8f9fa]"
            }`}
          >
            <span className="text-xs lg:text-sm">{classItem.name_en}</span>
          </Button>
        ))}
        
        {/* Show More/Less Pill */}
        {classes && classes.length > 12 && (
          <Button
            variant="ghost"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-10 lg:h-12 px-3 lg:px-5 py-2 rounded-full transition-all flex items-center gap-2 bg-[#f0f9ff] border border-solid border-[#3b82f6] shadow-[0px_2px_4px_#00000014] text-[#1d4ed8] font-medium hover:bg-[#dbeafe]"
          >
            {isExpanded ? (
              <>
                <span className="text-xs lg:text-sm">Show Less</span>
                <ChevronUp className="h-3 w-3" />
              </>
            ) : (
              <>
                <span className="text-xs lg:text-sm">+{classes.length - 12} More</span>
                <ChevronDown className="h-3 w-3" />
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  );
}