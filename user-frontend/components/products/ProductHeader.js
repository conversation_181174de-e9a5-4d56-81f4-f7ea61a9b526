'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SlidersHorizontal } from 'lucide-react';
// Define sort options directly
const sortOptionsData = [
  { value: 'name', label: 'Name (A-Z)' },
  { value: 'price-low', label: 'Price: Low to High' },
  { value: 'price-high', label: 'Price: High to Low' },
  { value: 'newest', label: 'Newest First' },
  { value: 'rating', label: 'Highest Rated' }
];

export default function ProductHeader({ 
  totalProducts, 
  sortBy, 
  setSortBy, 
  onClearFilters,
  compact = false,
  currentPage = 1,
  perPage = 10
}) {
  const startItem = ((currentPage - 1) * perPage) + 1;
  const endItem = Math.min(currentPage * perPage, totalProducts);

  if (compact) {
    return (
      <div className="flex items-center justify-between w-full mb-4">
        {/* Left side - Products count */}
        <div className="text-sm text-gray-600">
          <span className="font-medium">
            {startItem}-{endItem} of {totalProducts} products
          </span>
        </div>
        
        {/* Right side - Sort dropdown */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Sort by:</span>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[180px] bg-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {sortOptionsData.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        {/* Left side - Results count */}
        <div className="flex items-center gap-4">
          <h1 className="text-xl font-semibold text-gray-900">
            Products
          </h1>
          <span className="text-sm text-gray-600">
            {totalProducts} {totalProducts === 1 ? 'product' : 'products'} found
          </span>
        </div>

        {/* Right side - Controls */}
        <div className="flex items-center gap-3">
          {/* Clear Filters Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={onClearFilters}
            className="hidden sm:flex items-center gap-2"
          >
            <SlidersHorizontal className="w-4 h-4" />
            Clear Filters
          </Button>

          {/* Sort Dropdown */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600 hidden sm:block">Sort by:</span>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {sortOptionsData.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

        </div>
      </div>

      {/* Mobile Clear Filters */}
      <div className="sm:hidden mt-3 pt-3 border-t border-gray-200">
        <Button
          variant="outline"
          size="sm"
          onClick={onClearFilters}
          className="w-full flex items-center justify-center gap-2"
        >
          <SlidersHorizontal className="w-4 h-4" />
          Clear All Filters
        </Button>
      </div>
    </div>
  );
}