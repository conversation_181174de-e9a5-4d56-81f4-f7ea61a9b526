'use client';

import { useState } from 'react';
import { Plus, Edit } from 'lucide-react';
import PaymentForm from './PaymentForm';

export default function PaymentInformation({ cards, onSelectCard, onSaveCard, onSaveAddress, onCashOnDelivery }) {
  const [selectedCard, setSelectedCard] = useState(null);
  const [showPaymentForm, setShowPaymentForm] = useState(false);

  const handleSelectCard = (card) => {
    setSelectedCard(card);
    if (onSelectCard) {
      onSelectCard(card);
    }
  };

  const getCardIcon = (type) => {
    switch (type) {
      case 'card':
        return <img src="/images/profile/visaCard.png" alt="Card" className="w-12 h-8" />;
      case 'cod':
        return <img src="/images/cod.png" alt="Cash on Delivery" className="w-12 h-8" />;
      default:
        return <img src="/images/profile/masterCard.png" alt="Payment" className="w-12 h-8" />;
    }
  };

  return (
    <div>
      {cards.length === 0 || showPaymentForm ? (
        <PaymentForm onSave={() => setShowPaymentForm(false)} onSaveCard={onSaveCard} onSaveAddress={onSaveAddress} />
      ) : (
        <>
          {cards.map((card) => (
            <div
              key={card.id}
              className={`border rounded-lg p-4 mb-4 cursor-pointer ${
                selectedCard?.id === card.id ? 'border-teal-500' : ''
              }`}
              onClick={() => handleSelectCard(card)}
            >
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  {getCardIcon(card.cardType)}
                  <div className="ml-4">
                    <h3 className="font-semibold">{card.name}</h3>
                    <p className="text-sm text-gray-600">{card.description}</p>
                  </div>
                </div>
                <button className="text-blue-500 hover:text-blue-700">
                  <Edit size={16} />
                </button>
              </div>
            </div>
          ))}
          <div className="flex gap-4">
            <button
              className="flex items-center text-teal-600 hover:text-teal-800"
              onClick={() => setShowPaymentForm(true)}
            >
              <Plus size={20} className="mr-2" />
              Add New Card
            </button>
            <button
              className="flex items-center text-teal-600 hover:text-teal-800"
              onClick={() => onCashOnDelivery?.(true)}
            >
              <Plus size={20} className="mr-2" />
              Cash on Delivery
            </button>
          </div>
        </>
      )}
    </div>
  );
}