'use client';

import { useState } from 'react';
export default function ShippingMethod({ methods, onSelectMethod }) {
  const [selectedMethod, setSelectedMethod] = useState(null);

  const handleSelectMethod = (method) => {
    setSelectedMethod(method);
    if (onSelectMethod) {
      onSelectMethod(method);
    }
  };

  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-[#196c67]">Shipping Method</h3>
        <div className="flex gap-2">
          <button className="border border-[#196c67] text-[#196c67] bg-[#196c67] text-white px-4 py-2 rounded-md text-sm">
            Change
          </button>
        </div>
      </div>

      <div className="space-y-4">
        {methods.map((method, index) => (
          <div
            key={method.id}
            className={`border-2 rounded-lg overflow-hidden cursor-pointer transition-colors ${
              selectedMethod?.id === method.id 
                ? 'border-[#196c67] bg-[#f0fdf4]' 
                : 'border-gray-200 hover:border-[#196c67]'
            }`}
            onClick={() => handleSelectMethod(method)}
          >
            {/* Header inside card */}
            <div className="flex items-center justify-between p-3 bg-teal-50 border-b">
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-4 h-4">
                  <div className={`w-3 h-3 rounded-full border-2 ${
                    selectedMethod?.id === method.id 
                      ? 'border-[#196c67] bg-[#196c67]' 
                      : 'border-gray-300'
                  }`}>
                    {selectedMethod?.id === method.id && (
                      <div className="w-full h-full rounded-full bg-white scale-50"></div>
                    )}
                  </div>
                </div>
                <img src="/images/dhl.png" alt="DHL" className="w-12 h-8" />
                <h4 className="font-medium text-gray-900">{method.name}</h4>
              </div>
             
            </div>
            
            {/* Content */}
            <div className="p-4 bg-white">
              <div className="space-y-1 text-sm text-gray-600">
                <p>Time: {method.time}</p>
                <p>Cost: ${method.cost}</p>
                <p className="text-gray-500">{method.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
