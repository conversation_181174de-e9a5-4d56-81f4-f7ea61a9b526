'use client';

import { useF<PERSON>, Controller } from 'react-hook-form';
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import api from '@/lib/axios';
import { toast } from 'sonner';

export default function AddressForm({ onSave, editingAddress = null, type = 'shipping' }) {
  const { register, handleSubmit, control, formState: { errors, isSubmitting } } = useForm({
    defaultValues: {
      address_type: editingAddress?.address_type || '',
      country: editingAddress?.country || 'UAE',
      flat_or_villa_number: editingAddress?.flat_or_villa_number || '',
      building_name: editingAddress?.building_name || '',
      address_line_1: editingAddress?.address_line_1 || '',
      address_line_2: editingAddress?.address_line_2 || '',
      state: editingAddress?.state || '',
      city: editingAddress?.city || '',
      postal_code: editingAddress?.postal_code || '',
      is_default: editingAddress?.is_default || false,
      is_shipping: editingAddress?.is_shipping || type === 'shipping',
      is_billing: editingAddress?.is_billing || type === 'billing'
    }
  });

  const onSubmit = (data) => {
    const addressData = {
      ...data,
      is_shipping: type === 'shipping',
      is_billing: type === 'billing',
    };
    onSave(addressData);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="border border-dashed border-[#196c67] rounded-lg p-4 my-4">
      <div className="space-y-6">
        {/* Address Type and Country Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="address-type" className="text-sm font-medium text-gray-700 mb-2 block">Address Type</Label>
            <Controller
              name="address_type"
              control={control}
              rules={{ required: 'Address type is required' }}
              render={({ field }) => (
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <SelectTrigger id="address-type" className="h-12">
                    <SelectValue placeholder="Select address type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="home">Home</SelectItem>
                    <SelectItem value="office">Office</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              )}
            />
            {errors.address_type && <p className="text-red-500 text-xs mt-1">{errors.address_type.message}</p>}
          </div>
          <div>
            <Label htmlFor="country" className="text-sm font-medium text-gray-700 mb-2 block">Country</Label>
            <Controller
              name="country"
              control={control}
              rules={{ required: 'Country is required' }}
              render={({ field }) => (
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <SelectTrigger id="country" className="h-12">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="UAE">UAE</SelectItem>
                    <SelectItem value="Saudi Arabia">Saudi Arabia</SelectItem>
                    <SelectItem value="Kuwait">Kuwait</SelectItem>
                    <SelectItem value="Qatar">Qatar</SelectItem>
                  </SelectContent>
                </Select>
              )}
            />
            {errors.country && <p className="text-red-500 text-xs mt-1">{errors.country.message}</p>}
          </div>
        </div>

        {/* Address Details Row */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <Label htmlFor="flat-villa" className="text-sm font-medium text-gray-700 mb-2 block">Flat/Villa Number,</Label>
            <Input
              id="flat-villa"
              className={`h-12 ${errors.flat_or_villa_number ? 'border-red-500' : ''}`}
              {...register('flat_or_villa_number', { required: 'Flat/Villa number is required' })}
              placeholder="Enter flat/villa number"
            />
            {errors.flat_or_villa_number && <p className="text-red-500 text-xs mt-1">{errors.flat_or_villa_number.message}</p>}
          </div>
          <div>
            <Label htmlFor="building-name" className="text-sm font-medium text-gray-700 mb-2 block">Building Name</Label>
            <Input
              id="building-name"
              className={`h-12 ${errors.building_name ? 'border-red-500' : ''}`}
              {...register('building_name', { required: 'Building name is required' })}
              placeholder="Enter building name"
            />
            {errors.building_name && <p className="text-red-500 text-xs mt-1">{errors.building_name.message}</p>}
          </div>
          <div>
            <Label htmlFor="street-address" className="text-sm font-medium text-gray-700 mb-2 block">Street Address:</Label>
            <Input
              id="street-address"
              className={`h-12 ${errors.address_line_1 ? 'border-red-500' : ''}`}
              {...register('address_line_1', { required: 'Street address is required' })}
              placeholder="Enter street address"
            />
            {errors.address_line_1 && <p className="text-red-500 text-xs mt-1">{errors.address_line_1.message}</p>}
          </div>
        </div>

        {/* Second Address Row */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <Label htmlFor="area" className="text-sm font-medium text-gray-700 mb-2 block">
              Area/Zone/Locality <span className="text-red-500">*</span>
            </Label>
            <Input
              id="area"
              className={`h-12 ${errors.address_line_2 ? 'border-red-500' : ''}`}
              {...register('address_line_2', { required: 'Area/Zone/Locality is required' })}
              placeholder="Enter area/zone/locality"
            />
            {errors.address_line_2 && (
              <p className="text-red-500 text-xs mt-1">{errors.address_line_2.message}</p>
            )}
          </div>
          <div>
            <Label htmlFor="city" className="text-sm font-medium text-gray-700 mb-2 block">City</Label>
            <Input
              id="city"
              className={`h-12 ${errors.city ? 'border-red-500' : ''}`}
              {...register('city', { required: 'City is required' })}
              placeholder="Enter city"
            />
            {errors.city && <p className="text-red-500 text-xs mt-1">{errors.city.message}</p>}
          </div>
          <div>
            <Label htmlFor="state" className="text-sm font-medium text-gray-700 mb-2 block">State/Province/Region</Label>
            <Input
              id="state"
              className={`h-12 ${errors.state ? 'border-red-500' : ''}`}
              {...register('state', { required: 'State/Province/Region is required' })}
              placeholder="Enter state/province/region"
            />
            {errors.state && <p className="text-red-500 text-xs mt-1">{errors.state.message}</p>}
          </div>
        </div>

        {/* Location Details Row */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 items-end">
          <div>
            <Label htmlFor="zip-code" className="text-sm font-medium text-gray-700 mb-2 block">Zip/Postal Code</Label>
            <Input
              id="zip-code"
              className={`h-12 ${errors.postal_code ? 'border-red-500' : ''}`}
              {...register('postal_code', { required: 'Postal code is required' })}
              placeholder="Enter zip/postal code"
            />
            {errors.postal_code && <p className="text-red-500 text-xs mt-1">{errors.postal_code.message}</p>}
          </div>
          <div className="md:col-span-2 flex justify-end">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-[#196c67] hover:bg-[#145a56] text-white px-8 py-2 rounded-md w-full md:w-auto"
            >
              {isSubmitting ? 'Saving...' : 'Save and Continue'}
            </Button>
          </div>
        </div>
      </div>
    </form>
  );
}
