'use client';

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CalendarIcon } from 'lucide-react';
import AddressForm from './AddressForm';

export default function PaymentForm({ onSave, onSaveCard, onSaveAddress }) {
  const [showAddressForm, setShowAddressForm] = useState(false);
  const [cardDetails, setCardDetails] = useState({
    nickName: '',
    cardType: '',
    nameOnCard: '',
    cardNumber: '',
    expiryDate: '',
  });

  const handleInputChange = (e) => {
    const { id, value } = e.target;
    setCardDetails((prev) => ({ ...prev, [id]: value }));
  };

  const handleSave = () => {
    if (onSaveCard) {
      onSaveCard(cardDetails);
    }
    if (onSave) {
      onSave();
    }
  };

  return (
    <div className="border rounded-lg p-6 my-4">
      <h3 className="text-lg font-semibold mb-4">Add a Payment Method</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="card-nick-name">Card Nick Name</Label>
          <Input id="nickName" placeholder="Enter Card Nick Name" value={cardDetails.nickName} onChange={handleInputChange} />
        </div>
        <div>
          <Label htmlFor="card-type">Card Type</Label>
          <Select onValueChange={(value) => setCardDetails((prev) => ({ ...prev, cardType: value }))}>
            <SelectTrigger id="card-type">
              <SelectValue placeholder="Default" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="visa">Visa</SelectItem>
              <SelectItem value="mastercard">Mastercard</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="name-on-card">Name on Card</Label>
          <Input id="nameOnCard" placeholder="Enter Name on Card" value={cardDetails.nameOnCard} onChange={handleInputChange} />
        </div>
        <div>
          <Label htmlFor="card-number">Card Number</Label>
          <Input id="cardNumber" placeholder="1234 1234 1234 1234" value={cardDetails.cardNumber} onChange={handleInputChange} />
        </div>
        <div className="relative">
          <Label htmlFor="expiry-date">Expiry Date</Label>
          <Input id="expiryDate" placeholder="MM/YY" value={cardDetails.expiryDate} onChange={handleInputChange} />
          <CalendarIcon className="absolute right-3 top-9 h-5 w-5 text-gray-400" />
        </div>
      </div>
      <div className="mt-6">
        <div className="flex items-center space-x-2">
          <Checkbox id="billing-address-same" defaultChecked onCheckedChange={(checked) => setShowAddressForm(!checked)} />
          <Label htmlFor="billing-address-same">Shipping and billing addresses are the same.</Label>
        </div>
      </div>

      {showAddressForm && (
        <div className="mt-4">
            <AddressForm onSave={onSaveAddress} type="billing" />
        </div>
      )}

      <div className="flex justify-between items-center mt-6">
        <div className="flex items-center space-x-2">
          <Checkbox id="save-card" />
          <Label htmlFor="save-card">Save for Future Use</Label>
        </div>
        <div className="flex space-x-4">
          <Button variant="outline" onClick={onSave}>Discard</Button>
          <Button onClick={handleSave}>Save and Continue</Button>
        </div>
      </div>
    </div>
  );
}