  'use client';

import { useState } from 'react';
import { Edit, Trash2 } from 'lucide-react';
import { Button } from "@/components/ui/button";
import api from '@/lib/axios';
import { toast } from 'sonner';

export default function AddressList({ addresses, onEdit, onDelete, selectedAddressId, onSelectAddress }) {
  const [deletingId, setDeletingId] = useState(null);
  const [loadingEdit, setLoadingEdit] = useState(null);

  const handleDelete = async (addressId) => {
    if (!confirm('Are you sure you want to delete this address?')) return;
    
    setDeletingId(addressId);
    try {
      await api.delete(`/client/user-addresses/${addressId}`);
      toast.success('Address deleted successfully');
      onDelete?.(addressId);
    } catch (error) {
      toast.error('Failed to delete address');
      console.error('Delete address error:', error);
    } finally {
      setDeletingId(null);
    }
  };

  const handleEdit = async (addressId) => {
    setLoadingEdit(addressId);
    try {
      const response = await api.get(`/client/user-addresses/${addressId}`);
      const addressData = response.data.data || response.data;
      onEdit?.(addressData);
    } catch (error) {
      toast.error('Failed to load address details');
      console.error('Edit address error:', error);
    } finally {
      setLoadingEdit(null);
    }
  };

  if (!addresses || addresses.length === 0) {
    return null;
  }

  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-[#196c67]">Shipping Information</h3>
        <div className="flex gap-2">
          <Button 
            variant="outline"
            className="border-[#196c67] text-[#196c67] hover:bg-[#196c67] hover:text-white"
          >
            Add a New Shipping Address
          </Button>
          <Button 
            className="bg-[#196c67] hover:bg-[#145a56] text-white"
          >
            Change
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        {addresses.map((address, index) => (
          <div 
            key={address.id}
            className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
              selectedAddressId === address.id 
                ? 'border-[#196c67] bg-[#f0fdf4]' 
                : 'border-gray-200 hover:border-[#196c67]'
            }`}
            onClick={() => onSelectAddress?.(address.id)}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3">
                <div className="flex items-center justify-center w-4 h-4 mt-1">
                  <div className={`w-3 h-3 rounded-full border-2 ${
                    selectedAddressId === address.id 
                      ? 'border-[#196c67] bg-[#196c67]' 
                      : 'border-gray-300'
                  }`}>
                    {selectedAddressId === address.id && (
                      <div className="w-full h-full rounded-full bg-white scale-50"></div>
                    )}
                  </div>
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-1">
                    Address {index + 1}
                  </h4>
                  <div className="space-y-1 text-sm text-gray-600">
                    <p className="font-medium">User Name</p>
                    <p className="text-gray-800">
                      Work Place | Flat No: {address.flat_or_villa_number}, {address.building_name}, {address.address_line_1}, {address.address_line_2}, {address.city}, {address.state} {address.postal_code}, {address.country}
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2 ml-4">
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEdit(address.id);
                  }}
                  disabled={loadingEdit === address.id}
                  className="w-8 h-8 bg-white border border-gray-300 rounded flex items-center justify-center text-[#196c67] hover:text-[#145a56] hover:border-[#196c67] disabled:opacity-50"
                >
                  {loadingEdit === address.id ? (
                    <div className="w-4 h-4 border-2 border-[#196c67] border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <Edit className="w-4 h-4" />
                  )}
                </button>
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDelete(address.id);
                  }}
                  disabled={deletingId === address.id}
                  className="w-8 h-8 bg-white border border-gray-300 rounded flex items-center justify-center text-red-500 hover:text-red-700 hover:border-red-500 disabled:opacity-50"
                >
                  {deletingId === address.id ? (
                    <div className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <Trash2 className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
