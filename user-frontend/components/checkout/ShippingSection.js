'use client';

import { useState, useEffect } from 'react';
import AddressForm from './AddressForm';
import AddressList from './AddressList';
import { useSWRAxios } from '@/lib/useSWR';

export default function ShippingSection() {
  const { data: addressData, mutate } = useSWRAxios('/client/user-addresses');
  const [selectedAddressId, setSelectedAddressId] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState(null);

  const addresses = Array.isArray(addressData?.data?.data) 
    ? addressData.data.data 
    : Array.isArray(addressData?.data) 
    ? addressData.data 
    : Array.isArray(addressData) 
    ? addressData 
    : [];

  const handleAddressSaved = (newAddress) => {
    mutate(); // Refresh addresses
    setShowAddForm(false);
    setEditingAddress(null);
    setSelectedAddressId(newAddress.id);
  };

  const handleEditAddress = (address) => {
    setEditingAddress(address);
    setShowAddForm(true);
  };

  const handleDeleteAddress = (addressId) => {
    mutate(); // Refresh addresses
    if (selectedAddressId === addressId) {
      setSelectedAddressId(null);
    }
  };

  return (
    <div>
      <AddressList 
        addresses={addresses}
        selectedAddressId={selectedAddressId}
        onSelectAddress={setSelectedAddressId}
        onEdit={handleEditAddress}
        onDelete={handleDeleteAddress}
      />
      
      {showAddForm && (
        <AddressForm
          onSave={handleAddressSaved}
          editingAddress={editingAddress}
          type="shipping"
        />
      )}
      
      {!showAddForm && (
        <button 
          onClick={() => setShowAddForm(true)}
          className="w-full border-2 border-dashed border-[#196c67] rounded-lg p-4 text-[#196c67] hover:bg-[#f0fdf4] transition-colors"
        >
          + Add New Address
        </button>
      )}
    </div>
  );
}