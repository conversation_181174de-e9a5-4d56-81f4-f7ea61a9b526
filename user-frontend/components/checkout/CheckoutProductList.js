'use client';

import CartItem from '@/components/cart/CartItem';

export default function CheckoutProductList({ cart }) {
  if (!cart || !cart.vendors) {
    return <p>Your cart is empty.</p>;
  }

  return (
    <div className="border rounded-lg p-4">
      <h2 className="text-lg font-semibold mb-4">Items in your cart</h2>
      {Object.values(cart.vendors).map((vendor) => (
        <div key={vendor.id}>
          <h3 className="font-semibold">{vendor.name}</h3>
          {vendor.items.map((item) => (
            <CartItem key={item.id} item={item} />
          ))}
        </div>
      ))}
    </div>
  );
}