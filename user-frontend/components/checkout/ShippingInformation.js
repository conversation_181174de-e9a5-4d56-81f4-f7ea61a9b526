'use client';

import { useState } from 'react';
import { Edit, Trash2 } from 'lucide-react';
import { Button } from "@/components/ui/button";
import AddressForm from './AddressForm';
import useAuthStore from '@/store/authStore';
import api from '@/lib/axios';
import { toast } from 'sonner';

export default function ShippingInformation({ addresses, onSelectAddress }) {
  const [selectedAddress, setSelectedAddress] = useState(null);
  const [showAddressForm, setShowAddressForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState(null);
  const [deletingId, setDeletingId] = useState(null);
  const [loadingEdit, setLoadingEdit] = useState(null);

  const { user } = useAuthStore();

  const handleSelectAddress = (address) => {
    setSelectedAddress(address);
    if (onSelectAddress) {
      onSelectAddress(address);
    }
  };

  const handleEdit = async (addressId) => {
    setLoadingEdit(addressId);
    try {
      const response = await api.get(`/client/user-addresses/${addressId}`);
      const addressData = response.data.data || response.data;
      setEditingAddress(addressData);
      setShowAddressForm(true);
    } catch (error) {
      toast.error('Failed to load address details');
      console.error('Edit address error:', error);
    } finally {
      setLoadingEdit(null);
    }
  };

  const handleDelete = async (addressId) => {
    if (!confirm('Are you sure you want to delete this address?')) return;
    
    setDeletingId(addressId);
    try {
      await api.delete(`/client/user-addresses/${addressId}`);
      toast.success('Address deleted successfully');
      if (selectedAddress?.id === addressId) {
        setSelectedAddress(null);
      }
    } catch (error) {
      toast.error('Failed to delete address');
      console.error('Delete address error:', error);
    } finally {
      setDeletingId(null);
    }
  };

  const handleAddressSaved = (newAddress) => {
    setShowAddressForm(false);
    setEditingAddress(null);
    setSelectedAddress(newAddress);
    if (onSelectAddress) {
      onSelectAddress(newAddress);
    }
  };

  return (
    <div>
      {/* Address Cards */}
      <div className="space-y-4">
        {addresses.map((address, index) => (
          <div
            key={address.id}
            className={`border-2 rounded-lg overflow-hidden cursor-pointer transition-colors ${
              selectedAddress?.id === address.id 
                ? 'border-[#196c67] bg-[#f0fdf4]' 
                : 'border-gray-200 hover:border-[#196c67]'
            }`}
            onClick={() => handleSelectAddress(address)}
          >
            {/* Header inside card */}
            <div className="flex items-center justify-between p-3 bg-[#f2fffd] border-b">
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-4 h-4 shadow-md">
                  <div className={`w-3 h-3 rounded-full border-2 ${
                    selectedAddress?.id === address.id 
                      ? 'border-[#196c67] bg-[#196c67]' 
                      : 'border-gray-300'
                  }`}>
                    {selectedAddress?.id === address.id && (
                      <div className="w-full h-full rounded-full bg-white scale-50"></div>
                    )}
                  </div>
                </div>
                <h4 className="font-medium text-teal-600">
                  Address {index + 1}
                </h4>
              </div>
              <div className="flex items-center space-x-2">
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEdit(address.id);
                  }}
                  disabled={loadingEdit === address.id}
                  className="w-8 h-8 bg-white border border-gray-300 rounded flex items-center justify-center text-[#196c67] hover:text-[#145a56] hover:border-[#196c67] disabled:opacity-50"
                >
                  {loadingEdit === address.id ? (
                    <div className="w-4 h-4 border-2 border-[#196c67] border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <Edit className="w-4 h-4" />
                  )}
                </button>
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDelete(address.id);
                  }}
                  disabled={deletingId === address.id}
                  className="w-8 h-8 bg-white border border-gray-300 rounded flex items-center justify-center text-red-500 hover:text-red-700 hover:border-red-500 disabled:opacity-50"
                >
                  {deletingId === address.id ? (
                    <div className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <Trash2 className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>
            
            {/* Content */}
            <div className="p-4">
              <div className="space-y-1 text-sm text-gray-600">
                <p className="font-medium">{user?.name || user?.fullName || 'User Name'}</p>
                <p className="text-gray-800">
                  Work Place | Flat No: {address.flat_or_villa_number}, {address.building_name}, {address.address_line_1}, {address.address_line_2}, {address.city}, {address.state} {address.postal_code}, {address.country}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Address Form */}
      {showAddressForm && (
        <AddressForm
          onSave={handleAddressSaved}
          editingAddress={editingAddress}
          type="shipping"
        />
      )}
    </div>
  );
}
