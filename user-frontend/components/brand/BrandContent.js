'use client';

import { useRef } from 'react';
import { useBrands } from '@/hooks/useBrands';
import BrandPageSkeleton from '@/components/skeletons/BrandPageSkeleton';
import Image from 'next/image';
import Link from 'next/link';

export default function BrandContent() {
  const { brandData, loading, error, retry } = useBrands();
  const sectionsRef = useRef({});

  if (loading) {
    return <BrandPageSkeleton />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container flex items-center justify-center">
          <div className="text-center">
            <div className="text-red-500 text-xl mb-4">Error loading brands</div>
            <p className="text-gray-600">{error}</p>
            <button
              onClick={retry}
              className="mt-4 px-4 py-2 bg-[#196c67] text-white rounded-lg hover:bg-[#155a56]"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  const handleLetterClick = (letter) => {
    const section = sectionsRef.current[letter];
    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="bg-white">
      <div
          className="text-2xl"
          style={{
            background: 'linear-gradient(to bottom right, #FEE1FE, #ECF7FF, #FFEBDE, #EFFEFF)',
          }}
        >
          <div className="container py-2 flex items-center justify-between">
            <h1 className="text-rico-secondary-dark-4">Brand A-Z</h1>
            <Image
              src="/images/brand/header.png"
              alt="Brand Header"
              width={175}
              height={120}
              className="object-contain"
            />
          </div>
          
        </div>
      <div className="container">
        <div className="flex flex-wrap justify-center gap-2 mb-8 sticky top-0 bg-white py-4 z-10">
          {brandData.index.map((item) => (
            <button
              key={item.key}
              onClick={() => handleLetterClick(item.key)}
              disabled={!item.exist}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                item.exist
                  ? 'bg-rico-shade-light text-gray-700 hover:bg-gray-300'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }`}
            >
              {item.key}
            </button>
          ))}
        </div>

        <div>
          {brandData.brand_groups.map((group) =>
            group.brands.length > 0 ? (
              <div className="mb-8" key={group.key} ref={(el) => (sectionsRef.current[group.key] = el)}>
                <div className="text-left mb-8">
                  <div className="flex items-center">
                    <div className="inline-block bg-rico-shade-light text-black font-semibold px-8 py-2 tracking-wide focus:outline-none focus:ring-2 focus:ring-rico-primary focus:ring-opacity-50 min-w-[40px] text-center"
                         style={{
                           clipPath: 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)'
                         }}>
                      <h2 className="text-lg md:text-xl font-semibold">
                        {group.key}
                      </h2>
                    </div>
                    <div className="flex-1 h-0.5 ml-4 border-dashed border-t-2 border-[#40C4B6]"></div>
                  </div>
                </div>
                <div className="grid grid-cols-[repeat(auto-fit,120px)] gap-4">
                  {group.brands.map((brand) => (
                    <Link key={brand.id} href={`/brands/${brand.slug}`} className="flex flex-col items-center text-center rounded-lg overflow-hidden border">
                      <div className="w-full h-25 relative p-1 bg-white">
                        <Image
                          src={brand.logo_url}
                          alt={brand.name_en}
                          layout="fill"
                          objectFit="contain"
                          className=""
                        />
                      </div>
                      <div className="w-full p-3 bg-[#F6F6F6] h-14 flex items-center justify-center">
                        <span className="text-sm text-gray-600">{brand.name_en}</span>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            ) : null
          )}
        </div>
      </div>
    </div>
  );
}