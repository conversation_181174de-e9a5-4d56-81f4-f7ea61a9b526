import { Card, CardContent } from '@/components/ui/card';
import DealHeader from './DealHeader';
import CountdownTimer from './CountdownTimer';
import PriceSection from './PriceSection';

const DealCard = ({ deal }) => {
  return (
    <Card
      className={`flex flex-col w-[342px] h-[440px] items-start gap-14 relative rounded-[20px_20px_8px_8px] overflow-hidden border-none cursor-pointer`}
      style={{ backgroundImage: `url(${deal.bgImage})` }}
    >
      <CardContent className="flex flex-col w-full h-[164px] items-start gap-2 pt-5 pb-2 px-5">
        <div className="inline-flex flex-col items-start gap-3 relative flex-[0_0_auto] mb-[-1.00px]">
          <div className="inline-flex flex-col items-start gap-3 relative flex-[0_0_auto]">
            <DealHeader title={deal.title} titleColor={deal.titleColor} />
            <div className="inline-flex flex-col items-start gap-1 relative flex-[0_0_auto]">
              <div className="flex w-[302px] h-[46px] items-center gap-3 relative">
                {deal.validTill ? (
                  <>
                  <div className="relative w-[71px] h-[19px]">
                  <span className="h-[19px] top-0 left-0 [font-family:'Inter',Helvetica] font-medium text-neutral-600 text-sm md:text-base tracking-[0] leading-[normal] whitespace-nowrap">
                    Valid Till:
                  </span>
                </div>
                <CountdownTimer time={deal.validTill} />
                  </>
                ) : <div>{deal.description}</div>}
              </div>
              <PriceSection price={deal.price} originalPrice={deal.originalPrice} discount={deal.discount} color={deal.titleColor} />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DealCard;