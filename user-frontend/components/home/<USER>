import { Card, CardContent } from '@/components/ui/card';
import Image from 'next/image';
import Link from 'next/link';

const BrandsSection = ({ brands }) => {
  if (!brands || brands.length === 0) {
    return null;
  }

  return (
    <section className="py-16 bg-rico-secondary-light-4">
      <div className="container">
        <h2 className="text-3xl font-bold text-rico-secondary-dark-3 mb-12">
          Brands
        </h2>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-7 gap-6">
          {brands.map((brand) => (
          <Link href={`/brands/${brand.slug}`} key={brand.id} className="block">
            <Card
              className="group hover:shadow-lg transition-all duration-300 overflow-hidden h-full"
            >
              <CardContent className="p-6 h-32 flex items-center justify-center">
                <div className="w-24 h-24 relative group-hover:scale-110 transition-transform duration-300">
                  <Image
                    src={brand.logo_url || '/images/brand/placeholder.png'}
                    alt={brand.name_en}
                    layout="fill"
                    objectFit="contain"
                  />
                </div>
              </CardContent>
              <div className="bg-gray-100 p-3 text-center border-t">
                <p className="font-medium text-gray-700 group-hover:text-teal-600 transition-colors text-sm truncate">
                  {brand.name_en}
                </p>
              </div>
            </Card>
          </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BrandsSection;