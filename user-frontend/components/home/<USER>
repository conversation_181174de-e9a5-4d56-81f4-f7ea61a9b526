'use client';
import React from 'react';
import { useRouter } from 'next/navigation';

const PromoSection = ({ promos, api, current }) => {
  const router = useRouter();

  const handlePromoClick = (index) => {
    if (api) {
      api.scrollTo(index);
    }
  };

  return (
    <div className="max-w-6xl mx-auto flex items-center -mt-8 relative z-10 justify-center">
      <div className="flex flex-wrap items-center bg-rico-shade-light rounded-full shadow-lg overflow-hidden divide-x divide-gray-300">
        {promos.map((promo, index) => (
          <div
            key={index}
            className={`shrink-0 px-4 h-12 flex items-center justify-center cursor-pointer ${
              current === index ? 'bg-rico-secondary-light-3 font-bold' : ''
            } ${index === 0 ? 'rounded-l-full' : ''}`}
            onClick={() => handlePromoClick(index)}
            style={{ minWidth: '10rem' }}
          >
            <p className="text-sm text-gray-700">{promo}</p>
          </div>
        ))}
        <div className="flex-none p-2">
          <button
            onClick={() => router.push('/products')}
            className="bg-rico-shade-light text-black px-6 py-2 rounded-full shadow-md text-sm"
          >
            See All
          </button>
        </div>
      </div>
    </div>
  );
};

export default PromoSection;
