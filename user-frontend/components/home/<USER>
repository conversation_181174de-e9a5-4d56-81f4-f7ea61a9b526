import { Button } from '@/components/ui/button';
import Image from 'next/image';
import Link from 'next/link';

const CategoriesSection = ({ categories }) => {
  return (
    <section className="py-16 bg-rico-background">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold text-rico-secondary-dark-1">Products by Categories</h2>
          <Button variant="main">See More</Button>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
          {categories && categories.length > 0 ? (
            categories.map((category) => (
              <Link href={`/products/${category.slug}`} key={category.id}>
                <div className="block group">
                  <Image
                    src={category.icon_url || '/images/categories/placeholder.svg'}
                    alt={category.name_en}
                    width={400}
                    height={150}
                    className="w-full h-auto rounded-lg group-hover:drop-shadow-md transition-shadow duration-300"
                  />
                </div>
              </Link>
            ))
          ) : (
            <p>No categories found.</p>
          )}
        </div>
    </section>
  );
};

export default CategoriesSection;