import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from '@/components/ui/carousel';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import DealCard from './deals/DealCard';

const calculateTimeLeft = (endTime) => {
  const difference = +new Date(endTime) - +new Date();
  let timeLeft = {};

  if (difference > 0) {
    timeLeft = {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
      minutes: Math.floor((difference / 1000 / 60) % 60),
      seconds: Math.floor((difference / 1000) % 60)
    };
  }

  return timeLeft;
};

const DealsSection = ({ offer_and_deals }) => {
  const deals = offer_and_deals.map(deal => {
    const timeLeft = calculateTimeLeft(deal.end_time);
    const validTill = timeLeft.hours && timeLeft.minutes && timeLeft.seconds ? `${timeLeft.hours} : ${timeLeft.minutes} : ${timeLeft.seconds}` : null;

    return {
      title: deal.title_en,
      validTill: validTill,
      price: deal.offer_price,
      originalPrice: deal.regular_price,
      discount: `${deal.discount_percentage}% off`,
      image: deal.image_url,
      bgColor: 'bg-rico-secondary-light-4', // Default value
      bgImage: deal.image_url,
      titleColor: 'rico-text-icon-red-1', // Default value
      badge: deal.tag,
      description: deal.description_en
    };
  });

  return (
    <section className="py-12 bg-white">
        <h2 className="text-3xl font-bold text-rico-secondary-dark-3 mb-8">Offers & Deals</h2>
        <Carousel
          opts={{
            align: 'start',
            loop: true
          }}
          className="w-full"
        >
          <CarouselContent>
            {deals.map((deal, index) => (
              <CarouselItem key={index} className="basis-full md:basis-1/2 lg:basis-1/4 mr-4">
                <DealCard deal={deal} />
              </CarouselItem>
            ))}
          </CarouselContent>

          <CarouselPrevious className="absolute left-2 sm:left-4 top-1/2 transform -translate-y-1/2 bg-[#F1FCF9] shadow-lg hover:bg-[#e2fff9] text-[#196c67] rounded-full transition-all duration-300 w-10 h-10 sm:w-14 sm:h-14 z-20 p-2 sm:p-4">
            <ChevronLeft className="w-5 h-5 sm:w-6 sm:h-6" />
          </CarouselPrevious>

          <CarouselNext className="absolute right-2 sm:right-4 top-1/2 transform -translate-y-1/2 bg-[#F1FCF9] shadow-lg hover:bg-[#e2fff9] text-[#196c67] rounded-full transition-all duration-300 w-10 h-10 sm:w-14 sm:h-14 z-20 p-2 sm:p-4">
            <ChevronRight className="w-5 h-5 sm:w-6 sm:h-6" />
          </CarouselNext>
        </Carousel>
    </section>
  );
};

export default DealsSection;