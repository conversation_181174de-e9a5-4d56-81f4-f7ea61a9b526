import { Button } from '@/components/ui/button';
import Link from 'next/link';

const TrendingSection = ({ trending_products }) => {
  return (
    <section className="py-16">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold text-rico-secondary-dark-3">Explore Trending Product</h2>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          {trending_products && trending_products.map((product, index) => (
            <div
              key={index}
              className="group drop-shadow-sm hover:drop-shadow-md rounded-lg transition-shadow duration-300 cursor-pointer p-4 text-center"
              style={{ backgroundImage: `url('/images/trending-bg.svg')` }}
            >
              <p className="text-xl font-medium text-rico-secondary-dark-3 group-hover:text-teal-600 transition-colors">
                {product.short_name}
              </p>
              <Link href={`/products/details/${product.slug}`} className="block">
                <Button variant="outline" size="sm" className="mt-2 bg-teal-100/50 text-teal-800 hover:bg-teal-100 hover:text-teal-800 rounded-md shadow-md">
                  View All
                </Button>
              </Link>
              
            </div>
          ))}
        </div>
    </section>
  );
};

export default TrendingSection;