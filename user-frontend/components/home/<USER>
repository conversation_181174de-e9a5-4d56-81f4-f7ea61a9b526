import { Button } from '@/components/ui/button';
import ProductCard from '@/components/ui/product-card';

const NewArrivalsSection = ({ latest_products }) => {
  return (
    <section className="py-2">
      <div className="flex justify-between items-center mb-8">
        <h2 className="text-3xl font-bold text-rico-secondary-dark-3">New Arrivals</h2>
        <Button variant="main">See All</Button>
      </div>
      <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        {latest_products && latest_products.length > 0 ? (
          latest_products.map((product) => (
            <ProductCard
              key={product.uuid}
              uuid={product.uuid}
              id={product.id}
              slug={product.slug}
              name={product.name_en}
              price={parseFloat(product.offer_price)}
              originalPrice={parseFloat(product.regular_price)}
              image={product.image_url || 'https://images.pexels.com/photos/208518/pexels-photo-208518.jpeg?auto=compress&cs=tinysrgb&w=400'}
              discount={product.discount_percentage}
              badge="New"
              vendor_id={product.vendor_id}
            />
          ))
        ) : (
          <p>No new arrivals to show.</p>
        )}
      </div>
    </section>
  );
};

export default NewArrivalsSection;