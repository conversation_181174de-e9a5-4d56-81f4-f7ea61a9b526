import { Button } from '@/components/ui/button';
import ProductCard from '@/components/ui/product-card';
import Image from 'next/image';

const DiscountedSection = ({ discount_products, discounted_product_banners }) => {
  if (!discount_products || discount_products.length === 0) {
    return null;
  }

  const leftBanner = discounted_product_banners?.items?.[0];
  const rightBanner = discounted_product_banners?.items?.[1];

  return (
    <section className="py-16">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold text-rico-secondary-dark-3">Discounted Products</h2>
          <Button variant="main">
            See All
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <div className="hidden md:block md:col-span-1">
            <Image
              src={leftBanner?.media_url || "/images/discount/banner.png"}
              alt={leftBanner?.alt_text || "Deal Banner"}
              width={300}
              height={800}
              className="object-cover h-full w-full"
            />
          </div>
          <div className="col-span-1 md:col-span-3">
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-6">
              {discount_products.slice(0, 6).map((product) => {
                const regularPrice = parseFloat(product.regular_price);
                const offerPrice = parseFloat(product.offer_price);
                const discount = Math.round(((regularPrice - offerPrice) / regularPrice) * 100);
                return (
                  <ProductCard
                    key={product.uuid}
                    id={product.id}
                    uuid={product.uuid}
                    slug={product.slug}
                    name={product.name_en}
                    price={parseFloat(product.offer_price)}
                    originalPrice={parseFloat(product.regular_price)}
                    image={product.image_url || 'https://images.pexels.com/photos/208518/pexels-photo-208518.jpeg?auto=compress&cs=tinysrgb&w=400'}
                    discount={product.discount_percentage}
                    badge={`${discount}% OFF`}
                    vendor_id={product.vendor_id}
                  />
                );
              })}
            </div>
          </div>
          <div className="hidden md:block md:col-span-1">
            <Image
              src={rightBanner?.media_url || "/images/discount/banner.png"}
              alt={rightBanner?.alt_text || "Deal Banner"}
              width={300}
              height={800}
              className="object-cover h-full w-full"
            />
          </div>
        </div>
    </section>
  );
};

export default DiscountedSection;