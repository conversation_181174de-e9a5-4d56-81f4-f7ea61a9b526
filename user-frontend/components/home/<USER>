'use client';
import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import SmallProductCard from '@/components/ui/small-product-card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const CarouselDots = ({ scrollSnaps, selectedIndex, scrollTo }) => (
  <div className="flex justify-center items-center gap-2 mt-4">
    {scrollSnaps.map((_, index) => (
      <button
        key={index}
        className={`w-2 h-2 rounded-full ${
          index === selectedIndex ? 'bg-rico-white-shade-1' : 'bg-gray-300'
        }`}
        onClick={() => scrollTo(index)}
      />
    ))}
  </div>
);

const BestSellersSection = ({ best_sellers }) => {
  const [activeTab, setActiveTab] = useState();
  const [emblaApi, setEmblaApi] = useState(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState([]);

  const updateCarouselState = useCallback((api) => {
    if (api) {
      setScrollSnaps(api.scrollSnapList());
      setSelectedIndex(api.selectedScrollSnap());
      api.on('select', () => {
        setSelectedIndex(api.selectedScrollSnap());
      });
    }
  }, []);

  useEffect(() => {
    if (best_sellers?.length) {
      const categoryNames = best_sellers.map((item) => item.category.name_en);
      if (!categoryNames.includes(activeTab)) {
        setActiveTab(categoryNames[0]);
      }
    }
  }, [best_sellers, activeTab]);

  useEffect(() => {
    if (emblaApi) {
      updateCarouselState(emblaApi);
    }
  }, [emblaApi, updateCarouselState]);

  if (!best_sellers || best_sellers.length === 0) {
    return null;
  }

  const scrollTo = (index) => {
    emblaApi && emblaApi.scrollTo(index);
  };

  return (
    <section className="my-8">
      <div className="container mx-auto rounded-lg px-4 py-8 bg-rico-gradient-2">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-rico-secondary-dark-3">
            Best Sellers
          </h2>
          <Button variant="main">See All</Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="flex flex-wrap justify-start gap-2 mb-6 bg-transparent p-0">
            {best_sellers.map((item) => (
              <TabsTrigger
                key={item.category.id}
                value={item.category.name_en}
                className={`px-4 py-2 rounded-full bg-rico-background text-xs font-medium shadow-md transition-all duration-300
                  ${
                    activeTab === item.category.name_en
                      ? ' text-rico-secondary-dark-5 border border-rico-secondary-dark-5'
                      : ' text-gray-500 hover:text-gray-800'
                  }`}
              >
                {item.category.name_en}
              </TabsTrigger>
            ))}
          </TabsList>

          {best_sellers.map((item) => (
            <TabsContent key={item.category.id} value={item.category.name_en}>
              <Carousel
                setApi={setEmblaApi}
                opts={{
                  align: 'start',
                  loop: true,
                }}
                className="w-full relative"
              >
                <CarouselContent className="-ml-2">
                  {item.products.map((product) => (
                    <CarouselItem
                      key={product.uuid}
                      className="pl-4 basis-full md:basis-1/2 lg:basis-1/4"
                    >
                      <div className="p-1">
                        <SmallProductCard
                          product={{
                            id: product.id,
                            uuid: product.uuid,
                            slug: product.slug,
                            name: product.name_en,
                            price: product.offer_price,
                            originalPrice: product.regular_price,
                            image:
                              product.image_url ||
                              '/images/brand/placeholder.png',
                            discount: product.discount_percentage,
                            badge: 'Best Seller',
                            vendor_id: product.vendor_id,
                          }}
                        />
                      </div>
                    </CarouselItem>
                  ))}
                </CarouselContent>

                <CarouselPrevious className="absolute -left-11 top-1/2 transform -translate-y-1/2 bg-[#F1FCF9] shadow-lg hover:bg-[#e2fff9] text-[#196c67] rounded-full transition-all duration-300 w-14 h-14 z-20 p-4 hidden md:flex">
                  <ChevronLeft className="w-6 h-6" />
                </CarouselPrevious>

                <CarouselNext className="absolute -right-11 top-1/2 transform -translate-y-1/2 bg-[#F1FCF9] shadow-lg hover:bg-[#e2fff9] text-[#196c67] rounded-full transition-all duration-300 w-14 h-14 z-20 p-4 hidden md:flex">
                  <ChevronRight className="w-6 h-6" />
                </CarouselNext>
                <div className="md:hidden">
                  <CarouselDots
                    scrollSnaps={scrollSnaps}
                    selectedIndex={selectedIndex}
                    scrollTo={scrollTo}
                  />
                </div>
              </Carousel>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </section>
  );
};

export default BestSellersSection;
