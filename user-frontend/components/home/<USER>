import { Button } from '@/components/ui/button';
import ProductCard from '@/components/ui/product-card';

const RecentlyViewedSection = ({ recently_viewed_products }) => {
  if (!recently_viewed_products || recently_viewed_products.length === 0) {
    return null;
  }

  return (
    <section className="py-16">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold text-rico-secondary-dark-3">Recently Viewed Product</h2>
          <Button variant="main">See More</Button>
        </div>
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
          {recently_viewed_products.map((product) => (
            <ProductCard
              key={product.uuid}
              uuid={product.uuid}
              id={product.id}
              slug={product.slug}
              name={product.name_en}
              price={parseFloat(product.offer_price)}
              originalPrice={parseFloat(product.regular_price)}
              image={product.image_url || 'https://images.pexels.com/photos/208518/pexels-photo-208518.jpeg?auto=compress&cs=tinysrgb&w=400'}
            />
          ))}
        </div>
    </section>
  );
};

export default RecentlyViewedSection;