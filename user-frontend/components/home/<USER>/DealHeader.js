import Image from "next/image";

const DealHeader = ({ title, titleColor }) => {
  return (
    <div className="flex flex-col w-[302px] items-start gap-2 relative flex-[0_0_auto]">
      <h2 className={`relative self-stretch mt-[-1.00px] [font-family:'Inter',Helvetica] font-medium ${`text-`.concat(titleColor)} text-2xl tracking-[0] leading-[normal]`}>
        {title}
      </h2>
    </div>
  );
};

export default DealHeader;