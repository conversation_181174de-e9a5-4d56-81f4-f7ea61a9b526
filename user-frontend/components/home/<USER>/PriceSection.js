import { Badge } from '@/components/ui/badge';
import DirhamSymbol from '@/components/ui/dirham-symbol';

const PriceSection = ({ price, originalPrice, discount, color }) => {
  return (
    <div className="flex w-[302px] h-[46px] items-center justify-between relative">
      <div className="relative w-auto h-8">
        <div className={`flex items-center gap-1 h-8 font-medium ${`text-`.concat(color)} text-2xl leading-8 [font-family:'Roboto',Helvetica] tracking-[0] whitespace-nowrap`}>
          <DirhamSymbol className="w-5 h-5" />
          <span>{Number(price).toFixed()}</span>
        </div>
      </div>

      {originalPrice && <div className="inline-flex flex-col items-start justify-center gap-0.5 relative self-stretch flex-[0_0_auto]">
        <div className="relative w-7 h-5">
          <div className="absolute h-5 top-0 left-0 [font-family:'Roboto',Helvetica] font-normal text-neutral-600 text-sm tracking-[0] leading-5 whitespace-nowrap">
            was
          </div>
        </div>

        <div className="inline-flex items-center gap-2 relative flex-[0_0_auto]">
          <div className="flex items-center gap-1 [font-family:'Roboto',Helvetica] font-medium text-[#6c0093] text-base tracking-[0] leading-6 whitespace-nowrap line-through">
            <DirhamSymbol className="w-4 h-4" />
            <span>{Number(originalPrice).toFixed()}</span>
          </div>
          <div className="[font-family:'Inter',Helvetica] font-normal text-[#6c0093] text-[13px] tracking-[0] leading-[normal]">
            (Regular price)
          </div>
        </div>
      </div>}

      {discount && <div className="inline-flex flex-col items-start justify-center gap-0.5 relative flex-[0_0_auto]">
        <div className="flex items-center gap-0.5 relative self-stretch w-full flex-[0_0_auto]">
          <Badge className="bg-transparent p-0">
            <span className="text-[#ff0404] font-medium text-base">
              {discount}
            </span>
          </Badge>
        </div>
      </div>}
    </div>
  );
};

export default PriceSection;