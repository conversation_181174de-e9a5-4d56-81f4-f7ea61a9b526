import React from 'react';

const CountdownTimer = ({ time }) => {
  const timerData = time ? time.split(' : ').map(item => ({ value: item })) : [];

  return (
    <div className="inline-flex items-start justify-center gap-1 relative flex-[0_0_auto]">
      {timerData.map((item, index) => (
        <React.Fragment key={`timer-${index}`}>
          <div className="inline-flex flex-col items-center justify-center gap-1 relative flex-[0_0_auto]">
            <div className="flex w-7 h-7 items-center justify-center gap-2.5 p-1 relative bg-[#d0005d] rounded border border-solid border-[#f1b0c4] shadow-[0px_2px_1px_#0000001f]">
              <div className="relative w-fit [font-family:'Roboto',Helvetica] font-normal text-[#feffff] text-sm tracking-[0] leading-[normal] whitespace-nowrap">
                {item.value}
              </div>
            </div>

            <div className="relative w-fit [font-family:'Roboto',Helvetica] font-normal text-[#d0005d] text-xs text-center tracking-[0] leading-[normal] whitespace-nowrap">
              {index === 0 ? 'hrs' : index === 1 ? 'min' : 'sec'}
            </div>
          </div>

          {index < timerData.length - 1 && (
            <div className="flex flex-col w-[5px] items-center justify-center gap-2.5 px-0 py-1.5 relative">
              <div className="relative self-stretch mt-[-1.00px] [font-family:'Roboto',Helvetica] font-semibold text-[#d0005d] text-base tracking-[0] leading-[normal]">
                :
              </div>
            </div>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

export default CountdownTimer;