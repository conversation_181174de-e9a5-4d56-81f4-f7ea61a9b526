import Link from 'next/link';
export default function MegaMenu({ data }) {
  if (!data) return null;

  return (
    <div className="absolute bg-white text-gray-800 shadow-lg rounded-b-md w-[80vw] left-1/2 -translate-x-1/2 top-full z-20 transition-all duration-300 ease-in-out">
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 md:grid-cols-6 gap-8">
          <div className="md:col-span-6 grid grid-cols-1 md:grid-cols-5 gap-8">
            {data.map((category) => (
              <div key={category.id}>
                <h3 className="font-bold text-teal-600 mb-4">
                  <Link href={`/products/${category.name_en.toLowerCase().replace(/ /g, '-')}`} className="hover:text-teal-500 transition-colors">
                    {category.name_en}
                  </Link>
                </h3>
                <ul className="flex flex-col flex-wrap max-h-72">
                  {category.sub_category.map((sub) => (
                    <li key={sub.id} className="mb-2 mr-4">
                      <Link href={`/products/${category.name_en.toLowerCase()}?subcategory=${sub.name_en.toLowerCase().replace(/ /g, '-')}`} className="hover:text-teal-500 transition-colors text-sm">
                        {sub.name_en}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}