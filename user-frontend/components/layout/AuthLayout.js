'use client';

export default function AuthLayout({ 
  children, 
  title, 
  subtitle
}) {
  return (
    <div 
      className="min-h-screen bg-gray-50 flex items-center justify-center bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage: 'url(/bg.png)'
      }}
    >
      <div className="bg-white rounded-2xl shadow-2xl overflow-hidden flex border-2 border p-6">
        {/* Left Side - 280px */}
        <div className="w-[280px] pb-20 relative flex items-center justify-center rounded-xl shadow-xl overflow-hidden" style={{backgroundImage: 'url(/image.png)', backgroundSize: 'cover', backgroundPosition: 'bottom', backgroundRepeat: 'no-repeat', minHeight: 'calc(100% + 8px)', margin: '-4px'}}>
          <div className="text-center px-8">
            <div className="mb-0">
              <img src="/images/vitamin.svg" alt="Vitamin.ae Logo" className="w-40 h-10 mx-auto" />
            </div>
            <h3 className="text-2xl font-bold mb-4" style={{color: '#196C67'}}>{title}</h3>
            <p className="text-lg opacity-90 text-white">{subtitle}</p>
          </div>
        </div>

        {/* Right Side - 460px */}
        <div className="w-[460px] px-12 py-8 flex items-center justify-center">
          <div className="w-full">
            {/* Form Content */}
            <div>
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}