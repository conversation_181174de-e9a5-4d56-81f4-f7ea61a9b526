'use client';

import { Skeleton } from '@/components/ui/skeleton';

export default function BlogPageSkeleton() {
  return (
    <div className="container py-6">
      {/* Page Header Skeleton */}
      <div className="mb-8">
        <Skeleton className="h-10 w-80 mb-4" />
        <Skeleton className="h-6 w-96" />
      </div>

      {/* Three-Column Layout Skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        
        {/* Left Sidebar Skeleton */}
        <aside className="lg:col-span-1">
          <div className="space-y-6">
            {/* Categories Card */}
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <Skeleton className="h-5 w-24 mb-4" />
              <div className="space-y-2">
                {[1, 2, 3, 4].map(i => (
                  <Skeleton key={i} className="h-8 w-full" />
                ))}
              </div>
            </div>

            {/* Content Types Card */}
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <Skeleton className="h-5 w-32 mb-4" />
              <div className="space-y-2">
                {[1, 2].map(i => (
                  <Skeleton key={i} className="h-8 w-full" />
                ))}
              </div>
            </div>

            {/* Stats Card */}
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <Skeleton className="h-5 w-24 mb-4" />
              <div className="space-y-3">
                {[1, 2, 3].map(i => (
                  <div key={i} className="flex justify-between">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-8" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </aside>

        {/* Main Content Skeleton */}
        <main className="lg:col-span-3">
          <div className="space-y-8">
            {/* Search Bar */}
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <Skeleton className="h-10 w-full" />
            </div>

            {/* Content Sections */}
            {[1, 2].map(section => (
              <section key={section}>
                <Skeleton className="h-8 w-48 mb-6" />
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                  {[1, 2, 3, 4, 5, 6].map(card => (
                    <div key={card} className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                      <Skeleton className="h-48 w-full" />
                      <div className="p-4">
                        <Skeleton className="h-4 w-16 mb-2" />
                        <Skeleton className="h-6 w-full mb-2" />
                        <Skeleton className="h-4 w-full mb-3" />
                        <div className="flex justify-between">
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-4 w-20" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </section>
            ))}
          </div>
        </main>
      </div>
    </div>
  );
}
