'use client';

import Link from 'next/link';
import { Badge } from '@/components/ui/badge';

export default function CategoryChip({ category, size = 'default', className = '' }) {
  if (!category) return null;

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    default: 'text-sm px-3 py-1'
  };

  return (
    <Link href={`/blog/${category.slug}`}>
      <Badge 
        variant="secondary" 
        className={`${sizeClasses[size]} bg-rico-secondary-light-3 text-rico-secondary-dark-4 hover:bg-rico-secondary-light-2 transition-colors ${className}`}
      >
        {category.name}
      </Badge>
    </Link>
  );
}
