'use client';

import Image from 'next/image';
import { User } from 'lucide-react';

export default function AuthorMiniCard({ author }) {
  if (!author) return null;

  return (
    <div className="flex items-center gap-2 p-2 rounded-lg hover:bg-gray-50 transition-colors">
      <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
        {author.avatar ? (
          <Image
            src={author.avatar}
            alt={author.name}
            width={32}
            height={32}
            className="w-full h-full object-cover"
          />
        ) : (
          <User className="w-4 h-4 text-gray-500" />
        )}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate">{author.name}</p>
        <p className="text-xs text-gray-500">{author.article_count} articles</p>
      </div>
    </div>
  );
}
