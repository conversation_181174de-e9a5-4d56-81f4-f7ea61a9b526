'use client';

import Image from 'next/image';
import { User } from 'lucide-react';

export default function AuthorBadge({ author, size = 'default', showCredentials = false }) {
  if (!author) return null;

  const sizeClasses = {
    sm: 'w-6 h-6 text-xs',
    default: 'w-8 h-8 text-sm'
  };

  return (
    <div className="flex items-center gap-2">
      <div className={`${sizeClasses[size]} bg-gray-200 rounded-full flex items-center justify-center overflow-hidden`}>
        {author.avatar ? (
          <Image
            src={author.avatar}
            alt={author.name}
            width={size === 'sm' ? 24 : 32}
            height={size === 'sm' ? 24 : 32}
            className="w-full h-full object-cover"
          />
        ) : (
          <User className="w-3 h-3 text-gray-500" />
        )}
      </div>
      <div>
        <span className={`${size === 'sm' ? 'text-xs' : 'text-sm'} text-gray-700 font-medium`}>
          {author.name}
        </span>
        {showCredentials && author.credentials && (
          <div className="text-xs text-gray-500">
            {author.credentials.join(', ')}
          </div>
        )}
      </div>
    </div>
  );
}
