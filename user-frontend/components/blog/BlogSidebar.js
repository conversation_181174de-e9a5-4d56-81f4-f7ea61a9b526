'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Menu, X } from 'lucide-react';
import AuthorMiniCard from './AuthorMiniCard';

function SidebarLink({ href, children, active = false, count }) {
  return (
    <Link 
      href={href}
      className={`flex items-center justify-between px-3 py-2 rounded-md text-sm transition-colors ${
        active 
          ? 'bg-rico-secondary-light-3 text-rico-secondary-dark-4 font-medium' 
          : 'text-gray-600 hover:bg-gray-50 hover:text-rico-secondary-dark-4'
      }`}
    >
      <span>{children}</span>
      {count && (
        <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
          {count}
        </span>
      )}
    </Link>
  );
}

export default function BlogSidebar({ categories = [], featuredAuthors = [], blogStats = {} }) {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      {/* Mobile toggle button */}
      <div className="lg:hidden mb-4">
        <Button
          variant="outline"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full flex items-center justify-center gap-2"
        >
          {isOpen ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
          Categories & Filters
        </Button>
      </div>

      {/* Sidebar content */}
      <div className={`${isOpen ? 'block' : 'hidden'} lg:block space-y-6`}>
      {/* Categories Section */}
      <Card className="bg-white border border-gray-200">
        <CardHeader className="pb-4">
          <CardTitle className="text-sm font-semibold text-rico-secondary-dark-1 uppercase tracking-wide">
            Categories
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <nav className="space-y-2">
            <SidebarLink href="/blog" active={pathname === '/blog'}>
              Most Popular
            </SidebarLink>
            {categories.map(category => (
              <SidebarLink 
                key={category.id}
                href={`/blog/${category.slug}`}
                active={pathname === `/blog/${category.slug}`}
                count={category.article_count}
              >
                {category.name}
              </SidebarLink>
            ))}
          </nav>
        </CardContent>
      </Card>



      {/* Blog Statistics */}
      {Object.keys(blogStats).length > 0 && (
        <Card className="bg-rico-secondary-light-4 border border-rico-secondary-light-3">
          <CardHeader className="pb-4">
            <CardTitle className="text-sm font-semibold text-rico-secondary-dark-1 uppercase tracking-wide">
              Blog Stats
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Articles</span>
                <span className="font-semibold text-rico-secondary-dark-4">
                  {blogStats.total_articles || 0}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Categories</span>
                <span className="font-semibold text-rico-secondary-dark-4">
                  {blogStats.total_categories || 0}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Authors</span>
                <span className="font-semibold text-rico-secondary-dark-4">
                  {blogStats.total_authors || 0}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Wellness Experts Section */}
      {featuredAuthors.length > 0 && (
        <Card className="bg-white border border-gray-200">
          <CardHeader className="pb-4">
            <CardTitle className="text-sm font-semibold text-rico-secondary-dark-1 uppercase tracking-wide">
              Wellness Experts
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              {featuredAuthors.slice(0, 3).map(author => (
                <AuthorMiniCard key={author.id} author={author} />
              ))}
            </div>
            <Button variant="outline" size="sm" className="w-full mt-4">
              View All Experts
            </Button>
          </CardContent>
        </Card>
      )}
      </div>
    </>
  );
}
