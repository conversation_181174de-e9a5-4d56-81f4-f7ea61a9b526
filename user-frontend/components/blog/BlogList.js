'use client';

import Blog<PERSON>ard from './BlogCard';
import Section<PERSON>ead<PERSON> from './SectionHeader';

export default function BlogList({ 
  articles = [], 
  title, 
  subtitle,
  showHeader = true,
  emptyMessage = "No articles found",
  emptyDescription = "There are no articles available at the moment.",
  className = "",
  gridClassName = "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4",
  variant = "compact",
  showViewAll = false,
  viewAllHref = "",
  viewAllText = "View All"
}) {
  return (
    <section className={className}>
      {showHeader && (title || subtitle) && (
        <div className="flex items-center justify-between mb-6">
          <SectionHeader 
            title={title}
            subtitle={subtitle}
          />
          {showViewAll && viewAllHref && (
            <a 
              href={viewAllHref}
              className="text-rico-primary hover:text-rico-primary-dark font-medium text-sm transition-colors"
            >
              {viewAllText} →
            </a>
          )}
        </div>
      )}
      
      {articles.length > 0 ? (
        <div className={gridClassName}>
          {articles.map(article => {
            // Additional validation for article data
            if (!article || !article.id || !article.slug) {
              console.warn('Invalid article data:', article);
              return null;
            }
            return (
              <BlogCard
                key={article.id}
                variant={variant}
                article={article}
              />
            );
          })}
        </div>
      ) : (
        <div className="text-center py-12">
          <h3 className="text-xl font-semibold text-gray-800 mb-2">{emptyMessage}</h3>
          <p className="text-gray-600">{emptyDescription}</p>
        </div>
      )}
    </section>
  );
}
