'use client';

import use<PERSON>WR<PERSON>ustom from '@/lib/useSWR';
import BlogSidebar from './BlogSidebar';
import BlogMainContent from './BlogMainContent';
import BlogPageSkeleton from './BlogPageSkeleton';

export default function BlogLandingContent() {
  const { data: result, error, isLoading } = useSWRCustom('/client/blogs?layout=comprehensive');

  if (error) {
    console.error('Failed to fetch blog data:', error);
    return (
      <div className="container py-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Failed to load blog content</h2>
          <p className="text-gray-600">Please try refreshing the page.</p>
        </div>
      </div>
    );
  }

  if (isLoading || !result) {
    return <BlogPageSkeleton />;
  }

  const data = result?.data;

  return (
    <div className="container py-6">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold text-rico-secondary-dark-1 mb-4">
          Vitamins Wellness Hub
        </h1>
        <p className="text-lg text-gray-600 max-w-3xl">
          Your go-to place for wellness information curated by our experts.
        </p>
      </div>

      {/* Three-Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        
        {/* Left Sidebar - Navigation & Filters */}
        <aside className="lg:col-span-1">
          <div className="sticky top-24 space-y-6">
            <BlogSidebar 
              categories={data?.categories || []}
              featuredAuthors={data?.featured_authors || []}
              blogStats={data?.blog_stats || {}}
            />
          </div>
        </aside>

        {/* Main Content Area */}
        <main className="lg:col-span-3">
          <BlogMainContent 
            latestArticles={data?.latest_articles || []}
            popularArticles={data?.popular_articles || []}
            featuredAuthors={data?.featured_authors || []}
          />
        </main>
      </div>
    </div>
  );
}
