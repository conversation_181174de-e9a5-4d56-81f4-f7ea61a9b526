'use client';

import Image from 'next/image';
import { User } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

export default function AuthorCard({ author }) {
  if (!author) return null;

  return (
    <Card className="bg-white border border-gray-200 hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center gap-3 mb-3">
          <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
            {author.avatar ? (
              <Image
                src={author.avatar}
                alt={author.name}
                width={48}
                height={48}
                className="w-full h-full object-cover"
              />
            ) : (
              <User className="w-6 h-6 text-gray-500" />
            )}
          </div>
          <div>
            <h3 className="font-semibold text-rico-secondary-dark-1">{author.name}</h3>
            <p className="text-sm text-gray-600">{author.article_count} articles</p>
          </div>
        </div>
        {author.bio && (
          <p className="text-sm text-gray-600 line-clamp-2">{author.bio}</p>
        )}
      </CardContent>
    </Card>
  );
}
