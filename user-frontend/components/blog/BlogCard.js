'use client';

import Link from 'next/link';
import Image from 'next/image';
import CategoryChip from './CategoryChip';
import AuthorBadge from './AuthorBadge';
import { formatDate } from '@/lib/utils';

export default function BlogCard({ article, variant = 'compact' }) {
  if (!article) return null;

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
      {article.cover_image && (
        <div className="aspect-video overflow-hidden">
          <Image
            src={article.cover_image}
            alt={article.title}
            width={400}
            height={225}
            className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
          />
        </div>
      )}
      <div className="p-4">
        <CategoryChip category={article.category} size="sm" className="mb-2" />
        <h3 className="font-semibold text-rico-secondary-dark-1 mb-2 line-clamp-2 hover:text-rico-primary transition-colors">
          <Link href={`/blog/${article.category?.slug || 'uncategorized'}/${article.slug}`}>
            {article.title}
          </Link>
        </h3>
        <p className="text-gray-600 text-sm line-clamp-2 mb-3">{article.excerpt}</p>
        <div className="flex items-center justify-between text-xs text-gray-500">
          <AuthorBadge author={article.author} size="sm" />
          <div className="flex items-center gap-2">
            <span>{article.read_time} min read</span>
            <span>•</span>
            <span>{formatDate(article.published_at)}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
