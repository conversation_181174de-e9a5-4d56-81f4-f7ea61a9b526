import React from 'react';
import { Icon } from '@iconify/react';

interface ReturnWarrantyCardProps {
  returnPolicy: string;
  warranty: string;
}

const ReturnWarrantyCard: React.FC<ReturnWarrantyCardProps> = ({ returnPolicy, warranty }) => {
  return (
    <div className="bg-white rounded-xl shadow p-5">
      <div className="text-teal-700 font-bold text-xl mb-3">Return & Warranty</div>
      <div className="flex items-center gap-2 text-gray-700 mb-2 text-base">
        <Icon icon="mdi:history" className="text-2xl text-gray-500" />
        <span>{returnPolicy}</span>
      </div>
      <div className="flex items-center gap-2 text-gray-700 text-base">
        <Icon icon="mdi:shield-cog-outline" className="text-2xl text-gray-500" />
        <span>{warranty}</span>
      </div>
    </div>
  );
};

export default ReturnWarrantyCard;