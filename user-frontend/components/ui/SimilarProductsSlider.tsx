import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import SimilarProductCard from './SimilarProductCard';
import { Button } from './button';

interface Product {
  id: string;
  image: string;
  name: string;
  unit: string;
  price: number;
  rating: number;
}

interface SimilarProductsSliderProps {
  products: Product[];
  onSeeMore?: () => void;
}

const SimilarProductsSlider: React.FC<SimilarProductsSliderProps> = ({ products, onSeeMore }) => {
  return (
    <div className="w-full mt-8">
      <div className="flex justify-between items-center mb-3 px-2">
        <h2 className="text-lg sm:text-2xl font-semibold text-teal-700">Similar Product for you</h2>
        <Button variant="main" onClick={onSeeMore} className="cursor-pointer text-sm sm:text-base px-3 sm:px-4 py-2">See More</Button>
      </div>
      <Swiper
        modules={[Navigation]}
        spaceBetween={16}
        slidesPerView={2}
        breakpoints={{
          480: { slidesPerView: 2, spaceBetween: 16 },
          640: { slidesPerView: 2, spaceBetween: 20 },
          768: { slidesPerView: 2.5, spaceBetween: 20 },
          1024: { slidesPerView: 3, spaceBetween: 24 },
          1280: { slidesPerView: 4, spaceBetween: 24 },
        }}
        navigation={{
          nextEl: '.swiper-next',
          prevEl: '.swiper-prev',
        }}
        className="relative px-2 sm:px-0"
      >
        {products.map((product) => (
          <SwiperSlide key={product.id}>
            <SimilarProductCard product={product} />
          </SwiperSlide>
        ))}
        <button aria-label="Previous" className="cursor-pointer swiper-prev absolute -left-2 sm:left-0 top-1/2 -translate-y-1/2 z-10 bg-teal-50 w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center shadow hover:bg-teal-100 transition-colors">
          <svg width="16" height="16" className="sm:w-6 sm:h-6" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="15 18 9 12 15 6" /></svg>
        </button>
        <button aria-label="Next" className="cursor-pointer swiper-next absolute -right-2 sm:right-0 top-1/2 -translate-y-1/2 z-10 bg-teal-50 w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center shadow hover:bg-teal-100 transition-colors">
          <svg width="16" height="16" className="sm:w-6 sm:h-6" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="9 18 15 12 9 6" /></svg>
        </button>
      </Swiper>
    </div>
  );
};

export default SimilarProductsSlider; 