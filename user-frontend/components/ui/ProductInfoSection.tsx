import React from 'react';
import SectionTitleDivider from './SectionTitleDivider';
import KeyIngredientSection from './KeyIngredientSection';

interface KeyIngredient {
  label: string;
  value: string;
}

interface ProductInfoSectionProps {
  description?: string;
  usageInstructions?: string;
  showUsageInstructions?: boolean;
  mode?: 'main' | 'additional';
  keyIngredients?: KeyIngredient[];
  nutritionImages?: string[];
}

const ProductInfoSection: React.FC<ProductInfoSectionProps> = ({
  description = '',
  usageInstructions = '',
  showUsageInstructions = true,
  mode = 'additional',
  keyIngredients = [],
  nutritionImages = [],
}) => {
  if (mode === 'main') {
    return (
      <div className="w-full">
        <SectionTitleDivider title="Product Information" />
        <div className="mt-4">
          <div className="font-semibold text-base text-gray-800 mb-2">Description</div>
          <div className="text-xs text-gray-600 leading-relaxed whitespace-pre-line">{description}</div>
        </div>
      </div>
    );
  }

  // Default: Additional Product Information
  return (
    <div className="w-full">
      <SectionTitleDivider title="Additional Product Information" />
      <div className="mt-4">
        {showUsageInstructions && (
          <div className="mb-6">
            <div className="font-semibold text-base text-gray-800 mb-2">Usage Instructions</div>
            <div className="text-xs text-gray-600 leading-relaxed whitespace-pre-line">{usageInstructions}</div>
          </div>
        )}
        <KeyIngredientSection keyIngredients={keyIngredients} nutritionImages={nutritionImages} />
      </div>
    </div>
  );
};

export default ProductInfoSection; 
