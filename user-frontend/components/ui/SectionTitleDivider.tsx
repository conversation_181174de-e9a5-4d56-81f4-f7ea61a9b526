import React from 'react';

const SectionTitleDivider: React.FC<{ title: string }> = ({ title }) => (
  <div className="flex items-center w-full">
    <div className="relative shrink-0">
      <span
        className="bg-rico-secondary-light-3 px-4 py-1 text-2xl font-semibold text-gray-800 shadow"
        style={{
          clipPath: 'polygon(0 50%, 12px 0, calc(100% - 12px) 0, 100% 50%, calc(100% - 12px) 100%, 12px 100%, 0 50%)',
          boxShadow: '0 1px 2px rgba(34, 33, 33, 0.03)',
          display: 'inline-block',
        }}
      >
        {title}
      </span>
    </div>
    <div
      className="flex-1 border-t border-dotted ml-2"
      style={{ borderColor: '#b2f5ea', marginTop: 2 }}
    />
  </div>
);

export default SectionTitleDivider; 