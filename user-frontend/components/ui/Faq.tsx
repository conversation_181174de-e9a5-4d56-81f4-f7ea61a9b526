'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import Image from 'next/image';


interface FaqItem {
  question: string;
  answer: string;
}

interface FaqProps {
  title?: string;
  items: FaqItem[];
  className?: string;
}

const Faq: React.FC<FaqProps> = ({
  title = "Frequently Asking Question (FAQ)",
  items,
  className
}) => {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleItem = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className={cn("relative w-full flex justify-center items-center py-16 px-5 min-h-[60vh] bg-white", className)}>
      {/* Left background question mark */}
      <img
        src="/images/leftQues.svg"
        alt="Left question mark"
        className="hidden md:block absolute left-28 top-0 z-0 opacity-300 h-4/5"
        style={{ maxHeight: '500px' }}
      />
      {/* Right background question mark */}
      <img
        src="/images/rightQues.svg"
        alt="Right question mark"
        className="hidden md:block absolute right-28 bottom-0 z-0 opacity-300 h-4/5"
        style={{ maxHeight: '500px' }}
      />
      {/* FAQ Content Box */}
      <div className="relative z-10 w-full max-w-4xl">
        {title && (
          <div className="mb-6">
            <h2 className="text-2xl font-semibold text-teal-700 text-left">{title}</h2>
          </div>
        )}
        <div className="bg-white rounded shadow-md overflow-hidden">
          {items.map((item, index) => (
            <div key={index} className="border-b">
              <button
                onClick={() => toggleItem(index)}
                className="w-full text-left px-6 py-4 font-medium text-rico-secondary-dark-4"
              >
                {item.question}
              </button>
              {openIndex === index && (
                <div className="px-6 pb-4">
                  <span className="font-semibold text-teal-700">Answer:</span>
                  <span className="ml-2 text-gray-700">{item.answer}</span>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Faq;
