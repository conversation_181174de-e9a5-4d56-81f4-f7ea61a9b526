'use client'
import React, { useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';

interface ProductImageGalleryProps {
  images: string[];
}

export const ProductImageGallery: React.FC<ProductImageGalleryProps> = ({ images }) => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollIndex, setScrollIndex] = useState(0);
  const visibleThumbs = 5;

  const handlePrev = () => {
    setScrollIndex((prev) => Math.max(prev - 1, 0));
  };
  const handleNext = () => {
    setScrollIndex((prev) => Math.min(prev + 1, images.length - visibleThumbs));
  };

  return (
    <div className="w-full mx-auto">
      {/* Main Image */}
      <div className="border border-gray-200 mb-3 flex justify-center items-start min-h-[220px]">
        <img
          src={images[selectedIndex]}
          alt={`Product ${selectedIndex + 1}`}
          className="w-full h-[470px] object-cover"
        />
      </div>
      {/* Thumbnails Row with Swiper */}
      <div className="relative h-[60px]">
        <Swiper
          modules={[Navigation]}
          slidesPerView={4}
          spaceBetween={10}
          navigation={{
            nextEl: '.thumb-swiper-next',
            prevEl: '.thumb-swiper-prev',
          }}
          className="w-full h-[96px]"
        >
          {images.map((img, idx) => (
            <SwiperSlide key={img + idx} className="flex items-center justify-center h-[60px]">
              <img
                src={img}
                alt={`Thumbnail ${idx + 1}`}
                onClick={() => setSelectedIndex(idx)}
                className={
                  `w-full h-[96px] object-contain rounded cursor-pointer bg-white transition-all border ` +
                  (idx === selectedIndex
                    ? 'border-rico-primary shadow-[0_2px_8px_rgba(42,212,183,0.08)] border-2'
                    : 'border-gray-200 border')
                }
              />
            </SwiperSlide>
          ))}
        </Swiper>
        {/* Custom Navigation Buttons */}
        <button
          className="thumb-swiper-prev absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white border border-gray-200 rounded-full w-7 h-7 flex items-center justify-center shadow-sm cursor-pointer"
          aria-label="Previous thumbnails"
          type="button"
        >
          {'<'}
        </button>
        <button
          className="thumb-swiper-next absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white border border-gray-200 rounded-full w-7 h-7 flex items-center justify-center shadow-sm cursor-pointer"
          aria-label="Next thumbnails"
          type="button"
        >
          {'>'}
        </button>
      </div>
    </div>
  );
};

export default ProductImageGallery; 