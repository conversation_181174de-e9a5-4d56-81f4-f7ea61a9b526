'use client';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import ProductImageGallery from './ProductImageGallery';
import ProductDetailsCard from './ProductDetailsCard';
import ProductPriceCard from './ProductPriceCard';
import DeliveryInfoCard from './DeliveryInfoCard';
import ReturnWarrantyCard from './ReturnWarrantyCard';
import SellerInfoCard from './SellerInfoCard';
import SimilarProductsSlider from './SimilarProductsSlider';
import ProductInfoSection from './ProductInfoSection';
import RatingReviewsSection from '@/components/ui/RatingReviewsSection';

export default function ProductDetailsClient({ product }) {
  const router = useRouter();
  const [quantity, setQuantity] = useState(product.quantity || 1);

  const handleQuantityChange = (qty) => {
    if (qty < 1) return;
    setQuantity(qty);
  };


  const handleCheckout = () => {
    // Implement checkout logic
    alert('Proceed to checkout!');
  };

  const handleWishlist = () => {
    // Implement wishlist logic
    alert('Added to wishlist!');
  };

  const handleGoToShop = () => {
    // Implement go to shop logic
    alert('Go to shop!');
  };

  const handleViewCart = () => {
    router.push('/cart/view');
  };

  // Transform related products from API data
  const similarProducts = product.related_products?.map(relatedProduct => ({
    id: relatedProduct.id.toString(),
    image: relatedProduct.main_image_url || '/images/placeholder-product.jpg',
    name: relatedProduct.title_en,
    unit: `${relatedProduct.net_weight} ${relatedProduct.net_weight_unit?.value_en || 'units'}`,
    price: parseFloat(relatedProduct.offer_price || relatedProduct.regular_price),
    rating: 4.5, // Default rating since not provided in API
  })) || [];

  return (
    <>
      <div className="md:grid grid-cols-12 gap-4 pt-[12px]">
        <div className="col-span-4">
          <ProductImageGallery images={product.images} />
        </div>
        <div className="col-span-5">
          <ProductDetailsCard
            title={product.name}
            description={product.description}
            brand={product.brand?.name_en}
            vendor={product.vendor?.name_tl_en}
            rating={4.5}
            ratingCount={0}
            modelNo={product.model_number}
            country={product.country_of_origin?.value_en}
            bestBefore={product.bbe_date ? new Date(product.bbe_date).toLocaleDateString() : null}
            userGroup={product.user_group?.value_en}
            inStock={product.is_active && product.is_approved}
            formulation={product.formulation?.value_en || `${product.servings} servings`}
            dietaryNeeds={product.dietary_needs ? product.dietary_needs.map(need => need.value_en).join(', ') : 'Not specified'}
            allergenInfo={product.allergen_info ? product.allergen_info.map(allergen => allergen.value_en).join(', ') : 'Not specified'}
            vegan={product.is_vegan}
            vegetarian={product.is_vegetarian}
            halal={product.is_halal}
            storageInfo={product.storage_conditions?.value_en}
            shippingInfo={`${product.package_length}x${product.package_width}x${product.package_height} cm, wt: ${product.package_weight} kg`}
            soldBy={product.vendor?.name_tl_en}
            shippedBy={product.vendor?.name_tl_en}
            flavours={product.flavour ? [product.flavour.value_en] : ['Original']}
            sizes={['Standard']}
            weights={[`${product.net_weight} ${product.net_weight_unit?.value_en || 'units'}`]}
          />
        </div>
        <div className="col-span-3">
          <ProductPriceCard
            product={product}
            price={product.price}
            oldPrice={product.oldPrice}
            discount={product.discount}
            quantity={quantity}
            onQuantityChange={handleQuantityChange}
            onCheckout={handleCheckout}
            onWishlist={handleWishlist}
          />
          <DeliveryInfoCard
            expectedDate={product.expectedDate}
            deliveryType={product.deliveryType}
          />
          <ReturnWarrantyCard
            returnPolicy={product.returnPolicy}
            warranty={product.warranty}
          />
          <SellerInfoCard
            sellerName={product.seller}
            onGoToShop={handleGoToShop}
          />
        </div>
      </div>
      <div className="col-span-12 mt-10">
        <SimilarProductsSlider products={similarProducts} />
      </div>
      {/* Product Information Section */}
      <div className="col-span-12 mt-10">
        <ProductInfoSection
          mode="main"
          description={product.description}
        />
      </div>
      {/* Additional Product Information Section */}
      <div className="col-span-12 mt-10">
        <ProductInfoSection
          mode="additional"
          usageInstructions="Product Description will display here.Product Description will display here.
          Product Description will display here.Product Description will display here.Product Description will display here.Product Description will display here.Product Description will display here.
          Product Description will display here.Product Description will display here.Product Description will display here."
          keyIngredients={[
            { label: "Servings:", value: "100ml" },
            { label: "Sugar:", value: "Free" },
            { label: "Dietary fat:", value: "0.03%" },
            { label: "Saturated Fat:", value: "Free" },
            { label: "Sugar:", value: "Free" },
            // ...more items  
          ]}
          nutritionImages={[
            "/images/p1.png",
            "/images/p2.png"
          ]}
        />
      </div>
      <div className="col-span-12 mt-10">
        <RatingReviewsSection />
      </div>
    </>
  );
} 
