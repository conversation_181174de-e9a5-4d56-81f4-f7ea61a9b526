'use client';

import { MouseEvent, useState } from 'react';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Heart, ShoppingCart, Star } from 'lucide-react';
import { toast } from 'sonner';
import DirhamSymbol from '@/components/ui/dirham-symbol';
import Link from 'next/link';
import useCartStore from '@/store/cartStore';

interface ProductCardProps {
  id: string;
  uuid: string;
  slug: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  rating: number;
  reviews: number;
  discount?: number;
  badge?: string;
  inStock?: boolean;
  unit: string;
  vendor_id: number;
}

const ProductCard = ({
  id,
  uuid,
  slug,
  name,
  price,
  originalPrice,
  image,
  rating = 0,
  reviews,
  discount,
  badge,
  unit = "120 Unit",
  inStock = true,
  vendor_id,
}: ProductCardProps) => {
  const [isWishlisted, setIsWishlisted] = useState(false);
  const { addItem } = useCartStore();

  const handleAddToCart = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    e.preventDefault();
    addItem({ product_id: id, quantity: 1, vendor_id });
  };

  const handleToggleWishlist = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    e.preventDefault();
    setIsWishlisted(!isWishlisted);
    toast.success(isWishlisted ? 'Removed from wishlist' : 'Added to wishlist');
  };

  return (
    <Link href={`/products/details/${slug}`} className="block group hover:shadow-lg transition-all duration-300 overflow-hidden">
      <Card className="group hover:shadow-lg transition-all duration-300 overflow-hidden">
        <CardContent className="p-0">
          <div className="relative overflow-hidden">
            {badge && (
              <Badge className="absolute top-0 left-0 z-10 bg-rico-secondary-dark-6 text-white rounded-tl-lg rounded-tr-none rounded-br-full rounded-bl-none pr-3 pl-2 py-1 text-xs sm:pr-5 sm:pl-2 sm:py-1">
                <span className="font-medium tracking-wide">{badge}</span>
              </Badge>
            )}
            <Button
              variant="ghost"
              size="icon"
              className={`absolute top-3 right-3 z-10 rounded-full p-1 text-red-500`}
              onClick={handleToggleWishlist}
            >
              <Heart className={`w-5 h-5 ${isWishlisted ? 'fill-current' : ''}`} />
            </Button>
            <div className="aspect-square bg-gray-100 overflow-hidden">
              <Image
                src={image}
                alt={id}
                width={300}
                height={300}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
            </div>
          </div>
          <div className="p-4">
            <h3 className="font-semibold text-base sm:text-lg text-gray-800 mb-1 line-clamp-2 h-12 sm:h-14 group-hover:text-teal-600 transition-colors">
              {name}
            </h3>
            <p className="text-sm text-gray-500 mb-2">{unit}</p>
            {/* <div className="flex items-center gap-2 mb-3">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 ${
                      i < Math.floor(rating)
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-gray-600">({rating.toFixed(1)})</span>
            </div> */}
            <div className="flex items-baseline gap-2 mb-4">
              {originalPrice && (
                <span className="text-sm sm:text-md text-gray-400 line-through">
                  <DirhamSymbol className="inline-block h-3 w-3 sm:h-4 sm:w-4" />{originalPrice.toFixed(2)}
                </span>
              )}
              <span className="text-lg sm:text-xl font-bold text-red-500"><DirhamSymbol className="inline-block h-3 w-3 sm:h-4 sm:w-4" />{price.toFixed(2)}</span>
            </div>
          </div>
            <Button
              className="w-full bg-rico-secondary-light-3 text-rico-secondary-dark-2 hover:bg-rico-secondary-light-2 rounded-tl-none rounded-tr-none"
              onClick={handleAddToCart}
              disabled={!inStock}
            >
              <ShoppingCart className="w-4 h-4 mr-2" />
              {inStock ? 'Add to Cart' : 'Out of Stock'}
            </Button>
        </CardContent>
      </Card>
    </Link>
  );
};

export default ProductCard;