import React from 'react';

interface SellerInfoCardProps {
  sellerName: string;
  onGoToShop: () => void;
}

const SellerInfoCard: React.FC<SellerInfoCardProps> = ({ sellerName, onGoToShop }) => {
  return (
    <div className="bg-white rounded-xl shadow p-4 mt-4">
      <div className="text-teal-700 font-bold text-lg mb-2">Seller Name : <span className="font-normal text-gray-800">{sellerName}</span></div>
      <button className="w-full py-2 rounded-lg bg-teal-50 text-teal-600 font-medium mt-1 hover:bg-teal-100" onClick={onGoToShop}>
        Go to Shop
      </button>
    </div>
  );
};

export default SellerInfoCard; 