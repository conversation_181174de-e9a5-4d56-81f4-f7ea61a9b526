import React from 'react';
import SectionTitleDivider from './SectionTitleDivider';

interface KeyIngredient {
  label: string;
  value: string;
}

interface KeyIngredientSectionProps {
  keyIngredients?: KeyIngredient[];
  nutritionImages?: string[];
}

const KeyIngredientSection: React.FC<KeyIngredientSectionProps> = ({
  keyIngredients = [],
  nutritionImages = [],
}) => {
  if (!keyIngredients.length) return null;

  return (
    <div className="w-full">
      <SectionTitleDivider title="Key Ingredients" />
      
      <div className="mt-6 flex flex-col lg:flex-row gap-8 items-start">
        {/* Left side - Key Ingredients */}
        <div className="flex-1 w-full">
          <div className="rounded-lg p-2 pt-0">
            <div className="space-y-2">
              {keyIngredients.map((item, idx) => (
                <div key={idx} className="flex justify-between items-center py-1 pr-195">
                  <span className="text-sm text-gray-600 font-medium">{item.label}</span>
                  <span className="text-sm text-gray-800 font-semibold ">{item.value}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Right side - Nutrition Facts Images */}
        {nutritionImages.length > 0 && (
          <div className='bg-gray-100 p-5 pt-2 pb-2'>
          <div className="flex gap-3 w-full lg:w-auto overflow-x-auto">
            {nutritionImages.map((image, idx) => (
              <div key={idx} className="w-32 h-40 lg:w-36 lg:h-48 flex-shrink-0">
                <img
                  src={image}
                  alt={`Nutrition Facts ${idx + 1}`}
                  className="w-full h-full object-contain"
                />
              </div>
            ))}
          </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default KeyIngredientSection;



