import React, { useEffect, useRef } from 'react';
import { Fancybox as NativeFancybox } from '@fancyapps/ui';
import '@fancyapps/ui/dist/fancybox/fancybox.css';
import { ThumbsUp, ThumbsDown } from 'lucide-react';

interface CustomerReviewCardProps {
  customerName: string;
  date: string;
  rating: number;
  reviewText: string;
  images?: string[];
  isVerified?: boolean;
  likes?: number;
  dislikes?: number;
}

const MAX_REVIEW_LENGTH = 220;

const StarRating: React.FC<{ rating: number }> = ({ rating }) => (
  <span className="flex items-center gap-0.5 text-yellow-500 text-base mt-1">
    {[1,2,3,4,5].map((star) => (
      <span key={star}>{rating >= star ? '★' : '☆'}</span>
    ))}
  </span>
);

const VerifiedBadge = () => (
  <span className="flex items-center gap-1 text-xs text-gray font-medium whitespace-nowrap">
    <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20"><circle cx="10" cy="10" r="10" fill="#2563eb"/><path d="M7.5 10.5l2 2 3-4" stroke="#fff" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/></svg>
    Verified Purchase
  </span>
);

const CustomerReviewCard: React.FC<CustomerReviewCardProps> = ({
  customerName,
  date,
  rating,
  reviewText,
  images = [],
  isVerified = false,
  likes = 0,
  dislikes = 0,
}) => {
  const [showFull, setShowFull] = React.useState(false);
  const displayText = showFull || reviewText.length <= MAX_REVIEW_LENGTH
    ? reviewText
    : reviewText.slice(0, MAX_REVIEW_LENGTH) + '...';

  const galleryRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (galleryRef.current) {
      NativeFancybox.bind(galleryRef.current, '[data-fancybox="review-gallery"]');
    }
    return () => {
      if (galleryRef.current) {
        NativeFancybox.unbind(galleryRef.current, '[data-fancybox="review-gallery"]');
      }
    };
  }, [images]);

  return (
    <div className="relative bg-white rounded-xl shadow p-5 mb-4 border border-gray-200 min-h-[180px] flex flex-col justify-between">
      {/* Top: Avatar, Name, Date, Verified */}
      <div className="flex items-start justify-between flex-wrap gap-y-1 mb-1 w-full">
        <div className="flex items-start min-w-0">
          <img src="/images/brand/placeholder.png" alt="avatar" className="w-8 h-8 rounded-full mr-3 border border-gray-200 mt-1" />
          <div className="flex flex-col flex-1 min-w-0">
            <span className="font-semibold text-gray-800 text-sm leading-tight truncate">{customerName}</span>
            <span className="text-xs text-gray-500 mt-1 truncate">{date}</span>
          </div>
        </div>
        {isVerified && (
          <div className="flex items-end justify-end w-full sm:w-auto mt-1 sm:mt-0 sm:ml-2">
            <VerifiedBadge />
          </div>
        )}
      </div>
      <StarRating rating={rating} />
      {/* Unified Review Text & Images Block */}
      <div className="bg-rico-secondary-light-4 rounded-lg p-4 mb-2 mt-3">
        <div className="text-gray-700 text-sm mb-2">
          {displayText}
          {reviewText.length > MAX_REVIEW_LENGTH && (
            <button className="text-blue-600 ml-2 text-xs font-medium hover:underline" onClick={() => setShowFull(!showFull)}>
              {showFull ? 'Show Less' : 'Show More'}
            </button>
          )}
        </div>
        {images.length > 0 && (
          <>
            <div className="font-medium text-xs text-rico-secondary-dark-5 mb-2">Image for Reference Purpose:</div>
            <div className="flex gap-3 flex-wrap" ref={galleryRef}>
              {images.map((img, idx) => (
                <a
                  key={idx}
                  href={img}
                  data-fancybox="review-gallery"
                  data-src={img}
                  className="block"
                >
                  <img
                    src={img}
                    alt="review"
                    className="w-16 h-16 object-cover rounded-md border border-gray-200 bg-white cursor-pointer hover:scale-105 transition-transform"
                    onError={e => {
                      const target = e.currentTarget;
                      if (target.src !== '/images/brand/placeholder.png') {
                        target.src = '/images/brand/placeholder.png';
                      }
                    }}
                  />
                </a>
              ))}
            </div>
          </>
        )}
      </div>
      {/* Actions */}
      <div className="flex items-center justify-between mt-2 flex-wrap gap-y-2">
        <div className="flex items-center gap-3 flex-wrap">
          <button className="text-xs text-blue-600 font-normal hover:underline p-0 m-0 bg-transparent border-none outline-none cursor-pointer" style={{minWidth: 'auto'}}>
            See All Reply
          </button>
          <span className="flex items-center gap-1 text-xs text-gray-700 font-normal">
            <ThumbsUp className="w-4 h-4 text-rico-secondary-dark-4" strokeWidth={1.5} />
            {likes}
          </span>
          <span className="flex items-center gap-1 text-xs text-gray-700 font-normal">
            <ThumbsDown className="w-4 h-4 text-rico-secondary-dark-4" strokeWidth={1.5} />
            {dislikes.toString().padStart(2, '0')}
          </span>
        </div>
        <div className="flex items-center gap-2 flex-wrap">
          <button className="text-xs text-red-500 hover:underline">Report</button>
          <span className="mx-2 h-5 border-l border-gray-300"></span>
          <button className="px-3 py-1 rounded border border-rico-primary text-xs font-medium text-gray-700 hover:bg-gray-100">Give a Reply</button>
        </div>
      </div>
    </div>
  );
};

export default CustomerReviewCard; 