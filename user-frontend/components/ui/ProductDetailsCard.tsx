'use client'
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

interface ProductDetailsCardProps {
  brand?: string;
  vendor?: string;
  title?: string;
  rating?: number;
  ratingCount?: number;
  modelNo?: string;
  country?: string;
  bestBefore?: string;
  userGroup?: string;
  inStock?: boolean;
  description?: string;
  flavours?: string[];
  sizes?: string[];
  weights?: string[];
  formulation?: string;
  dietaryNeeds?: string;
  allergenInfo?: string;
  storageInfo?: string;
  shippingInfo?: string;
  vegan?: boolean;
  vegetarian?: boolean;
  halal?: boolean;
  soldBy?: string;
  shippedBy?: string;
}

const ProductDetailsCard: React.FC<ProductDetailsCardProps> = ({
  brand,
  vendor,
  title,
  rating = 0,
  ratingCount = 0,
  modelNo,
  country,
  bestBefore,
  userGroup,
  inStock = false,
  description,
  flavours = [],
  sizes = [],
  weights = [],
  formulation,
  dietaryNeeds,
  allergenInfo,
  storageInfo,
  shippingInfo,
  vegan = false,
  vegetarian = false,
  halal = false,
  soldBy,
  shippedBy,
}) => {
  // Add state for selected options
  const [selectedFlavour, setSelectedFlavour] = useState(flavours[0]);
  const [selectedSize, setSelectedSize] = useState(sizes[0]);
  const [selectedWeight, setSelectedWeight] = useState(weights[0]);
  const { t } = useTranslation('common');

  return (
    <div className="w-full max-w-3xl mx-auto">
      <div className="flex items-center mb-2 justify-between">
        <span className="bg-rico-secondary-light-3 text-gray-700 text-xs font-medium px-3 py-1 rounded-full mr-2 flex items-center shadow-sm">
          <svg className="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a2 2 0 01-2.828 0L9 13zm-6 6h6v-6H3v6z" /></svg>
          {brand}
        </span>
        <div className="flex items-center gap-3">
          <button className="w-10 h-10 rounded-full bg-rico-secondary-light-4 flex items-center justify-center shadow-md focus:outline-none">
            <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M12 21C12 21 4 13.5 4 8.5C4 5.42 6.42 3 9.5 3C11.24 3 12.91 3.81 14 5.08C15.09 3.81 16.76 3 18.5 3C21.58 3 24 5.42 24 8.5C24 13.5 16 21 16 21H12Z"/></svg>
          </button>
          <button className="w-10 h-10 rounded-full bg-rico-secondary-light-4 flex items-center justify-center shadow-md focus:outline-none">
            <svg className="w-6 h-6 text-rico-primary" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><circle cx="18" cy="5" r="3"/><circle cx="6" cy="12" r="3"/><circle cx="18" cy="19" r="3"/><line x1="8.59" y1="13.51" x2="15.42" y2="17.49"/><line x1="15.41" y1="6.51" x2="8.59" y2="10.49"/></svg>
          </button>
        </div>
      </div>
      <h1 className="text-3xl font-semibold text-rico-gray-shade-2 leading-tight mb-2">{title}</h1>
      <div className="text-sm text-gray-500 mb-2">{t('by')} : <span className="text-teal-600 font-medium">{vendor}</span></div>
      
      <div className="bg-rico-secondary-light-4 rounded-lg p-3 mb-4 shadow-sm">
        <div className="flex justify-between items-start w-full flex-wrap">
          {/* Left: Stars and Info */}
          <div className="flex-1 min-w-[320px]">
            <div className="flex items-center mb-1">
              <span className="flex items-center text-yellow-500 font-semibold text-base">
                {[1,2,3,4,5].map((star) => (
                  <span key={star} className="mr-0.5">
                    {rating >= star ? '★' : rating >= star - 0.5 ? '★' : '☆'}
                  </span>
                ))}
                <span className="ml-1 text-gray-700 font-normal">({rating} / 5)</span>
              </span>
            </div>
            <div className="grid grid-cols-2 gap-x-8 gap-y-0.5 text-sm mt-1">
              <div><span className="font-bold text-gray-700">Country of Origin</span> : <span className="font-normal">{country}</span></div>
              <div><span className="font-bold text-gray-700">Reg No</span>: <span className="font-normal">12314mn</span></div>
              <div><span className="font-bold text-gray-700">Best Before Date</span> : <span className="font-normal">{bestBefore}</span></div>
              <div><span className="font-bold text-gray-700">Model No</span>: <span className="font-normal">{modelNo}</span></div>
              <div><span className="font-bold text-gray-700">User Group</span>: <span className="font-normal">{userGroup}</span></div>
              <div><span className="font-bold text-gray-700">Reg No</span>: <span className="font-normal">12314mn</span></div>
            </div>
          </div>
          {/* Right: FAQ, In Stock */}
          <div className="flex flex-col items-end min-w-[120px]">
            <div className="flex items-center gap-2 mb-1">
              <span className="font-medium text-teal-600 cursor-pointer">FAQ</span>
              <span className="text-gray-400 font-bold">|</span>
              <span className="font-semibold text-teal-600">In Stock</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mb-2">
        <span className="font-semibold text-gray-700">Description :</span>
        <span className="text-gray-600"> {description}</span>
      </div>
      <div className="flex flex-wrap items-center gap-3 mb-2">
        <span className="font-semibold text-gray-700">Flavour :</span>
        {flavours.map((flavour, i) => (
          <button
            key={i}
            type="button"
            onClick={() => setSelectedFlavour(flavour)}
            className={
              `px-4 py-1 rounded-lg text-base font-medium border transition-all duration-150 ` +
              (selectedFlavour === flavour
                ? 'bg-teal-100 border border-teal-600 text-gray-700 shadow-[0_0_0_2px_#05966920]'
                : 'bg-white border border-rico-primary text-gray-700 hover:bg-teal-100')
            }
          >
            {flavour}
          </button>
        ))}
      </div>
      <div className="flex flex-wrap items-center gap-3 mb-2">
        <span className="font-semibold text-gray-700">Size :</span>
        {sizes.map((size, i) => (
          <button
            key={i}
            type="button"
            onClick={() => setSelectedSize(size)}
            className={
              `px-4 py-1 rounded-lg text-base font-medium border transition-all duration-150 ` +
              (selectedSize === size
                ? 'bg-teal-100 border border-teal-600 text-gray-700 shadow-[0_0_0_2px_#05966920]'
                : 'bg-white border border-rico-primary text-gray-700 hover:bg-teal-100')
            }
          >
            {size}
          </button>
        ))}
      </div>
      <div className="flex flex-wrap items-center gap-3 mb-2">
        <span className="font-semibold text-gray-700">Weight :</span>
        {weights.map((weight, i) => (
          <button
            key={i}
            type="button"
            onClick={() => setSelectedWeight(weight)}
            className={
              `px-4 py-1 rounded-lg text-base font-medium border transition-all duration-150 ` +
              (selectedWeight === weight
                ? 'bg-teal-100 border border-teal-600 text-gray-700 shadow-[0_0_0_2px_#05966920]'
                : 'bg-white border border-rico-primary text-gray-700 hover:bg-teal-100')
            }
          >
            {weight}
          </button>
        ))}
      </div>
      <div className="mb-2">
        <span className="font-semibold text-gray-700">Formulation :</span>
        <span className="text-gray-600"> {formulation}</span>
      </div>
      <div className="mb-2">
        <span className="font-semibold text-gray-700">Dietary Needs :</span>
        <span className="text-gray-600"> {dietaryNeeds}</span>
      </div>
      <div className="grid grid-cols-3 gap-x-4 mb-2">
        <div>
          <span className="font-semibold text-gray-700">Vegan:</span>
          <span className="text-gray-600"> {vegan ? 'Yes' : 'No'}</span>
        </div>
        <div>
          <span className="font-semibold text-gray-700">Vegetarian:</span>
          <span className="text-gray-600"> {vegetarian ? 'Yes' : 'No'}</span>
        </div>
        <div>
          <span className="font-semibold text-gray-700">Halal:</span>
          <span className="text-gray-600"> {halal ? 'Yes' : 'No'}</span>
        </div>
      </div>
      <div className="mb-2">
        <span className="font-semibold text-gray-700">Allergen Information :</span>
        <span className="text-gray-600"> {allergenInfo}</span>
      </div>
      <div className="grid grid-cols-2 gap-x-4 mb-2">
        <div>
          <span className="font-semibold text-gray-700">Shipping Info :</span>
          <span className="text-gray-600"> {shippingInfo}</span>
        </div>
        <div>
          <span className="font-semibold text-gray-700">Storage Instruction :</span>
          <span className="text-gray-600"> {storageInfo}</span>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-x-4">
        <div>
          <span className="font-semibold text-gray-700">Sold by :</span>
          <span className="text-gray-600"> {soldBy}</span>
        </div>
        <div>
          <span className="font-semibold text-gray-700">Shipped by :</span>
          <span className="text-gray-600"> {shippedBy}</span>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailsCard; 
