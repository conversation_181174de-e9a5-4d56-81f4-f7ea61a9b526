'use client';

import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Star, ShoppingCart } from 'lucide-react';
import DirhamSymbol from '@/components/ui/dirham-symbol';
import Link from 'next/link';
import useCart from '@/hooks/useCart';

const SmallProductCard = ({ product }) => {
  const { addItem } = useCart();

  const handleAddToCart = (e) => {
    e.preventDefault();
    e.stopPropagation();
    addItem({
      product_id: product.id,
      quantity: 1,
      vendor_id: product.vendor_id,
    });
  };

  return (
    <Link href={`/products/details/${product.slug}`} className="block">
      <Card className="w-full rounded-xl overflow-hidden shadow-md border-0 bg-white">
        <CardContent className="p-3 flex items-center space-x-4">
          <div className="relative w-24 h-24 shrink-0 bg-gray-100 rounded-lg p-2">
            <Image
              src={product.image}
              alt={product.name}
              layout="fill"
              objectFit="contain"
            />
          </div>
          <div className="flex-1 flex flex-col justify-between self-stretch">
            <div>
              <h3 className="text-sm font-semibold text-gray-800 mb-1 h-auto md:h-10 overflow-hidden">
                {product.name}
              </h3>
              <div className="flex items-center my-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 ${
                      i < Math.floor(product.rating)
                        ? 'text-yellow-400 fill-yellow-400'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <p className="text-sm text-gray-500">{product.unit || '120 Unit'}</p>
            </div>
            <div className="flex items-center justify-between mt-2">
              <span className="text-base sm:text-lg font-bold text-gray-800">
                <DirhamSymbol className="inline-block h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                {Number(product.price).toFixed(2)}
              </span>
              <Button
                size="icon"
                className="bg-rico-secondary-light-3 text-rico-secondary-dark-3 hover:bg-cyan-200 rounded-lg w-9 h-9"
                onClick={handleAddToCart}
              >
                <ShoppingCart className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

export default SmallProductCard;