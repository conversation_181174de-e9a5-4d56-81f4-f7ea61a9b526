import React from 'react';
import { Icon } from '@iconify/react';

interface DeliveryInfoCardProps {
  expectedDate?: string;
  deliveryType?: string;
}

const DeliveryInfoCard: React.FC<DeliveryInfoCardProps> = ({ expectedDate, deliveryType }) => {
  return (
    <div className="bg-white rounded-xl shadow p-5 mb-5">
      <div className="text-teal-700 font-bold text-xl mb-3">Delivery Information</div>
      <div className="flex items-center gap-2 text-gray-700 mb-2 text-base">
        <Icon icon="mdi:calendar-clock-outline" className="text-2xl text-gray-500" />
        <span>Expected Delivery Date : <span className="font-bold">{expectedDate}</span></span>
      </div>
      <div className="flex items-center gap-2 text-gray-700 text-base">
        <Icon icon="mdi:truck-outline" className="text-2xl text-gray-500" />
        <span>{deliveryType}</span>
      </div>
    </div>
  );
};

export default DeliveryInfoCard; 