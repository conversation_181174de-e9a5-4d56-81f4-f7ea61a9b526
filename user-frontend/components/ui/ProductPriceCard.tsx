import React from 'react';
import { Icon } from '@iconify/react';
import { TriangleAlert } from 'lucide-react';
import DirhamSymbol from './dirham-symbol';
import useCart from '@/hooks/useCart';
import useWishlist from '@/hooks/useWishlist';

interface ProductPriceCardProps {
  product: any; // Replace 'any' with your actual product type
  price?: number;
  oldPrice?: number;
  discount?: string;
  quantity: number;
  onQuantityChange: (qty: number) => void;
  onCheckout: () => void;
  onWishlist: () => void;
}

const ProductPriceCard: React.FC<ProductPriceCardProps> = ({
  product,
  price,
  oldPrice,
  discount,
  quantity,
  onQuantityChange,
  onCheckout,
  onWishlist,
}) => {
  const { addItem, loading } = useCart();
  const { addToWishlist, loading: wishlistLoading } = useWishlist();

  const handleAddToCart = () => {
    addItem({
      product_id: product.id,
      quantity: quantity,
    });
  };

  const handleAddToWishlist = () => {
    console.log('Product object:', product);
    console.log('Product ID:', product.id);
    console.log('Product ID:', product.id);
    
    // Use uuid if available, otherwise fallback to id
    const productIdentifier = product.id || product.id;
    console.log('Using product identifier:', productIdentifier);
    
    if (!productIdentifier) {
      console.error('No product identifier found!');
      return;
    }
    
    addToWishlist(productIdentifier);
  };
  return (
    <div className="bg-white rounded-xl shadow p-5 flex flex-col gap-4 mb-4">
      <div className="flex justify-between items-start">
        {/* Left Side: Price and Quantity */}
        <div className="flex flex-col gap-4">
          <div className="text-gray-900 text-3xl font-bold flex items-center">
            <DirhamSymbol className="w-6 h-6 mr-1" /> {price}
          </div>
          <div className="flex items-center gap-3">
            <span className="text-gray-700 font-semibold text-lg">Qty :</span>
            <div className="flex items-center h-9 rounded-full border border-gray-300 overflow-hidden">
              <button
                className="w-9 h-9 flex items-center justify-center text-xl text-gray-600 font-bold border-r border-gray-300 focus:outline-none hover:bg-gray-100 disabled:opacity-50"
                onClick={() => onQuantityChange(quantity - 1)}
                disabled={quantity <= 1}
              >
                –
              </button>
              <span className="w-9 h-9 flex items-center justify-center text-base font-semibold bg-white">
                {quantity}
              </span>
              <button
                className="w-9 h-9 flex items-center justify-center text-xl text-gray-600 font-bold border-l border-gray-300 focus:outline-none hover:bg-gray-100"
                onClick={() => onQuantityChange(quantity + 1)}
              >
                +
              </button>
            </div>
          </div>
        </div>

        {/* Right Side: Old Price and Discount */}
        <div className="flex flex-col items-end">
          <div className="flex items-center gap-2">
            <span className="text-gray-400 line-through text-lg font-medium flex items-center">
              {oldPrice && (
                <>
                  <DirhamSymbol className="w-4 h-4 mr-1" /> {oldPrice}
                </>
              )}
            </span>
            {discount && <span className="text-red-600 text-base font-semibold">{discount} off</span>}
          </div>
        </div>
      </div>
      <div className="flex gap-3 mt-2">
        <button className="flex-1 py-2 shadow rounded-lg bg-teal-50 text-teal-700 font-semibold text-base border border-teal-100 shadow-none hover:bg-teal-100 transition-colors cursor-pointer shadow-4" onClick={onCheckout}>
          Checkout Now
        </button>
        <button
          className="flex-1 py-2 rounded-lg bg-teal-700 text-white font-semibold text-base shadow-none hover:bg-teal-800 transition-colors cursor-pointer disabled:opacity-50"
          onClick={handleAddToCart}
          disabled={loading}
        >
          {loading ? 'Adding...' : 'Add to cart'}
        </button>
      </div>
      <button 
        className="w-full flex shadow items-center justify-center gap-2 py-2 rounded-lg bg-teal-50 text-gray-700 font-medium text-base mt-1 hover:bg-teal-100 transition-colors cursor-pointer disabled:opacity-50" 
        onClick={handleAddToWishlist}
        disabled={wishlistLoading}
      >
        <Icon icon="mdi:heart-outline" className="text-red-500 text-xl" /> 
        {wishlistLoading ? 'Adding...' : 'Add to wishlist'}
      </button>

      <button className="w-full flex shadow items-center justify-center gap-2 py-2 pl-4 bg-teal-50 text-[#D90035] text-start font-medium text-base mt-1 hover:bg-teal-100 transition-colors cursor-pointer" onClick={onWishlist}>
        <TriangleAlert className="text-red-500 text-xl" /> Purchase over AED 100 to enjoy free delivery charges.
      </button>
    </div>
  );
};

export default ProductPriceCard; 