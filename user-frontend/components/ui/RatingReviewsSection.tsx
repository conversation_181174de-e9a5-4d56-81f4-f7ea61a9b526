import React from 'react';
import SectionTitleDivider from './SectionTitleDivider';
import CustomerReviewCard from './CustomerReviewCard';


const mockStarBreakdown = [
  { star: 5, percent: 41 },
  { star: 4, percent: 21 },
  { star: 3, percent: 11 },
  { star: 2, percent: 6 },
  { star: 1, percent: 4 },
];

const averageRating = 4.6;
const reviewCount = 566;

// Mock review data
const mockReviews = [
  {
    id: 1,
    customerName: 'Customer Name',
    date: 'April 10, 2023',
    rating: 5,
    reviewText: 'Review text will shown here. Review text will shown hereReview text will shown hereReview text will shown hereReview text will shown hereReview text will shown hereReview text will shown hereReview text will shown hereReview text will shown hereReview text will shown hereReview text will shown hereReview text will shown hereReview text will shown here. Review text will shown hereReview text will shown here',
    images: ['/images/placeholder.jpg', '/images/placeholder.jpg', '/images/placeholder.jpg'],
    isVerified: true,
    likes: 44,
    dislikes: 4
  },
  {
    id: 2,
    customerName: 'Customer Name',
    date: 'April 10, 2023',
    rating: 4,
    reviewText: 'Review text will shown here. Review text will shown hereReview text will shown hereReview text will shown hereReview text will shown hereReview text will shown hereReview text will shown hereReview text will shown hereReview text will shown hereReview text will shown hereReview text will shown hereReview text will shown hereReview text will shown here. Review text will shown hereReview text will shown here',
    isVerified: true,
    likes: 44,
    dislikes: 4
  }
];

// FAQ items related to this product


const RatingReviewsSection: React.FC = () => {
  return (
    <div className="w-full">
      <SectionTitleDivider title="Rating & Reviews" />
      {/* Average rating with stars below title */}
     <br></br>
      {/* Main review box */}
      <div className="bg-rico-secondary-light-3 rounded-2xl py-7 px-5 flex flex-col md:flex-row gap-4 items-stretch mb-8">
        {/* Left: Star breakdown */}
          <div className="col-span-12 md:col-span-5 pr-18">
            <div className="font-medium text-base text-rico-secondary-dark-1 mb-3">Customer Review</div>
            <div className="flex flex-col gap-2">
              {mockStarBreakdown.map((item) => (
                <div key={item.star} className="flex items-center gap-2 text-sm">
                  <span className="w-10 text-rico-secondary-dark-1">{item.star} star</span>
                  <div className="w-32 sm:w-40 md:w-48 h-4 bg-white overflow-hidden">
                    <div className="h-full bg-yellow-500" style={{ width: `${item.percent}%` }}></div>
                  </div>
                  <span className="w-8 text-right text-rico-secondary-dark-2">{item.percent}%</span>
                </div>
              ))}
            </div>
          </div>
        {/* Center: Average rating */}
        <div className="flex flex-col justify-center min-w-[220px] pr-18">
          <div className="flex items-center mb-1">
            <span className="text-5xl font-bold text-rico-secondary-dark-1 mr-4">{averageRating.toFixed(2)}</span>
            <span className="flex items-center gap-1 text-yellow-500 text-2xl">
              {'★'.repeat(Math.round(averageRating))}
              <span className="text-rico-secondary-dark-1 text-lg font-semibold ml-2">(4.9/5)</span>
            </span>
          </div>
          <div className="text-base text-gray-600 ml-1">Reviewed by {reviewCount}</div>
        </div>

         {/* Vertical divider */}
          <div className="hidden md:flex md:col-span-2 justify-start items-center pr-18">
            <div className="h-16 w-0.5 bg-gray-300"></div>
          </div>
        {/* Right: Add review */}
        <div className="flex flex-col items-center justify-center min-w-[200px] px-8">
          <div className="font-semibold text-xl text-rico-secondary-dark-1 mb-2">Give a Review</div>
          <div className="text-base text-gray-600 mb-4">Share your thoughts</div>
          <button className="px-6 py-2 rounded-full bg-rico-secondary-dark-1 text-white font-semibold text-base shadow hover:bg-rico-secondary-dark-2 transition-colors">Add a Review</button>
        </div>
      </div>

      {/* Customer Reviews List */}
      <div className="mt-6">
        {mockReviews.map(review => (  
          <CustomerReviewCard
            key={review.id}
            customerName={review.customerName}
            date={review.date}
            rating={review.rating}
            reviewText={review.reviewText}
            images={review.images}
            isVerified={review.isVerified}
            likes={review.likes}
            dislikes={review.dislikes}
          />
        ))}
      </div>
      
      {/* FAQ Section */}
      
    </div>
  );
};

export default RatingReviewsSection; 
