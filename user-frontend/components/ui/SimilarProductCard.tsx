import React from 'react';
import { Icon } from '@iconify/react';

interface Product {
  id: string;
  image: string;
  name: string;
  unit: string;
  price: number;
  rating: number;
}

const SimilarProductCard: React.FC<{ product: Product }> = ({ product }) => {
  return (
    <div className="bg-white rounded-2xl border border-gray-200 shadow-sm p-3 sm:p-4 flex flex-col sm:flex-row items-center min-w-0 w-full h-auto sm:h-34">
      <div className="shrink-0 flex items-center justify-center w-20 h-20 sm:w-20 sm:h-20 mb-3 sm:mb-0 sm:mr-3">
        <img src={product.image} alt={product.name} className="w-20 h-20 sm:w-20 sm:h-20 object-contain" />
      </div>
      <div className="flex-1 flex flex-col justify-center h-full w-full">
        <div className="font-bold text-gray-900 text-sm sm:text-base leading-tight mb-1 text-center sm:text-left">{product.name}</div>
        <div className="flex items-center gap-0.5 text-yellow-500 text-xs mb-1 justify-center sm:justify-start">
          {[1,2,3,4,5].map((star) => (
            <Icon key={star} icon={star <= Math.floor(product.rating) ? 'mdi:star' : (star - product.rating <= 0.5 ? 'mdi:star-half-full' : 'mdi:star-outline')} className="text-base" />
          ))}
        </div>
        <div className="text-gray-500 text-xs mb-2 sm:mb-1 text-center sm:text-left">{product.unit}</div>
        <div className="flex items-center gap-1 w-full justify-center sm:justify-start">
          <Icon icon="mdi:currency-bdt" className="text-base sm:text-lg text-gray-800" />
          <span className="text-gray-900 font-bold text-sm sm:text-base">{product.price}</span>
          <button aria-label="Add to cart" className="ml-auto w-7 h-7 sm:w-8 sm:h-8 flex items-center justify-center bg-teal-100 hover:bg-teal-200 rounded-md border border-teal-100 cursor-pointer">
            <Icon icon="mdi:cart-outline" className="text-lg sm:text-xl w-5 h-5 sm:w-6 sm:h-6 text-teal-700" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimilarProductCard; 