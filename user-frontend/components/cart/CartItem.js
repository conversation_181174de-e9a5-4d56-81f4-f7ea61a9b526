import Image from 'next/image';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Heart, Trash2 } from 'lucide-react';
import DirhamSymbol from '@/components/ui/dirham-symbol';
import useCart from '@/hooks/useCart';

const CartItem = ({ item }) => {
  const { updateItem, removeItem, loading } = useCart();

  const handleQuantityChange = (newQuantity) => {
    if (newQuantity < 1) {
      removeItem(item.id);
    } else {
      updateItem(item.id, newQuantity);
    }
  };

  const handleRemoveItem = () => {
    removeItem(item.id);
  };

  return (
    <div className="relative flex items-center py-3 px-5 border">
      <div className="flex items-center fixed-left">
        <Checkbox id={`item-${item.id}`} className="mt-1" />
        <div className="relative w-20 h-20 ml-4">
          <Image
            src={item.product_image || '/images/placeholder-product.jpg'}
            alt={item.product_name}
            layout="fill"
            objectFit="contain"
            className="rounded-md"
          />
        </div>
        <div className="ml-4">
          <h3 className="text-base font-medium">{item.product_name}</h3>
          <div className="flex items-center mt-1">
            <DirhamSymbol className="w-4 h-4 mr-1" />
            <span className="text-base font-medium">{item.unit_price}</span>
          </div>
          <div className="flex items-center space-x-2 mt-4">
            <Button variant="ghost" size="icon" className="w-8 h-8 bg-[#f1fcf9] border-2 border-[#feffff] hover:bg-red-50 rounded-lg shadow-sm">
              <Heart className="w-4 h-4 text-red-500" />
            </Button>
            <Button variant="ghost" size="icon" className="w-8 h-8 bg-[#f1fcf9] hover:bg-red-50 border-2 border-[#feffff] rounded-lg shadow-sm" onClick={handleRemoveItem} disabled={loading}>
              <Trash2 className="w-4 h-4 text-red-500" />
            </Button>
          </div>
        </div>
      </div>
      <div className="absolute left-1/2 transform -translate-x-1/2">
        <div className="flex items-center justify-center border-2 border-gray-300 rounded-full overflow-hidden">
          <Button variant="ghost" size="icon" className="w-10 h-10 rounded-none bg-[#f4f4f4] hover:bg-gray-100 flex items-center justify-center" onClick={() => handleQuantityChange(item.quantity - 1)} disabled={loading}>
            {item.quantity === 1 ? (
              <Trash2 className="w-4 h-4" />
            ) : (
              <span className="text-gray-600 font-medium">−</span>
            )}
          </Button>
          <div className="px-4 py-2 border-l border-r border-gray-300 min-w-[50px] text-center flex items-center justify-center">
            <span className="text-base font-medium">{item.quantity}</span>
          </div>
          <Button variant="ghost" size="icon" className="w-10 h-10 rounded-none hover:bg-gray-100 bg-[#f4f4f4] flex items-center justify-center" onClick={() => handleQuantityChange(item.quantity + 1)} disabled={loading}>
            <span className="text-gray-600 font-medium">+</span>
          </Button>
        </div>
      </div>
        
      <div className="flex items-center ml-auto">
        
        <div className="text-right w-32 fixed-position">
          {item.surpriseSale && (
                      <p className="text-xs text-red-500 font-medium mb-5">Surprise Sale !!!</p>
                    )}
           {item.price_comparison?.has_discount && (
            <div className="flex items-center justify-end text-red-500 line-through text-sm">
              <DirhamSymbol className="mr-1" />
              <span>{item.base_price}</span>
            </div>
          )}
          <div className="flex items-center justify-end">
            <DirhamSymbol className="mr-1" />
            <span className="text-lg font-bold">{item.total_price}</span>
          </div>
         
        </div>
      </div>
    </div>
  );
};

export default CartItem;
