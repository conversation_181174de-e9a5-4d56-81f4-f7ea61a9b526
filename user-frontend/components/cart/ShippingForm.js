'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Heart, Trash2 } from 'lucide-react';
import Image from 'next/image';
import DirhamSymbol from '@/components/ui/dirham-symbol';
import CartItemsList from '@/app/(main)/profile/CartItemsList';
import useAuthStore from '@/store/authStore';

const ShippingForm = ({ onBack, onContinue, cartItems = [], selectedItems = [], handleSelectItem, handleQuantityChange, handleRemoveItem, selectAll = false, handleSelectAll, handleRemoveAll }) => {
  const [formData, setFormData] = useState({
    addressType: '',
    country: 'UAE',
    flatNumber: '',
    streetAddress: '',
    areaZone: '',
    stateProvince: '',
    nearestLandmark: '',
    zipCode: '',
    saveForFurtherUse: false
  });

  const [savedAddresses, setSavedAddresses] = useState([]);
  const [selectedAddressId, setSelectedAddressId] = useState(null);
  const [isEditing, setIsEditing] = useState(true);
  const [editingAddressId, setEditingAddressId] = useState(null);
  const [isContinued, setIsContinued] = useState(false);

  const [couponCode, setCouponCode] = useState('');
  const [errors, setErrors] = useState({});

  const user = useAuthStore((state) => state.user);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.addressType) newErrors.addressType = 'Address type is required';
    if (!formData.country) newErrors.country = 'Country is required';
    if (!formData.flatNumber.trim()) newErrors.flatNumber = 'Flat/Villa number is required';
    if (!formData.streetAddress.trim()) newErrors.streetAddress = 'Street address is required';
    if (!formData.areaZone) newErrors.areaZone = 'Area/Zone/Locality is required';
    if (!formData.stateProvince) newErrors.stateProvince = 'State/Province/Region is required';
    if (!formData.zipCode.trim()) newErrors.zipCode = 'ZIP/Postal code is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      if (editingAddressId) {
        handleEditAddress(editingAddressId, formData);
      } else {
        handleAddNewAddress(formData);
      }
    }
  };

  const handleAddNewAddress = (addressData) => {
    const newAddress = {
      id: Date.now(),
      ...addressData
    };
    setSavedAddresses(prev => [...prev, newAddress]);
    setSelectedAddressId(newAddress.id);
    setIsEditing(false);
    setFormData({
      addressType: '',
      country: 'UAE',
      flatNumber: '',
      streetAddress: '',
      areaZone: '',
      stateProvince: '',
      nearestLandmark: '',
      zipCode: '',
      saveForFurtherUse: false
    });
    onContinue(newAddress);
  };

  const handleEditAddress = (id, updatedData) => {
    setSavedAddresses(prev => prev.map(addr => 
      addr.id === id ? { ...addr, ...updatedData } : addr
    ));
    setIsEditing(false);
    setEditingAddressId(null);
    setFormData({
      addressType: '',
      country: 'UAE',
      flatNumber: '',
      streetAddress: '',
      areaZone: '',
      stateProvince: '',
      nearestLandmark: '',
      zipCode: '',
      saveForFurtherUse: false
    });
  };

  const handleDeleteAddress = (id) => {
    setSavedAddresses(prev => prev.filter(addr => addr.id !== id));
    if (selectedAddressId === id) {
      setSelectedAddressId(null);
    }
  };

  const handleSelectAddress = (id) => {
    setSelectedAddressId(id);
  };

  const startEditingAddress = (address) => {
    setFormData({
      addressType: address.addressType,
      country: address.country,
      flatNumber: address.flatNumber,
      streetAddress: address.streetAddress,
      areaZone: address.areaZone,
      stateProvince: address.stateProvince,
      nearestLandmark: address.nearestLandmark,
      zipCode: address.zipCode,
      saveForFurtherUse: address.saveForFurtherUse
    });
    setEditingAddressId(address.id);
    setIsEditing(true);
  };

  const startAddingNewAddress = () => {
    setFormData({
      addressType: '',
      country: 'UAE',
      flatNumber: '',
      streetAddress: '',
      areaZone: '',
      stateProvince: '',
      nearestLandmark: '',
      zipCode: '',
      saveForFurtherUse: false
    });
    setEditingAddressId(null);
    setIsEditing(true);
  };

  const handleApplyCoupon = () => {
    console.log('Applying coupon:', couponCode);
  };

  const groupedByVendor = cartItems.reduce((acc, item) => {
    const vendor = item.vendor || 'Default Vendor';
    if (!acc[vendor]) acc[vendor] = [];
    acc[vendor].push(item);
    return acc;
  }, {});

  return (
    <div className="bg-gray-50 min-h-screen py-6">
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            {/* Header - same width as card */}
            <div className="flex items-center gap-4 mb-4 md:mb-6">
              <h2 className="text-lg md:text-xl font-semibold text-[#196c67]">Shipping Information</h2>
              <hr className="flex-1" />
              {!isEditing && savedAddresses.length > 0 && (
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    className="bg-[#e6faf7] text-[#196c67] border-[#a2eddf] rounded-md px-4 py-2"
                    onClick={startAddingNewAddress}
                  >
                    Add a New Shipping Address
                  </Button>
                  <Button 
                    className="bg-[#196c67] text-white rounded-md px-4 py-2"
                    onClick={() => setIsContinued(false)}
                  >
                    Change
                  </Button>
                </div>
              )}
            </div>
                
                
            {/* Saved Addresses */}
            {savedAddresses.length > 0 && !isEditing && (
              <div className="space-y-4">
                {savedAddresses.filter(address => !isContinued || address.id === selectedAddressId).map((address) => (
                  <div key={address.id} className="bg-white border border-[#196c67] rounded-lg overflow-hidden">
                    <div className="flex items-center justify-between p-3 bg-[#f1fffc]">
                      <div className="flex items-center gap-2">
                        <input 
                          type="radio" 
                          checked={selectedAddressId === address.id}
                          onChange={() => handleSelectAddress(address.id)}
                          className="accent-[#196c67]" 
                        />
                        <span className="font-semibold text-[#196c67]">
                          {address.addressType ? address.addressType.charAt(0).toUpperCase() + address.addressType.slice(1) : 'Address'}
                        </span>
                      </div>
                      <div className="flex gap-2">
                        <Button 
                          size="icon" 
                          variant="ghost" 
                          onClick={() => startEditingAddress(address)}
                          title="Edit Address" 
                          className="bg-white border border-gray-200 rounded-lg shadow-sm hover:bg-gray-50 p-2"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.828l-4.243 1.414 1.414-4.243a4 4 0 01.828-1.414z" stroke="#196c67" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </Button>
                        <Button 
                          size="icon" 
                          variant="ghost" 
                          onClick={() => handleDeleteAddress(address.id)}
                          title="Delete Address" 
                          className="w-10 h-10 bg-white rounded flex items-center justify-center text-red-500 hover:text-red-700 shadow-sm"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="p-6">
                      <div className="flex items-center mb-1">
                        <span className="text-gray-600 mr-6">{user?.name || user?.fullName || 'User Name'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <span className="text-gray-600 mr-2">Work Place |</span>
                          <span className="text-[#196c67] font-medium">
                            {address.flatNumber && `Flat No: ${address.flatNumber}`}
                            {address.streetAddress && `, ${address.streetAddress}`}
                            {address.areaZone && `, ${address.areaZone}`}
                            {address.stateProvince && `, ${address.stateProvince}`}
                            {address.nearestLandmark && `. ${address.nearestLandmark}`}
                            {address.country && `. ${address.country}`}
                          </span>
                        </div>
                        {selectedAddressId === address.id && !isContinued && (
                          <Button 
                            className="bg-[#196c67] hover:bg-[#145a56] text-white px-6 py-2 rounded-md"
                            onClick={() => {
                              setIsContinued(true);
                              onContinue(address);
                            }}
                          >
                            Continue
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Shipping Information Form */}
            {isEditing && (
              <div className="bg-white rounded-lg border border-dashed border-[#196c67] p-6">
                <form onSubmit={handleSubmit} className="space-y-6">
                {/* Address Type and Country Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-2">
                      Address Type
                    </label>
                    <Select 
                      value={formData.addressType} 
                      onValueChange={(value) => handleInputChange('addressType', value)}
                    >
                      <SelectTrigger className={`h-12 ${errors.addressType ? 'border-red-500' : 'border-gray-300'}`}>
                        <SelectValue placeholder="Select address type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="home">Home</SelectItem>
                        <SelectItem value="office">Office</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.addressType && (
                      <p className="text-red-500 text-xs mt-1">{errors.addressType}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-2">
                      Country
                    </label>
                    <Select 
                      value={formData.country} 
                      onValueChange={(value) => handleInputChange('country', value)}
                    >
                      <SelectTrigger className={`h-12 ${errors.country ? 'border-red-500' : 'border-gray-300'}`}>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="UAE">UAE</SelectItem>
                        <SelectItem value="Saudi Arabia">Saudi Arabia</SelectItem>
                        <SelectItem value="Kuwait">Kuwait</SelectItem>
                        <SelectItem value="Qatar">Qatar</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.country && (
                      <p className="text-red-500 text-xs mt-1">{errors.country}</p>
                    )}
                  </div>
                </div>

                {/* Address Details Row */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-2">
                      Flat/Villa Number,
                    </label>
                    <Input
                      type="text"
                      value={formData.flatNumber}
                      onChange={(e) => handleInputChange('flatNumber', e.target.value)}
                      className={`h-12 ${errors.flatNumber ? 'border-red-500' : 'border-gray-300'}`}
                      placeholder=""
                    />
                    {errors.flatNumber && (
                      <p className="text-red-500 text-xs mt-1">{errors.flatNumber}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-2">
                      Street Address:
                    </label>
                    <Input
                      type="text"
                      value={formData.streetAddress}
                      onChange={(e) => handleInputChange('streetAddress', e.target.value)}
                      className={`h-12 ${errors.streetAddress ? 'border-red-500' : 'border-gray-300'}`}
                      placeholder=""
                    />
                    {errors.streetAddress && (
                      <p className="text-red-500 text-xs mt-1">{errors.streetAddress}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-2">
                      Area/Zone/Locality
                    </label>
                    <Select 
                      value={formData.areaZone} 
                      onValueChange={(value) => handleInputChange('areaZone', value)}
                    >
                      <SelectTrigger className={`h-12 ${errors.areaZone ? 'border-red-500' : 'border-gray-300'}`}>
                        <SelectValue placeholder="" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="dubai-marina">Dubai Marina</SelectItem>
                        <SelectItem value="downtown">Downtown Dubai</SelectItem>
                        <SelectItem value="jumeirah">Jumeirah</SelectItem>
                        <SelectItem value="deira">Deira</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.areaZone && (
                      <p className="text-red-500 text-xs mt-1">{errors.areaZone}</p>
                    )}
                  </div>
                </div>

                {/* Location Details Row */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-2">
                      State/Province/Region
                    </label>
                    <Select 
                      value={formData.stateProvince} 
                      onValueChange={(value) => handleInputChange('stateProvince', value)}
                    >
                      <SelectTrigger className={`h-12 ${errors.stateProvince ? 'border-red-500' : 'border-gray-300'}`}>
                        <SelectValue placeholder="" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="dubai">Dubai</SelectItem>
                        <SelectItem value="abu-dhabi">Abu Dhabi</SelectItem>
                        <SelectItem value="sharjah">Sharjah</SelectItem>
                        <SelectItem value="ajman">Ajman</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.stateProvince && (
                      <p className="text-red-500 text-xs mt-1">{errors.stateProvince}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-2">
                      Nearest Landmark
                    </label>
                    <Input
                      type="text"
                      value={formData.nearestLandmark}
                      onChange={(e) => handleInputChange('nearestLandmark', e.target.value)}
                      className="h-12 border-gray-300"
                      placeholder=""
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-2">
                      Zip/Postal Code
                    </label>
                    <Input
                      type="text"
                      value={formData.zipCode}
                      onChange={(e) => handleInputChange('zipCode', e.target.value)}
                      className={`h-12 ${errors.zipCode ? 'border-red-500' : 'border-gray-300'}`}
                      placeholder=""
                    />
                    {errors.zipCode && (
                      <p className="text-red-500 text-xs mt-1">{errors.zipCode}</p>
                    )}
                  </div>
                </div>

                {/* Save and Continue Row */}
                <div className="flex items-center justify-between pt-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="saveAddress"
                      checked={formData.saveForFurtherUse}
                      onCheckedChange={(checked) => handleInputChange('saveForFurtherUse', checked)}
                    />
                    <label 
                      htmlFor="saveAddress" 
                      className="text-sm font-medium text-[#196c67] cursor-pointer"
                    >
                      Save for Further Use
                    </label>
                  </div>
                  <Button 
                    type="submit"
                    className="bg-[#196c67] hover:bg-[#145a56] text-white px-8 py-2 rounded-md"
                  >
                    {editingAddressId ? 'Update Address' : (savedAddresses.length > 0 ? 'Add Address' : 'Save and Continue')}
                  </Button>
                </div>
              </form>
            </div>
            )}



            {/* Shipping Method Section */}
            <div className="bg-[#f4f4f4] rounded-lg p-4 shadow">
              <h3 className="text-lg font-medium text-gray-700">Shipping Method</h3>
            </div>

            {/* Payment Method Section */}
            <div className="bg-[#f4f4f4] rounded-lg p-4 shadow">
              <h3 className="text-lg font-medium text-gray-700">Payment Method</h3>
            </div>

            {/* Controls */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between p-3 md:p-4 bg-[#f1fcf9] rounded-lg mb-1 border gap-3 sm:gap-0 shadow-md">
              <div className="flex items-center">
                <Checkbox 
                  checked={selectAll}
                  onCheckedChange={handleSelectAll}
                />
                <label className="ml-3 text-sm font-medium text-[#196c67]">
                  Select All ({cartItems.length} items)
                </label>
              </div>
              <div className="flex items-center space-x-2 md:space-x-3">
                <Button variant="outline" size="sm" className="border-[#196c67] text-[#196c67] hover:bg-[#196c67] hover:text-white text-xs md:text-sm" onClick={onBack}>
                  Back to Shopping
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  className="text-red-500 border-red-500 hover:bg-red-50 text-xs md:text-sm"
                  onClick={handleRemoveAll}
                >
                  Remove All
                </Button>
              </div>
            </div>

            {/* Product List */}
            <CartItemsList 
              cartItems={cartItems}
              selectedItems={selectedItems}
              handleSelectItem={handleSelectItem}
              handleQuantityChange={handleQuantityChange}
              handleRemoveItem={handleRemoveItem}
            />

          </div>

          {/* Right Column - Order Summary */}
          <div className="lg:col-span-1 space-y-6">
            {/* Coupon Code Section */}
            <div className="bg-white rounded-lg p-4 border border-gray-200">
              <div className="space-y-3">
                <Input
                  type="text"
                  value={couponCode}
                  onChange={(e) => setCouponCode(e.target.value)}
                  placeholder="Enter your coupon code"
                  className="h-10 border-gray-300"
                />
                <p className="text-xs text-gray-500">(if any)</p>
                <Button 
                  onClick={handleApplyCoupon}
                  className="w-full bg-[#196c67] hover:bg-[#145a56] text-white py-2 rounded-md"
                >
                  Apply
                </Button>
              </div>
            </div>

            {/* Order Summary */}
            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Order Summary</h3>
              
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Items (5)</span>
                  <span className="font-medium">
                    <DirhamSymbol className="w-3 h-3 inline mr-1" />
                    480
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Discount</span>
                  <span className="font-medium text-red-500">
                    -<DirhamSymbol className="w-3 h-3 inline mr-1" />
                    180
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="font-medium">
                    <DirhamSymbol className="w-3 h-3 inline mr-1" />
                    300
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping Charge</span>
                  <span className="font-medium">
                    <DirhamSymbol className="w-3 h-3 inline mr-1" />
                    80
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Reward Redeemed</span>
                  <span className="font-medium">
                    <DirhamSymbol className="w-3 h-3 inline mr-1" />
                    0.00
                  </span>
                </div>
                <hr className="my-3" />
                <div className="flex justify-between text-lg font-semibold">
                  <span>Total</span>
                  <span>
                    <DirhamSymbol className="w-4 h-4 inline mr-1" />
                    480
                  </span>
                </div>
              </div>

              <Button 
                className="w-full mt-6 bg-gray-400 hover:bg-gray-500 text-white py-3 rounded-md"
                disabled
              >
                Confirm Order
              </Button>

              <p className="text-xs text-gray-500 mt-3 text-center">
                By clicking "Confirm Order", you agree to vitamin.ae Terms of Use, Refund Policy and Privacy Policy.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShippingForm; 
