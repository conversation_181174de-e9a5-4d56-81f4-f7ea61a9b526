'use client';
import React from 'react';
import { Button } from '@/components/ui/button';
import useCart from '@/hooks/useCart';
import api from '@/lib/axios';
import { toast } from 'sonner';

const CouponCodeInput = ({ page }) => {
  const { cart, mutate } = useCart();
  const [couponCode, setCouponCode] = React.useState('');

  const applyCoupon = async () => {
    const url = page === 'checkout'
      ? `/client/checkout/${cart.uuid}/apply-coupon`
      : `/client/cart/${cart.uuid}/apply-coupon`;
    try {
      await api.post(url, {
        coupon_code: couponCode,
      });
      mutate();
      toast.success('Coupon applied successfully');
    } catch (error) {
      toast.error('Failed to apply coupon');
    }
  };

  const removeCoupon = async () => {
    const url = page === 'checkout'
      ? `/client/checkout/${cart.uuid}/remove-coupon`
      : `/client/cart/${cart.uuid}/remove-coupon`;
    try {
      await api.delete(url);
      mutate();
      toast.success('Coupon removed successfully');
    } catch (error) {
      toast.error('Failed to remove coupon');
    }
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-md border">
      <div className="flex items-center">
        <input
          type="text"
          placeholder="Enter your coupon code"
          value={couponCode}
          onChange={(e) => setCouponCode(e.target.value)}
          className="flex-grow p-2 border rounded-l-md focus:ring-2 focus:ring-rico-secondary focus:outline-none"
        />
        <Button
          onClick={applyCoupon}
          className="ml-2 rounded-r-md"
        >
          Apply
        </Button>
      </div>
      <p className="text-sm text-gray-500 mb-4 pl-2 pt-2">(if any)</p>
      {cart?.coupon_code && (
        <div className="mt-4 flex justify-between items-center">
          <p className="text-sm text-green-600">
            Coupon applied: <strong>{cart.coupon_code}</strong>
          </p>
          <Button
            variant="link"
            className="text-red-500"
            onClick={removeCoupon}
          >
            Remove
          </Button>
        </div>
      )}
    </div>
  );
};

export default CouponCodeInput;