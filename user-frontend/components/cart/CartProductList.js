import React from 'react';
import VendorHeader from './VendorHeader';
import CartItem from './CartItem';

import useCart from '@/hooks/useCart';

const CartProductList = () => {
  const { cart, loading, error } = useCart();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  if (!cart || !cart.vendors || Object.keys(cart.vendors).length === 0) {
    return <div>Your cart is empty.</div>;
  }

  return (
    <div className="space-y-2">
      {Object.values(cart.vendors).map((vendor) => (
        <div key={vendor.id} className="bg-white rounded-lg shadow-md">
          <VendorHeader vendorName={vendor.name} />
          <div className="divide-y divide-gray-200">
            {vendor.items.map((item) => (
              <CartItem key={item.id} item={item} />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default CartProductList;