import React from 'react';
import { Button } from '@/components/ui/button';
import DirhamSymbol from '@/components/ui/dirham-symbol';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import useCart from '@/hooks/useCart';

const OrderSummary = ({ isCheckout = false, onPlaceOrder, isCashOnDelivery = false }) => {
  const { cart, loading } = useCart();
  const router = useRouter();

  if (loading && !cart) {
    return (
      <div className="bg-white p-3 md:p-4 rounded-lg border">
        <h3 className="text-base md:text-lg font-semibold mb-4 text-[#666666]">Order Summary</h3>
        <div>Loading...</div>
      </div>
    );
  }

  if (!cart) {
    return (
      <div className="bg-white p-3 md:p-4 rounded-lg border">
        <h3 className="text-base md:text-lg font-semibold mb-4 text-[#666666]">Order Summary</h3>
        <p>No items in the cart.</p>
      </div>
    );
  }

  const totalItems = cart.items_count || 0;
  const itemsPrice = cart.subtotal ? parseFloat(cart.subtotal) : 0;
  const discount = cart.discount_amount ? parseFloat(cart.discount_amount) : 0;
  const subtotal = itemsPrice - discount;
  const shippingCharge = cart.shipping_amount ? parseFloat(cart.shipping_amount) : 0;
  const rewardRedeemed = cart.reward_redeemed ? parseFloat(cart.reward_redeemed) : 0;
  const payableTotal = cart.total_amount ? parseFloat(cart.total_amount) : 0;

  const handleProceedToCheckout = () => {
    if (isCheckout) {
      onPlaceOrder();
    } else {
      router.push('/checkout');
    }
  };

  return (
    <div className="bg-white p-3 md:p-3 mt-4 rounded-lg border shadow-sm">
      <h3 className="text-base md:text-lg font-semibold mb-4 text-[#666666]">Order Summary</h3>
      <div className="space-y-2 text-sm">
                <hr className="border-dashed border-gray-400" />

        <div className="flex justify-between text-gray-500">
          <span>Total Items ({totalItems})</span>
          <span className="flex items-center text-black">
            <DirhamSymbol className="w-3 h-3 mr-1" />
            {itemsPrice.toFixed(2)}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-500">Discount</span>
          <span className="flex items-center text-red-500">
            -<DirhamSymbol className="w-3 h-3 mr-1" />
            {discount.toFixed(2)}
          </span>
        </div>
        <hr className="border-dashed" />
        <div className="flex justify-between font-medium text-gray-500">
          <span>Subtotal</span>
          <span className="flex items-center text-black">
            <DirhamSymbol className="w-3 h-3 mr-1" />
            {subtotal.toFixed(2)}
          </span>
        </div>
        <div className="flex justify-between text-gray-500">
          <span>Shipping Charge</span>
          <span className="flex items-center text-black">
            <DirhamSymbol className="w-3 h-3 mr-1" />
            {shippingCharge.toFixed(2)}
          </span>
        </div>
        <div className="flex justify-between text-gray-500">
          <span>Reward Redeemed</span>
          <span className="flex items-center text-black">
            <DirhamSymbol className="w-3 h-3 mr-1" />
            {rewardRedeemed.toFixed(2)}
          </span>
        </div>
        <hr className="border-dashed" />
        <div className="flex justify-between text-base md:text-lg font-bold">
          <span className="text-[#194846]">Payable Total</span>
          <span className="flex items-center ">
            <DirhamSymbol className="w-4 h-4 mr-1" />
            {payableTotal.toFixed(2)}
          </span>
        </div>
      </div>
      
      {!isCheckout && (
        <div className="mt-4 p-3 bg-white rounded-lg text-xs border shadow-sm relative">
          <button className="absolute top-2 right-2 text-[#d0025e]">
            ✕
          </button>
          <div className="flex items-start space-x-3 pr-6">
            <div className="text-[#FF00FF]">
              <Image
                src="/images/profile/brightness_alert.svg"
                alt="Alert"
                width={20}
                height={20}
              />
            </div>
            <div className="flex-1">
              <span className="text-[#d90136]">
                Purchase over AED 100 to enjoy free delivery charges.
              </span>
              <br />
              <a href="#" className="text-[#196c67] hover:text-[#007A6E]">
                Continue Shopping
              </a>
            </div>
          </div>
        </div>
      )}

      <Button
        className="w-full mt-4 hover:bg-teal-700"
        onClick={handleProceedToCheckout}
      >
        {isCheckout ? (isCashOnDelivery ? 'Confirm Order' : 'Place Order') : 'Proceed to Checkout'}
      </Button>
      <p className="text-center text-xs text-gray-500 mt-2">
        Estimated Delivery Time will show here
      </p>
    </div>
  );
};

export default OrderSummary;