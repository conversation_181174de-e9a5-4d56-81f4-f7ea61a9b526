'use client';

import { useState } from 'react';
// Products API function
async function getProductsWithFilters(filterParams) {
  try {
    const params = new URLSearchParams();
    
    // Basic filters
   if (filterParams.subClassId) {
       params.append('sub_class_id', filterParams.subClassId);
   } else if (filterParams.classId) {
       params.append('class_id', filterParams.classId);
   } else if (filterParams.subcategoryId) {
       params.append('subcategory_id', filterParams.subcategoryId);
   } else if (filterParams.categoryId) {
       params.append('category_id', filterParams.categoryId);
   } else if (filterParams.brandId) {
       params.append('brand_id[]', filterParams.brandId);
   }
    
    // Brand filters (array) - only if there are selected brands
    if (filterParams.brandIds && Array.isArray(filterParams.brandIds) && filterParams.brandIds.length > 0) {
      filterParams.brandIds.forEach(brandId => {
        if (brandId) params.append('brand_id[]', brandId);
      });
    }
    
    // User group filters (array) - only if there are selected user groups
    if (filterParams.userGroupIds && Array.isArray(filterParams.userGroupIds) && filterParams.userGroupIds.length > 0) {
      filterParams.userGroupIds.forEach(userGroupId => {
        if (userGroupId) params.append('user_group_id[]', userGroupId);
      });
    }
    
    // Country of origin filters (array) - only if there are selected countries
    if (filterParams.countryIds && Array.isArray(filterParams.countryIds) && filterParams.countryIds.length > 0) {
      filterParams.countryIds.forEach(countryId => {
        if (countryId) params.append('country_of_origin_id[]', countryId);
      });
    }
    
    // Storage conditions - only if there are selected storage conditions
    if (filterParams.storageConditionIds && Array.isArray(filterParams.storageConditionIds) && filterParams.storageConditionIds.length > 0) {
      filterParams.storageConditionIds.forEach(storageId => {
        if (storageId) params.append('storage_conditions_id[]', storageId);
      });
    }
    
    // Price range - only if values are set
   if (filterParams.minPrice !== undefined) {
     params.append('min_price', filterParams.minPrice);
   }
   if (filterParams.maxPrice !== undefined) {
     params.append('max_price', filterParams.maxPrice);
   }
    
    // Boolean filters (send 1 for true, 0 for false)
    if (filterParams.isVegan === true) params.append('is_vegan', 1);
    if (filterParams.isVegetarian === true) params.append('is_vegetarian', 1);
    if (filterParams.isHalal === true) params.append('is_halal', 1);
    
    // Search
    if (filterParams.search) params.append('search', filterParams.search);
    
    // Sorting
    if (filterParams.sortBy) params.append('sort_by', filterParams.sortBy);
    if (filterParams.sortOrder) params.append('sort_order', filterParams.sortOrder);
    
    // Pagination
    if (filterParams.page) params.append('page', filterParams.page);
    if (filterParams.perPage) params.append('per_page', filterParams.perPage);
    
    const url = `${process.env.NEXT_PUBLIC_API_URL}/client/products?${params.toString()}`;
    console.log('Fetching products from:', url);
    
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    console.log('Products response:', data);
    return data;
  } catch (error) {
    console.error('Error fetching products:', error);
    throw error;
  }
}

export function useProducts() {
  const [products, setProducts] = useState([]);
  const [productsLoading, setProductsLoading] = useState(false);
  const [totalProducts, setTotalProducts] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);

  // Function to fetch products based on filters
  const fetchProducts = async (filterParams) => {
    setProductsLoading(true);
    try {
      const productsData = await getProductsWithFilters(filterParams);
      setProducts(productsData.data?.data || []);
      setTotalProducts(productsData.data?.total_items || 0);
    } catch (error) {
      console.error('Failed to fetch products:', error);
      setProducts([]);
      setTotalProducts(0);
    } finally {
      setProductsLoading(false);
    }
  };

  // Build filter parameters for API call
  const buildFilterParams = (options) => {
    const {
      pageType,
      entityId,
      subcategoryId,
      classId,
      subClassId,
      filters,
      sortBy,
      dynamicFilters,
    } = options;

    const params = {
      page: currentPage,
      perPage: 10,
    };

    if (pageType === 'category') {
      params.categoryId = entityId;
      params.subcategoryId = subcategoryId;
      params.classId = classId;
      params.subClassId = subClassId;
    } else if (pageType === 'brand') {
      params.brandId = entityId;
    }

    // Map filter selections to API parameters - only include if filters are actually selected
    if (dynamicFilters) {
      // Brand IDs - only if brands are selected
      if (filters.brands && Array.isArray(filters.brands) && filters.brands.length > 0) {
        const selectedBrandIds = dynamicFilters.brands
          ?.filter(brand => filters.brands.includes(brand.name_en))
          .map(brand => brand.id)
          .filter(id => id) || [];
        
        if (selectedBrandIds.length > 0) {
          params.brandIds = selectedBrandIds;
        }
      }

      // User Group IDs - only if user groups are selected
      if (filters.userGroups && Array.isArray(filters.userGroups) && filters.userGroups.length > 0) {
        const selectedUserGroupIds = dynamicFilters.user_groups
          ?.filter(group => filters.userGroups.includes(group.value_en))
          .map(group => group.id)
          .filter(id => id) || [];
        
        if (selectedUserGroupIds.length > 0) {
          params.userGroupIds = selectedUserGroupIds;
        }
      }

      // Country IDs - only if countries are selected
      if (filters.countries && Array.isArray(filters.countries) && filters.countries.length > 0) {
        const selectedCountryIds = dynamicFilters.countries_of_origin
          ?.filter(country => filters.countries.includes(country.value_en))
          .map(country => country.id)
          .filter(id => id) || [];
        
        if (selectedCountryIds.length > 0) {
          params.countryIds = selectedCountryIds;
        }
      }

      // Storage Condition IDs - only if storage conditions are selected
      if (filters.storage && Array.isArray(filters.storage) && filters.storage.length > 0) {
        const selectedStorageIds = dynamicFilters.storage_conditions
          ?.filter(storage => filters.storage.includes(storage.value_en))
          .map(storage => storage.id)
          .filter(id => id) || [];
        
        if (selectedStorageIds.length > 0) {
          params.storageConditionIds = selectedStorageIds;
        }
      }
    }

    // Price range - only if different from defaults
   if (filters.priceRange && filters.priceRange.length === 2 && dynamicFilters?.price_range) {
     const minDefault = parseFloat(dynamicFilters.price_range.min_price);
     const maxDefault = parseFloat(dynamicFilters.price_range.max_price);

     if (filters.priceRange[0] > minDefault) {
       params.minPrice = filters.priceRange[0];
     }
     if (filters.priceRange[1] < maxDefault) {
       params.maxPrice = filters.priceRange[1];
     }
   }

    // Boolean filters (send 1 for true, 0 for false)
    if (filters.booleanFilters) {
      if (filters.booleanFilters.vegan === true) {
        params.isVegan = true;
      }
      if (filters.booleanFilters.vegetarian === true) {
        params.isVegetarian = true;
      }
      if (filters.booleanFilters.halal === true) {
        params.isHalal = true;
      }
    }

    // Sorting
    const sortMapping = {
      'price-low': { sortBy: 'regular_price', sortOrder: 'asc' },
      'price-high': { sortBy: 'regular_price', sortOrder: 'desc' },
      'rating': { sortBy: 'created_at', sortOrder: 'desc' },
      'newest': { sortBy: 'created_at', sortOrder: 'desc' },
      'name': { sortBy: 'title_en', sortOrder: 'asc' }
    };

    if (sortMapping[sortBy]) {
      params.sortBy = sortMapping[sortBy].sortBy;
      params.sortOrder = sortMapping[sortBy].sortOrder;
    }

    return params;
  };

  return {
    products,
    productsLoading,
    totalProducts,
    currentPage,
    setCurrentPage,
    fetchProducts,
    buildFilterParams
  };
}