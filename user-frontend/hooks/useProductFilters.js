'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';

async function getProductFilters(filterType, id, subcategoryId = null) {
  try {
    const params = new URLSearchParams();
    if (filterType === 'category') {
      if (id) params.append('category_id', id);
      if (subcategoryId) params.append('subcategory_id', subcategoryId);
    } else if (filterType === 'brand') {
      if (id) params.append('brand_id', id);
    }
    
    const url = `${process.env.NEXT_PUBLIC_API_URL}/client/filters?${params.toString()}`;
    console.log('Fetching product filters from:', url);
    
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    console.log('Product filters response:', data);
    return data;
  } catch (error) {
    console.error('Error fetching product filters:', error);
    throw error;
  }
}

export function useProductFilters() {
  const searchParams = useSearchParams();
  
  const initializeFiltersFromURL = () => {
    const priceMin = searchParams.get('priceMin');
    const priceMax = searchParams.get('priceMax');
    const brands = searchParams.get('brands');
    const userGroups = searchParams.get('userGroups');
    const offers = searchParams.get('offers');
    const priceRangeId = searchParams.get('priceRangeId');
    
    return {
      category: searchParams.get('category') || '',
      subcategory: searchParams.get('subcategory') || '',
      class: searchParams.get('class') || '',
      subclass: searchParams.get('subclass') || '',
      priceRange: [
        priceMin ? parseInt(priceMin) : 0,
        priceMax ? parseInt(priceMax) : 1000
      ],
      rating: searchParams.get('rating') ? parseInt(searchParams.get('rating')) : 0,
      brands: brands ? brands.split(',').filter(Boolean) : [],
      userGroups: userGroups ? userGroups.split(',').filter(Boolean) : [],
      offers: offers ? offers.split(',').filter(Boolean) : [],
      priceRangeId: priceRangeId || '',
      inStock: searchParams.get('inStock') === 'true',
      onSale: searchParams.get('onSale') === 'true',
      countries: searchParams.get('countries') ? searchParams.get('countries').split(',').filter(Boolean) : [],
      storage: searchParams.get('storage') ? searchParams.get('storage').split(',').filter(Boolean) : [],
      selectedClass: searchParams.get('class') || '',
      booleanFilters: {
        vegan: searchParams.get('vegan') === 'true',
        vegetarian: searchParams.get('vegetarian') === 'true',
        halal: searchParams.get('halal') === 'true',
      }
    };
  };

  const [filters, setFilters] = useState(initializeFiltersFromURL);
  const [dynamicFilters, setDynamicFilters] = useState(null);
  const [filtersLoading, setFiltersLoading] = useState(false);
  const [selectedSubcategoryId, setSelectedSubcategoryId] = useState(null);
  const [shouldFetchProducts, setShouldFetchProducts] = useState(false);

  const fetchProductFilters = async (filterType, id, subcategoryId = null) => {
    if (!id) return;
    
    setFiltersLoading(true);
    setShouldFetchProducts(false);
    try {
      const filtersData = await getProductFilters(filterType, id, subcategoryId);
      setDynamicFilters(filtersData.data);
      setShouldFetchProducts(true);
    } catch (error) {
      console.error('Failed to fetch product filters:', error);
      setDynamicFilters(null);
    } finally {
      setFiltersLoading(false);
    }
  };

  useEffect(() => {
    if (dynamicFilters?.price_range) {
      const minPrice = parseFloat(dynamicFilters.price_range.min_price);
      const maxPrice = parseFloat(dynamicFilters.price_range.max_price);
      
      setFilters(prev => ({
        ...prev,
        priceRange: [
          prev.priceRange[0] === 0 ? minPrice : prev.priceRange[0],
          prev.priceRange[1] === 1000 ? maxPrice : prev.priceRange[1]
        ]
      }));
    }
  }, [dynamicFilters]);

  useEffect(() => {
   setFilters(initializeFiltersFromURL());
 }, [searchParams]);

  return {
    filters,
    setFilters,
    dynamicFilters,
    filtersLoading,
    selectedSubcategoryId,
    setSelectedSubcategoryId,
    shouldFetchProducts,
    setShouldFetchProducts,
    fetchProductFilters
  };
}