'use client';

import useSWRCustom from '@/lib/useSWR';

export function useBrands() {
  const {
    data,
    error,
    isLoading,
    mutate,
  } = useSWRCustom('/client/brands');

  const brandData = data?.status ? data.data : { index: [], brand_groups: [] };
  const apiError = data?.status === false ? data.message : null;

  return {
    brandData,
    loading: isLoading,
    error: error || apiError,
    retry: mutate,
  };
}