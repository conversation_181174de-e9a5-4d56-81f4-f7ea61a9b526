"use client"

import useWishlistStore from '../store/wishlistStore';
import useAuth from './useAuth';

const useWishlist = () => {
  const {
    wishlist,
    loading,
    error,
    getWishlist,
    addToWishlist,
    removeFromWishlist,
    clearWishlist,
  } = useWishlistStore();

  const { token } = useAuth();

  return {
    wishlist,
    loading,
    error,
    getWishlist,
    addToWishlist,
    removeFromWishlist,
    clearWishlist,
    isUserAuthenticated: !!token,
  };
};

export default useWishlist;