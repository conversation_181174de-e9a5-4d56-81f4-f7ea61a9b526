import useSWRCustom from '../lib/useSWR';
import api from '../lib/axios';

const fetcher = (url) => api.get(url).then((res) => res.data.data);

export function useMenuData() {
  const { data, error } = useSWRCustom('/client/menu', fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    shouldRetryOnError: false,
  });

  return {
    data,
    isLoading: !error && !data,
    isError: error,
  };
}