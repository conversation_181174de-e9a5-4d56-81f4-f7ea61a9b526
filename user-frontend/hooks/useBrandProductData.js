'use client';

import { useMemo } from 'react';
import useSWRCustom from '@/lib/useSWR';

export function useBrandProductData(brandSlug) {
  const {
    data,
    error,
    isLoading,
    mutate,
  } = useSWRCustom(brandSlug ? `/client/brands/${brandSlug}` : null);

  const apiError = data?.status === false ? data.message : null;

  const memoizedBrandData = useMemo(() => {
    if (!data?.status) return null;

    const breadcrumb = [
      { name_en: 'Home', url: '/' },
      { name_en: 'Brands', url: '/brands' },
      { name_en: data.data.name_en, url: `/brands/${data.data.slug}` }
    ];

    return { ...data, data: { ...data.data, breadcrumb } };
  }, [data]);

  return {
    brandData: memoizedBrandData,
    loading: isLoading,
    error: error || apiError,
    retry: mutate,
  };
}