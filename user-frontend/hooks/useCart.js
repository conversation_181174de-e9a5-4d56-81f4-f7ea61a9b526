"use client"

import useCartStore from '../store/cartStore';
import useAuth from './useAuth';

const useCart = () => {
  const {
    cart,
    loading,
    error,
    getCart,
    addItem,
    updateItem,
    removeItem,
    migrateGuestCart,
    clearCart,
  } = useCartStore();

  const { token } = useAuth();

  return {
    cart,
    loading,
    error,
    getCart,
    addItem,
    updateItem,
    removeItem,
    migrateGuestCart,
    clearCart,
    isUserAuthenticated: !!token,
  };
};

export default useCart;