'use client';

import { useState } from 'react';

// Subcategory API function
async function getSubcategoryBySlug(subcategorySlug) {
  try {
    const response = await fetch(`http://103.209.40.213:7001/api/client/subcategories/${subcategorySlug}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    console.log('Subcategory response:', data);
    return data;
  } catch (error) {
    console.error('Error fetching subcategory data:', error);
    throw error;
  }
}

export function useSubcategoryData() {
  const [subcategoryData, setSubcategoryData] = useState(null);
  const [subcategoryLoading, setSubcategoryLoading] = useState(false);
  const [subcategoryError, setSubcategoryError] = useState(null);

  const fetchSubcategoryData = async (subcategorySlug) => {
    if (!subcategorySlug) {
      setSubcategoryData(null);
      return;
    }
    
    setSubcategoryLoading(true);
    setSubcategoryError(null);
    
    try {
      const data = await getSubcategoryBySlug(subcategorySlug);
      setSubcategoryData(data);
    } catch (err) {
      setSubcategoryError(err.message);
      console.error('Failed to fetch subcategory data:', err);
    } finally {
      setSubcategoryLoading(false);
    }
  };

  const clearSubcategoryData = () => {
    setSubcategoryData(null);
    setSubcategoryError(null);
  };

  return {
    subcategoryData,
    subcategoryLoading,
    subcategoryError,
    fetchSubcategoryData,
    clearSubcategoryData
  };
}