'use client';

import { useRouter, usePathname } from 'next/navigation';

export function useURLManager() {
  const router = useRouter();
  const pathname = usePathname();

  // Update URL when filters change
  const updateURL = (newFilters, newSortBy, dynamicFilters) => {
    const params = new URLSearchParams();
    
    if (newFilters?.category) params.set('category', newFilters.category);
    if (newFilters?.subcategory) params.set('subcategory', newFilters.subcategory);
    if (newFilters?.class) params.set('class', newFilters.class);
    if (newFilters?.subclass) params.set('subclass', newFilters.subclass);
    if (newFilters?.priceRange && newFilters.priceRange.length === 2 && dynamicFilters?.price_range) {
       const minDefault = parseFloat(dynamicFilters.price_range.min_price);
       const maxDefault = parseFloat(dynamicFilters.price_range.max_price);

       if (newFilters.priceRange[0] > minDefault) {
         params.set('priceMin', newFilters.priceRange[0]);
       }
       if (newFilters.priceRange[1] < maxDefault) {
         params.set('priceMax', newFilters.priceRange[1]);
       }
     }
    if (newFilters?.rating > 0) params.set('rating', newFilters.rating);
    if (newFilters?.brands && newFilters.brands.length > 0) params.set('brands', newFilters.brands.join(','));
    if (newFilters?.userGroups && newFilters.userGroups.length > 0) params.set('userGroups', newFilters.userGroups.join(','));
    if (newFilters?.offers && newFilters.offers.length > 0) params.set('offers', newFilters.offers.join(','));
    if (newFilters?.countries && newFilters.countries.length > 0) params.set('countries', newFilters.countries.join(','));
    if (newFilters?.storage && newFilters.storage.length > 0) params.set('storage', newFilters.storage.join(','));
    if (newFilters?.priceRangeId) params.set('priceRangeId', newFilters.priceRangeId);
    if (newFilters?.inStock) params.set('inStock', 'true');
    if (newFilters?.onSale) params.set('onSale', 'true');
    if (newFilters?.booleanFilters?.vegan) params.set('vegan', 'true');
    if (newFilters?.booleanFilters?.vegetarian) params.set('vegetarian', 'true');
    if (newFilters?.booleanFilters?.halal) params.set('halal', 'true');
    if (newFilters?.selectedClass) params.set('class', newFilters.selectedClass);
    if (newSortBy && newSortBy !== 'name') params.set('sortBy', newSortBy);

    const newURL = `${pathname}${params.toString() ? `?${params.toString()}` : ''}`;
    router.push(newURL, { scroll: false });
  };

  return { updateURL };
}