import { useEffect } from 'react';
import useCartStore from '../store/cartStore';
import useAuth from './useAuth';

const useCartInitializer = () => {
  const { getCart, migrateGuestCart, createGuestCart, cart } = useCartStore();
  const { token, user } = useAuth();

  useEffect(() => {
    // This check ensures we are on the client side, where localStorage is available.
    if (typeof window === 'undefined') {
      return;
    }

    const isGuestCart = cart && !cart.user_id;

    if (token && user) {
      // Authenticated user
      if (isGuestCart) {
        // If there's a guest cart from a previous session, migrate it.
        migrateGuestCart();
      } else if (!cart || (cart.user_id && cart.user_id !== user.id)) {
        // Otherwise, fetch the user's cart if it's not already loaded
        // or if the cart in state belongs to a different user.
        getCart();
      }
    } else {
      // Guest user: Check localStorage directly to see if a cart exists.
      const guestCartInStorage = localStorage.getItem('cart-storage');
      if (!guestCartInStorage) {
        // Only create a guest cart if one doesn't exist in storage.
        createGuestCart();
      }
      // If a guest cart *does* exist in storage, we do nothing.
      // Zustand's persist middleware will handle rehydrating the store.
    }

    // NOTE: The `cart` object is intentionally omitted from the dependency array
    // to prevent an infinite loop where `getCart` updates the `cart` and
    // triggers the effect again. This effect should only run on auth changes
    // or on initial load for guests.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token, user, getCart, migrateGuestCart, createGuestCart]);
};

export default useCartInitializer;