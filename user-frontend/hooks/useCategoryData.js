'use client';

import { useState, useEffect } from 'react';
// Category API function
async function getCategoryBySlug(slug) {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/client/categories/${slug}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching category data:', error);
    throw error;
  }
}

export function useCategoryData(categorySlug) {
  const [categoryData, setCategoryData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCategoryData = async () => {
      if (!categorySlug) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const data = await getCategoryBySlug(categorySlug);
        setCategoryData(data);
      } catch (err) {
        setError(err.message);
        console.error('Failed to fetch category data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCategoryData();
  }, [categorySlug]);

  return { categoryData, loading, error };
}