'use client';

import { useState } from 'react';

async function getClassBySlug(classSlug) {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/client/product-classes/${classSlug}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching class data:', error);
    throw error;
  }
}

export function useClassData() {
  const [classData, setClassData] = useState(null);
  const [classLoading, setClassLoading] = useState(false);
  const [classError, setClassError] = useState(null);

  const fetchClassData = async (classSlug) => {
    if (!classSlug) {
      setClassData(null);
      return;
    }
    
    setClassLoading(true);
    setClassError(null);
    
    try {
      const data = await getClassBySlug(classSlug);
      setClassData(data);
    } catch (err) {
      setClassError(err.message);
      console.error('Failed to fetch class data:', err);
    } finally {
      setClassLoading(false);
    }
  };

  const clearClassData = () => {
    setClassData(null);
    setClassError(null);
  };

  return {
    classData,
    classLoading,
    classError,
    fetchClassData,
    clearClassData
  };
}