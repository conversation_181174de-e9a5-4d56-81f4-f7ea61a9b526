import { useEffect } from 'react';
import useAuthStore from '../store/authStore';

const useAuth = () => {
  const {
    user,
    token,
    refreshToken,
    isAuthenticated,
    loading,
    error,
    login,
    logout,
    register,
    verifyOtp,
    resendOtp,
    initializeAuth,
    updateToken,
  } = useAuthStore();

  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);

  return {
    user,
    token,
    refreshToken,
    isAuthenticated,
    loading,
    error,
    login,
    logout,
    register,
    verifyOtp,
    resendOtp,
    initializeAuth,
    updateToken,
  };
};

export default useAuth;