import useSWR from 'swr';
import api from './axios';

const defaultConfig = {
  revalidateOnFocus: true,
  revalidateOnReconnect: true,
  shouldRetryOnError: true,
  errorRetryCount: 3,
  dedupingInterval: 2000,
};

const fetcher = (...args) => fetch(...args).then(res => res.json());
const axiosFetcher = url => api.get(url).then(res => res.data);

/**
 * Custom SWR hook with default configuration.
 * @param {string} route The API route to fetch.
 * @param {Function} [fetcherFn=fetcher] Optional fetcher function
 * @param {object} [config={}] SWR configuration options
 */
export default function useSWRCustom(route, fetcherFn = fetcher, config = {}) {
  const key = `${process.env.NEXT_PUBLIC_API_URL}${route}`;
  return useSWR(key, fetcherFn, { ...defaultConfig, ...config });
}

/**
 * Custom SWR hook with axios as the fetcher.
 * @param {string} route The API route to fetch.
 * @param {object} [config={}] SWR configuration options
 */
export function useSWRAxios(route, config = {}) {
  return useSWR(route, axiosFetcher, { ...defaultConfig, ...config });
}