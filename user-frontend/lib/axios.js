import axios from 'axios';
import useAuthStore from '../store/authStore';
import useCartStore from '../store/cartStore';

// Create axios instance
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Get token from Zustand store
    const { token } = useAuthStore.getState();

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    console.error('error', error)
    const originalRequest = error.config;
    
    // Don't intercept login/register/verify-otp requests - let them handle their own errors
    const isAuthRequest = originalRequest.url?.includes('/login') || 
                         originalRequest.url?.includes('/register') || 
                         originalRequest.url?.includes('/verify-otp') ||
                         originalRequest.url?.includes('/resend-otp');
    
    if (isAuthRequest) {
      return Promise.reject(error);
    }
    
    // Handle 401 unauthorized errors for other requests
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      // Try to refresh token
      const { refreshToken, updateToken, logout } = useAuthStore.getState();
      
      if (refreshToken) {
        try {
          const response = await axios.post(`${process.env.NEXT_PUBLIC_API_URL}/refresh`, {
            refresh_token: refreshToken
          });
          
          if (response.data.status && response.data.data.token) {
            const newToken = response.data.data.token.access_token;
            
            // Update token in store
            updateToken(newToken);
            
            // Retry original request with new token
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return api(originalRequest);
          }
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
        }
      }
      
      // If refresh fails, logout user
      logout();
      
      // Redirect to login if we're in browser
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }
    
    return Promise.reject(error);
  }
);

export default api;