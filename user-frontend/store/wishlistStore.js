import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import api from '../lib/axios';
import useAuthStore from './authStore';
import { toast } from 'sonner';

const initialState = {
  wishlist: [],
  loading: false,
  error: null,
};

const useWishlistStore = create(
  persist(
    (set, get) => ({
      ...initialState,

      // Actions
      setWishlist: (wishlist) => set({ wishlist }),
      setLoading: (loading) => set({ loading }),
      setError: (error) => set({ error }),

      clearWishlist: () => set({ ...initialState }),

      // Async Actions
      getWishlist: async () => {
        const token = useAuthStore.getState().token;
        
        if (!token) {
          return [];
        }

        set({ loading: true, error: null });

        try {
          const response = await api.get('/client/wishlist');

          if (response.data.status) {
            set({ wishlist: response.data.data, loading: false });
            return response.data.data;
          } else {
            set({ error: 'Failed to fetch wishlist.', loading: false });
            return [];
          }
        } catch (error) {
          set({ error: 'Failed to fetch wishlist.', loading: false })
          console.error('Fetch wishlist error:', error);
          throw error;
        }
      },

      addToWishlist: async (productId) => {
        const token = useAuthStore.getState().token;
        
        if (!token) {
          toast.error('Please login to add items to wishlist');
          return;
        }

        console.log('Adding to wishlist with productId:', productId);
        console.log('Type of productId:', typeof productId);
        console.log('ProductId value:', JSON.stringify(productId));
        set({ loading: true });

        try {
          const requestData = {
            product_id: productId
          };
          console.log('Sending request data:', JSON.stringify(requestData));
          
          const response = await api.post('/client/wishlist', requestData);
          
          console.log('API Response:', response.data);

          if (response.data.status) {
            toast.success('Item added to wishlist');
            // Refresh wishlist data
            await get().getWishlist();
          } else {
            toast.error(response.data.message || 'Failed to add item to wishlist');
          }
        } catch (error) {
          console.error('Add to wishlist error:', error);
          console.error('Error response data:', error.response?.data);
          toast.error('Failed to add item to wishlist');
        } finally {
          set({ loading: false });
        }
      },

      removeFromWishlist: async (wishlistId) => {
        const token = useAuthStore.getState().token;
        
        if (!token) {
          toast.error('Please login to manage wishlist');
          return;
        }

        set({ loading: true });

        try {
          const response = await api.delete(`/client/wishlist/${wishlistId}`);

          if (response.data.status) {
            toast.success('Item removed from wishlist');
            // Refresh wishlist
            await get().getWishlist();
          } else {
            toast.error(response.data.message || 'Failed to remove item from wishlist');
          }
        } catch (error) {
          toast.error('Failed to remove item from wishlist');
          console.error('Remove from wishlist error:', error);
        } finally {
          set({ loading: false });
        }
      },
    }),
    {
      name: 'wishlist-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({ wishlist: state.wishlist }),
    }
  )
);

export default useWishlistStore;