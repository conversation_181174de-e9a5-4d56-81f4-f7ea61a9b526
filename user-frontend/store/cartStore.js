import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import api from '../lib/axios';
import useAuthStore from './authStore';
import { toast } from 'sonner';

const initialState = {
  cart: null,
  loading: false,
  error: null,
};

const useCartStore = create(
  persist(
    (set, get) => ({
      ...initialState,

      // Actions
      setCart: (cart) => set({ cart }),
      setLoading: (loading) => set({ loading }),
      setError: (error) => set({ error }),

      clearCart: () => set({ ...initialState }),

      // Async Actions
      createGuestCart: async () => {
        set({ loading: true, error: null });
        try {
          const response = await api.post('/client/cart');
          if (response.data.status) {
            const newCart = response.data.data;
            set({
              cart: newCart,
              loading: false,
            });
            return newCart;
          } else {
            throw new Error('Failed to create guest cart without status.');
          }
        } catch (error) {
          const errorMessage = 'Failed to create guest cart.';
          set({ error: errorMessage, loading: false });
          toast.error(errorMessage);
          console.error('Create guest cart error:', error);
          throw error;
        }
      },

      getCart: async () => {
        const { cart } = get();
        const token = useAuthStore.getState().token;
        const cartId = cart?.uuid;
        const cartToken = cart?.cart_token;

        if (!cartId && !token) {
          return null; // No cart to fetch
        }

        set({ loading: true, error: null });

        try {
          let response;
          if (token) {
            response = await api.get('/client/my-cart');
          } else if (cartId && cartToken) {
            response = await api.get(`/client/cart/${cartId}`, {
              headers: { 'X-Cart-Token': cartToken },
            });
          }

          if (response && response.data.status) {
            set({ cart: response.data.data, loading: false });
            return response.data.data;
          } else {
            set({ error: 'Failed to fetch cart.', loading: false });
            return null;
          }
        } catch (error) {
          set({ error: 'Failed to fetch cart.', loading: false });
          console.error('Fetch cart error:', error);
          throw error;
        }
      },

      addItem: async (itemData) => {
        const { cart } = get();
        const token = useAuthStore.getState().token;
        let currentCart = cart;

        if (!token && !currentCart) {
          try {
            currentCart = await get().createGuestCart();
          } catch (error) {
            toast.error('Could not create a guest cart. Please try again.');
            return;
          }
        }

        const cartId = currentCart?.uuid;
        const cartToken = currentCart?.cart_token;

        // Optimistic update
        const previousCart = get().cart;
        
        const previousVendors = previousCart.vendors || {};
        const vendorItems = previousVendors[itemData.vendor_id]?.items || [];

        const newCart = {
          ...previousCart,
          items_count: (previousCart.items_count || 0) + 1,
          total_quantity: (previousCart.total_quantity || 0) + 1,
          vendors: {
            ...previousVendors,
            [itemData.vendor_id]: {
              ...(previousVendors[itemData.vendor_id] || { id: itemData.vendor_id, items: [] }),
              items: [...vendorItems, { ...itemData, id: `temp-${Date.now()}` }],
            },
          },
        };
        set({ cart: newCart });

        try {
          const endpoint = `/client/cart/${cartId}/items`;
          const headers = token ? {} : { headers: { 'X-Cart-Token': cartToken } };
          const response = await api.post(endpoint, itemData, headers);

          if (response.data.status) {
            toast.success('Item added to cart');
            set({ cart: response.data.data });
          } else {
            toast.error(response.data.message || 'Failed to add item.');
            set({ cart: previousCart }); // Revert
          }
        } catch (error) {
          toast.error('Failed to add item to cart.');
          set({ cart: previousCart }); // Revert
          console.error('Add item error:', error);
        }
      },

      updateItem: async (itemId, quantity) => {
        const { cart } = get();
        const token = useAuthStore.getState().token;

        if (!cart) {
          toast.error('No cart available for updating item.');
          return;
        }

        const cartId = cart.uuid;
        const cartToken = cart.cart_token;

        // Optimistic update
        const previousCart = get().cart;
        const newCart = { ...previousCart };
        // ... logic to update item quantity in newCart
        set({ cart: newCart });

        try {
          const endpoint = `/client/cart/${cartId}/items/${itemId}`;
          const headers = token ? {} : { headers: { 'X-Cart-Token': cartToken } };
          const response = await api.put(endpoint, { quantity }, headers);

          if (response.data.status) {
            toast.success('Item updated successfully');
            set({ cart: response.data.data });
          } else {
            toast.error(response.data.message || 'Failed to update item.');
            set({ cart: previousCart }); // Revert
          }
        } catch (error) {
          toast.error('Failed to update item.');
          set({ cart: previousCart }); // Revert
          console.error('Update item error:', error);
        }
      },

      removeItem: async (itemId) => {
        const { cart } = get();
        const token = useAuthStore.getState().token;

        if (!cart) {
          toast.error('No cart available for removing item.');
          return;
        }

        const cartId = cart.uuid;
        const cartToken = cart.cart_token;

        // Optimistic update
        const previousCart = get().cart;
        const newCart = { ...previousCart };
        // ... logic to remove item from newCart
        set({ cart: newCart });

        try {
          const endpoint = `/client/cart/${cartId}/items/${itemId}`;
          const headers = token ? {} : { headers: { 'X-Cart-Token': cartToken } };
          const response = await api.delete(endpoint, headers);

          if (response.data.status) {
            toast.success('Item removed from cart');
            set({ cart: response.data.data });
          } else {
            toast.error(response.data.message || 'Failed to remove item.');
            set({ cart: previousCart }); // Revert
          }
        } catch (error) {
          toast.error('Failed to remove item.');
          set({ cart: previousCart }); // Revert
          console.error('Remove item error:', error);
        }
      },

      migrateGuestCart: async () => {
        const { cart } = get();
        const token = useAuthStore.getState().token;
        const guestCartId = cart?.uuid;
        const guestCartToken = cart?.cart_token;
    
        // If there's no guest cart, no items in the cart, or no token, no need to migrate.
        if (!guestCartId || !token || !guestCartToken || !cart?.items_count > 0) {
          return;
        }

        set({ loading: true, error: null });
        try {
          const response = await api.post('/client/my-cart/migrate', {
            guest_cart_id : guestCartId,
            guest_cart_token: guestCartToken,
            merge_strategy: 'merge',
          });

          if (response.data.status) {
            set({ cart: response.data.data, loading: false });
            toast.success('Cart migrated successfully');
          } else {
            const errorMessage = response.data.message || 'Failed to migrate cart.';
            // set({ error: errorMessage, loading: false });
            toast.error(errorMessage);
          }
        } catch (error) {
          const errorMessage = 'Failed to migrate cart.';
          // set({ error: errorMessage, loading: false });
          toast.error(errorMessage);
          console.error('Migrate cart error:', error);
        }
      },

      placeOrder: async (orderDetails) => {
        const { cart } = get();
        const token = useAuthStore.getState().token;

        if (!cart || !token) {
          toast.error('You must be logged in and have items in your cart to place an order.');
          return;
        }

        set({ loading: true, error: null });
        try {
          const response = await api.post('/client/orders', orderDetails, {
            headers: { Authorization: `Bearer ${token}` },
          });

          if (response.data.status) {
            toast.success('Order placed successfully!');
            set({ cart: null }); // Clear the cart
          } else {
            toast.error(response.data.message || 'Failed to place order.');
          }
        } catch (error) {
          toast.error('Failed to place order.');
          console.error('Place order error:', error);
        } finally {
          set({ loading: false });
        }
      },
    }),
    {
      name: 'cart-storage', // unique name
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({ cart: state.cart }), // only persist the cart object
    }
  )
);

export default useCartStore;