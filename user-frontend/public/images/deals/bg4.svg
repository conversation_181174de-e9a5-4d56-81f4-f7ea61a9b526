<svg width="342" height="440" viewBox="0 0 342 440" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1633_56659)">
<path d="M0 20C0 8.95431 8.95431 0 20 0H322C333.046 0 342 8.95431 342 20V432C342 436.418 338.418 440 334 440H8.00001C3.58173 440 0 436.418 0 432V20Z" fill="#EBF3FF"/>
<rect y="191" width="342" height="249" rx="8" fill="#CCE0FF"/>
<g filter="url(#filter0_i_1633_56659)">
<path d="M342 172C255.246 189.403 62.1818 188.243 0 172V205.259H342V172Z" fill="#CCE0FF"/>
</g>
</g>
<defs>
<filter id="filter0_i_1633_56659" x="0" y="172" width="342" height="35.2585" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1633_56659"/>
</filter>
<clipPath id="clip0_1633_56659">
<path d="M0 20C0 8.95431 8.95431 0 20 0H322C333.046 0 342 8.95431 342 20V432C342 436.418 338.418 440 334 440H8.00001C3.58173 440 0 436.418 0 432V20Z" fill="white"/>
</clipPath>
</defs>
</svg>
