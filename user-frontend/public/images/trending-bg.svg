<svg width="231" height="140" viewBox="0 0 231 140" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1930_61544)">
<rect x="4" y="2" width="223" height="132" rx="8" fill="url(#paint0_linear_1930_61544)" fill-opacity="0.5" shape-rendering="crispEdges"/>
<rect x="4.5" y="2.5" width="222" height="131" rx="7.5" stroke="#ECECEC" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_d_1930_61544" x="0" y="0" width="231" height="140" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1930_61544"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1930_61544" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1930_61544" x1="55.9576" y1="-103.161" x2="287.623" y2="11.3002" gradientUnits="userSpaceOnUse">
<stop offset="0.08" stop-color="#FEE1FE"/>
<stop offset="0.4" stop-color="#ECF7FF"/>
<stop offset="0.66" stop-color="#FFEBDE"/>
<stop offset="0.91" stop-color="#EFFEFF"/>
</linearGradient>
</defs>
</svg>
