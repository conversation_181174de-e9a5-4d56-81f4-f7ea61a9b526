import { capitalizeFirstLetter } from "@/helper/Commonhelper";
// status Color
const StatusColors = {
  active: "bg-green-100 text-green-800",
  inactive: "bg-red-100 text-red-600",
  true: "bg-green-100 text-green-800",
  false: "bg-red-100 text-red-600",
  1: "bg-green-100 text-green-800",
  0: "bg-red-100 text-red-600",
};

const normalizeStatus = (status) => {
  if (typeof status === "boolean") return status ? "active" : "inactive";
  if (status === 1 || status === "1") return "active";
  if (status === 0 || status === "0") return "inactive";
  return String(status).toLowerCase().trim();
};

const StatusBadge = ({ data, fieldName, className = "", t }) => {
  if (!data || !fieldName || typeof data[fieldName] === "undefined")
    return null;
  let status = data[fieldName];
  const normalized = normalizeStatus(status);
  const color =
    StatusColors[status] ||
    StatusColors[normalized] ||
    "bg-gray-100 text-gray-800";
  let label =
    typeof status === "boolean"
      ? status
        ? t
          ? t("Active")
          : "Active"
        : t
        ? t("Inactive")
        : "Inactive"
      : status === 1 || status === "1"
      ? t
        ? t("Active")
        : "Active"
      : status === 0 || status === "0"
      ? t
        ? t("Inactive")
        : "Inactive"
      : t
      ? t(capitalizeFirstLetter(status))
      : capitalizeFirstLetter(status);

  // ? t(status?.charAt(0).toUpperCase() + status?.slice(1))
  // : status?.charAt(0).toUpperCase() + status?.slice(1);

  return (
    <span
      className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full shadow w-14 justify-center items-center ${color} ${className}`}
    >
      {label}
    </span>
  );
};

export default StatusBadge;
