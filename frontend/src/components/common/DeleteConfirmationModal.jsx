import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useTranslation } from "react-i18next";

const DeleteConfirmationModal = ({
  isOpen,
  onClose,
  onDelete,
  loading = false,
  title,
  message,
  itemName = "",
  itemValue = "",
  warning,
  cancelText,
  deleteText,
}) => {
  const { t } = useTranslation();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title || t("commonDelete.deleteTitle")}
      size="md"
      showCloseButton
      footer={
        <div className="flex justify-end gap-3">
          <Button variant="outline" onClick={onClose}>
            {cancelText || t("commonButton.cancel")}
          </Button>
          <Button variant="danger" onClick={onDelete} disabled={loading}>
            {loading ? (
              <LoadingSpinner size={20} />
            ) : (
              deleteText || t("commonButton.delete")
            )}
          </Button>
        </div>
      }
    >
      <div className="text-gray-700 my-2 gap-2">
        {message || t("commonDelete.deleteMessage")}{" "}
        {itemName && <span className="font-bold mx-1">{itemName}</span>}
        {itemValue && (
          <span className="font-semibold me-1 text-red-600">{itemValue}</span>
        )}
        {warning || t("commonDelete.deleteWarning")}
      </div>
    </Modal>
  );
};

export default DeleteConfirmationModal;
