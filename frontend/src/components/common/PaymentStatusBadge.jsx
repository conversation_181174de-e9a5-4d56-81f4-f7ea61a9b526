import { capitalizeFirstLetter } from "@/helper/Commonhelper";

// status Color
const StatusColors = {
  pending: "text-yellow-800 border-2 border-yellow-400",
  paid: "text-green-800 border-2 border-green-400",
  failed: "text-red-800 border-2 border-red-400",
  refunded: "text-blue-800 border-2 border-blue-400",
};

const PaymentStatusBadge = ({
  data,
  fieldName = "status",
  className = "",
  t,
}) => {
  if (!data || typeof data[fieldName] === "undefined") return null;

  const rawStatus = String(data[fieldName]).trim().toLowerCase();
  const color =
    StatusColors[rawStatus] || "text-gray-800 border-2 border-gray-400";

  const label = t
    ? t(`status.${rawStatus}`) || capitalizeFirstLetter(rawStatus)
    : capitalizeFirstLetter(rawStatus);

  return (
    <span
      className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-2xl shadow-lg w-20 justify-center items-center ${color} ${className}`}
    >
      {label}
    </span>
  );
};

export default PaymentStatusBadge;
