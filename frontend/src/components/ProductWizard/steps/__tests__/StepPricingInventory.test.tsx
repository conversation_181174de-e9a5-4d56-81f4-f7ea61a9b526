import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import StepPricingInventory from '../StepPricingInventory';

// Mock the hooks
jest.mock('@/hooks/list/useWarehouseList', () => ({
  __esModule: true,
  default: () => ({
    options: [
      { id: 1, label: 'Warehouse 1', value: 'warehouse-1' },
      { id: 2, label: 'Warehouse 2', value: 'warehouse-2' }
    ],
    loading: false,
    error: null,
    isAuthError: false
  })
}));

jest.mock('@/hooks/list/useProductAttributesList', () => ({
  __esModule: true,
  default: () => ({
    attributes: [
      {
        id: 1,
        name: 'Color',
        values: [
          { id: 1, name: 'Red' },
          { id: 2, name: 'Blue' }
        ]
      },
      {
        id: 2,
        name: '<PERSON><PERSON>',
        values: [
          { id: 3, name: 'Small' },
          { id: 4, name: 'Large' }
        ]
      }
    ],
    loading: false,
    getAttributeOptions: jest.fn(),
    getAttributeValueMap: () => ({
      1: { 1: 'Red', 2: 'Blue' },
      2: { 3: 'Small', 4: 'Large' }
    })
  })
}));

// Mock file upload utility
jest.mock('@/utils/fileUploadHelpers', () => ({
  uploadSingleFile: jest.fn(() => Promise.resolve({
    data: {
      data: {
        url: 'https://example.com/uploaded-file.pdf',
        path: '/uploads/test-file.pdf'
      }
    }
  }))
}));

const mockDropdownOptions = (slug: string) => {
  if (slug === 'vat-tax') {
    return [
      { id: 1, label: '5%', value: '5%' },
      { id: 2, label: '0 Rated', value: '0 Rated' },
      { id: 3, label: 'Exempted', value: 'Exempted' }
    ];
  }
  return [];
};

const defaultProps = {
  data: {
    regular_price: 100,
    offer_price: 80,
    vat_tax: '5%',
    vat_tax_id: 1,
    approx_commission: 5,
    fulfillment_id: null,
    reserved: 0,
    threshold: 5,
    has_varient: false,
    is_variant: false,
    product_variants: [],
    vat_tax_utl: null
  },
  onChange: jest.fn(),
  layoutMode: 'horizontal' as const,
  validationErrors: {},
  showValidation: false,
  dropdownOptions: mockDropdownOptions,
  fulfillmentOptions: () => []
};

describe('StepPricingInventory', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders basic pricing fields correctly', () => {
    render(<StepPricingInventory {...defaultProps} />);
    
    expect(screen.getByDisplayValue('100')).toBeInTheDocument(); // Regular price
    expect(screen.getByDisplayValue('80')).toBeInTheDocument();  // Offer price
    expect(screen.getByDisplayValue('5')).toBeInTheDocument();   // Commission
  });

  it('enables variant functionality when toggle is switched', async () => {
    const mockOnChange = jest.fn();
    render(<StepPricingInventory {...defaultProps} onChange={mockOnChange} />);
    
    const variantToggle = screen.getByRole('switch');
    fireEvent.click(variantToggle);
    
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({
          has_varient: true,
          is_variant: true
        })
      );
    });
  });

  it('generates variants when attributes are selected', async () => {
    const mockOnChange = jest.fn();
    const propsWithVariants = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        has_varient: true,
        is_variant: true
      },
      onChange: mockOnChange
    };
    
    render(<StepPricingInventory {...propsWithVariants} />);
    
    // Select Color attribute
    const colorCheckbox = screen.getByLabelText('Color');
    fireEvent.click(colorCheckbox);
    
    // Select Red value
    const redCheckbox = screen.getByLabelText('Red');
    fireEvent.click(redCheckbox);
    
    // Select Blue value
    const blueCheckbox = screen.getByLabelText('Blue');
    fireEvent.click(blueCheckbox);
    
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({
          product_variants: expect.arrayContaining([
            expect.objectContaining({
              attribute_id: 1,
              attribute_value_id: 1,
              regular_price: 100
            }),
            expect.objectContaining({
              attribute_id: 1,
              attribute_value_id: 2,
              regular_price: 100
            })
          ])
        })
      );
    });
  });

  it('enables VAT tax document upload when VAT is set to Exempted', async () => {
    const mockOnChange = jest.fn();
    const propsWithExemptVAT = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        vat_tax: 'Exempted'
      },
      onChange: mockOnChange
    };
    
    render(<StepPricingInventory {...propsWithExemptVAT} />);
    
    const uploadArea = screen.getByText('Click to upload VAT tax document');
    expect(uploadArea).toBeInTheDocument();
    
    // Upload area should be clickable (not disabled)
    expect(uploadArea.closest('label')).not.toHaveClass('cursor-not-allowed');
  });

  it('disables VAT tax document upload when VAT is not exempted', () => {
    render(<StepPricingInventory {...defaultProps} />);
    
    const uploadArea = screen.getByText(/Only available when VAT Tax is 'Exempted'/);
    expect(uploadArea).toBeInTheDocument();
  });

  it('uploads VAT tax document successfully', async () => {
    const { uploadSingleFile } = require('@/utils/fileUploadHelpers');
    const mockOnChange = jest.fn();
    const propsWithExemptVAT = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        vat_tax: 'Exempted'
      },
      onChange: mockOnChange
    };
    
    render(<StepPricingInventory {...propsWithExemptVAT} />);
    
    const fileInput = screen.getByLabelText(/Click to upload VAT tax document/);
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    await waitFor(() => {
      expect(uploadSingleFile).toHaveBeenCalledWith(file);
      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({
          vat_tax_utl: 'https://example.com/uploaded-file.pdf',
          vat_tax_document_name: 'test.pdf'
        })
      );
    });
  });

  it('validates file types for VAT tax document', async () => {
    const propsWithExemptVAT = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        vat_tax: 'Exempted'
      }
    };
    
    render(<StepPricingInventory {...propsWithExemptVAT} />);
    
    const fileInput = screen.getByLabelText(/Click to upload VAT tax document/);
    const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    
    fireEvent.change(fileInput, { target: { files: [invalidFile] } });
    
    await waitFor(() => {
      expect(screen.getByText(/Invalid file type/)).toBeInTheDocument();
    });
  });

  it('updates variant data correctly in the matrix', async () => {
    const mockOnChange = jest.fn();
    const propsWithVariants = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        has_varient: true,
        is_variant: true,
        product_variants: [
          {
            id: 'variant_1',
            sku: 'TEST-001',
            regular_price: 100,
            offer_price: 80,
            stock: 50,
            weight: 1.5,
            is_active: true,
            attributes: [{ attribute_id: 1, value_id: 1, attribute_name: 'Color', value_name: 'Red' }]
          }
        ]
      },
      onChange: mockOnChange
    };
    
    render(<StepPricingInventory {...propsWithVariants} />);
    
    // Find and update SKU field
    const skuInput = screen.getByDisplayValue('TEST-001');
    fireEvent.change(skuInput, { target: { value: 'UPDATED-001' } });
    
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({
          product_variants: expect.arrayContaining([
            expect.objectContaining({
              id: 'variant_1',
              sku: 'UPDATED-001'
            })
          ])
        })
      );
    });
  });

  it('applies bulk price updates to all variants', async () => {
    const mockOnChange = jest.fn();
    const propsWithVariants = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        has_varient: true,
        is_variant: true,
        regular_price: 150,
        product_variants: [
          {
            id: 'variant_1',
            regular_price: 100,
            attributes: [{ attribute_id: 1, value_id: 1 }]
          },
          {
            id: 'variant_2',
            regular_price: 100,
            attributes: [{ attribute_id: 1, value_id: 2 }]
          }
        ]
      },
      onChange: mockOnChange
    };
    
    render(<StepPricingInventory {...propsWithVariants} />);
    
    const bulkPriceButton = screen.getByText('Apply Base Price to All');
    fireEvent.click(bulkPriceButton);
    
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({
          product_variants: expect.arrayContaining([
            expect.objectContaining({
              id: 'variant_1',
              regular_price: 150
            }),
            expect.objectContaining({
              id: 'variant_2',
              regular_price: 150
            })
          ])
        })
      );
    });
  });

  it('clears variants when toggle is disabled with confirmation', async () => {
    const mockOnChange = jest.fn();
    const propsWithVariants = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        has_varient: true,
        is_variant: true,
        product_variants: [
          { id: 'variant_1', sku: 'TEST-001' }
        ]
      },
      onChange: mockOnChange
    };

    render(<StepPricingInventory {...propsWithVariants} />);

    const variantToggle = screen.getByRole('switch');
    fireEvent.click(variantToggle);

    // Confirmation modal should appear
    await waitFor(() => {
      expect(screen.getByText('Disabling variants will remove all variant data')).toBeInTheDocument();
    });

    const continueButton = screen.getByText('Continue');
    fireEvent.click(continueButton);

    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({
          has_varient: false,
          is_variant: false,
          product_variants: []
        })
      );
    });
  });

  it('prioritizes product_variants over variants for initialization', () => {
    const mockOnChange = jest.fn();
    const propsWithBothFields = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        has_varient: true,
        is_variant: true,
        product_variants: [
          { id: 'variant_1', sku: 'PRODUCT-001', regular_price: 100 }
        ],
        variants: [
          { id: 'variant_2', sku: 'VARIANT-001', regular_price: 200 }
        ]
      },
      onChange: mockOnChange
    };

    render(<StepPricingInventory {...propsWithBothFields} />);

    // Should display the product_variants data (primary field), not variants data
    expect(screen.getByDisplayValue('PRODUCT-001')).toBeInTheDocument();
    expect(screen.queryByDisplayValue('VARIANT-001')).not.toBeInTheDocument();
  });

  it('ensures product_variants field is populated when variants are generated', async () => {
    const mockOnChange = jest.fn();
    const propsWithVariants = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        has_varient: true,
        is_variant: true
      },
      onChange: mockOnChange
    };

    render(<StepPricingInventory {...propsWithVariants} />);

    // Select Color attribute
    const colorCheckbox = screen.getByLabelText('Color');
    fireEvent.click(colorCheckbox);

    // Select Red value
    const redCheckbox = screen.getByLabelText('Red');
    fireEvent.click(redCheckbox);

    await waitFor(() => {
      // Verify that onChange was called with product_variants populated
      const lastCall = mockOnChange.mock.calls[mockOnChange.mock.calls.length - 1];
      const updatedData = lastCall[0];

      expect(updatedData.product_variants).toBeDefined();
      expect(updatedData.product_variants).toHaveLength(1);
      expect(updatedData.product_variants[0]).toMatchObject({
        attribute_id: 1,
        attribute_value_id: 1,
        regular_price: expect.any(Number)
      });

      // Only product_variants field is updated (single source of truth)
      expect(updatedData.variants).toBeUndefined();
    });
  });

  it('maintains data consistency when updating individual variant properties', async () => {
    const mockOnChange = jest.fn();
    const propsWithVariants = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        has_varient: true,
        is_variant: true,
        product_variants: [
          {
            id: 'variant_1',
            sku: 'TEST-001',
            regular_price: 100,
            offer_price: 80,
            stock: 50,
            weight: 1.5,
            is_active: true,
            attributes: [{ attribute_id: 1, value_id: 1, attribute_name: 'Color', value_name: 'Red' }]
          }
        ]
      },
      onChange: mockOnChange
    };

    render(<StepPricingInventory {...propsWithVariants} />);

    // Update regular price
    const priceInput = screen.getByDisplayValue('100');
    fireEvent.change(priceInput, { target: { value: '150' } });

    await waitFor(() => {
      const lastCall = mockOnChange.mock.calls[mockOnChange.mock.calls.length - 1];
      const updatedData = lastCall[0];

      // Verify product_variants is updated (primary field)
      expect(updatedData.product_variants[0].regular_price).toBe(150);

      // Verify other properties remain unchanged
      expect(updatedData.product_variants[0].sku).toBe('TEST-001');
      expect(updatedData.product_variants[0].stock).toBe(50);
    });
  });

  it('handles API response format for product variants correctly', () => {
    const mockOnChange = jest.fn();
    const propsWithApiVariants = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        has_varient: true,
        is_variant: true,
        product_variants: [
          {
            id: 3,
            product_id: 27,
            regular_price: "139.00",
            offer_price: "115.00",
            vat_tax: "standard_5",
            stock: 0,
            sku: "5ert",
            system_sku: "SKU-688216cb8c7a3",
            weight: "0.00",
            is_active: true,
            inventory: {
              stock: 0,
              reserved: 0,
              threshold: 5,
              location: null,
              note: null
            },
            product_variant_attribute: {
              product_attribute_id: 1,
              product_attribute_value_id: 1,
              attribute: {
                name: "Flavor"
              },
              attribute_value: {
                value: "Vanilla"
              }
            }
          }
        ]
      },
      onChange: mockOnChange
    };

    render(<StepPricingInventory {...propsWithApiVariants} />);

    // Should display the transformed variant data without errors
    expect(screen.getByDisplayValue('5ert')).toBeInTheDocument();
    expect(screen.getByDisplayValue('139')).toBeInTheDocument();
    expect(screen.getByText('Flavor: Vanilla')).toBeInTheDocument();
  });
});
