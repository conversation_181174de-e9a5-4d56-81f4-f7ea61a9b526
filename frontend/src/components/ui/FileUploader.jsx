import { useState, useRef } from "react";
import { uploadSingleFile } from "@/utils/fileUploadHelpers";
import LoadingSpinner from "./LoadingSpinner";
import { useTranslation } from "react-i18next";

const FileUploader = ({
  value,
  onUploadSuccess,
  onFileSuccess,
  asFile = false,
  width = "w-full",
}) => {
  const { t, i18n } = useTranslation();
  const [fileName, setFileName] = useState(value || "");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const fileInputRef = useRef(null);
  const isRTL =
    typeof window !== "undefined"
      ? document?.dir === "rtl"
      : i18n.dir && i18n.dir() === "rtl";

  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    if (asFile) {
      setFileName(file.name);
      onFileSuccess?.(file);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const response = await uploadSingleFile(file);
      const url = response.data?.data?.path;
      if (!url) throw new Error("No URL returned from upload");
      setFileName(file.name);
      onUploadSuccess?.(url);
      fileInputRef.current.value = "";
    } catch (err) {
      setError(err.message || "Failed to upload file");
    } finally {
      setIsLoading(false);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current.click();
  };

  const clearFile = () => {
    setFileName("");
    onUploadSuccess?.(null);
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center relative">
        <div
          className={`relative flex-grow ${
            isRTL ? "rtl-flex-row-reverse" : ""
          }`}
          onClick={triggerFileInput}
        >
          <div
            className={`absolute top-1/2 transform -translate-y-1/2 text-gray-500 ltr:border-e-2 rtl:border-e-2 border-slate-500 pe-2
            ${
              isRTL ? "right-3 rtl-pl-0 rtl-pr-0" : "left-3 rtl-pl-0 rtl-pr-0"
            }`}
            style={
              isRTL
                ? { right: "0.75rem", left: "auto" }
                : { left: "0.75rem", right: "auto" }
            }
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="lucide lucide-file-up-icon lucide-file-up"
            >
              <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
              <path d="M14 2v4a2 2 0 0 0 2 2h4" />
              <path d="M12 12v6" />
              <path d="m15 15-3-3-3 3" />
            </svg>
          </div>
          <input
            type="text"
            readOnly
            value={fileName}
            placeholder={t("commonPlaceholder.filePlaceholder")}
            className={`border border-gray-300 rounded py-2 text-sm ${width} focus:outline-none focus:ring-2 focus:ring-blue-400 ${
              isRTL ? "rtl-text-right" : "rtl-text-left"
            }`}
            dir={isRTL ? "rtl" : "ltr"}
            style={
              isRTL
                ? { paddingRight: "3.5rem", paddingLeft: "2rem" }
                : { paddingLeft: "3.5rem", paddingRight: "2rem" }
            }
          />
          {fileName && (
            <button
              type="button"
              onClick={clearFile}
              className={`absolute top-1/2 transform -translate-y-1/2 text-red-500 hover:text-slate-50 bg-red-300 p-1 hover:bg-red-600 rounded shadow ${
                isRTL ? "left-2" : "right-2"
              }`}
              aria-label="Remove file"
              style={
                isRTL
                  ? { left: "0.5rem", right: "auto" }
                  : { right: "0.5rem", left: "auto" }
              }
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-4 h-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth="2"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          )}
        </div>
        <button
          type="button"
          onClick={triggerFileInput}
          className="flex items-center justify-center w-10 h-10 bg-gray-100 rounded transition-colors border border-gray-200 shadow hover:border hover:border-primary-400 ms-1"
          aria-label="Upload File"
        >
          {isLoading ? (
            <LoadingSpinner />
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="lucide lucide-folder-up-icon lucide-folder-up"
            >
              <path d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z" />
              <path d="M12 10v6" />
              <path d="m9 13 3-3 3 3" />
            </svg>
          )}
        </button>
      </div>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept=".pdf,.doc,.docx,.zip,.rar,.txt"
        className="hidden"
      />
      {error && <p className="text-red-500 text-sm">{error}</p>}
    </div>
  );
};

export default FileUploader;

// ---------previous code

// import { useState, useRef } from "react";
// import { uploadSingleFile } from "@/utils/fileUploadHelpers";
// import LoadingSpinner from "./LoadingSpinner";
// import { useTranslation } from "react-i18next";

// const FileUploader = ({
//   value,
//   onUploadSuccess,
//   onFileSuccess,
//   asFile = false,
//   width = "w-full",
// }) => {
//   const { t } = useTranslation();
//   const [fileName, setFileName] = useState(value || "");
//   const [isLoading, setIsLoading] = useState(false);
//   const [error, setError] = useState(null);
//   const fileInputRef = useRef(null);

//   const handleFileChange = async (e) => {
//     const file = e.target.files[0];
//     if (!file) return;

//     if (asFile) {
//       setFileName(file.name);
//       onFileSuccess?.(file);
//       return;
//     }

//     try {
//       setIsLoading(true);
//       setError(null);
//       const response = await uploadSingleFile(file);
//       const url = response.data?.data?.path;
//       if (!url) throw new Error("No URL returned from upload");
//       setFileName(file.name);
//       onUploadSuccess?.(url);
//       fileInputRef.current.value = "";
//     } catch (err) {
//       setError(err.message || "Failed to upload file");
//       console.error("Upload error:", err);
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   const triggerFileInput = () => {
//     fileInputRef.current.click();
//   };

//   const clearFile = () => {
//     setFileName("");
//     onUploadSuccess?.(null);
//     if (fileInputRef.current) fileInputRef.current.value = "";
//   };

//   return (
//     <div className="space-y-2">
//       <div className="flex items-center relative">
//         {/* Wrapper with padding-left for icon */}
//         <div className="relative flex-grow" onClick={triggerFileInput}>
//           {/* Left-side file icon */}
//           <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 border-e-2 border-slate-500 pe-2">
//             <svg
//               xmlns="http://www.w3.org/2000/svg"
//               width="24"
//               height="24"
//               viewBox="0 0 24 24"
//               fill="none"
//               stroke="currentColor"
//               stroke-width="2"
//               stroke-linecap="round"
//               stroke-linejoin="round"
//               class="lucide lucide-file-up-icon lucide-file-up"
//             >
//               <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
//               <path d="M14 2v4a2 2 0 0 0 2 2h4" />
//               <path d="M12 12v6" />
//               <path d="m15 15-3-3-3 3" />
//             </svg>
//           </div>

//           {/* Input with left padding for icon */}

//           <input
//             type="text"
//             readOnly
//             value={fileName}
//             placeholder={t("commonPlaceholder.filePlaceholder")}
//             className={`border border-gray-300 rounded pl-14 pr-8 py-2 text-sm ${width} focus:outline-none focus:ring-2 focus:ring-blue-400`}
//           />

//           {/* Cross button (only if file is selected) */}
//           {fileName && (
//             <button
//               type="button"
//               onClick={clearFile}
//               className="absolute right-2 top-1/2 transform -translate-y-1/2 text-red-500 hover:text-slate-50 bg-red-300 p-1 hover:bg-red-600 rounded shadow"
//               aria-label="Remove file"
//             >
//               <svg
//                 xmlns="http://www.w3.org/2000/svg"
//                 className="w-4 h-4"
//                 fill="none"
//                 viewBox="0 0 24 24"
//                 stroke="currentColor"
//                 strokeWidth="2"
//               >
//                 <path
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   d="M6 18L18 6M6 6l12 12"
//                 />
//               </svg>
//             </button>
//           )}
//         </div>

//         {/* Upload button */}
//         <button
//           type="button"
//           onClick={triggerFileInput}
//           className="flex items-center justify-center w-10 h-10 bg-gray-100 rounded transition-colors border border-gray-200 shadow hover:border hover:border-primary-400 ms-1"
//           aria-label="Upload File"
//         >
//           {isLoading ? (
//             <LoadingSpinner />
//           ) : (
//             <svg
//               xmlns="http://www.w3.org/2000/svg"
//               width="24"
//               height="24"
//               viewBox="0 0 24 24"
//               fill="none"
//               stroke="currentColor"
//               stroke-width="2"
//               stroke-linecap="round"
//               stroke-linejoin="round"
//               class="lucide lucide-folder-up-icon lucide-folder-up"
//             >
//               <path d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z" />
//               <path d="M12 10v6" />
//               <path d="m9 13 3-3 3 3" />
//             </svg>
//           )}
//         </button>
//       </div>

//       {/* Hidden file input */}
//       <input
//         type="file"
//         ref={fileInputRef}
//         onChange={handleFileChange}
//         accept=".pdf,.doc,.docx,.zip,.rar,.txt"
//         className="hidden"
//       />

//       {error && <p className="text-red-500 text-sm">{error}</p>}
//     </div>
//   );
// };

// export default FileUploader;
