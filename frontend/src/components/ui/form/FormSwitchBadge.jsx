import { useField } from "formik";
import { motion } from "framer-motion";

const FormSwitchBadge = ({
  label,
  helperText,
  className = "",
  onText = "YES",
  offText = "NO",
  onClass = "bg-emerald-600 hover:bg-emerald-700 focus:ring-2 focus:ring-emerald-300",
  offClass = "bg-rose-600 hover:bg-rose-700 focus:ring-2 focus:ring-rose-300",
  onChange,
  ...props
}) => {
  const [field, meta, helpers] = useField({ ...props, type: "checkbox" });
  const hasError = meta.touched && meta.error;

  const DOT_SIZE = 24;
  const PADDING = 2;
  const SWITCH_WIDTH = 64;

  const handleToggle = () => {
    if (props.disabled) return;
    const next = !field.value;
    helpers.setValue(next);
    if (typeof onChange === "function") onChange(next);
  };

  return (
    <div className={`mb-0 ${className}`}>
      <div className="flex items-start justify-start gap-3">
        {label && (
          <label
            htmlFor={props.id || props.name}
            className={`text-sm font-medium ${
              props.disabled ? "text-gray-400" : "text-gray-700"
            }`}
          >
            {label}
            {props.required && (
              <span className="text-red-500 ml-1 rtl-ml-0 rtl-mr-1">*</span>
            )}
          </label>
        )}

        <div
          onClick={handleToggle}
          className={`relative h-7 rounded-full cursor-pointer transition-colors duration-200 overflow-hidden
            ${field.value ? onClass : offClass}
            ${props.disabled ? "opacity-50 cursor-not-allowed" : ""}`}
          style={{ width: SWITCH_WIDTH }}
          role="switch"
          aria-checked={field.value ? "true" : "false"}
          tabIndex={props.disabled ? -1 : 0}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              handleToggle();
            }
          }}
        >
          <span
            className={`absolute left-2 top-1/2 -translate-y-1/2 text-[12px] transition-opacity font-extrabold text-slate-50 capitalize ${
              field.value ? "opacity-100" : "opacity-0"
            }`}
          >
            {offText}
          </span>
          <span
            className={`absolute right-2 top-1/2 -translate-y-1/2 text-[12px] font-extrabold text-slate-50 transition-opacity capitalize ${
              field.value ? "opacity-0" : "opacity-100"
            }`}
          >
            {onText}
          </span>

          <motion.div
            className="absolute bg-white rounded-full shadow"
            style={{
              width: DOT_SIZE,
              height: DOT_SIZE,
              top: PADDING,
              left: field.value ? SWITCH_WIDTH - DOT_SIZE - PADDING : PADDING,
            }}
            animate={{
              left: field.value ? SWITCH_WIDTH - DOT_SIZE - PADDING : PADDING,
            }}
            transition={{ type: "spring", stiffness: 400, damping: 30 }}
          />
        </div>
      </div>

      <div>
        {hasError ? (
          <motion.p
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-sm text-red-600"
          >
            {meta.error}
          </motion.p>
        ) : helperText ? (
          <p className="text-xs text-gray-500">{helperText}</p>
        ) : null}
      </div>
    </div>
  );
};

export default FormSwitchBadge;
