import React, { useState } from "react";
import { useField } from "formik";
import { useTranslation } from "react-i18next";

const CalculatePasswordStrength = (password) => {
  if (!password) return 0;
  let strength = 0;
  if (password.length >= 8) strength += 20;
  if (/[A-Z]/.test(password)) strength += 20;
  if (/[a-z]/.test(password)) strength += 20;
  if (/[0-9]/.test(password)) strength += 20;
  if (/[!@#$%^&*]/.test(password)) strength += 20;
  return strength;
};

const getStrengthColor = (strength) => {
  if (strength <= 20) return "bg-red-500";
  if (strength <= 40) return "bg-orange-500";
  if (strength <= 60) return "bg-yellow-500";
  if (strength <= 80) return "bg-blue-500";
  return "bg-green-500";
};

const getStrengthText = (strength) => {
  if (strength <= 20) return "Very Weak";
  if (strength <= 40) return "Weak";
  if (strength <= 60) return "Medium";
  if (strength <= 80) return "Strong";
  return "Very Strong";
};

const FormPassword = ({
  className,
  label,
  required,
  placeholder,
  strongCheck = true,
  dir,
  ...props
}) => {
  const [field, meta] = useField(props);
  const { i18n } = useTranslation();
  const [showPassword, setShowPassword] = useState(false);
  const isError = meta.touched && meta.error;
  const strength = CalculatePasswordStrength(field.value);
  const resolvedDir = dir || i18n.dir();
  const isRTL = resolvedDir === "rtl";

  const hideBrowserRevealIcon = `
    input::-ms-reveal, input::-ms-clear { display: none !important; }
    input::-webkit-credentials-auto-fill-button,
    input::-webkit-inner-spin-button,
    input::-webkit-contacts-auto-fill-button {
      visibility: hidden; display: none !important; pointer-events: none; height: 0; width: 0;
    }
    input[type="password"] { -moz-appearance: textfield !important; }
  `;

  return (
    <div dir={resolvedDir} key={resolvedDir}>
      <style>{hideBrowserRevealIcon}</style>
      {label && (
        <label
          htmlFor={props.id || props.name}
          className={`block text-sm font-medium text-gray-700 mb-2 ${
            isRTL ? "text-right" : ""
          }`}
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <div className="relative">
        <input
          {...field}
          {...props}
          type={showPassword ? "text" : "password"}
          placeholder={placeholder}
          className={`rounded-lg appearance-none border w-full p-3 ${
            isRTL ? "pl-10 pr-3" : "pr-10 pl-3"
          } text-gray-700 leading-tight focus:outline-none placeholder:text-sm ${className} ${
            isError ? "border-red-500" : ""
          }`}
          autoComplete="new-password"
        />
        <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          className={`absolute top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 ${
            isRTL ? "left-3" : "right-3"
          }`}
          aria-label={showPassword ? "Hide password" : "Show password"}
        >
          {showPassword ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="lucide lucide-eye-off"
            >
              <path d="M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49" />
              <path d="M14.084 14.158a3 3 0 0 1-4.242-4.242" />
              <path d="M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143" />
              <path d="m2 2 20 20" />
            </svg>
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="lucide lucide-view"
            >
              <path d="M21 17v2a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-2" />
              <path d="M21 7V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v2" />
              <circle cx="12" cy="12" r="1" />
              <path d="M18.944 12.33a1 1 0 0 0 0-.66 7.5 7.5 0 0 0-13.888 0 1 1 0 0 0 0 .66 7.5 7.5 0 0 0 13.888 0" />
            </svg>
          )}
        </button>
      </div>
      {strongCheck && field.value && (
        <div className="mt-2">
          <div className="flex mb-1">
            <div className="h-2 flex-1 rounded-full bg-slate-100 overflow-hidden">
              <div
                className={`h-full transition-all ${getStrengthColor(
                  strength
                )}`}
                style={{ width: `${strength}%` }}
              />
            </div>
          </div>
          <p
            className={`text-xs ${getStrengthColor(strength).replace(
              "bg-",
              "text-"
            )} ${isRTL ? "text-right" : ""}`}
          >
            {getStrengthText(strength)}
          </p>
        </div>
      )}
      {isError && <span className="text-red-500 text-xs">{meta.error}</span>}
    </div>
  );
};

export default FormPassword;
