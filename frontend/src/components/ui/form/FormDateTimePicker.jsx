"use client";
import * as React from "react";
import { CalendarClock } from "lucide-react";
import { format } from "date-fns";
import { useField } from "formik";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/product/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Label } from "@radix-ui/react-label";

const FormDateTimePicker = ({ name, label, minDate, ...props }) => {
  const [field, meta, helpers] = useField(name);
  const { value } = meta;
  const { setValue } = helpers;
  const [isOpen, setIsOpen] = React.useState(false);

  const handleDateSelect = (selectedDate) => {
    if (selectedDate) {
      const newDate = value ? new Date(value) : new Date();
      newDate.setFullYear(selectedDate.getFullYear());
      newDate.setMonth(selectedDate.getMonth());
      newDate.setDate(selectedDate.getDate());
      setValue(newDate);
    }
  };

  const handleTimeChange = (type, timeValue) => {
    const newDate = value ? new Date(value) : new Date();
    if (type === "hour") {
      newDate.setHours(
        (parseInt(timeValue) % 12) + (newDate.getHours() >= 12 ? 12 : 0)
      );
    } else if (type === "minute") {
      newDate.setMinutes(parseInt(timeValue));
    } else if (type === "ampm") {
      const currentHours = newDate.getHours();
      if (timeValue === "PM" && currentHours < 12) {
        newDate.setHours(currentHours + 12);
      } else if (timeValue === "AM" && currentHours >= 12) {
        newDate.setHours(currentHours - 12);
      }
    }
    setValue(newDate);
  };

  const hours = Array.from({ length: 12 }, (_, i) => i + 1);

  return (
    <div className="w-full">
      {label && <Label htmlFor={name}>{label}</Label>}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal gap-2",
              !value && "text-muted-foreground"
            )}
            onClick={() => setIsOpen(!isOpen)}
          >
            <CalendarClock className="mr-2 h-5 w-5" />
            {value ? (
              format(new Date(value), "MM/dd/yyyy hh:mm aa")
            ) : (
              <span>MM/DD/YYYY hh:mm aa</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 bg-white">
          <div className="sm:flex">
            <Calendar
              mode="single"
              selected={value ? new Date(value) : undefined}
              onSelect={handleDateSelect}
              initialFocus
              disabled={minDate ? { before: minDate } : undefined}
            />
            <div className="flex flex-col sm:flex-row sm:h-[300px] divide-y sm:divide-y-0 sm:divide-x">
              <ScrollArea className="w-64 sm:w-auto">
                <div className="text-center pt-2 pb-3 bg-slate-200">Hour</div>
                <div className="flex sm:flex-col p-2 border-s-2 border-t-2 border-b-2 border-gray-400">
                  {hours.map((hour) => (
                    <Button
                      key={hour}
                      size="icon"
                      variant={
                        value && new Date(value).getHours() % 12 === hour % 12
                          ? "default"
                          : "ghost"
                      }
                      className="sm:w-full shrink-0 aspect-square"
                      onClick={() => handleTimeChange("hour", hour.toString())}
                    >
                      {hour}
                    </Button>
                  ))}
                </div>
                <ScrollBar orientation="horizontal" className="sm:hidden" />
              </ScrollArea>
              <ScrollArea className="w-64 sm:w-auto">
                <div className="text-center pt-2 pb-3 bg-slate-200">Minute</div>
                <div className="flex sm:flex-col p-2 border-2 border-gray-400">
                  {Array.from({ length: 12 }, (_, i) => i * 5).map((minute) => (
                    <Button
                      key={minute}
                      size="icon"
                      variant={
                        value && new Date(value).getMinutes() === minute
                          ? "default"
                          : "ghost"
                      }
                      className="sm:w-full shrink-0 aspect-square"
                      onClick={() =>
                        handleTimeChange("minute", minute.toString())
                      }
                    >
                      {minute}
                    </Button>
                  ))}
                </div>
                <ScrollBar orientation="horizontal" className="sm:hidden" />
              </ScrollArea>
              <ScrollArea className="">
                <div className="text-center pt-2 pb-3 bg-slate-200">Period</div>
                <div className="flex sm:flex-col items-center p-2 justify-center border-t-2 border-gray-400">
                  {["AM", "PM"].map((ampm) => (
                    <Button
                      key={ampm}
                      size="icon"
                      variant={
                        value &&
                        ((ampm === "AM" && new Date(value).getHours() < 12) ||
                          (ampm === "PM" && new Date(value).getHours() >= 12))
                          ? "default"
                          : "ghost"
                      }
                      className="sm:w-full shrink-0 aspect-square"
                      onClick={() => handleTimeChange("ampm", ampm)}
                    >
                      {ampm}
                    </Button>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </div>
        </PopoverContent>
      </Popover>
      {meta.touched && meta.error && (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      )}
    </div>
  );
};
export default FormDateTimePicker;
