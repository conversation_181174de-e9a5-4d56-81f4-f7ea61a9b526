import { useState, useRef, useEffect, useContext } from "react";
import { useTranslation } from "react-i18next";
import { motion, AnimatePresence } from "framer-motion";
import EnglishIcon from "./icons/EnglishIcon";
import ArabicIcon from "./icons/ArabicIcon";
import GlobeIcon from "./icons/GlobeIcon";
import { LanguageContext } from "../../contexts/LanguageContext";

const LanguageSwitcher = () => {
  const { i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const { direction } = useContext(LanguageContext);
  const isRtl = direction === "rtl";

  const languages = [
    {
      code: "en",
      name: "English",
      icon: <EnglishIcon className="w-5 h-5" />,
      direction: "ltr",
    },
    {
      code: "ar",
      name: "العربية",
      icon: <ArabicIcon className="w-5 h-5" />,
      direction: "rtl",
    },
  ];

  const currentLanguage =
    languages.find((lang) => lang.code === i18n.language) || languages[0];

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const changeLanguage = (langCode) => {
    i18n.changeLanguage(langCode);
    setIsOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={toggleDropdown}
        className="flex items-center gap-2 rtl-flex-row-reverse px-2 py-2 rounded-full bg-black bg-opacity-80 hover:bg-opacity-100 text-white transition-all duration-200 border border-gray-700"
        aria-label="Change language"
        title={`Current language: ${currentLanguage.name}`}
      >
        {/* <GlobeIcon className="w-4 h-4 text-gray-200" /> */}
        <span>{currentLanguage.icon}</span>
        <span className="text-xs font-medium uppercase">
          {currentLanguage.code}
        </span>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2 }}
          className="w-4 h-4 flex items-center justify-center"
        >
          <svg
            className="w-3 h-3"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </motion.div>
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 mt-2 w-48 bg-black bg-opacity-90 backdrop-blur-sm rounded-lg shadow-lg py-1 z-50 border border-gray-700"
            style={{
              [isRtl ? "left" : "right"]: 0,
              [isRtl ? "right" : "left"]: "auto",
            }}
          >
            {languages.map((language) => (
              <button
                key={language.code}
                onClick={() => changeLanguage(language.code)}
                className={`block w-full text-left rtl-text-right px-4 py-3 text-sm transition-colors ${
                  language.code === i18n.language
                    ? "bg-gray-800 text-white"
                    : "text-gray-300 hover:bg-gray-800 hover:text-white"
                }`}
              >
                <div
                  className="flex items-center rtl-flex-row-reverse"
                  dir="ltr"
                >
                  <div className="mr-3 rtl-ml-3 rtl-mr-0 flex-shrink-0">
                    {language.icon}
                  </div>
                  <span className="font-medium">{language.name}</span>
                </div>
              </button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LanguageSwitcher;
