import { useState, useEffect, useRef } from "react";
import { FaChevronDown, FaSearch, FaSpinner, FaTimes } from "react-icons/fa";
import { useApi } from "@/hooks/useApi";

const SearchableDropdown = ({
  label,
  placeholder = "All Vendors",
  value,
  onChange,
  apiEndpoint,
  apiParams = {},
  displayField = "name",
  valueField = "id",
  className = "",
  disabled = false,
  searchPlaceholder = "Search vendors...",
  noResultsText = "No vendors found",
  loadingText = "Loading vendors...",
  showCount = true,
  pageSize = 20,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedOption, setSelectedOption] = useState(null);

  const dropdownRef = useRef(null);
  const searchInputRef = useRef(null);

  // Use the existing useApi hook for consistency
  const apiParamsWithSearch = {
    ...apiParams,
    pagination: true,
    per_page: pageSize,
    page: currentPage,
    ...(debouncedSearchTerm && { search: debouncedSearchTerm })
  };

  const { data: apiData, loading, error } = useApi(
    isOpen ? apiEndpoint : null,
    apiParamsWithSearch
  );

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setCurrentPage(1); // Reset to first page on search
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Extract options from API response
  const options = apiData?.data?.data || apiData?.data || [];
  const totalCount = apiData?.data?.total || options.length;
  const hasMore = apiData?.data?.current_page ? apiData?.data?.current_page < apiData?.data?.last_page : false;

  // Find selected option when value changes
  useEffect(() => {
    if (value && options.length > 0) {
      const selected = options.find(option => String(option[valueField]) === String(value));
      setSelectedOption(selected);
    } else if (!value) {
      setSelectedOption(null);
    }
  }, [value, options, valueField]);

  // Helper function to get display text from option
  const getDisplayText = (option) => {
    if (typeof displayField === 'function') {
      return displayField(option);
    }

    // For vendor data, try multiple fields in order of preference
    if (displayField === 'vendor_display_name_en') {
      return option.vendor_display_name_en || option.name_tl_en || option.spoc_name || option.name || 'Unknown Vendor';
    }

    return option[displayField] || option.name || option.title || 'Unknown';
  };

  const loadMore = () => {
    if (!loading && hasMore) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const handleToggle = () => {
    if (disabled) return;
    
    if (!isOpen) {
      setIsOpen(true);
      // Focus search input when opening
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    } else {
      setIsOpen(false);
      setSearchTerm("");
    }
  };

  const handleSelect = (option) => {
    setSelectedOption(option);
    onChange(option[valueField]);
    setIsOpen(false);
    setSearchTerm("");
  };

  const handleClear = (e) => {
    e.stopPropagation();
    setSelectedOption(null);
    onChange("");
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      setIsOpen(false);
    } else if (e.key === 'Enter' && options.length > 0) {
      handleSelect(options[0]);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        setSearchTerm("");
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const displayValue = selectedOption ? getDisplayText(selectedOption) : placeholder;

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>
      )}
      
      <div
        className={`relative w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white cursor-pointer focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
          disabled ? 'bg-gray-100 cursor-not-allowed' : 'hover:border-gray-400'
        }`}
        onClick={handleToggle}
      >
        <div className="flex items-center justify-between">
          <span className={`block truncate ${!selectedOption ? 'text-gray-500' : 'text-gray-900'}`}>
            {displayValue}
          </span>
          <div className="flex items-center space-x-2">
            {selectedOption && (
              <button
                onClick={handleClear}
                className="text-gray-400 hover:text-gray-600"
                type="button"
              >
                <FaTimes className="w-3 h-3" />
              </button>
            )}
            <FaChevronDown
              className={`w-4 h-4 text-gray-400 transition-transform ${
                isOpen ? 'transform rotate-180' : ''
              }`}
            />
          </div>
        </div>
      </div>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-hidden">
          {/* Search Input */}
          <div className="p-3 border-b border-gray-200">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                ref={searchInputRef}
                type="text"
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                placeholder={searchPlaceholder}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={handleKeyDown}
              />
            </div>
            {showCount && totalCount > 0 && (
              <div className="text-xs text-gray-500 mt-2">
                Showing {options.length} of {totalCount} {label?.toLowerCase() || 'items'}
              </div>
            )}
          </div>

          {/* Options List */}
          <div 
            ref={optionsListRef}
            className="max-h-60 overflow-y-auto"
            onScroll={(e) => {
              const { scrollTop, scrollHeight, clientHeight } = e.target;
              if (scrollHeight - scrollTop === clientHeight && hasMore && !loading) {
                loadMore();
              }
            }}
          >
            {loading && options.length === 0 ? (
              <div className="flex items-center justify-center py-4">
                <FaSpinner className="animate-spin w-4 h-4 mr-2" />
                <span className="text-gray-500">{loadingText}</span>
              </div>
            ) : options.length === 0 ? (
              <div className="py-4 text-center text-gray-500">
                {noResultsText}
              </div>
            ) : (
              <>
                {options.map((option, index) => (
                  <div
                    key={`${option[valueField]}_${index}`}
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                    onClick={() => handleSelect(option)}
                  >
                    {getDisplayText(option)}
                  </div>
                ))}
                
                {loading && options.length > 0 && (
                  <div className="flex items-center justify-center py-2 border-t border-gray-200">
                    <FaSpinner className="animate-spin w-3 h-3 mr-2" />
                    <span className="text-xs text-gray-500">Loading more...</span>
                  </div>
                )}
                
                {hasMore && !loading && (
                  <div 
                    className="px-4 py-2 text-center text-sm text-indigo-600 hover:bg-gray-50 cursor-pointer border-t border-gray-200"
                    onClick={loadMore}
                  >
                    Load more...
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchableDropdown;
