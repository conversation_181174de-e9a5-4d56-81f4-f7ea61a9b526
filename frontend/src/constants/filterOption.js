
// All filter option use common 

export const statusOptions = [
    { label: "Active", value: "active" },
    { label: "Inactive", value: "inactive" },
];

export const booleanStatusOptions = [
    { label: "Active", value: 1 },
    { label: "Inactive", value: 0 },
];

export const mainSubOptions = [
    { label: "Main", value: "main" },
    { label: "Sub", value: "sub" },
];

export const publishedDraftOptions = [
    {
        label: "Draft",
        value: "draft",
    },
    {
        label: "Published",
        value: "published",
    },
];

export const approvalOptions = [
    {
        label: "Pending",
        value: "pending",
    },
    {
        label: "Approved",
        value: "approved",
    },
    {
        label: "Rejected",
        value: "rejected",
    },
];


export const typeOptions = [
    { value: "percentage", label: "Percentage" },
    { value: "fixed", label: "Fixed" },
];

export const statusBooleanOptions = [
    { label: "Active", value: true },
    { label: "Inactive", value: false },
];

export const priorityOptions = [
    { value: "low", label: "Low" },
    { value: "medium", label: "Medium" },
    { value: "high", label: "High" },
];

export const progressStatusOptions = [
    { value: "open", label: "Open" },
    { value: "in-progress", label: "In Progress" },
    { value: "resolved", label: "Resolved" },
    { value: "closed", label: "Closed" },
];

export const offerDealTypeStatusOptions = [
    { value: "product", label: "Product" },
    { value: "shipping", label: "Shipping" },

];

// Vendor Approval Status
export const approvalVendorStatusOptions = [
    { value: "Pending", label: "Pending" },
    { value: "Approved", label: "Approved" },
    { value: "Rejected", label: "Rejected" },
    { value: "OnHold", label: "On Hold" },
    { value: "Cancelled", label: "Cancelled" },
];
//Vendor EOI Approval Status
export const eoiVendorStatusOptions = [
    { value: "pending", label: "Pending" },
    { value: "draft", label: "Draft" },
    { value: "submitted", label: "Submitted" },
];

//user verification status
export const userBooleanStatusOptions = [
    { label: "Yes", value: 1 },
    { label: "No", value: 0 },
];
//user Approval status
export const userApprovalStatusOptions = [
    { label: "Active", value: "active" },
    { label: "Inactive", value: "inactive" },
    { label: "Banned", value: "banned" },
    { label: "Pending", value: "pending" },
];

// Gender Status
export const genderOptions = [
    { label: "Male", value: "male" },
    { label: "Female", value: "female" },
    { label: "Other", value: "other" },
];

// Customer Type
export const customerTypeOptions = [
    { label: "Retail", value: "retail" },
    { label: "Wholesale", value: "wholesale" },
];
// Preferred Language
export const preferredLanguageOptions = [
    { label: "English", value: "en" },
    { label: "Arabic", value: "ar" },
];

// Preferred currency
export const preferredCurrencyOptions = [
    { label: "AED (United Arab Emirates Dirham)", value: "AED" },
    { label: "USD (United States Dollar)", value: "USD" },
    { label: "EUR (Euro)", value: "EUR" },
    { label: "GBP (British Pound)", value: "GBP" },
];

//Reg Option 
// //const regOptions = [
// { label: t("commonOptions.yesNo.Yes"), value: "yes" },
// { label: t("commonOptions.yesNo.No"), value: "no" },
//   ];
export const regOptions = [
    { label: "Yes", value: "yes" },
    { label: "No", value: "no" },
];

//KYC Document Type
export const kycDocumentTypeOptions = [
    { label: "Passport", value: "passport" },
    { label: "Emirates ID", value: "emirates_id" },
    { label: "Driving License", value: "driving_license" },
    { label: "National ID", value: "national_id" },
    { label: "Student ID", value: "student_id" },
];
//KYC Verified option
export const kycVerifiedOptions = [
    { label: "Yes", value: true },
    { label: "No", value: false },
];

//order Status Option
export const orderStatusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'processing', label: 'Processing' },
    { value: 'shipped', label: 'Shipped' },
    { value: 'delivered', label: 'Delivered' },
    { value: 'cancelled', label: 'Cancelled' },
    { value: 'returned', label: 'Returned' },
];
// Payment Status Option 
export const paymentStatusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'paid', label: 'Paid' },
    { value: 'failed', label: 'Failed' },
    { value: 'refunded', label: 'Refunded' },
];

//payment method Option
export const paymentMethodOptions = [
    { value: 'cod', label: 'Cash on Delivery' },
    { value: 'card', label: 'Credit/Debit Card' },
    { value: 'wallet', label: 'Digital Wallet' },
    { value: 'bank', label: 'Bank Transfer' },
];
//Order Value Option
export const orderValueOptions = [
    { value: '0-100', label: 'AED (0-100)' },
    { value: '100-500', label: 'AED (100-500)' },
    { value: '500-1000', label: 'AED (500-1,000)' },
    { value: '1000+', label: 'AED (1,000+)' },
];
//Date Range Option
export const dateRangeOptions = [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'last_7_days', label: 'Last 7 Days' },
    { value: 'last_30_days', label: 'Last 30 Days' },
    { value: 'this_month', label: 'This Month' },
    { value: 'last_month', label: 'Last Month' },
];








// test purpose 
export const testOptions = [
    { label: "Gift", value: "TestOne" },
    { label: "Promotion", value: "TestTwo" },
];