import { renderNA } from "./commonFunctionHelper";

// Utility function to safely access nested table properties
export const getNestedValue = (object, path, fallback = renderNA) => {
    return !object || typeof path !== 'string'
        ? fallback
        : path.split('.').reduce((acc, key) => acc && key in acc ? acc[key] : undefined, object) || fallback;
};

// Converts the first letter of a string to uppercase.
// export const capitalizeFirstLetter = (value) =>
//     value ? value.charAt(0).toUpperCase() + value.slice(1) : "N/A";

export const capitalizeFirstLetter = (value) =>
    value ? value.charAt(0).toUpperCase() + value.slice(1) : value;


// Converts the entire string to all uppercase.
// export const capitalizeLetter = (value) =>
//     value ? value.toString().toUpperCase() : "N/A";

export const capitalizeLetter = (value) =>
    value ? value.toString().toUpperCase() : value;


// Converts a string into a human-readable format.
export const humanize = (value, options = {}) => {
    const { titleCase = false } = options;
    const str = String(value ?? "")
        .replace(/[_-]+/g, " ")
        .replace(/\s+/g, " ")
        .trim();
    if (!titleCase) return str;
    return str
        .split(" ")
        .map((word) => capitalizeFirstLetter(word))
        .join(" ");
};