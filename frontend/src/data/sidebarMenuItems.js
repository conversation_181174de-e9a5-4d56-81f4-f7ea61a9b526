// src/data/menuItems.js
import {
  FaHome, FaShoppingCart, FaBox, FaUsers, FaCogs, FaChartBar, FaClipboardList, FaTags, FaList, FaUserTie, FaBlog, FaBullhorn, FaHeadset, FaGlobe, FaUserShield, FaTruck, FaGift, FaWpforms, FaTable, FaPager, FaBorderStyle, FaUsersCog, FaPlus, FaCog, FaDotCircle, FaRegDotCircle, FaWarehouse, FaTrademark, FaGifts, FaListOl, FaEnvelope
} from 'react-icons/fa';
import { Dot } from 'lucide-react';

import * as permissions from '../constants/permissions';

const menuItems = [
  {
    path: '/dashboard',
    icon: FaHome,
    label: 'Dashboard',
    permissions: [permissions.BROWSE_ORDERS, permissions.BROWSE_PRODUCTS, permissions.BROWSE_USERS]
  },
  {
    path: '/order/list',
    icon: FaListOl,
    label: 'orders',
    permissions: [permissions.BROWSE_ORDERS, permissions.BROWSE_PRODUCTS, permissions.BROWSE_USERS]
  },
  {
    label: 'Products',
    icon: FaBox,
    permissions: [permissions.BROWSE_PRODUCTS],
    children: [
      // {
      //   path: '/products/all',
      //   label: 'All Products',
      //   icon: FaBox,
      //   permissions: [permissions.BROWSE_PRODUCTS]
      // },
      // product circle
      // {
      //   path: '/products/list',
      //   label: 'Products List',
      //   icon: FaBox,
      //   permissions: [permissions.BROWSE_PRODUCTS]
      // },
      {
        path: '/products/all',
        label: 'All Products',
        icon: FaRegDotCircle,
        permissions: [permissions.BROWSE_PRODUCTS]
      },
      {
        path: '/products/add',
        label: 'Add Product',
        icon: FaRegDotCircle,
        permissions: [permissions.CREATE_PRODUCT]
      },
      {
        path: '/products/categories',
        label: 'Category',
        icon: FaRegDotCircle,
        permissions: [permissions.BROWSE_CATEGORIES]
      },
      {
        path: '/products/class',
        label: 'Class',
        icon: FaRegDotCircle,
        permissions: [permissions.BROWSE_PRODUCTS]
      },
      {
        path: '/products/attribute',
        label: 'attribute',
        icon: FaRegDotCircle,
        permissions: [permissions.BROWSE_PRODUCTS]
      },
      // {
      //   path: '/products/brands',
      //   label: 'Brand',
      //   icon: FaRegDotCircle,
      //   permissions: [permissions.BROWSE_PRODUCTS]
      // },
      // {
      //   path: '/products/reviews',
      //   label: 'Product Reviews',
      //   icon: FaClipboardList,
      //   permissions: [permissions.BROWSE_REVIEWS]
      // }
    ]
  },
  {
    label: 'Vendor Management',
    icon: FaUsersCog,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
    children: [
      {
        path: '/vendor/eoi',
        label: 'vendor',
        icon: FaRegDotCircle,
        permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT]
      },
      {
        path: '/vendor/list',
        label: 'vendor_list',
        icon: FaRegDotCircle,
        permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT]
      },
    ]
  },
  {
    path: '/customers/list',
    icon: FaUsers,
    label: 'Customers',
    permissions: [permissions.BROWSE_USERS]
  },
  {
    path: '/products/brands',
    icon: FaTrademark,
    label: 'Brand',
    permissions: [permissions.BROWSE_PRODUCTS]
  },
  {
    label: 'Support',
    icon: FaHeadset,
    permissions: [permissions.BROWSE_SUPPORT_TICKETS],
    children: [
      {
        path: '/support/ticket',
        label: 'Ticket',
        icon: FaRegDotCircle,
        permissions: [permissions.BROWSE_SUPPORT_TICKETS]
      },
      {
        path: '/support/category',
        label: 'Category',
        icon: FaRegDotCircle,
        permissions: [permissions.BROWSE_SUPPORT_TICKETS]
      },
      {
        path: '/support/topic',
        label: 'Topic',
        icon: FaRegDotCircle,
        permissions: [permissions.BROWSE_SUPPORT_TICKETS]
      },
      // {
      //   path: '/support/reason',
      //   label: 'Reason',
      //   icon: FaRegDotCircle,
      //   permissions: [permissions.BROWSE_SUPPORT_TICKETS]
      // }
    ]
  },

  {
    label: 'Promotion',
    icon: FaGifts,
    permissions: [permissions.STAFF_VIEW],
    children: [
      {
        path: '/popup/list',
        label: 'PopUp',
        icon: FaRegDotCircle,
        permissions: [permissions.STAFF_VIEW]
      },
      {
        path: '/coupon/list',
        icon: FaRegDotCircle,
        label: 'Coupon',
        permissions: [permissions.STAFF_VIEW],
      },
      {
        path: '/OfferDeal/list',
        label: 'Offer',
        icon: FaRegDotCircle,
        permissions: [permissions.STAFF_VIEW]
      },
    ]
  },
  {
    path: '/warehouse/list',
    icon: FaWarehouse,
    label: 'Warehouse',
    permissions: [permissions.STAFF_VIEW],
  },

  // {
  //   label: 'Sales and Order',
  //   icon: FaShoppingCart,
  //   permissions: [permissions.BROWSE_ORDERS],
  //   children: [
  //     {
  //       path: '/orders/pending',
  //       label: 'Pending Orders',
  //       permissions: [permissions.BROWSE_ORDERS]
  //     },
  //     {
  //       path: '/orders/completed',
  //       label: 'Completed Orders',
  //       permissions: [permissions.BROWSE_ORDERS]
  //     },
  //     {
  //       path: '/orders/cancelled',
  //       label: 'Cancelled Orders',
  //       permissions: [permissions.BROWSE_ORDERS]
  //     }
  //   ]
  // },


  // {
  //   label: 'Configurations',
  //   icon: FaCogs,
  //   permissions: [permissions.BROWSE_ORDERS],
  //   children: [
  //     {
  //       path: '/configuration/banners',
  //       label: 'Banners',
  //       permissions: [permissions.BROWSE_ORDERS]
  //     },
  //     {
  //       path: '/configuration/banner/items',
  //       label: 'banneritem',
  //       permissions: [permissions.BROWSE_ORDERS]
  //     },
  //   ]
  // },
  // {
  //   label: 'Partner Vendors',
  //   icon: FaUserTie,
  //   path: '/vendors',
  //   permissions: [permissions.BROWSE_VENDORS]
  // },
  // {
  //   label: 'Reports',
  //   icon: FaChartBar,
  //   permissions: [permissions.VIEW_SALES_REPORTS],
  //   children: [
  //     {
  //       path: '/reports/sales',
  //       label: 'Sales Reports',
  //       permissions: [permissions.VIEW_SALES_REPORTS]
  //     },
  //     {
  //       path: '/reports/inventory',
  //       label: 'Inventory Reports',
  //       permissions: [permissions.VIEW_SALES_REPORTS]
  //     },
  //     {
  //       path: '/reports/customers',
  //       label: 'Customer Reports',
  //       permissions: [permissions.ANALYZE_BUYER_BEHAVIOR]
  //     },
  //     {
  //       path: '/reports/vendors',
  //       label: 'Vendor Reports',
  //       permissions: [permissions.VIEW_SALES_REPORTS]
  //     }
  //   ]
  // },
  {
    label: 'Blog Management',
    icon: FaBlog,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
    children: [
      {
        path: '/blog/posts',
        // label: 'All Posts',
        label: 'Blog',
        icon: FaRegDotCircle,
        permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT]
      },
      {
        path: '/blog/categories',
        label: 'Categories',
        icon: FaRegDotCircle,
        permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT]
      },
      // {
      //   path: '/blog/comments',
      //   label: 'Comments',
      //   permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT]
      // }
    ]
  },
  {
    label: 'Email Templates',
    icon: FaEnvelope,
    permissions: [permissions.BROWSE_EMAIL_TEMPLATES],
    children: [
      {
        path: '/email-templates',
        label: 'All Templates',
        icon: FaRegDotCircle,
        permissions: [permissions.BROWSE_EMAIL_TEMPLATES]
      },
      {
        path: '/email-templates/categories',
        label: 'Categories',
        icon: FaRegDotCircle,
        permissions: [permissions.MANAGE_EMAIL_TEMPLATE_CATEGORIES]
      },
      {
        path: '/email-templates/variables',
        label: 'Variables',
        icon: FaRegDotCircle,
        permissions: [permissions.MANAGE_EMAIL_TEMPLATE_VARIABLES]
      }
    ]
  },

  {
    path: '/pages/list',
    icon: FaPager,
    label: 'Static Pages',
    permissions: [permissions.PAGE_VIEW]
  },
  // {
  //   label: 'Marketing',
  //   icon: FaBullhorn,
  //   permissions: [permissions.MANAGE_DISCOUNT_CODES_AND_CAMPAIGNS],
  //   children: [
  //     {
  //       path: '/marketing/campaigns',
  //       label: 'Campaigns',
  //       permissions: [permissions.MANAGE_DISCOUNT_CODES_AND_CAMPAIGNS]
  //     },
  //     {
  //       path: '/marketing/promotions',
  //       label: 'Promotions',
  //       permissions: [permissions.MANAGE_DISCOUNT_CODES_AND_CAMPAIGNS]
  //     },
  //     {
  //       path: '/marketing/coupons',
  //       label: 'Coupons',
  //       permissions: [permissions.BROWSE_COUPONS]
  //     }
  //   ]
  // },
  // {
  //   label: 'Support',
  //   icon: FaHeadset,
  //   path: '/support',
  //   permissions: [permissions.BROWSE_SUPPORT_TICKETS]
  // },
  // {
  //   label: 'Homepage Setup',
  //   icon: FaGlobe,
  //   path: '/homepage-setup',
  //   permissions: [permissions.HOMEPAGE_EDIT]
  // },
  // {
  //   label: 'Users',
  //   icon: FaUserShield,
  //   path: '/staff/users',
  //   permissions: [permissions.STAFF_VIEW]
  // },
  {
    label: 'User Management',
    icon: FaUserShield,
    permissions: [permissions.STAFF_VIEW],
    children: [
      {
        path: '/staff/roles',
        label: 'Roles',
        icon: FaRegDotCircle,
        permissions: [permissions.STAFF_VIEW]
      },
      {
        path: '/staff/permissions',
        label: 'Permissions',
        icon: FaRegDotCircle,
        permissions: [permissions.STAFF_VIEW]
      },
      {
        path: '/staff/users',
        label: 'Users',
        icon: FaRegDotCircle,
        permissions: [permissions.STAFF_VIEW]
      }
    ]
  },
  // {
  //   label: 'Delivery',
  //   icon: FaTruck,
  //   path: '/delivery',
  //   permissions: [permissions.BROWSE_SHIPMENTS]
  // },
  // {
  //   label: 'Loyalty and Reward Points',
  //   icon: FaGift,
  //   path: '/loyalty',
  //   permissions: [] // No direct equivalent in permissions.js
  // },
  // {
  //   label: 'Demo Form',
  //   icon: FaWpforms,
  //   path: '/demo/form',
  //   permissions: [] // No direct equivalent in permissions.js
  // },
  // {
  //   label: 'Demo List',
  //   icon: FaTable,
  //   path: '/demo/list',
  //   permissions: [] // No direct equivalent in permissions.js
  // }

  {
    label: 'Setting',
    icon: FaCog,
    permissions: [permissions.STAFF_VIEW],
    children: [
      {
        path: '/setting/banner',
        label: 'Banner',
        icon: FaRegDotCircle,
        permissions: [permissions.STAFF_VIEW]
      },
      {
        path: '/setting/menu/dropdown',
        label: 'DropDown Menu',
        icon: FaRegDotCircle,
        permissions: [permissions.STAFF_VIEW]
      },
      {
        path: '/fulfillment/list',
        label: 'Fulfillment',
        icon: FaRegDotCircle,
        permissions: [permissions.STAFF_VIEW]
      },
    ]
  },

];

export default menuItems; 