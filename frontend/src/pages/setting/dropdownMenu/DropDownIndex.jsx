import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import DropDownFilter from "./dropDownFilters";
import { FaEdit, FaTrash, FaPlus, FaBars, FaEye } from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import PreviewSection from "./dropDownPreview";

const DropDownIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedDropDown, setSelectedDropDown] = useState(null);
  const [previewId, setPreviewId] = useState(null);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: "",
  });

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: dropDownData,
    isLoading,
    isError: dropDownError,
    refetch,
  } = fetchData("admin/dropdowns", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    status: filterOptions.status || "",
  });

  const dropDownList = dropDownData?.data?.data || [];
  const paginationInfo = {
    currentPage: dropDownData?.data?.current_page || 1,
    perPage: dropDownData?.data?.per_page || itemsPerPage,
    totalItems: dropDownData?.data?.total_items || 0,
    totalPages: dropDownData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditDropDown = (dropDown) => {
    navigate(`/setting/menu/dropdown/edit/${dropDown.id}`);
  };

  const handlePreviewClick = (dropDown) => {
    setPreviewId(dropDown.id);
  };

  const handleDeleteClick = (dropDown) => {
    setSelectedDropDown(dropDown);
  };

  const handleDeleteDropDown = async () => {
    if (!selectedDropDown) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/dropdowns/${selectedDropDown.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setSelectedDropDown(null);
          refetch();
          toast.success(t("commonToast.dropDownToast.dropDownDelete"));
        },
        onError: (error) => {
          console.error(
            "= deletion failed:",
            error?.response?.data || error.message
          );
          toast.error(
            "DropDown deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const canEditDropDown = (dropDown) => true;

  const canDeleteDropDown = (dropDown) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.name_en"),
      accessor: "name_en",
    },
    {
      header: t("commonTableLabel.name_ar"),
      accessor: "name_ar",
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("dropdownMenu.previewClassContent")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditDropDown(row)}
            disabled={!canEditDropDown(row)}
            title={
              !canEditDropDown(row)
                ? "You don't have permission to edit this DropDown Menu"
                : t("dropdownMenu.editAction")
            }
          >
            <FaEdit className="icon-edit" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteDropDown(row)}
            title={
              !canDeleteDropDown(row)
                ? "You can't delete this DropDown Menu"
                : t("dropdownMenu.deleteAction")
            }
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("dropdownMenu.title")}
          icon={<FaBars className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => navigate("/setting/menu/dropdown/add")}
            >
              <FaPlus className="mr-2" /> {t("dropdownMenu.add")}
            </Button>
          }
        >
          <DropDownFilter onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={dropDownList}
            emptyMessage={t("dropdownMenu.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      {/* PreView Page  */}
      <Modal
        isOpen={previewId}
        onClose={() => setPreviewId(null)}
        title={t("dropdownMenu.previewClassContent")}
        size="xl"
        showCloseButton={true}
      >
        <PreviewSection
          dropdownMenuId={previewId}
          onClose={() => setPreviewId(null)}
        />
      </Modal>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationModal
        isOpen={!!selectedDropDown}
        onClose={() => setSelectedDropDown(null)}
        onDelete={handleDeleteDropDown}
        loading={deleteLoading}
        itemName={t("dropdownMenu.dropdown")}
        itemValue={selectedDropDown?.name_en}
      />
    </div>
  );
};

export default DropDownIndex;
