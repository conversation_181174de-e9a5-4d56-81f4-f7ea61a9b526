import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useApi, fetchData } from "@/hooks/useApi";
import { useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import Card from "@/components/ui/Card";
import { FaEye } from "react-icons/fa";
import Modal from "@/components/ui/Modal";
import { useState } from "react";
import Button from "@/components/ui/Button";
import moment from "moment";
import BackArrowIcon from "@/components/common/BackArrowIcon";

const LinkValue = ({ value }) => {
  const isLink =
    value &&
    (String(value).startsWith("http") || String(value).startsWith("www"));
  if (isLink) {
    return (
      <a
        href={value.startsWith("http") ? value : `https://${value}`}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-600 hover:underline"
      >
        {value}
      </a>
    );
  }
  return <span className="text-gray-800 font-semibold">{value || "N/A"}</span>;
};

const Label = ({ label, value }) => (
  <div className="flex items-center gap-2 text-base">
    <strong className="w-60 text-gray-700 font-medium">{label}</strong>
    <strong className="w-4 text-gray-600">:</strong>
    <LinkValue value={value} />
  </div>
);

const FileLink = ({ label, value }) => (
  <div className="flex items-center gap-2 text-base">
    <strong className="w-60 text-gray-700 font-medium">{label}</strong>
    <strong className="w-4 text-gray-600">:</strong>
    {value ? (
      <a
        href={value}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-600 hover:underline"
      >
        View File
      </a>
    ) : (
      <span className="text-gray-800 font-semibold">N/A</span>
    )}
  </div>
);

const DateLabel = ({ label, value }) => (
  <Label label={label} value={value ? moment(value).format("LL") : "N/A"} />
);

const BusinessType = ({ label, value }) => {
  let businessData = [];
  if (typeof value === "string") {
    try {
      businessData = JSON.parse(value);
    } catch {
      businessData = [];
    }
  }

  return (
    <div className="flex items-start gap-2 text-base">
      <strong className="w-60 text-gray-700 font-medium">{label}</strong>
      <strong className="w-4 text-gray-600">:</strong>
      <div className="flex flex-col gap-2">
        {Array.isArray(businessData) && businessData.length > 0 ? (
          businessData.map((item, index) => (
            <div key={index} className="flex items-center gap-2">
              <span className="font-semibold">{item.type}</span>
              <span>-</span>
              <span>{item.details}</span>
            </div>
          ))
        ) : (
          <span className="text-gray-800 font-semibold">{value || "N/A"}</span>
        )}
      </div>
    </div>
  );
};

const VendorPreview = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { putMutation } = useApi();
  const [showModal, setShowModal] = useState(false);

  const {
    data: previewData,
    isLoading,
    isError,
  } = fetchData(`admin/vendors/${id}`);

  const data = previewData?.data;

  const handleOpenModal = () => setShowModal(true);
  const handleCloseModal = () => setShowModal(false);

  const handleSubmitSuccess = () => {
    putMutation.mutate(
      { endpoint: `admin/vendors/${id}/approve` },
      {
        onSuccess: () => {
          toast({
            title: "Success",
            description: "Vendor approved successfully.",
          });
          queryClient.invalidateQueries({ queryKey: [`admin/vendors/${id}`] });
          handleCloseModal();
          navigate("/vendor/list");
        },
        onError: (error) => {
          toast({
            title: "Error",
            description: error.message || "An unexpected error occurred.",
            variant: "destructive",
          });
        },
      }
    );
  };

  if (isLoading) return <LoadingSpinner size={64} overlay />;
  if (isError)
    return <div className="text-red-600 p-4">{t("error.unexpected")}</div>;

  return (
    <div className="page-container">
      <div className="bg-white rounded-lg shadow-xl border border-gray-300">
        <div className="title-add-edit-card">
          <div className="title-add-edit-div">
            <button onClick={() => navigate("/vendor/list")}>
              <BackArrowIcon />
            </button>
            <h3 className="title-add-edit">{t("vendorSection.title")}</h3>
          </div>
        </div>
        <div className="space-y-6 page-container">
          <Card>
            <div className="flex justify-between items-center">
              <div className="text-base font-semibold flex items-center gap-2">
                {t("vendor.vendorId")} :
                <span className="bg-red-100 text-red-700 px-2 py-1 rounded-md text-sm font-semibold cursor-not-allowed">
                  {data?.code}
                </span>
              </div>
              {data?.approval_status !== "Approved" && (
                <div
                  onClick={handleOpenModal}
                  className="flex gap-2 bg-primary-100 px-3 py-2 rounded-md text-primary-500 font-semibold items-center cursor-pointer hover:bg-green-100 hover:text-green-500 shadow-lg"
                >
                  {t("commonButton.approval")}
                  <span>
                    <FaEye />
                  </span>
                </div>
              )}
            </div>
          </Card>
          <Card titleLabel={t("vendor.vendorInfo")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-base">
              <Label
                label={t("commonTableLabel.name_en")}
                value={data?.name_tl_en}
              />
              <Label
                label={t("commonTableLabel.name_ar")}
                value={data?.name_tl_ar}
              />
              <Label
                label={t("commonTableLabel.vendor_name_en")}
                value={data?.vendor_display_name_en}
              />
              <Label
                label={t("commonTableLabel.vendor_name_ar")}
                value={data?.vendor_display_name_ar}
              />
              <Label
                label={t("commonTableLabel.website")}
                value={data?.website}
              />
              <Label
                label={t("commonTableLabel.approval_status")}
                value={data?.approval_status}
              />
            </div>
          </Card>

          <Card titleLabel={t("vendor.vendorSocialInfo")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-sm">
              <Label
                label={t("commonTableLabel.facebook")}
                value={data?.facebook_page}
              />
              <Label
                label={t("commonTableLabel.instagram")}
                value={data?.instagram_page}
              />
              <Label
                label={t("commonTableLabel.otherSocialMedia")}
                value={data?.other_social_media}
              />
            </div>
          </Card>

          <Card titleLabel={t("vendor.businessDetails")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-base">
              <BusinessType
                label={t("vendor.businessType")}
                value={data?.business_type}
              />
              <Label
                label={t("vendor.manufacturerBrands")}
                value={data?.manufacturer_brands}
              />
              <Label
                label={t("vendor.categoriesToSell")}
                value={data?.categories_to_sell}
              />
            </div>
          </Card>

          <Card titleLabel={t("vendor.tradeLicenseDetails")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-base">
              <Label
                label={t("vendor.tl_license_issuing_authority")}
                value={data?.tl_license_issuing_authority}
              />
              <DateLabel
                label={t("vendor.tl_license_first_issue_date")}
                value={data?.tl_license_first_issue_date}
              />
              <DateLabel
                label={t("vendor.tl_license_renewal_date")}
                value={data?.tl_license_renewal_date}
              />
              <DateLabel
                label={t("vendor.tl_license_valid_till")}
                value={data?.tl_license_valid_till}
              />
              <Label
                label={t("vendor.tl_entity_type")}
                value={data?.tl_entity_type}
              />
              <Label
                label={t("vendor.tl_no_of_partners")}
                value={data?.tl_no_of_partners}
              />
              <FileLink
                label={t("vendor.tl_doc_copy_of_trade_license")}
                value={data?.tl_doc_copy_of_trade_license}
              />
            </div>
          </Card>

          <Card titleLabel={t("vendor.vatDetails")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-base">
              <Label
                label={t("vendor.tax_registration_number")}
                value={data?.tax_registration_number}
              />
              <DateLabel
                label={t("vendor.trn_issue_date")}
                value={data?.trn_issue_date}
              />
              <Label
                label={t("vendor.trn_name_in_english")}
                value={data?.trn_name_in_english}
              />
              <Label
                label={t("vendor.trn_name_in_arabic")}
                value={data?.trn_name_in_arabic}
              />
              <FileLink
                label={t("vendor.vat_doc_copy_of_registration_certificate")}
                value={data?.vat_doc_copy_of_registration_certificate}
              />
            </div>
          </Card>

          <Card titleLabel={t("vendor.directorDetails")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-base">
              <Label
                label={t("vendor.director_name")}
                value={data?.director_name}
              />
              <Label
                label={t("vendor.director_designation")}
                value={data?.director_designation}
              />
              <Label
                label={t("vendor.director_full_name_passport")}
                value={data?.director_full_name_passport}
              />
              <Label
                label={t("vendor.director_passport_number")}
                value={data?.director_passport_number}
              />
              <Label
                label={t("vendor.director_emirates_id_number")}
                value={data?.director_emirates_id_number}
              />
              <DateLabel
                label={t("vendor.director_emirates_id_issue_date")}
                value={data?.director_emirates_id_issue_date}
              />
              <DateLabel
                label={t("vendor.director_emirates_id_expiry_date")}
                value={data?.director_emirates_id_expiry_date}
              />
              <Label
                label={t("vendor.director_email")}
                value={data?.director_email}
              />
              <Label
                label={t("vendor.director_mobile")}
                value={data?.director_mobile}
              />
              <Label
                label={t("vendor.director_preferred_language")}
                value={data?.director_preferred_language}
              />
              <FileLink
                label={t("vendor.director_passport_copy")}
                value={data?.director_passport_copy}
              />
              <FileLink
                label={t("vendor.director_emirates_id_copy")}
                value={data?.director_emirates_id_copy}
              />
            </div>
          </Card>

          <Card titleLabel={t("vendor.vendorSpocInfo")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-sm">
              <Label
                label={t("commonTableLabel.publicName")}
                value={data?.spoc_name}
              />
              <Label
                label={t("commonTableLabel.designation")}
                value={data?.spoc_designation}
              />
              <Label
                label={t("commonTableLabel.email")}
                value={data?.spoc_email}
              />
              <Label
                label={t("commonTableLabel.mobile")}
                value={data?.spoc_mobile}
              />
              <Label
                label={t("vendor.spoc_passport_number")}
                value={data?.spoc_passport_number}
              />
              <Label
                label={t("vendor.spoc_emirates_id_number")}
                value={data?.spoc_emirates_id_number}
              />
              <DateLabel
                label={t("vendor.spoc_emirates_id_issue_date")}
                value={data?.spoc_emirates_id_issue_date}
              />
              <DateLabel
                label={t("vendor.spoc_emirates_id_expiry_date")}
                value={data?.spoc_emirates_id_expiry_date}
              />
              <Label
                label={t("vendor.spoc_letter_of_authorization")}
                value={data?.spoc_letter_of_authorization}
              />
              <FileLink
                label={t("vendor.spoc_passport_copy")}
                value={data?.spoc_passport_copy}
              />
              <FileLink
                label={t("vendor.spoc_emirates_id_copy")}
                value={data?.spoc_emirates_id_copy}
              />
              <FileLink
                label={t("vendor.spoc_loa_copy")}
                value={data?.spoc_loa_copy}
              />
            </div>
          </Card>

          <Card titleLabel={t("vendor.selfDeclaration")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-base">
              <Label
                label={t("vendor.signing_self_declaration")}
                value={data?.signing_self_declaration}
              />
            </div>
          </Card>

          <Card titleLabel={t("vendor.contact")}>
            <div className="space-y-4">
              {data?.vendor_contact?.map((contact, index) => (
                <div key={index} className="p-4 border rounded-md">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-base">
                    <Label label={t("vendor.type")} value={contact.type} />
                    <Label
                      label={t("vendor.full_name")}
                      value={contact.full_name}
                    />
                    <Label
                      label={t("vendor.designation")}
                      value={contact.designation}
                    />
                    <Label label={t("vendor.email")} value={contact.email} />
                    <Label
                      label={t("vendor.mobile_number")}
                      value={contact.mobile_number}
                    />
                  </div>
                </div>
              ))}
            </div>
          </Card>

          <Card titleLabel={t("vendor.location")}>
            <div className="space-y-4">
              {data?.vendor_address?.map((address, index) => (
                <div key={index} className="p-4 border rounded-md">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-base">
                    <Label label={t("vendor.type")} value={address.type} />
                    <Label
                      label={t("vendor.address")}
                      value={address.address}
                    />
                  </div>
                </div>
              ))}
            </div>
          </Card>

          <Card titleLabel={t("vendor.bankDetails")}>
            <div className="space-y-4">
              {data?.vendor_bank?.map((bank, index) => (
                <div key={index} className="p-4 border rounded-md">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-base">
                    <Label
                      label={t("vendor.bank_name")}
                      value={bank.bank_name}
                    />
                    <Label
                      label={t("vendor.branch_name")}
                      value={bank.branch_name}
                    />
                    <Label
                      label={t("vendor.account_holder_name")}
                      value={bank.account_holder_name}
                    />
                    <Label
                      label={t("vendor.iban_number")}
                      value={bank.iban_number}
                    />
                    <Label
                      label={t("vendor.original_cheque_number")}
                      value={bank.original_cheque_number}
                    />
                    <FileLink
                      label={t("vendor.bank_certificate_copy")}
                      value={bank.bank_certificate_copy}
                    />
                  </div>
                </div>
              ))}
            </div>
          </Card>

          <Modal
            isOpen={showModal}
            onClose={handleCloseModal}
            title={t("vendor.vendorConfirmApproval")}
            size="md"
          >
            <div>
              <p>Are you sure you want to approve this vendor?</p>
              <div className="flex justify-end gap-4 mt-4">
                <Button variant="outline" onClick={handleCloseModal}>
                  {t("commonButton.cancel")}
                </Button>
                <Button onClick={handleSubmitSuccess}>
                  {t("commonButton.approval")}
                </Button>
              </div>
            </div>
          </Modal>
        </div>
      </div>
    </div>
  );
};

export default VendorPreview;
