import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { <PERSON>aEye, FaHandshake } from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { useState } from "react";
import VendorFilterPage from "./vendorFilter";
import StatusBadge from "@/components/common/StatusBadge";
import ApprovalStatusBadge from "@/components/common/ApprovalStatusBadge";

const vendorIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    is_active: "",
    approval_status: "",
  });

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: vendorData,
    isLoading,
    isError: vendorError,
    refetch,
  } = fetchData("admin/vendors", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    is_active: filterOptions.is_active ?? "",
    approval_status: filterOptions.approval_status || "",
  });

  const vendorList = vendorData?.data?.data || [];
  const paginationInfo = {
    currentPage: vendorData?.data?.current_page || 1,
    perPage: vendorData?.data?.per_page || itemsPerPage,
    totalItems: vendorData?.data?.total_items || 0,
    totalPages: vendorData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handlePreviewClick = (row) => {
    navigate(`/vendor/preview/${row.id}`);
  };

  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.name_en"),
      accessor: "name_tl_en",
    },
    // {
    //   header: t("commonTableLabel.name_ar"),
    //   accessor: "name_tl_ar",
    // },
    {
      header: t("commonTableLabel.code"),
      accessor: "code",
    },
    {
      header: t("commonTableLabel.spocName"),
      accessor: "spoc_name",
    },
    {
      header: t("commonTableLabel.spocEmail"),
      accessor: "spoc_email",
    },
    {
      header: t("commonTableLabel.spocPhone"),
      accessor: "spoc_mobile",
    },
    {
      header: t("commonTableLabel.appliedOn"),
      accessor: "created_at",
      render: (row) => {
        return new Date(row.created_at).toLocaleDateString();
      },
    },
    {
      header: t("commonField.approvalStatus"),
      render: (row) => (
        <ApprovalStatusBadge data={row} fieldName="approval_status" />
      ),
    },
    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row} fieldName="is_active" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("commonTableLabel.previewAction")}
          >
            <FaEye className="icon-view" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("vendorSection.title")}
          icon={<FaHandshake className="title-icon" />}
        >
          <VendorFilterPage onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={vendorList}
            emptyMessage={t("vendorSection.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>
    </div>
  );
};

export default vendorIndex;
