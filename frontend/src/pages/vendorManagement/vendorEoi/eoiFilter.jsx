import { useState } from "react";
import SearchInput from "@/components/ui/SearchInput";
import FilterDropdown from "@/components/ui/FilterDropdown";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import {
  eoiVendorStatusOptions,
  approvalVendorStatusOptions,
} from "@/constants/filterOption";

const eoiFilter = ({ onChange }) => {
  const { t } = useTranslation();
  const [filters, setFilters] = useState({
    search: "",
    approval_status: "",
    status: "",
  });

  const triggerChange = (updated) => {
    const payload = {};
    if (updated.search.trim()) payload.search = updated.search.trim();
    if (updated.approval_status)
      payload.approval_status = updated.approval_status;
    if (updated.status) payload.status = updated.status;
    onChange?.(payload);
  };

  const handleSearchChange = (val) => {
    const updated = { ...filters, search: val };
    setFilters(updated);
    triggerChange(updated);
  };

  const handleFilterChange = (key, values) => {
    const updated = { ...filters, [key]: values };
    setFilters(updated);
    triggerChange(updated);
  };

  const handleReset = () => {
    const reset = { search: "", approval_status: "", status: "" };
    setFilters(reset);
    onChange?.({});
  };

  return (
    <div className="mb-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
        <div className="flex-1 max-w-md">
          <SearchInput
            value={filters.search}
            onChange={handleSearchChange}
            placeholder={t("vendor.searchPlaceholder")}
          />
        </div>

        <div className="flex flex-wrap items-center gap-2">
          <FilterDropdown
            label={t("commonField.approvalState")}
            className="w-40"
            options={approvalVendorStatusOptions}
            selectedValues={filters.approval_status}
            onChange={(value) => handleFilterChange("approval_status", value)}
          />
          <FilterDropdown
            label={t("commonField.status")}
            className="w-40"
            options={eoiVendorStatusOptions}
            selectedValues={filters.status}
            onChange={(value) => handleFilterChange("status", value)}
          />
          <Button variant="outline" onClick={handleReset} className="ml-2">
            {t("commonButton.reset")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default eoiFilter;
