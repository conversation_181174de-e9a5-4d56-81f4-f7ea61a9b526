import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { fetchData } from "@/hooks/useApi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import Card from "@/components/ui/Card";
import { FaEye } from "react-icons/fa";
import Modal from "@/components/ui/Modal";
import { useState } from "react";
import EOIApprovedForm from "./eoiApprovedForm";
import BackArrowIcon from "@/components/common/BackArrowIcon";

const LinkValue = ({ value }) => {
  const isLink =
    value &&
    (String(value).startsWith("http") || String(value).startsWith("www"));
  if (isLink) {
    return (
      <a
        href={value.startsWith("http") ? value : `https://${value}`}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-600 hover:underline"
      >
        {value}
      </a>
    );
  }
  return <span className="text-gray-800 font-semibold">{value || "N/A"}</span>;
};

const Label = ({ label, value }) => (
  <div className="flex items-center gap-2 text-base">
    <strong className="w-60 text-gray-700 font-medium">{label}</strong>
    <strong className="w-4 text-gray-600">:</strong>
    <LinkValue value={value} />
  </div>
);

const EoiPreview = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [showModal, setShowModal] = useState(false);

  const {
    data: previewData,
    isLoading,
    isError,
  } = fetchData(`admin/vendor-eoi/${id}`, {}, [id]);

  const data = previewData?.data;

  const handleOpenModal = () => setShowModal(true);
  const handleCloseModal = () => setShowModal(false);

  const handleSubmitSuccess = () => {
    handleCloseModal();
  };

  if (isLoading) return <LoadingSpinner size={64} overlay />;
  if (isError)
    return <div className="text-red-600 p-4">{t("error.unexpected")}</div>;

  const getInventoryManagementLabel = (value) => {
    switch (value) {
      case "store_inventory":
        return "Use our inventory";
      case "manage_orders":
        return "Use their inventory";
      case "both":
        return "Both";
      default:
        return "N/A";
    }
  };

  return (
    <div className="page-container">
      <div className="bg-white rounded-lg shadow-xl border border-gray-300">
        <div className="title-add-edit-card">
          <div className="title-add-edit-div">
            <button onClick={() => navigate("/vendor/eoi")}>
              <BackArrowIcon />
            </button>
            <h3 className="title-add-edit">{t("vendor.title")}</h3>
          </div>
        </div>
        <div className="space-y-6 page-container">
          <Card>
            <div className="flex justify-between items-center">
              <div className="text-base font-semibold flex items-center gap-2">
                {t("vendor.vendorId")} :
                <span className="bg-red-100 text-red-700 px-2 py-1 rounded-md text-sm font-semibold cursor-not-allowed">
                  {data?.veoi_id}
                </span>
              </div>
              {data?.approval_status !== "Approved" && (
                <div
                  onClick={handleOpenModal}
                  className="flex gap-2 bg-primary-100 px-3 py-2 rounded-md text-primary-500 font-semibold items-center cursor-pointer hover:bg-green-100 hover:text-green-500 shadow-lg"
                >
                  {t("commonButton.approval")}
                  <span>
                    <FaEye />
                  </span>
                </div>
              )}
            </div>
          </Card>
          <Card titleLabel={t("vendor.vendorInfo")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-base">
              <Label
                label={t("commonTableLabel.name_en")}
                value={data?.name_tl_en}
              />
              <Label
                label={t("commonTableLabel.name_ar")}
                value={data?.name_tl_ar}
              />
              <Label
                label={t("commonTableLabel.vendor_name_en")}
                value={data?.vendor_display_name_en}
              />
              <Label
                label={t("commonTableLabel.vendor_name_ar")}
                value={data?.vendor_display_name_ar}
              />
              <Label
                label={t("commonTableLabel.website")}
                value={data?.website}
              />
              <Label
                label={t("commonTableLabel.approval_status")}
                value={data?.approval_status}
              />
              <Label
                label={t("commonTableLabel.status")}
                value={
                  data?.is_active
                    ? t("commonOptions.status.active")
                    : t("commonOptions.status.inactive")
                }
              />
            </div>
          </Card>

          <Card titleLabel={t("vendor.businessDetails")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-base">
              {data?.business_data?.map((business, index) => (
                <Label
                  key={index}
                  label={business.type}
                  value={business.details || "N/A"}
                />
              ))}
            </div>
          </Card>

          <Card titleLabel={t("vendor.vendorAdditionalInfo")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-base">
              <Label
                label={t("commonTableLabel.brandSell")}
                value={data?.brands_to_sell}
              />
              <Label
                label={t("commonTableLabel.categoriesToSell")}
                value={data?.categories_to_sell
                  ?.map((cat) => cat.name)
                  .join(", ")}
              />
            </div>
          </Card>

          <Card titleLabel={t("vendor.vendorSocialInfo")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-sm">
              <Label
                label={t("commonTableLabel.facebook")}
                value={data?.facebook_page}
              />
              <Label
                label={t("commonTableLabel.instagram")}
                value={data?.instagram_page}
              />
              <Label
                label={t("commonTableLabel.otherSocialMedia")}
                value={data?.other_social_media}
              />
            </div>
          </Card>

          <Card titleLabel={t("vendor.vendorSpocInfo")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-sm">
              <Label
                label={t("commonTableLabel.publicName")}
                value={data?.spoc_name}
              />
              <Label
                label={t("commonTableLabel.designation")}
                value={data?.spoc_designation}
              />
              <Label
                label={t("commonTableLabel.email")}
                value={data?.spoc_email}
              />
              <Label
                label={t("commonTableLabel.mobile")}
                value={data?.spoc_mobile}
              />
            </div>
          </Card>
          <Card titleLabel={t("vendor.vendorSkuInfo")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-sm">
              <Label
                label={t("commonTableLabel.onAmazon")}
                value={data?.sku_on_amazon}
              />
              <Label
                label={t("commonTableLabel.onOtherMarketplaces")}
                value={data?.sku_on_other_marketplaces}
              />
              <Label
                label={t("commonTableLabel.onNoon")}
                value={data?.sku_on_noon}
              />
              <Label
                label={t("commonTableLabel.onOwnWebsite")}
                value={data?.sku_on_own_website}
              />
            </div>
          </Card>
          <Card titleLabel={t("vendor.vendorTLInfo")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-sm">
              <Label
                label={t("commonTableLabel.tlType")}
                value={data?.tl_entity_type}
              />
              <Label
                label={t("commonTableLabel.issuingAuthority")}
                value={data?.tl_license_issuing_authority}
              />
              <Label
                label={t("commonTableLabel.issuingDate")}
                value={data?.tl_license_first_issue_date}
              />
              <Label
                label={t("commonTableLabel.expiryDate")}
                value={data?.tl_license_valid_till}
              />
            </div>
          </Card>
          <Card titleLabel={t("vendor.additionalDetails")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 text-base">
              <Label
                label={t("commonTableLabel.inventoryManagement")}
                value={getInventoryManagementLabel(data?.inventory_management)}
              />
              <Label
                label={t("commonTableLabel.orderCollectionLocation")}
                value={data?.order_collection_location?.join(", ")}
              />
              <Label
                label={t("commonTableLabel.orderCollectionLocationDetails")}
                value={data?.order_collection_location_details}
              />
            </div>
          </Card>

          {/* Approved modal  */}
          <Modal
            isOpen={showModal}
            onClose={handleCloseModal}
            title={t("vendor.vendorConfirmApproval")}
            size="md"
          >
            <EOIApprovedForm
              eoiId={id}
              vendorData={data}
              onSubmit={handleSubmitSuccess}
              onCancel={handleCloseModal}
            />
          </Modal>
        </div>
      </div>
    </div>
  );
};

export default EoiPreview;
