import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { FaEdit, <PERSON>a<PERSON>ye, FaHandshake } from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { useState } from "react";
import VendorEOIFilter from "./eoiFilter";
import WorkflowStatusBadge from "@/components/common/WorkflowStatusBadge";
import ApprovalStatusBadge from "@/components/common/ApprovalStatusBadge";

const eoiIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [filterOptions, setFilterOptions] = useState({
    search: "",
  });

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: eoiData,
    isLoading,
    isError: eoiError,
    refetch,
  } = fetchData("admin/vendor-eoi", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
  });

  const eoiList = eoiData?.data?.data || [];
  const paginationInfo = {
    currentPage: eoiData?.data?.current_page || 1,
    perPage: eoiData?.data?.per_page || itemsPerPage,
    totalItems: eoiData?.data?.total_items || 0,
    totalPages: eoiData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handlePreviewClick = (row) => {
    navigate(`/vendor/eoi/preview/${row.id}`);
  };

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.name_en"),
      accessor: "name_tl_en",
    },
    // {
    //   header: t("commonTableLabel.name_ar"),
    //   accessor: "name_tl_ar",
    // },
    {
      // header: t("EOI ID"),
      header: t("commonTableLabel.eoiId"),
      accessor: "veoi_id",
    },
    {
      header: t("commonTableLabel.spocName"),
      accessor: "spoc_name",
    },
    // {
    //   header: t("commonTableLabel.spocEmail"),
    //   accessor: "spoc_email",
    // },
    {
      header: t("commonTableLabel.spocPhone"),
      accessor: "spoc_mobile",
    },
    // {
    //   header: t("brand.brand"),
    //   accessor: "brands_to_sell",
    // },
    {
      header: t("commonTableLabel.appliedOn"),
      accessor: "created_at",
      render: (row) => {
        return new Date(row.created_at).toLocaleDateString();
      },
    },
    {
      header: t("commonField.approvalStatus"),
      render: (row) => (
        <ApprovalStatusBadge data={row} fieldName="approval_status" />
      ),
    },
    {
      header: t("commonTableLabel.status"),
      accessor: "status",
      render: (row) => <WorkflowStatusBadge data={row} fieldName="status" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("commonTableLabel.previewAction")}
          >
            <FaEye className="icon-view" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("vendor.title")}
          icon={<FaHandshake className="title-icon" />}
        >
          <VendorEOIFilter onChange={handleFilterChange} />
          <Table
            columns={columns}
            data={eoiList}
            emptyMessage={t("vendor.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>
    </div>
  );
};

export default eoiIndex;
