import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import ProductFilters from "./ProductFilters";
import PreviewModal from "./PreviewModal";
import {
  FaEdit,
  FaTrash,
  FaClipboardList,
  FaEye,
  FaPlus,
  FaBox,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";

// Mobile card component for small screens (< 640px)
const ProductCard = ({ product, onPreview, onEdit, onDelete }) => (
  <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4 space-y-3 shadow-sm">
    <div className="flex items-start space-x-3">
      <div className="w-10 h-10 xs:w-12 xs:h-12 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
        {product.main_image_url ? (
          <img
            src={product.main_image_url}
            alt={product.title_en}
            className="w-full h-full object-cover"
            onError={(e) => {
              e.target.style.display = "none";
              e.target.nextSibling.style.display = "flex";
            }}
          />
        ) : null}
        <div
          className="w-full h-full flex items-center justify-center"
          style={{ display: product.main_image_url ? "none" : "flex" }}
        >
          <FaBox className="text-gray-400 text-sm" />
        </div>
      </div>
      <div className="flex-1 min-w-0">
        <h3 className="font-medium text-gray-900 truncate text-sm">
          {product.title_en || "Untitled Product"}
        </h3>
        {product.title_ar && (
          <p className="text-xs text-gray-500 truncate mt-1" dir="rtl">
            {product.title_ar}
          </p>
        )}
        {(product.system_sku || product.sku) && (
          <p className="text-xs text-gray-400 font-mono truncate mt-1">
            {product.system_sku || product.sku}
          </p>
        )}
      </div>
    </div>

    <div className="grid grid-cols-2 gap-3 text-sm">
      <div className="min-w-0">
        <span className="text-gray-500 text-xs">Price:</span>
        <div className="font-medium truncate">AED {product.regular_price}</div>
        {product.offer_price && (
          <div className="text-xs text-green-600 truncate">
            Offer: AED {product.offer_price}
          </div>
        )}
      </div>
      <div className="min-w-0">
        <span className="text-gray-500 text-xs">Status:</span>
        <div className="mt-1">
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap ${
              product.status === "active"
                ? "bg-green-100 text-green-800"
                : product.status === "draft"
                ? "bg-yellow-100 text-yellow-800"
                : "bg-red-100 text-red-800"
            }`}
          >
            {product.status}
          </span>
        </div>
      </div>
    </div>

    <div className="flex justify-between items-center pt-2 border-t border-gray-100">
      <div className="text-xs text-gray-500 min-w-0 flex-1 mr-2">
        {product.category?.name_en && product.sub_category?.name_en && (
          <span className="truncate block">
            {product.category.name_en} → {product.sub_category.name_en}
          </span>
        )}
        {product.product_class?.name_en && (
          <span className="truncate block">{product.product_class.name_en}</span>
        )}
        {product.brand?.name_en && (
          <span className="truncate block">Brand: {product.brand.name_en}</span>
        )}
        {product.vendor?.name_tl_en && (
          <span className="truncate block">Vendor: {product.vendor.name_tl_en}</span>
        )}
      </div>
      <div className="flex space-x-1 flex-shrink-0">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPreview(product)}
          className="text-blue-600 hover:text-blue-800 p-1.5"
          title="Preview"
        >
          <FaEye className="w-3 h-3" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onEdit(product)}
          className="text-green-600 hover:text-green-800 p-1.5"
          title="Edit"
        >
          <FaEdit className="w-3 h-3" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onDelete(product)}
          className="text-red-600 hover:text-red-800 p-1.5"
          title="Delete"
        >
          <FaTrash className="w-3 h-3" />
        </Button>
      </div>
    </div>
  </div>
);

const ProductIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [previewProduct, setPreviewProduct] = useState(null);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    consolidated_status: "",
    status: "",
    is_active: "",
    is_approved: "",
    vendor_id: "",
    category_id: "",
    brand_id: "",
    stock_status: "",
    min_stock: "",
    max_stock: "",
  });

  const [debouncedFilters, setDebouncedFilters] = useState(filterOptions);

  // Debounce filter changes (especially for search)
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedFilters(filterOptions);
    }, 300); // 300ms delay

    return () => clearTimeout(timer);
  }, [filterOptions]);

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
  } = usePagination(1, 10);

  // Clean filter options to remove empty values and exclude consolidated_status (it's converted to individual fields)
  const cleanFilterOptions = Object.fromEntries(
    Object.entries(debouncedFilters).filter(
      ([key, value]) =>
        key !== "consolidated_status" &&
        value !== "" &&
        value !== null &&
        value !== undefined
    )
  );

  const {
    data: productsData,
    isLoading,
    refetch,
  } = fetchData("admin/products", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    ...cleanFilterOptions,
  });

  const productList = productsData?.data?.data || [];
  const paginationInfo = {
    currentPage: productsData?.data?.current_page || 1,
    perPage: productsData?.data?.per_page || itemsPerPage,
    totalItems: productsData?.data?.total_items || 0,
    totalPages: productsData?.data?.total_pages || 1,
  };

  const handleFilterChange = useCallback(
    (filters) => {
      setFilterOptions(filters);
      setCurrentPage(1);
    },
    [setCurrentPage]
  );

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditProduct = (product) => {
    navigate(`/products/edit/${product.id}`);
  };

  const handleDeleteClick = (product) => {
    setSelectedProduct(product);
  };

  const handlePreviewClick = (product) => {
    setPreviewProduct(product);
  };

  const handleDeleteConfirm = () => {
    if (!selectedProduct) return;

    setDeleteLoading(true);
    deleteMutation.mutate(
      { endpoint: `admin/products/${selectedProduct.id}` },
      {
        onSuccess: () => {
          toast.success("Product deleted successfully");
          setSelectedProduct(null);
          refetch();
        },
        onError: (error) => {
          console.error(
            "Product deletion failed:",
            error?.response?.data || error.message
          );
          toast.error(
            "Product deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => setDeleteLoading(false),
      }
    );
  };

  const columns = [
    {
      key: "main_image_url",
      header: "Image",
      className: "w-12 sm:w-16", // Optimized responsive width for image column
      render: (product) => (
        <div className="w-8 h-8 sm:w-12 sm:h-12 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
          {product.main_image_url ? (
            <img
              src={product.main_image_url}
              alt={product.title_en}
              className="w-full h-full object-cover"
              onError={(e) => {
                e.target.style.display = "none";
                e.target.nextSibling.style.display = "flex";
              }}
            />
          ) : null}
          <div
            className="w-full h-full flex items-center justify-center"
            style={{ display: product.main_image_url ? "none" : "flex" }}
          >
            <FaBox className="text-gray-400 text-xs" />
          </div>
        </div>
      ),
    },
    {
      key: "title_en",
      header: "Product Name",
      className:
        "min-w-0 w-auto max-w-[180px] sm:max-w-[220px] lg:max-w-[280px]", // Responsive width constraints
      render: (product) => (
        <div className="min-w-0">
          {" "}
          {/* Allow text truncation */}
          <div className="font-medium text-gray-900 truncate">
            {product.title_en || "Untitled Product"}
          </div>
          {product.title_ar && (
            <div className="text-sm text-gray-500 mt-1 truncate" dir="rtl">
              {product.title_ar}
            </div>
          )}
          {/* Display SKU - prioritize system_sku, fallback to sku */}
          {(product.system_sku || product.sku) && (
            <div className="text-xs text-gray-400 mt-1 font-mono truncate">
              {product.system_sku || product.sku}
            </div>
          )}
        </div>
      ),
    },
    {
      key: "classification",
      header: "Classification",
      className:
        "hidden md:table-cell min-w-0 w-auto max-w-[140px] lg:max-w-[160px]", // Show from tablet landscape (768px+)
      render: (product) => (
        <div className="text-sm min-w-0">
          <div className="font-medium text-gray-900 leading-tight">
            {product.category?.name_en && product.sub_category?.name_en ? (
              <span className="truncate block">
                {product.category.name_en} → {product.sub_category.name_en}
              </span>
            ) : product.category?.name_en ? (
              <span className="truncate block">{product.category.name_en}</span>
            ) : (
              <span className="text-gray-400">N/A</span>
            )}
          </div>
          {product.product_class?.name_en && (
            <div className="text-xs text-gray-500 mt-1 truncate">
              {product.product_class.name_en}
            </div>
          )}
        </div>
      ),
    },
    {
      key: "brand",
      header: "Brand",
      className:
        "hidden lg:table-cell min-w-0 w-auto max-w-[140px] xl:max-w-[160px]", // Show from desktop (1024px+) with increased width
      render: (product) => (
        <div className="text-sm min-w-0">
          <div className="font-medium text-gray-900 leading-tight break-words">
            {product.brand?.name_en || product.brand?.name_ar || "N/A"}
          </div>
        </div>
      ),
    },
    {
      key: "vendor",
      header: "Vendor",
      className: "hidden xl:table-cell min-w-0 w-auto max-w-[140px]", // Show from large desktop (1280px+)
      render: (product) => (
        <div className="text-sm min-w-0">
          <div className="font-medium text-gray-900 truncate">
            {product.vendor?.name_tl_en || product.vendor?.name_tl_ar || "N/A"}
          </div>
        </div>
      ),
    },
    {
      key: "price",
      header: "Price",
      className: "min-w-0 w-auto max-w-[90px] sm:max-w-[110px]", // Responsive width for price
      render: (product) => (
        <div className="min-w-0">
          <div className="font-medium text-sm truncate">
            AED {product.regular_price}
          </div>
          {product.offer_price && (
            <div className="text-xs text-green-600 truncate">
              Offer: AED {product.offer_price}
            </div>
          )}
        </div>
      ),
    },
    {
      key: "stock",
      header: "Stock",
      className: "hidden md:table-cell w-auto max-w-[70px]", // Show from tablet landscape (768px+)
      render: (product) => (
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap ${
            product.stock_quantity > 10
              ? "bg-green-100 text-green-800"
              : product.stock_quantity > 0
              ? "bg-yellow-100 text-yellow-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {product.stock_quantity || 0}
        </span>
      ),
    },
    {
      key: "status",
      header: "Status",
      className: "w-auto max-w-[80px] sm:max-w-[90px]", // Responsive width for status
      render: (product) => (
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap ${
            product.status === "active"
              ? "bg-green-100 text-green-800"
              : product.status === "draft"
              ? "bg-yellow-100 text-yellow-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {product.status}
        </span>
      ),
    },
    {
      key: "actions",
      header: "Actions",
      className: "w-auto min-w-[100px] max-w-[120px]", // Responsive width for actions
      render: (product) => (
        <div className="flex space-x-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePreviewClick(product)}
            className="text-blue-600 hover:text-blue-800 p-1.5"
            title="Preview"
          >
            <FaEye className="w-3 h-3" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditProduct(product)}
            className="text-green-600 hover:text-green-800 p-1.5"
            title="Edit"
          >
            <FaEdit className="w-3 h-3" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(product)}
            className="text-red-600 hover:text-red-800 p-1.5"
            title="Delete"
          >
            <FaTrash className="w-3 h-3" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="relative p-4 sm:p-5 w-full">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full"
      >
        <Card
          title="Product Management"
          icon={<FaClipboardList className="text-indigo-600" />}
          action={
            <Button
              variant="primary"
              className="ml-2"
              onClick={() => navigate("/products/add")}
            >
              <FaPlus className="mr-2" />
              <span className="hidden sm:inline">Add Product</span>
              <span className="sm:hidden">Add</span>
            </Button>
          }
          className="w-full"
        >
          <ProductFilters onChange={handleFilterChange} />

          <div className="w-full">
            {/* Desktop/Tablet Table View - Show from 640px (sm) and above */}
            <div className="hidden sm:block w-full">
              <div className="overflow-x-auto">
                <Table
                  columns={columns}
                  data={productList}
                  emptyMessage={
                    t("products.emptyMessage") || "No products found"
                  }
                  className="w-full min-w-[600px]"
                />
              </div>
            </div>

            {/* Mobile Card View - Show below 640px */}
            <div className="sm:hidden">
              {productList?.length > 0 ? (
                <div className="space-y-3">
                  {productList.map((product, index) => (
                    <ProductCard
                      key={product.id || index}
                      product={product}
                      onPreview={handlePreviewClick}
                      onEdit={handleEditProduct}
                      onDelete={handleDeleteClick}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-10 text-gray-500">
                  {t("products.emptyMessage") || "No products found"}
                </div>
              )}
            </div>
          </div>

          <PaginationInfo
            currentPage={paginationInfo.currentPage}
            totalPages={paginationInfo.totalPages}
            totalItems={paginationInfo.totalItems}
            itemsPerPage={paginationInfo.perPage}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        </Card>
      </motion.div>

      {/* Delete Confirmation Modal */}
      {selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-4 sm:p-6 max-w-md w-full">
            <h3 className="title-add-edit mb-4">Confirm Delete</h3>
            <p className="text-sm text-gray-500 mb-6">
              Are you sure you want to delete "{selectedProduct.title_en}"? This
              action cannot be undone.
            </p>
            <div className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-4">
              <Button
                variant="outline"
                onClick={() => setSelectedProduct(null)}
                disabled={deleteLoading}
                className="w-full sm:w-auto"
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={handleDeleteConfirm}
                loading={deleteLoading}
                disabled={deleteLoading}
                className="w-full sm:w-auto"
              >
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {previewProduct && (
        <PreviewModal
          product={previewProduct}
          onClose={() => setPreviewProduct(null)}
        />
      )}
    </div>
  );
};

export default ProductIndex;
