import React, { useState, useEffect } from "react";
import {
  useParams,
  useNavigate,
  useSearchParams,
  useLocation,
} from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/product/card.jsx";
import { Button } from "@/components/ui/product/button.jsx";

import {
  ChevronLeft,
  ChevronRight,
  Save,
  Check,
  HelpCircle,
  Package,
  Lock,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast.js";
import { useApi } from "@/hooks/useApi.js";
import LoadingSpinner from "@/components/ui/LoadingSpinner.jsx";
import useDropdownList from "@/hooks/list/useDropdownList.js";
import useFulfillmentList from "@/hooks/list/useFulfillmentList.js";

// Import your existing step components
import StepProductDetails from "@/components/ProductWizard/steps/StepProductDetails.js";
import StepDetailsDescription from "@/components/ProductWizard/steps/StepDetailsDescription.js";
import StepProductMedia from "@/components/ProductWizard/steps/StepProductMedia.js";
import StepPricingInventory from "@/components/ProductWizard/steps/StepPricingInventory.js";
import StepComplianceFulfillment from "@/components/ProductWizard/steps/StepComplianceFulfillment.js";
import StepSEOFAQs from "@/components/ProductWizard/steps/StepSEOFAQs.js";
import StepReview from "@/components/ProductWizard/steps/StepReview.js";

// Utility functions for date formatting
const formatDateForInput = (isoDateString) => {
  if (!isoDateString) return "";
  try {
    // Convert ISO date string to YYYY-MM-DD format for HTML date input
    const date = new Date(isoDateString);
    if (isNaN(date.getTime())) return "";
    return date.toISOString().split("T")[0];
  } catch (error) {
    console.error("Error formatting date for input:", error, isoDateString);
    return "";
  }
};

const formatDateForAPI = (dateString) => {
  if (!dateString) return "";
  try {
    // Convert YYYY-MM-DD format to ISO string for API
    const date = new Date(dateString + "T00:00:00.000Z");
    if (isNaN(date.getTime())) return "";
    return date.toISOString();
  } catch (error) {
    console.error("Error formatting date for API:", error, dateString);
    return "";
  }
};

// Define the product data interface
const STEPS = [
  { id: 1, title: "Classification", component: StepProductDetails },
  { id: 2, title: "Product Details", component: StepDetailsDescription },
  { id: 3, title: "Product Media", component: StepProductMedia },
  { id: 4, title: "Pricing & Inventory", component: StepPricingInventory },
  {
    id: 5,
    title: "Compliance & Fulfillment",
    component: StepComplianceFulfillment,
  },
  { id: 6, title: "SEO & FAQs", component: StepSEOFAQs },
  { id: 7, title: "Review & Submit", component: StepReview },
];

const ProductWizardPage = () => {
  const { id } = useParams(); // For /edit/{id} routes
  const [searchParams] = useSearchParams(); // For /add?id={id} routes
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Determine the product ID and mode
  const productId = id || searchParams.get("id");
  // Only consider it edit mode if we're on the /edit/{id} route, not /add?id={id}
  const isEditMode = Boolean(id) && location.pathname.includes("/edit/");

  const { toast } = useToast();
  const { fetchData, postMutation, putMutation } = useApi();

  // Fetch dropdown data for dynamic dropdowns
  const { loading: dropdownsLoading, getDropdownOptions } = useDropdownList();

  const { loading: fulfillmentsLoading, getFulfillmentOptions } =
    useFulfillmentList();

  // Fetch categories and brands for text value population
  const { data: categoriesData } = fetchData(
    "general/categories/active-list",
    {},
    {
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
    }
  );

  const { data: brandsData } = fetchData(
    "general/brands/active-list",
    {},
    {
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
    }
  );

  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [completedSteps, setCompletedSteps] = useState(new Set());
  const [validationErrors] = useState({});
  const [showValidation, setShowValidation] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [createdProductId, setCreatedProductId] = useState(null);

  // Step descriptions for tooltips
  const stepDescriptions = {
    1: "Classify your product by category, brand, and define unique identifiers like SKU and barcode",
    2: "Set product titles, descriptions, and key product information",
    3: "Upload up to 6 product images. Drag to reorder, set one as primary, and add alt text for accessibility",
    4: "Configure pricing, inventory, and product variants",
    5: "Set compliance information, dietary certifications, and fulfillment logistics",
    6: "Configure SEO metadata and frequently asked questions for better discoverability",
    7: "Review all your product information before submitting for approval",
  };

  const [productData, setProductData] = useState({
    // Basic Product Information
    category_id: "",
    sub_category_id: "",
    class_id: "",
    sub_class_id: "",
    brand_id: "",
    vendor_sku: "",
    barcode: "",
    system_sku: "",
    model_number: "",

    // Product Details
    title_en: "",
    title_ar: "",
    short_name: "",
    short_description_en: "",
    short_description_ar: "",
    description_en: "",
    description_ar: "",

    // Product Specifications
    key_ingredients: "",
    usage_instructions_en: "",
    usage_instructions_ar: "",
    // Dropdown fields - store both text values and IDs
    user_group: "",
    user_group_id: "",
    net_weight: "",
    net_weight_unit: "",
    net_weight_unit_id: "",
    formulation: "",
    formulation_id: "",
    servings: 0,
    flavour: "",
    flavour_id: "",

    // Media
    media: [],

    // Pricing & Inventory
    is_variant: false,
    regular_price: 0,
    offer_price: 0,
    discount_start_date: "",
    discount_end_date: "",
    vat_tax: "standard_5", // Changed to match API format
    vat_tax_id: "",
    approx_commission: 0,

    // Inventory fields (UI only - will be handled by separate inventory API)
    stock: 0,
    reserved: 0,
    threshold: 0,
    stock_status: "in_stock",
    warehouse_id: "",

    // Inventory object for component compatibility
    inventory: {
      stock: 0,
      reserved: 0,
      threshold: 0,
      stock_status: "in_stock",
      warehouse_id: "",
    },

    // Compliance & Certifications
    is_vegan: false,
    is_vegetarian: false,
    is_halal: false,
    allergen_info: "",
    storage_conditions: "",
    storage_conditions_id: "",
    country_of_origin: "",
    country_of_origin_id: "",
    is_returnable: "",
    is_returnable_id: "",
    warranty: "",
    warranty_id: "",
    bbe_date: "",
    regulatory_product_registration: "",

    // Package Dimensions
    package_length: 0,
    package_width: 0,
    package_height: 0,
    package_weight: 0,

    // Product Status
    status: "draft",
    is_active: 1,
    is_approved: 0,

    // Nested Objects for API
    product_seo: {
      meta_title_en: "",
      meta_title_ar: "",
      meta_description_en: "",
      meta_description_ar: "",
      keywords_en: "",
      keywords_ar: "",
    },

    product_faqs: [],

    product_fulfillment: {
      mode: "",
      mode_id: "",
      collection_point: "",
      collection_point_id: "",
      shipping_time: 0,
      shipping_fee: 0,
    },

    product_variants: [],

    // Legacy fields for backward compatibility
    attributes: [],
    variants: [],
    dietary_needs: "",
    slug: "",
  });

  // Fetch existing product data if editing OR if we have a product ID for persistence
  const shouldFetchProduct =
    isEditMode || (productId && searchParams.get("id"));
  const {
    data: existingProductData,
    isLoading: productLoading,
    isError: productError,
  } = fetchData(
    shouldFetchProduct ? `admin/products/${productId}` : null,
    {},
    {
      enabled: !!(shouldFetchProduct && productId),
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
    }
  );

  // Initialize form data when product loads
  useEffect(() => {
    if (existingProductData?.data) {
      const product = existingProductData.data;

      // Transform product_media to match our media structure
      const transformedMedia = (product.product_media || []).map(
        (mediaItem) => ({
          id: `media_${mediaItem.id}`,
          product_id: mediaItem.product_id,
          type: mediaItem.type,
          path: mediaItem.path,
          url: mediaItem.path_url, // Use path_url for display
          full_url: mediaItem.path_url,
          title: mediaItem.title,
          alt_text: mediaItem.alt_text,
          lang_code: mediaItem.lang_code || "en",
          position: mediaItem.position,
          is_primary: mediaItem.is_primary ? 1 : 0, // Convert boolean to number
          order: mediaItem.position,
          original_name: null, // Not available from API
          size: null, // Not available from API
          mime_type: null, // Not available from API
          uploading: false,
          upload_error: undefined,
        })
      );

      console.log("Original product_media:", product.product_media);
      console.log("Transformed media:", transformedMedia);

      // Transform API data to match our state structure
      const transformedData = {
        ...product,
        // Transform media data
        media: transformedMedia,
        // Ensure nested objects exist
        product_seo: product.product_seo || {
          meta_title_en: product.meta_title_en || "",
          meta_title_ar: product.meta_title_ar || "",
          meta_description_en: product.meta_description_en || "",
          meta_description_ar: product.meta_description_ar || "",
          keywords_en: product.keywords_en || "",
          keywords_ar: product.keywords_ar || "",
        },
        product_faqs: (product.product_faqs || product.faqs || []).map(
          (faq) => ({
            question: faq.question || "",
            answer: faq.answer || "",
          })
        ),
        product_fulfillment: product.product_fulfillment || {
          mode:
            product.mode ||
            (product.product_fulfillment && product.product_fulfillment.mode) ||
            "",
          mode_id:
            product.mode_id ||
            (product.product_fulfillment &&
              product.product_fulfillment.mode_id) ||
            "",
          is_returnable: product.is_returnable || false,
          collection_point:
            product.collection_point ||
            (product.product_fulfillment &&
              product.product_fulfillment.collection_point) ||
            "",
          shipping_time:
            product.shipping_time ||
            (product.product_fulfillment &&
              product.product_fulfillment.shipping_time) ||
            0,
          shipping_fee:
            product.shipping_fee ||
            (product.product_fulfillment &&
              product.product_fulfillment.shipping_fee) ||
            0,
        },
        product_variants: product.product_variants || product.variants || [],
        // Include variant setup configuration
        variant_setup: product.variant_setup || null,
        // Set variant flags based on API data
        has_varient: Boolean(
          product.is_variant ||
            (product.product_variants && product.product_variants.length > 0)
        ),
        is_variant: Boolean(
          product.is_variant ||
            (product.product_variants && product.product_variants.length > 0)
        ),
        // Convert date fields from ISO format to YYYY-MM-DD format for date inputs
        discount_start_date: formatDateForInput(product.discount_start_date),
        discount_end_date: formatDateForInput(product.discount_end_date),
        bbe_date: formatDateForInput(product.bbe_date),
        // Populate inventory object for component compatibility (UI only)
        inventory: {
          stock: product.stock || 0,
          reserved: product.reserved || 0,
          threshold: product.threshold || 0,
          stock_status: product.stock_status || "in_stock",
          warehouse_id: product.warehouse_id || "",
        },
        // Ensure required fields have defaults
        vat_tax: product.vat_tax || "standard_5",
        status: product.status || "draft",
        is_active: product.is_active !== undefined ? product.is_active : 1,
        is_approved:
          product.is_approved !== undefined ? product.is_approved : 0,

        // Map API field names to frontend field names (handle singular/plural discrepancy)
        usage_instructions_en:
          product.usage_instruction_en || product.usage_instructions_en || "",
        usage_instructions_ar:
          product.usage_instruction_ar || product.usage_instructions_ar || "",

        // Populate dropdown ID fields from API response
        user_group_id: product.user_group_id || "",
        net_weight_unit_id: product.net_weight_unit_id || "",
        formulation_id: product.formulation_id || "",
        flavour_id: product.flavour_id || "",
        vat_tax_id: product.vat_tax_id || "",

        // Compliance dropdown ID fields
        storage_conditions_id: product.storage_conditions || "",
        country_of_origin_id: product.country_of_origin || "",
        is_returnable_id: product.is_returnable || "",
        warranty_id: product.warranty || "",
      };

      // Populate text values from ID values using dropdown options
      const populateDropdownTextValues = (data) => {
        const updatedData = { ...data };

        // Helper function to get text from ID
        const getTextFromId = (slug, id) => {
          if (!id || !getDropdownOptions) return "";
          const options = getDropdownOptions(slug);
          const option = options.find(
            (opt) => opt.id.toString() === id.toString()
          );
          return option ? option.value : "";
        };

        // Populate text values from IDs
        if (updatedData.user_group_id) {
          updatedData.user_group = getTextFromId(
            "user-group",
            updatedData.user_group_id
          );
        }
        if (updatedData.net_weight_unit_id) {
          updatedData.net_weight_unit = getTextFromId(
            "net-weight-unit",
            updatedData.net_weight_unit_id
          );
        }
        if (updatedData.formulation_id) {
          updatedData.formulation = getTextFromId(
            "formulation",
            updatedData.formulation_id
          );
        }
        if (updatedData.flavour_id) {
          updatedData.flavour = getTextFromId(
            "flavour",
            updatedData.flavour_id
          );
        }
        if (updatedData.vat_tax_id) {
          updatedData.vat_tax = getTextFromId(
            "vat-tax",
            updatedData.vat_tax_id
          );
        }

        // Handle compliance dropdown fields
        if (updatedData.storage_conditions_id) {
          updatedData.storage_conditions = getTextFromId(
            "storage-conditions",
            updatedData.storage_conditions_id
          );
        }
        if (updatedData.country_of_origin_id) {
          updatedData.country_of_origin = getTextFromId(
            "country-of-origin",
            updatedData.country_of_origin_id
          );
        }
        if (updatedData.is_returnable_id) {
          updatedData.is_returnable = getTextFromId(
            "is-returnable",
            updatedData.is_returnable_id
          );
        }
        if (updatedData.warranty_id) {
          updatedData.warranty = getTextFromId(
            "warranty",
            updatedData.warranty_id
          );
        }

        // Handle fulfillment dropdown fields
        if (updatedData.product_fulfillment) {
          // Handle fulfillment mode using fulfillmentOptions
          if (
            updatedData.product_fulfillment.mode_id &&
            getFulfillmentOptions
          ) {
            const fulfillmentOptions = getFulfillmentOptions();
            const modeOption = fulfillmentOptions.find(
              (opt) =>
                opt.id.toString() ===
                updatedData.product_fulfillment.mode_id.toString()
            );
            if (modeOption) {
              updatedData.product_fulfillment.mode = modeOption.value;
            }
          }

          // Handle collection point using dropdown options
          if (updatedData.product_fulfillment.collection_point_id) {
            updatedData.product_fulfillment.collection_point = getTextFromId(
              "collection-point",
              updatedData.product_fulfillment.collection_point_id
            );
          }
        }

        return updatedData;
      };

      // Helper function to populate category and brand text values from IDs
      const populateCategoryBrandTextValues = (
        data,
        categoriesData,
        brandsData
      ) => {
        const updatedData = { ...data };

        // Populate category text if category_id exists and categoriesData is available
        if (updatedData.category_id && categoriesData?.data) {
          const category = categoriesData.data.find(
            (cat) => cat.id.toString() === updatedData.category_id.toString()
          );
          if (category) {
            updatedData.category = category.name_en || category.name;
          }
        }

        // Populate brand text if brand_id exists and brandsData is available
        if (updatedData.brand_id && brandsData?.data) {
          const brand = brandsData.data.find(
            (br) => br.id.toString() === updatedData.brand_id.toString()
          );
          if (brand) {
            updatedData.brand = brand.name_en || brand.name;
          }
        }

        return updatedData;
      };

      // Helper function to populate ID fields from text values (fallback for existing products)
      const populateIdFieldsFromText = (data) => {
        const updatedData = { ...data };

        // Helper function to get ID from text
        const getIdFromText = (slug, textValue) => {
          if (!textValue || !getDropdownOptions) return null;
          const options = getDropdownOptions(slug);
          const option = options.find((opt) => opt.value === textValue);
          return option ? option.id : null;
        };

        // Populate ID fields from text values if ID fields are missing
        if (!updatedData.user_group_id && updatedData.user_group) {
          updatedData.user_group_id = getIdFromText(
            "user-group",
            updatedData.user_group
          );
        }
        if (!updatedData.net_weight_unit_id && updatedData.net_weight_unit) {
          updatedData.net_weight_unit_id = getIdFromText(
            "net-weight-unit",
            updatedData.net_weight_unit
          );
        }
        if (!updatedData.formulation_id && updatedData.formulation) {
          updatedData.formulation_id = getIdFromText(
            "formulation",
            updatedData.formulation
          );
        }
        if (!updatedData.flavour_id && updatedData.flavour) {
          updatedData.flavour_id = getIdFromText(
            "flavour",
            updatedData.flavour
          );
        }
        if (!updatedData.vat_tax_id && updatedData.vat_tax) {
          updatedData.vat_tax_id = getIdFromText(
            "vat-tax",
            updatedData.vat_tax
          );
        }

        // Handle compliance fields
        if (
          !updatedData.storage_conditions_id &&
          updatedData.storage_conditions
        ) {
          updatedData.storage_conditions_id = getIdFromText(
            "storage-conditions",
            updatedData.storage_conditions
          );
        }
        if (
          !updatedData.country_of_origin_id &&
          updatedData.country_of_origin
        ) {
          updatedData.country_of_origin_id = getIdFromText(
            "country-of-origin",
            updatedData.country_of_origin
          );
        }
        if (!updatedData.is_returnable_id && updatedData.is_returnable) {
          updatedData.is_returnable_id = getIdFromText(
            "is-returnable",
            updatedData.is_returnable
          );
        }
        if (!updatedData.warranty_id && updatedData.warranty) {
          updatedData.warranty_id = getIdFromText(
            "warranty",
            updatedData.warranty
          );
        }

        // Handle fulfillment fields
        if (updatedData.product_fulfillment) {
          if (
            !updatedData.product_fulfillment.mode_id &&
            updatedData.product_fulfillment.mode &&
            getFulfillmentOptions
          ) {
            const fulfillmentOptions = getFulfillmentOptions();
            const modeOption = fulfillmentOptions.find(
              (opt) => opt.value === updatedData.product_fulfillment.mode
            );
            if (modeOption) {
              updatedData.product_fulfillment.mode_id = modeOption.id;
            }
          }

          if (
            !updatedData.product_fulfillment.collection_point_id &&
            updatedData.product_fulfillment.collection_point
          ) {
            updatedData.product_fulfillment.collection_point_id = getIdFromText(
              "collection-point",
              updatedData.product_fulfillment.collection_point
            );
          }
        }

        return updatedData;
      };

      // Only populate dropdown text values if dropdown options are loaded
      let finalData = transformedData;
      if (
        !dropdownsLoading &&
        !fulfillmentsLoading &&
        getDropdownOptions &&
        getFulfillmentOptions
      ) {
        console.log("Populating dropdown text values for edit mode", {
          user_group_id: transformedData.user_group_id,
          net_weight_unit_id: transformedData.net_weight_unit_id,
          formulation_id: transformedData.formulation_id,
          flavour_id: transformedData.flavour_id,
        });
        finalData = populateDropdownTextValues(transformedData);
        finalData = populateIdFieldsFromText(finalData);
        finalData = populateCategoryBrandTextValues(
          finalData,
          categoriesData,
          brandsData
        );
        console.log("Final data after dropdown population", {
          user_group: finalData.user_group,
          user_group_id: finalData.user_group_id,
          net_weight_unit: finalData.net_weight_unit,
          net_weight_unit_id: finalData.net_weight_unit_id,
          formulation: finalData.formulation,
          formulation_id: finalData.formulation_id,
          flavour: finalData.flavour,
          flavour_id: finalData.flavour_id,
          category: finalData.category,
          brand: finalData.brand,
        });
      } else {
        console.log("Skipping dropdown population - dropdowns not loaded yet", {
          dropdownsLoading,
          fulfillmentsLoading,
          hasGetDropdownOptions: !!getDropdownOptions,
          hasGetFulfillmentOptions: !!getFulfillmentOptions,
        });
      }
      setProductData(finalData);

      // Set createdProductId if we're in add mode with ID (for URL persistence)
      if (searchParams.get("id") && !isEditMode) {
        setCreatedProductId(product.id);
      }

      // Mark steps as completed based on available data
      const completed = new Set();
      // Step 1: Classification - Check all required fields
      if (
        product.category_id &&
        product.sub_category_id &&
        product.class_id &&
        product.brand_id &&
        product.vendor_sku &&
        product.barcode &&
        product.model_number
      ) {
        completed.add(1);
      }
      // Step 2: Product Details - Check required fields (check both text and ID values for dropdowns)
      if (
        product.title_en &&
        product.description_en &&
        product.key_ingredients &&
        product.usage_instructions_en &&
        (product.user_group || product.user_group_id) &&
        product.net_weight &&
        (product.net_weight_unit || product.net_weight_unit_id) &&
        (product.formulation || product.formulation_id)
      ) {
        completed.add(2);
      }
      // Step 3: Product Media - Check for at least one primary image
      if (
        transformedMedia.length > 0 &&
        transformedMedia.some(
          (item) => item.is_primary === 1 && item.type === "image"
        )
      ) {
        completed.add(3);
      }
      // Step 4: Pricing & Inventory - Check required fields
      if (
        product.regular_price &&
        product.regular_price > 0 &&
        product.vat_tax
      ) {
        completed.add(4);
      }
      // Step 5: Compliance & Fulfillment - Check required fields
      if (
        product.is_vegan !== undefined &&
        product.is_vegan !== null &&
        product.is_vegetarian !== undefined &&
        product.is_vegetarian !== null &&
        product.is_halal !== undefined &&
        product.is_halal !== null &&
        product.allergen_info &&
        (product.product_fulfillment?.mode || product.mode) &&
        (product.product_fulfillment?.collection_point ||
          product.collection_point) &&
        product.is_returnable_id !== undefined
      ) {
        completed.add(5);
      }
      // Step 6: SEO & FAQs - Optional step, mark as completed if any data exists
      if (
        product.product_seo?.meta_title_en ||
        product.meta_title_en ||
        product.product_faqs?.length > 0 ||
        product.faqs?.length > 0
      ) {
        completed.add(6);
      }
      setCompletedSteps(completed);
    }
  }, [
    existingProductData,
    isEditMode,
    searchParams,
    dropdownsLoading,
    fulfillmentsLoading,
  ]);

  // Remove auto-generation of system SKU - it should only come from the API after product creation

  // Auto-generate vendor SKU from barcode if empty
  useEffect(() => {
    if (
      !productData.vendor_sku &&
      productData.barcode &&
      productData.barcode.length >= 6
    ) {
      const generatedSku = productData.barcode.slice(-6);
      setProductData((prev) => ({
        ...prev,
        vendor_sku: `SKU-${generatedSku}`,
      }));
    }
  }, [productData.barcode, productData.vendor_sku]);

  // Calculate progress function - moved here to be available for validateCurrentStep
  const calculateProgress = () => {
    const totalFields = Object.keys(productData).length - 3;
    const filledFields = Object.entries(productData).filter(([key, value]) => {
      if (
        [
          "media",
          "attributes",
          "variants",
          "product_faqs",
          "product_variants",
        ].includes(key)
      )
        return false;
      if (key === "system_sku") return false;
      if (typeof value === "object" && value !== null) {
        return Object.values(value).some(
          (v) => v !== "" && v !== 0 && v !== false
        );
      }
      return value !== "" && value !== 0 && value !== false;
    }).length;

    return Math.round((filledFields / totalFields) * 100);
  };

  // Validate current step function - moved here to be available for useEffect
  const validateCurrentStep = (stepId) => {
    switch (stepId) {
      case 1: // Classification & Identity - MANDATORY: Product Category, Sub Category, Product Class, Brand Name, Product Code (SKU), Barcode
        return !!(
          productData.category_id &&
          productData.sub_category_id &&
          productData.class_id &&
          productData.brand_id &&
          productData.vendor_sku &&
          productData.barcode &&
          productData.model_number
        );
      case 2: // Details & Description - MANDATORY: Product Name/Title[en], Full Product Description[en], Key Ingredients, Usage Instructions, User Group, Net Weight, Net Weight Unit, Formulation
        return !!(
          productData.title_en &&
          productData.description_en &&
          productData.key_ingredients &&
          productData.usage_instructions_en &&
          (productData.user_group || productData.user_group_id) &&
          productData.net_weight &&
          (productData.net_weight_unit || productData.net_weight_unit_id) &&
          (productData.formulation || productData.formulation_id)
        );
      case 3: // Product Media - MANDATORY: At least one main image
        const mediaArray = productData.media || [];
        const hasMainImage = mediaArray.some(
          (item) => item.is_primary === 1 && item.type === "image"
        );
        return hasMainImage;
      case 4: // Pricing & Inventory - MANDATORY: Regular Price, VAT Tax
        return !!(productData.regular_price > 0 && productData.vat_tax);
      case 5: // Compliance & Fulfillment - MANDATORY: Vegan, Vegetarian, Halal, Allergen Information, Fulfillment Mode, Collection Point, Returnable
        return !!(
          productData.is_vegan !== undefined &&
          productData.is_vegan !== null &&
          productData.is_vegetarian !== undefined &&
          productData.is_vegetarian !== null &&
          productData.is_halal !== undefined &&
          productData.is_halal !== null &&
          productData.allergen_info &&
          productData.product_fulfillment?.mode &&
          productData.product_fulfillment?.collection_point &&
          productData.is_returnable_id !== undefined &&
          productData.is_returnable_id !== null
        );
      case 6: // SEO & FAQs - Optional
        return true;
      case 7: // Review & Submit - Check overall completion
        return calculateProgress() >= 80;
      default:
        return true;
    }
  };

  // Hide validation when user fills required fields
  useEffect(() => {
    if (showValidation && validateCurrentStep(currentStep)) {
      setShowValidation(false);
    }
  }, [productData, currentStep, showValidation]);

  // Navigation restriction: Redirect to Classification if trying to access later steps without completion
  useEffect(() => {
    // Skip validation during initial load or if already on step 1
    if (currentStep === 1 || productLoading) return;

    // Check if current step is accessible
    if (!isStepAccessible(currentStep)) {
      // Show warning and redirect to Classification step
      toast({
        title: "Access Restricted",
        description:
          "Please complete the Classification step first before accessing other steps.",
        variant: "destructive",
      });
      setCurrentStep(1);
    }
  }, [
    currentStep,
    createdProductId,
    productId,
    isEditMode,
    completedSteps,
    productLoading,
  ]);

  // Close tooltip when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showTooltip && !event.target.closest(".tooltip-container")) {
        setShowTooltip(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showTooltip]);

  // Extract step-specific data for incremental API updates
  const getStepSpecificData = (stepId, data) => {
    switch (stepId) {
      case 1: // Classification & Identity - Already handled in createProduct
        const step1Data = {
          category_id: Number(data.category_id),
          sub_category_id: Number(data.sub_category_id),
          class_id: Number(data.class_id),
          sub_class_id: data.sub_class_id ? Number(data.sub_class_id) : null,
          vendor_sku: data.vendor_sku,
          model_number: data.model_number,
          brand_id: Number(data.brand_id),
          barcode: data.barcode,
        };

        // In edit mode, preserve variant data to prevent it from being cleared
        if (
          isEditMode &&
          data.is_variant &&
          (data.product_variants || data.variants)
        ) {
          const productVariantsData =
            data.product_variants || data.variants || [];
          step1Data.is_variant = Boolean(data.is_variant);
          step1Data.has_varient = Boolean(data.is_variant); // Keep for backward compatibility
          step1Data.product_variants = productVariantsData;
          step1Data.variant_setup = data.variant_setup || null;
        }

        return step1Data;

      case 2: // Details & Description
        const step2Data = {
          title_en: data.title_en,
          title_ar: data.title_ar,
          short_name: data.short_name,
          short_description_en: data.short_description_en,
          short_description_ar: data.short_description_ar,
          description_en: data.description_en,
          description_ar: data.description_ar,
          key_ingredients: data.key_ingredients,
          usage_instruction_en: data.usage_instructions_en,
          usage_instruction_ar: data.usage_instructions_ar,
          user_group_id: data.user_group_id
            ? Number(data.user_group_id)
            : undefined,
          net_weight: data.net_weight,
          net_weight_unit_id: data.net_weight_unit_id
            ? Number(data.net_weight_unit_id)
            : undefined,
          formulation_id: data.formulation_id
            ? Number(data.formulation_id)
            : undefined,
          servings: Number(data.servings) || 0,
          flavour_id: data.flavour_id ? Number(data.flavour_id) : undefined,
        };

        // In edit mode, preserve variant data to prevent it from being cleared
        if (
          isEditMode &&
          data.is_variant &&
          (data.product_variants || data.variants)
        ) {
          const productVariantsData =
            data.product_variants || data.variants || [];
          step2Data.is_variant = Boolean(data.is_variant);
          step2Data.has_varient = Boolean(data.is_variant);
          step2Data.product_variants = productVariantsData;
          step2Data.variant_setup = data.variant_setup || null;
        }

        console.log("Step 2 data being sent to API:", {
          fullStep2Data: step2Data,
        });
        return step2Data;

      case 3: // Product Media
        console.log("getStepSpecificData for step 3:", {
          originalMedia: data.media,
          mediaCount: (data.media || []).length,
          videos: (data.media || []).filter((item) => item.type === "video"),
          images: (data.media || []).filter((item) => item.type === "image"),
          supplement_image: data.supplement_image,
        });
        return {
          media: data.media || [],
          supplement_image: data.supplement_image,
        };

      case 4: // Pricing & Inventory
        // Use is_variant consistently - if toggle is enabled, variants are enabled
        const isVariantEnabled = Boolean(data.is_variant);

        // Use product_variants as the primary field, with variants as fallback for backward compatibility
        const productVariantsData =
          data.product_variants || data.variants || [];

        const step4Data = {
          // Use is_variant consistently
          is_variant: isVariantEnabled,
          has_varient: isVariantEnabled, // Keep for backward compatibility
          regular_price: Number(data.regular_price) || 0,
          offer_price: Number(data.offer_price) || 0,
          vat_tax_id: data.vat_tax_id ? Number(data.vat_tax_id) : undefined,
          vat_tax: data.vat_tax || undefined,
          vat_tax_utl: data.vat_tax_utl || undefined, // Include VAT tax document
          discount_start_date: formatDateForAPI(data.discount_start_date),
          discount_end_date: formatDateForAPI(data.discount_end_date),
          approx_commission: Number(data.approx_commission) || 0,
          fulfillment_id: data.fulfillment_id
            ? Number(data.fulfillment_id)
            : undefined,
          reserved: Number(data.reserved) || 0,
          threshold: Number(data.threshold) || 0,
          // Use product_variants as the primary field
          product_variants: isVariantEnabled ? productVariantsData : undefined,
          // Only include variant setup when variants are enabled
          variant_setup: isVariantEnabled ? data.variant_setup || null : null,
        };

        return step4Data;

      case 5: // Compliance & Fulfillment
        const step5Data = {
          is_vegan: Boolean(data.is_vegan),
          is_vegetarian: Boolean(data.is_vegetarian),
          is_halal: Boolean(data.is_halal),
          allergen_info: data.allergen_info,
          // Send ID fields instead of text values for dropdown fields
          storage_conditions: data.storage_conditions_id
            ? Number(data.storage_conditions_id)
            : undefined,
          country_of_origin: data.country_of_origin_id
            ? Number(data.country_of_origin_id)
            : undefined,
          is_returnable: data.is_returnable_id
            ? Number(data.is_returnable_id)
            : undefined,
          warranty: data.warranty_id ? Number(data.warranty_id) : undefined,
          bbe_date: formatDateForAPI(data.bbe_date),
          regulatory_product_registration: data.regulatory_product_registration,
          vat_tax_url: data.vat_tax_url,
          package_length: Number(data.package_length) || 0,
          package_width: Number(data.package_width) || 0,
          package_height: Number(data.package_height) || 0,
          package_weight: Number(data.package_weight) || 0,
          product_fulfillment:
            data.product_fulfillment &&
            Object.keys(data.product_fulfillment).length > 0
              ? {
                  ...data.product_fulfillment,
                  // Send ID for mode field (backend expects mode_id)
                  mode_id: data.product_fulfillment.mode_id
                    ? Number(data.product_fulfillment.mode_id)
                    : undefined,
                  // Send string for collection_point field (backend expects collection_point as string)
                  collection_point:
                    data.product_fulfillment.collection_point || undefined,
                  // Remove text field for mode from API payload
                  mode: undefined,
                }
              : undefined,
        };

        // In edit mode, preserve variant data to prevent it from being cleared
        if (
          isEditMode &&
          data.is_variant &&
          (data.product_variants || data.variants)
        ) {
          const productVariantsData =
            data.product_variants || data.variants || [];
          step5Data.is_variant = Boolean(data.is_variant);
          step5Data.has_varient = Boolean(data.is_variant);
          step5Data.product_variants = productVariantsData;
          step5Data.variant_setup = data.variant_setup || null;
        }

        return step5Data;

      case 6: // SEO & FAQs
        const step6Data = {
          product_seo:
            data.product_seo && Object.keys(data.product_seo).length > 0
              ? data.product_seo
              : undefined,
          product_faqs:
            data.product_faqs && data.product_faqs.length > 0
              ? data.product_faqs
              : undefined,
        };

        // In edit mode, preserve variant data to prevent it from being cleared
        if (
          isEditMode &&
          data.is_variant &&
          (data.product_variants || data.variants)
        ) {
          const productVariantsData =
            data.product_variants || data.variants || [];
          step6Data.is_variant = Boolean(data.is_variant);
          step6Data.has_varient = Boolean(data.is_variant);
          step6Data.product_variants = productVariantsData;
          step6Data.variant_setup = data.variant_setup || null;
        }

        return step6Data;

      default:
        return {};
    }
  };

  // Transform form data to API format
  const transformDataForAPI = (data) => {
    console.log(
      "transformDataForAPI input data supplement_image:",
      data.supplement_image
    );
    const apiData = {
      // Basic fields
      barcode: data.barcode,
      model_number: data.model_number,
      brand_id: Number(data.brand_id),

      // Product details
      title_en: data.title_en,
      title_ar: data.title_ar,
      short_name: data.short_name,
      short_description_en: data.short_description_en,
      short_description_ar: data.short_description_ar,
      description_en: data.description_en,
      description_ar: data.description_ar,

      // Specifications - send ID fields to API (map to API field names)
      key_ingredients: data.key_ingredients,
      supplement_image: data.supplement_image, // Add supplement_image field
      usage_instruction_en: data.usage_instructions_en,
      usage_instruction_ar: data.usage_instructions_ar,
      user_group_id: data.user_group_id
        ? Number(data.user_group_id)
        : undefined,
      net_weight: data.net_weight,
      net_weight_unit_id: data.net_weight_unit_id
        ? Number(data.net_weight_unit_id)
        : undefined,
      formulation_id: data.formulation_id
        ? Number(data.formulation_id)
        : undefined,
      servings: Number(data.servings) || 0,
      flavour_id: data.flavour_id ? Number(data.flavour_id) : undefined,
      is_variant: Boolean(data.is_variant),

      // Pricing
      regular_price: Number(data.regular_price) || 0,
      offer_price: Number(data.offer_price) || 0,
      vat_tax_id: data.vat_tax_id ? Number(data.vat_tax_id) : undefined,
      discount_start_date: formatDateForAPI(data.discount_start_date),
      discount_end_date: formatDateForAPI(data.discount_end_date),
      approx_commission: Number(data.approx_commission) || 0,

      // Compliance - send ID fields instead of text values for dropdown fields
      country_of_origin: data.country_of_origin_id
        ? Number(data.country_of_origin_id)
        : undefined,
      storage_conditions: data.storage_conditions_id
        ? Number(data.storage_conditions_id)
        : undefined,
      is_returnable: data.is_returnable_id
        ? Number(data.is_returnable_id)
        : undefined,
      warranty: data.warranty_id ? Number(data.warranty_id) : undefined,
      bbe_date: formatDateForAPI(data.bbe_date),
      regulatory_product_registration: data.regulatory_product_registration,
      is_vegan: Boolean(data.is_vegan),
      is_vegetarian: Boolean(data.is_vegetarian),
      is_halal: Boolean(data.is_halal),
      allergen_info: data.allergen_info,

      // Package dimensions
      package_length: Number(data.package_length) || 0,
      package_width: Number(data.package_width) || 0,
      package_height: Number(data.package_height) || 0,
      package_weight: Number(data.package_weight) || 0,

      // Status
      status: data.status || "draft",
      is_active: Number(data.is_active) || 1,
      is_approved: Number(data.is_approved) || 0,

      // Nested objects
      product_seo:
        data.product_seo && Object.keys(data.product_seo).length > 0
          ? data.product_seo
          : undefined,
      product_faqs:
        data.product_faqs && data.product_faqs.length > 0
          ? data.product_faqs.map((faq) => ({
              question: faq.question || "",
              answer: faq.answer || "",
            }))
          : undefined,
      product_fulfillment:
        data.product_fulfillment &&
        Object.keys(data.product_fulfillment).length > 0
          ? {
              ...data.product_fulfillment,
              // Send ID fields instead of text values
              mode_id: data.product_fulfillment.mode_id
                ? Number(data.product_fulfillment.mode_id)
                : undefined,
              collection_point_id: data.product_fulfillment.collection_point_id
                ? Number(data.product_fulfillment.collection_point_id)
                : undefined,
              // Remove text fields from API payload
              mode: undefined,
              collection_point: undefined,
            }
          : undefined,
      product_variants: data.is_variant
        ? (data.product_variants || data.variants || []).map((variant) => {
            // Remove location field from variants to prevent API validation errors
            const { location, ...cleanVariant } = variant;
            return cleanVariant;
          })
        : undefined,
      variant_setup: data.is_variant ? data.variant_setup || null : null,
    };

    // Remove empty or undefined values (but preserve false boolean values)
    Object.keys(apiData).forEach((key) => {
      if (
        apiData[key] === "" ||
        apiData[key] === undefined ||
        apiData[key] === null
      ) {
        delete apiData[key];
      }
    });

    console.log(
      "transformDataForAPI final output supplement_image:",
      apiData.supplement_image
    );
    console.log(
      "transformDataForAPI final apiData keys:",
      Object.keys(apiData)
    );
    console.log("=== FINAL API PAYLOAD ===");
    console.log("is_variant:", apiData.is_variant);
    console.log("product_variants:", apiData.product_variants);
    console.log(
      "product_variants length:",
      apiData.product_variants?.length || 0
    );
    console.log("variant_setup:", apiData.variant_setup);
    console.log("transformDataForAPI final apiData:", apiData);

    return apiData;
  };

  // Validate complete product data for submission
  const validateCompleteProduct = () => {
    const errors = [];

    // Required basic fields
    if (!productData.category_id) errors.push("Product Category");
    if (!productData.sub_category_id) errors.push("Sub Category");
    if (!productData.class_id) errors.push("Product Class");
    if (!productData.brand_id) errors.push("Brand");
    if (!productData.vendor_sku) errors.push("Vendor SKU");
    if (!productData.barcode) errors.push("Barcode");
    if (!productData.model_number) errors.push("Model Number");

    // Required product details
    if (!productData.title_en) errors.push("Product Title (English)");
    if (!productData.description_en)
      errors.push("Product Description (English)");
    if (!productData.usage_instructions_en)
      errors.push("Usage Instructions (English)");
    if (!productData.user_group && !productData.user_group_id)
      errors.push("User Group");
    if (!productData.net_weight) errors.push("Net Weight");
    if (!productData.net_weight_unit && !productData.net_weight_unit_id)
      errors.push("Net Weight Unit");

    // Required pricing
    if (!productData.regular_price || productData.regular_price <= 0)
      errors.push("Regular Price");
    if (!productData.vat_tax) errors.push("VAT Tax");

    // Required compliance
    if (productData.is_vegan === undefined || productData.is_vegan === null)
      errors.push("Vegan Status");
    if (
      productData.is_vegetarian === undefined ||
      productData.is_vegetarian === null
    )
      errors.push("Vegetarian Status");
    if (productData.is_halal === undefined || productData.is_halal === null)
      errors.push("Halal Status");
    if (!productData.allergen_info) errors.push("Allergen Information");

    // Required fulfillment
    if (!productData.product_fulfillment?.mode) errors.push("Fulfillment Mode");
    if (!productData.product_fulfillment?.collection_point)
      errors.push("Collection Point");
    if (
      productData.is_returnable_id === undefined ||
      productData.is_returnable_id === null
    )
      errors.push("Returnable Status");

    return errors;
  };

  // Function to save product media using the specific media API
  const saveProductMedia = async (productIdToUpdate, stepData) => {
    const mediaArray = stepData.media || [];

    console.log("saveProductMedia called with:", {
      productId: productIdToUpdate,
      mediaArray: mediaArray,
      mediaCount: mediaArray.length,
    });

    // Transform media data to match the required API format (both images and videos)
    const transformedMedia = mediaArray
      .filter((item) => {
        console.log("Filtering media item:", {
          id: item.id,
          type: item.type,
          path: item.path,
          hasPath: !!item.path,
          willInclude:
            (item.type === "image" && item.path) ||
            (item.type === "video" && item.path),
        });

        // Include images that have been uploaded (have path)
        if (item.type === "image" && item.path) return true;
        // Include videos that have valid URLs (stored in path field)
        if (item.type === "video" && item.path) return true;
        return false;
      })
      .map((item, index) => ({
        product_id: Number(productIdToUpdate),
        type: item.type, // 'image' or 'video'
        path: item.path, // For images: upload path, For videos: video URL
        title: item.title || null,
        alt_text: item.alt_text || null,
        lang_code: item.lang_code || "en",
        position: item.position || index + 1,
        is_primary: item.is_primary || 0, // Videos should always be 0, images can be 1 for primary
      }));

    // Only proceed if we have media to save
    if (transformedMedia.length === 0) {
      console.log("No media to save");
      return;
    }

    const payload = {
      media: transformedMedia,
    };

    console.log("Saving product media with payload:", payload);
    console.log("Media breakdown:", {
      images: transformedMedia.filter((item) => item.type === "image").length,
      videos: transformedMedia.filter((item) => item.type === "video").length,
      total: transformedMedia.length,
    });

    await postMutation.mutateAsync({
      endpoint: "admin/product-media/store-update",
      data: payload,
    });
  };

  // Auto-save function for step completion
  const autoSaveStep = async (stepId) => {
    // Skip auto-save for step 1 in add mode (handled by createProduct) and step 7 (handled by submit)
    // But allow step 1 auto-save in edit mode
    if (stepId === 7) return;
    if (stepId === 1 && !isEditMode) return;

    // Only auto-save if we have a product to update
    if (!createdProductId && !isEditMode) return;

    const stepData = getStepSpecificData(stepId, productData);

    // Remove empty or undefined values (but preserve false boolean values)
    Object.keys(stepData).forEach((key) => {
      if (
        stepData[key] === "" ||
        stepData[key] === undefined ||
        stepData[key] === null
      ) {
        delete stepData[key];
      }
    });

    // Skip if no data to save
    if (Object.keys(stepData).length === 0) return;

    try {
      const productIdToUpdate = isEditMode ? productId : createdProductId;

      // Special handling for Product Media step (step 3)
      if (stepId === 3) {
        await saveProductMedia(productIdToUpdate, stepData);
      } else {
        // Regular product update for other steps
        await putMutation.mutateAsync({
          endpoint: `admin/products/${productIdToUpdate}`,
          data: stepData,
        });
      }

      toast({
        title: "Progress Saved",
        description: `Step ${stepId} data has been automatically saved.`,
      });
    } catch (error) {
      console.error("Auto-save failed:", error);
      toast({
        title: "Auto-save Failed",
        description:
          "Failed to save step progress: " +
          (error?.response?.data?.message || error.message),
        variant: "destructive",
      });
      throw error; // Re-throw to prevent navigation
    }
  };

  // Function to create product after step 1 completion
  const createProduct = async () => {
    if (createdProductId || isEditMode) return; // Already created or in edit mode

    const requiredData = {
      category_id: Number(productData.category_id),
      sub_category_id: Number(productData.sub_category_id),
      class_id: Number(productData.class_id),
      sub_class_id: productData.sub_class_id
        ? Number(productData.sub_class_id)
        : null,
      vendor_sku: productData.vendor_sku,
      model_number: productData.model_number,
      brand_id: Number(productData.brand_id),
      barcode: productData.barcode,
    };

    try {
      const response = await postMutation.mutateAsync({
        endpoint: "admin/products",
        data: requiredData,
      });

      if (response?.data?.id) {
        setCreatedProductId(response.data.id);
        // Update productData with the system-generated SKU and ID
        setProductData((prev) => ({
          ...prev,
          id: response.data.id,
          system_sku: response.data.system_sku,
        }));

        // Update URL to include the created product ID for data persistence
        const currentPath = location.pathname;
        if (currentPath === "/products/add") {
          navigate(`/products/add?id=${response.data.id}`, { replace: true });
        }

        toast({
          title: "Product Created",
          description:
            "Product has been created successfully. Continue with the next steps.",
        });
      }
    } catch (error) {
      console.error("Product creation failed:", error);
      toast({
        title: "Error",
        description:
          "Failed to create product: " +
          (error?.response?.data?.message || error.message),
        variant: "destructive",
      });
      throw error; // Re-throw to prevent navigation
    }
  };

  const handleNext = async () => {
    // Show validation for the current step
    setShowValidation(true);
    setShowTooltip(false); // Hide tooltip when navigating

    // Validate current step before proceeding
    if (!validateCurrentStep(currentStep)) {
      toast({
        title: "Incomplete Step",
        description: "Please fill in all required fields before proceeding.",
        variant: "destructive",
      });
      return;
    }

    // Handle Classification step (step 1)
    if (currentStep === 1) {
      if (!isEditMode && !createdProductId) {
        // This is the Classification step and we need to create the product
        setLoading(true);
        try {
          await createProduct();
          setCompletedSteps((prev) => new Set([...prev, currentStep]));
          setShowValidation(false);
          const nextStepIndex =
            STEPS.findIndex((s) => s.id === currentStep) + 1;
          if (nextStepIndex < STEPS.length) {
            setCurrentStep(STEPS[nextStepIndex].id);
          }
        } catch (error) {
          // Error already handled in createProduct, just prevent navigation
          return;
        } finally {
          setLoading(false);
        }
      } else {
        // Product already exists (edit mode or already created)
        if (isEditMode) {
          // In edit mode, we need to update the product with classification changes
          setLoading(true);
          try {
            await autoSaveStep(currentStep);
            setCompletedSteps((prev) => new Set([...prev, currentStep]));
            setShowValidation(false);
            const nextStepIndex =
              STEPS.findIndex((s) => s.id === currentStep) + 1;
            if (nextStepIndex < STEPS.length) {
              setCurrentStep(STEPS[nextStepIndex].id);
            }
          } catch (error) {
            // Error already handled in autoSaveStep, just prevent navigation
            return;
          } finally {
            setLoading(false);
          }
        } else {
          // Product already created in add mode, just mark step as completed and proceed
          setCompletedSteps((prev) => new Set([...prev, currentStep]));
          setShowValidation(false);
          const nextStepIndex =
            STEPS.findIndex((s) => s.id === currentStep) + 1;
          if (nextStepIndex < STEPS.length) {
            setCurrentStep(STEPS[nextStepIndex].id);
          }
        }
      }
    } else if (currentStep > 1) {
      // For subsequent steps, ensure Classification is completed and product exists
      const hasProductId =
        createdProductId || (productId && searchParams.get("id")) || isEditMode;
      const classificationCompleted =
        completedSteps.has(1) || validateCurrentStep(1);

      if (!hasProductId || !classificationCompleted) {
        toast({
          title: "Classification Required",
          description:
            "Please complete the Classification step first to create the product.",
          variant: "destructive",
        });
        setCurrentStep(1);
        return;
      }

      // Auto-save step data for steps 2-6
      setLoading(true);
      try {
        await autoSaveStep(currentStep);
        setCompletedSteps((prev) => new Set([...prev, currentStep]));
        setShowValidation(false);
        const nextStepIndex = STEPS.findIndex((s) => s.id === currentStep) + 1;
        if (nextStepIndex < STEPS.length) {
          setCurrentStep(STEPS[nextStepIndex].id);
        }
      } catch (error) {
        // Error already handled in autoSaveStep, just prevent navigation
        return;
      } finally {
        setLoading(false);
      }
    }
  };

  const handlePrevious = () => {
    setShowTooltip(false); // Hide tooltip when navigating
    const currentIndex = STEPS.findIndex((s) => s.id === currentStep);
    if (currentIndex > 0) {
      const previousStepId = STEPS[currentIndex - 1].id;
      // Previous navigation should always be allowed to go back to accessible steps
      setCurrentStep(previousStepId);
    }
  };

  // Check if a step is accessible based on validation rules
  const isStepAccessible = (stepId) => {
    // Step 1 (Classification) is always accessible
    if (stepId === 1) return true;

    // For edit mode, all steps are accessible since product already exists
    if (isEditMode) return true;

    // For add mode, subsequent steps require Classification completion and product ID
    const hasProductId =
      createdProductId || (productId && searchParams.get("id"));
    const classificationCompleted =
      completedSteps.has(1) || validateCurrentStep(1);

    return hasProductId && classificationCompleted;
  };

  // Get tooltip message for inaccessible steps
  const getStepTooltipMessage = (stepId) => {
    if (isStepAccessible(stepId)) return null;

    if (stepId > 1) {
      return "Complete the Classification step first to unlock this step";
    }

    return null;
  };

  const handleStepClick = (stepId) => {
    // Check if step is accessible
    if (!isStepAccessible(stepId)) {
      toast({
        title: "Step Not Available",
        description:
          "Please complete the Classification step first to access this step.",
        variant: "destructive",
      });
      return;
    }

    setCurrentStep(stepId);
    setShowValidation(false); // Hide validation when changing steps
    setShowTooltip(false); // Hide tooltip when changing steps
  };

  const handleSaveDraft = async () => {
    setLoading(true);
    try {
      const apiData = transformDataForAPI({ ...productData, status: "draft" });

      if (isEditMode) {
        // Use productId which works for both /edit/{id} and /add?id={id}
        await putMutation.mutateAsync({
          endpoint: `admin/products/${productId}`,
          data: apiData,
        });
      } else if (createdProductId) {
        // Update the created product as draft
        await putMutation.mutateAsync({
          endpoint: `admin/products/${createdProductId}`,
          data: apiData,
        });
      } else {
        // Create product as draft if not created yet
        const response = await postMutation.mutateAsync({
          endpoint: "admin/products",
          data: apiData,
        });
        if (response?.data?.id) {
          setCreatedProductId(response.data.id);
        }
      }
      toast({
        title: "Draft Saved",
        description: "Your product has been saved as a draft.",
      });
      navigate("/products/all");
    } catch (error) {
      console.error("Save draft failed:", error);
      toast({
        title: "Error",
        description:
          "Failed to save draft: " +
          (error?.response?.data?.message || error.message),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitForApproval = async () => {
    // Validate required fields
    const validationErrors = validateCompleteProduct();
    if (validationErrors.length > 0) {
      toast({
        title: "Missing Required Fields",
        description: `Please complete the following fields: ${validationErrors
          .slice(0, 3)
          .join(", ")}${
          validationErrors.length > 3
            ? ` and ${validationErrors.length - 3} more`
            : ""
        }`,
        variant: "destructive",
      });
      return;
    }

    if (calculateProgress() < 80) {
      toast({
        title: "Incomplete Product",
        description:
          "Please complete at least 80% of the form before submitting.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const apiData = transformDataForAPI({
        ...productData,
        status: "submitted",
      });

      if (isEditMode) {
        // Use productId which works for both /edit/{id} and /add?id={id}
        await putMutation.mutateAsync({
          endpoint: `admin/products/${productId}`,
          data: apiData,
        });
      } else if (createdProductId) {
        // Update the created product with all the additional data
        await putMutation.mutateAsync({
          endpoint: `admin/products/${createdProductId}`,
          data: apiData,
        });
      } else {
        // Fallback: create product if somehow not created yet
        const response = await postMutation.mutateAsync({
          endpoint: "admin/products",
          data: apiData,
        });
        if (response?.data?.id) {
          setCreatedProductId(response.data.id);
        }
      }
      toast({
        title: "Submitted for Approval",
        description: "Your product has been submitted for review.",
      });
      navigate("/products/all");
    } catch (error) {
      console.error("Submit failed:", error);
      toast({
        title: "Error",
        description:
          "Failed to submit product: " +
          (error?.response?.data?.message || error.message),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getCurrentStepComponent = () => {
    const currentStepData = STEPS.find((s) => s.id === currentStep);
    return currentStepData?.component || STEPS[0].component;
  };

  const CurrentStepComponent = getCurrentStepComponent();

  if (productLoading || dropdownsLoading || fulfillmentsLoading) {
    return <LoadingSpinner size={64} overlay />;
  }

  if (productError) {
    return (
      <div className="max-w-7xl mx-auto relative pt-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-red-800">
            Error Loading Product
          </h3>
          <p className="text-red-600 mt-2">
            Failed to load product data. Please try again or contact support.
          </p>
          <Button
            variant="outline"
            onClick={() => navigate("/products/all")}
            className="mt-4"
          >
            Back to Products
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="page-container">
      {loading && <LoadingSpinner size={64} overlay />}
      {/* Compact Header */}
      <div className="mb-4">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex flex-col">
                <h1 className="text-xl font-semibold text-gray-900">
                  {isEditMode
                    ? t("products.editProduct")
                    : t("products.createProduct")}
                </h1>
                {/* System SKU Display - Shows after product creation or during editing */}
                {productData.system_sku && (createdProductId || isEditMode) && (
                  <div className="flex items-center gap-2 mt-1">
                    <div className="flex items-center gap-1.5 px-2 py-1 bg-green-50 border border-green-200 rounded-md">
                      <Package className="w-3.5 h-3.5 text-green-600" />
                      <span className="text-xs font-medium text-green-700">
                        System SKU:
                      </span>
                      <span className="text-xs font-mono font-semibold text-green-800">
                        {productData.system_sku}
                      </span>
                    </div>

                    {isEditMode && (
                      <div className="flex items-center gap-1 px-2 py-1 bg-orange-50 border border-orange-200 rounded-md">
                        <Package className="w-3.5 h-3.5 text-orange-600" />
                        <span className="text-xs font-medium text-orange-700">
                          Edit Mode
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>
              <div className="hidden sm:flex items-center gap-2 text-sm text-gray-500">
                <span>
                  {t("products.step")} {currentStep}
                </span>
                <span>/</span>
                <span>{STEPS.length}</span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="text-right">
                  <div className="text-lg font-bold text-blue-600">
                    {calculateProgress()}%
                  </div>
                  <div className="text-xs text-gray-500">
                    {completedSteps.size}/{STEPS.length}
                  </div>
                </div>
                <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-blue-500 to-indigo-500 transition-all duration-300"
                    style={{ width: `${calculateProgress()}%` }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Compact Step Indicator */}
      <div className="mb-4">
        <div className="bg-white rounded-lg border border-gray-200 p-3">
          <div className="hidden sm:block">
            <div className="flex items-center justify-between">
              {STEPS.map((step, index) => {
                const isAccessible = isStepAccessible(step.id);
                const tooltipMessage = getStepTooltipMessage(step.id);

                return (
                  <div key={step.id} className="flex items-center flex-1">
                    <div className="flex items-center">
                      <div className="relative group">
                        <div
                          onClick={() => handleStepClick(step.id)}
                          className={`flex items-center justify-center w-8 h-8 rounded-full transition-all duration-200 ${
                            !isAccessible
                              ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                              : currentStep === step.id
                              ? "bg-blue-600 text-white cursor-pointer"
                              : completedSteps.has(step.id)
                              ? "bg-green-600 text-white cursor-pointer"
                              : "bg-gray-200 text-gray-500 hover:bg-gray-300 cursor-pointer"
                          }`}
                          title={tooltipMessage || undefined}
                        >
                          {completedSteps.has(step.id) ? (
                            <Check className="w-4 h-4" />
                          ) : (
                            <span className="text-xs font-semibold">
                              {index + 1}
                            </span>
                          )}
                        </div>
                        {/* Tooltip for inaccessible steps */}
                        {tooltipMessage && (
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                            {tooltipMessage}
                            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                          </div>
                        )}
                      </div>
                      <div className="ml-2 hidden lg:block">
                        <p
                          className={`text-xs font-medium ${
                            !isAccessible
                              ? "text-gray-400"
                              : currentStep === step.id
                              ? "text-blue-600"
                              : "text-gray-600"
                          }`}
                        >
                          {step.title}
                        </p>
                      </div>
                    </div>
                    {index < STEPS.length - 1 && (
                      <div className="flex-1 mx-3">
                        <div
                          className={`h-0.5 rounded-full transition-all duration-300 ${
                            completedSteps.has(step.id)
                              ? "bg-green-400"
                              : "bg-gray-200"
                          }`}
                        />
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Mobile View */}
          <div className="sm:hidden">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full ${
                    currentStep === STEPS.find((s) => s.id === currentStep)?.id
                      ? "bg-blue-600 text-white"
                      : "bg-gray-200 text-gray-500"
                  }`}
                >
                  <span className="text-xs font-semibold">{currentStep}</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {STEPS.find((s) => s.id === currentStep)?.title}
                  </p>
                  <p className="text-xs text-gray-500">
                    {t("products.step")} {currentStep} {t("products.of")}{" "}
                    {STEPS.length}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-1">
                {STEPS.map((step) => {
                  const isAccessible = isStepAccessible(step.id);
                  const tooltipMessage = getStepTooltipMessage(step.id);

                  return (
                    <div
                      key={step.id}
                      onClick={() => handleStepClick(step.id)}
                      className={`w-2 h-2 rounded-full transition-all duration-200 ${
                        !isAccessible
                          ? "bg-gray-200 cursor-not-allowed"
                          : currentStep === step.id
                          ? "bg-blue-600 cursor-pointer"
                          : completedSteps.has(step.id)
                          ? "bg-green-600 cursor-pointer"
                          : "bg-gray-300 cursor-pointer"
                      }`}
                      title={tooltipMessage || undefined}
                    />
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile System SKU Display - Shows after product creation or during editing */}
      {productData.system_sku && (createdProductId || isEditMode) && (
        <div className="mb-4 sm:hidden">
          <div className="bg-white rounded-lg border border-gray-200 p-3">
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <Package className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-green-700">
                  System SKU:
                </span>
                <span className="text-sm font-mono font-semibold text-green-800">
                  {productData.system_sku}
                </span>
              </div>

              {isEditMode && (
                <div className="flex items-center gap-2">
                  <Package className="w-4 h-4 text-orange-600" />
                  <span className="text-sm font-medium text-orange-700">
                    Editing Product
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <Card className="min-h-[600px]">
        <CardHeader className="border-b border-gray-200 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CardTitle className="text-lg font-semibold">
                {STEPS.find((s) => s.id === currentStep)?.title}
              </CardTitle>
              {/* Step accessibility indicator */}
              {!isStepAccessible(currentStep) && (
                <div className="flex items-center gap-1 px-2 py-1 bg-red-50 border border-red-200 rounded-md">
                  <Lock className="w-3.5 h-3.5 text-red-600" />
                  <span className="text-xs font-medium text-red-700">
                    Locked
                  </span>
                </div>
              )}
              <div className="relative tooltip-container">
                <button
                  onClick={() => setShowTooltip(!showTooltip)}
                  className="flex items-center justify-center w-5 h-5 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                  aria-label="Step information"
                  aria-expanded={showTooltip}
                  aria-describedby={showTooltip ? "step-tooltip" : undefined}
                >
                  <HelpCircle className="w-3 h-3 text-gray-500" />
                </button>
                {showTooltip && (
                  <div
                    id="step-tooltip"
                    className="absolute left-0 top-6 z-[9999] w-80 max-w-[calc(100vw-2rem)] p-3 bg-gray-900 text-white text-sm rounded-lg shadow-lg sm:left-0 sm:w-80"
                    role="tooltip"
                  >
                    <div className="relative">
                      {stepDescriptions[currentStep]}
                      <div className="absolute -top-1 left-2 w-2 h-2 bg-gray-900 transform rotate-45"></div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          {!isStepAccessible(currentStep) ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <Lock className="w-8 h-8 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Step Not Available
              </h3>
              <p className="text-gray-600 mb-4 max-w-md">
                This step is locked until you complete the Classification step.
                The Classification step creates your product and generates a
                unique product ID required for subsequent steps.
              </p>
              <Button
                onClick={() => setCurrentStep(1)}
                className="flex items-center gap-2"
              >
                <Package className="w-4 h-4" />
                Go to Classification Step
              </Button>
            </div>
          ) : (
            <CurrentStepComponent
              data={productData}
              onChange={(newData) => {
                console.log("ProductData onChange called:", {
                  currentStep,
                  newData,
                  mediaCount: (newData.media || []).length,
                  videos: (newData.media || []).filter(
                    (item) => item.type === "video"
                  ),
                  images: (newData.media || []).filter(
                    (item) => item.type === "image"
                  ),
                });
                setProductData(newData);
              }}
              layoutMode="horizontal"
              validationErrors={validationErrors}
              showValidation={showValidation}
              onStepClick={currentStep === 7 ? handleStepClick : undefined}
              dropdownOptions={getDropdownOptions}
              fulfillmentOptions={getFulfillmentOptions}
            />
          )}
        </CardContent>
        <div className="border-t border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === STEPS[0].id}
              className="flex items-center gap-1 text-sm px-3 py-2"
            >
              <ChevronLeft className="w-4 h-4" />
              {t("products.previous")}
            </Button>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={handleSaveDraft}
                className="flex items-center gap-1 text-sm px-3 py-2"
                disabled={loading}
              >
                <Save className="w-4 h-4" />
                <span className="hidden sm:inline">
                  {t("products.saveDraft")}
                </span>
              </Button>

              {currentStep === STEPS[STEPS.length - 1].id ? (
                <Button
                  onClick={handleSubmitForApproval}
                  className="flex items-center gap-1 bg-green-600 hover:bg-green-700 text-sm px-3 py-2"
                  disabled={loading}
                >
                  <Check className="w-4 h-4" />
                  {t("products.submitForApproval")}
                </Button>
              ) : (
                <Button
                  onClick={handleNext}
                  className="flex items-center gap-1 text-sm px-3 py-2"
                  disabled={loading}
                >
                  {t("products.next")}
                  <ChevronRight className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ProductWizardPage;
