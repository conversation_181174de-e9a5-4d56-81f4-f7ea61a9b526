import { Formik, Form } from "formik";
import * as Yup from "yup";
import {
  FormInput,
  FormTextarea,
  FormSwitch,
  FormSelect,
  FormDatePicker,
  FormDateTimePicker,
  FormSwitchBadge,
  FormRadioGroup,
} from "@/components/ui/form";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import { typeOptions, statusBooleanOptions } from "@/constants/filterOption";

const CouponForm = ({ coupon, onSubmit, onCancel }) => {
  const { t } = useTranslation();

  const initialValues = coupon
    ? {
        id: coupon.id,
        title_en: coupon.title_en || "",
        title_ar: coupon.title_ar || "",
        code: coupon.code || "",
        description_en: coupon.description_en || "",
        description_ar: coupon.description_ar || "",
        type: coupon.type || "percentage",
        value: coupon.value || "",
        min_order_value: coupon.min_order_value || "",
        usage_limit: coupon.usage_limit || "",
        per_user_limit: coupon.per_user_limit || "",
        vendor_id: coupon.vendor_id || "",
        start_date: coupon.start_date ? new Date(coupon.start_date) : null,
        end_date: coupon.end_date ? new Date(coupon.end_date) : null,
        is_active: coupon.is_active || false,
      }
    : {
        title_en: "",
        title_ar: "",
        code: "",
        type: "percentage",
        value: "",
        description_en: "",
        description_ar: "",
        min_order_value: "",
        usage_limit: "",
        per_user_limit: "",
        vendor_id: "",
        start_date: null,
        end_date: null,
        is_active: false,
      };

  const validationSchema = Yup.object({
    title_en: Yup.string().required(t("commonValidation.title_en")),
    code: Yup.string().required(t("commonValidation.code")),
    type: Yup.string().required(t("commonValidation.discountType")),
    value: Yup.number()
      .typeError(t("commonValidation.discountValueError"))
      .required(t("commonValidation.discountValue")),
    min_order_value: Yup.number()
      .typeError(t("commonValidation.minOrderValueError"))
      .nullable(),
    usage_limit: Yup.number()
      .typeError(t("commonValidation.usageLimitError"))
      .nullable(),
    per_user_limit: Yup.number()
      .typeError(t("commonValidation.perUserLimitError"))
      .nullable()
      .when("usage_limit", (usage_limit, schema) => {
        return (
          usage_limit &&
          schema.max(usage_limit, t("commonValidation.perUserLimitExceeded"))
        );
      }),
    start_date: Yup.date().nullable(),
    end_date: Yup.date()
      .nullable()
      .min(Yup.ref("start_date"), t("commonValidation.endDateBeforeStart")),
    // status: Yup.string().required(t("commonValidation.status")),
  });

  // Handle form submission
  const handleSubmit = (values, { setSubmitting }) => {
    onSubmit(values);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, values }) => (
        <Form className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-3 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormInput
                  name="title_en"
                  label={t("commonField.title_en")}
                  placeholder={t("commonPlaceholder.title_enPlaceholder")}
                  required
                />
                <FormInput
                  name="title_ar"
                  label={t("commonField.title_ar")}
                  placeholder={t("commonPlaceholder.title_arPlaceholder")}
                />
              </div>
              <FormTextarea
                name="description_en"
                label={t("commonField.description_en")}
                placeholder={t("commonPlaceholder.description_enPlaceholder")}
              />
              <FormTextarea
                name="description_ar"
                label={t("commonField.description_ar")}
                placeholder={t("commonPlaceholder.description_arPlaceholder")}
              />
            </div>
            <div className="p-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormInput
                  name="code"
                  label={t("commonField.code")}
                  placeholder={t("commonPlaceholder.codePlaceholder")}
                  required
                />
                <FormSelect
                  name="type"
                  label={t("commonField.discountType")}
                  options={typeOptions}
                  required
                />
                <FormInput
                  name="value"
                  label={t("commonField.discountValue")}
                  placeholder={t("commonPlaceholder.discountValuesPlaceholder")}
                  required
                />
                <FormInput
                  name="min_order_value"
                  label={t("commonField.minOrderValue")}
                  placeholder={t("commonPlaceholder.minOrderValuePlaceholder")}
                />
                <FormInput
                  name="usage_limit"
                  label={t("commonField.usageLimit")}
                  placeholder={t("commonPlaceholder.usageLimitPlaceholder")}
                />
                <FormInput
                  name="per_user_limit"
                  label={t("commonField.perUserLimit")}
                  placeholder={t("commonPlaceholder.perUserLimitPlaceholder")}
                />
                <FormDateTimePicker
                  name="start_date"
                  label={t("commonField.start_date")}
                  minDate={new Date()}
                />
                <FormDateTimePicker
                  name="end_date"
                  label={t("commonField.end_date")}
                  minDate={values.start_date}
                />
                <FormRadioGroup
                  name="is_active"
                  label={t("commonField.status")}
                  options={statusBooleanOptions}
                  required
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end pt-4 border-t border-gray-200 mt-8 gap-3">
            <Button type="button" variant="outline" onClick={onCancel}>
              {t("commonButton.cancel")}
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {coupon
                ? t("commonButton.coupon.updated")
                : t("commonButton.coupon.create")}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default CouponForm;
