import { useState } from "react";
import SearchInput from "@/components/ui/SearchInput";
import FilterDropdown from "@/components/ui/FilterDropdown";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import { booleanStatusOptions } from "@/constants/filterOption";

const CouponFilter = ({ onChange }) => {
  const { t } = useTranslation();
  const [filters, setFilters] = useState({
    search: "",
    is_active: "",
  });

  const triggerChange = (updated) => {
    const payload = {};
    if (updated.search && updated.search.trim())
      payload.search = updated.search.trim();
    payload.is_active = String(updated.is_active);
    onChange?.(payload);
  };

  const handleSearchChange = (val) => {
    const updated = { ...filters, search: val };
    setFilters(updated);
    triggerChange(updated);
  };

  const handleFilterChange = (key, value) => {
    const updated = { ...filters, [key]: value };
    setFilters(updated);
    triggerChange(updated);
  };

  const handleReset = () => {
    const reset = { search: "", is_active: "" };
    setFilters(reset);
    onChange?.({});
  };

  return (
    <div className="mb-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
        <div className="flex-1 max-w-md">
          <SearchInput
            value={filters.search}
            onChange={handleSearchChange}
            placeholder={t("coupon.searchPlaceholder")}
          />
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <FilterDropdown
            label={t("commonField.status")}
            className="w-40"
            options={booleanStatusOptions}
            selectedValues={filters.is_active}
            onChange={(value) => handleFilterChange("is_active", value)}
          />
          <Button variant="outline" onClick={handleReset} className="ml-2">
            {t("commonButton.reset")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CouponFilter;
