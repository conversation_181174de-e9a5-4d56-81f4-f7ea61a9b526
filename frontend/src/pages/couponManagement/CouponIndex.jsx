import { useState, useCallback, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { toast } from "sonner";
import { FaPlus, FaEdit, FaTrash, FaEye } from "react-icons/fa";
import { RiListCheck3 } from "react-icons/ri";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import CouponFilters from "./CouponFilter";
import { useTranslation } from "react-i18next";
import StatusBadge from "@/components/common/StatusBadge";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import PreviewSection from "./CouponPreview";

const CouponIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { delete: removeCoupon } = useApi();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
  } = usePagination(1, 10);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    is_active: "",
  });

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedCoupon, setSelectedCoupon] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [previewId, setPreviewId] = useState(null);

  const apiParams = useMemo(
    () => ({
      pagination: true,
      per_page: itemsPerPage,
      page: currentPage,
      search: filterOptions.search || "",
      is_active: filterOptions.is_active || "",
    }),
    [itemsPerPage, currentPage, filterOptions]
  );

  const {
    data: couponData,
    isLoading: copuponsLoading,
    refetch: refetchCoupons,
  } = fetchData("admin/coupons", apiParams);

  const couponList = couponData?.data?.data || [];
  const paginationInfo = {
    currentPage: couponData?.data?.current_page || 1,
    perPage: couponData?.data?.per_page || itemsPerPage,
    totalItems: couponData?.data?.total || 0,
    totalPages: couponData?.data?.last_page || 1,
  };

  const handleFilterChange = useCallback(
    (newFilters) => {
      setFilterOptions((prevFilters) => {
        const searchChanged = prevFilters.search !== newFilters.search;
        const statusesChanged =
          JSON.stringify(prevFilters.is_active) !==
          JSON.stringify(newFilters.is_active);

        if (searchChanged || statusesChanged) {
          setCurrentPage(1);
          return newFilters;
        }

        return prevFilters;
      });
    },
    [setCurrentPage]
  );

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditCoupon = (coupon) => {
    navigate(`/coupon/edit/${coupon.id}`);
  };

  const handlePreviewClick = (coupon) => {
    setPreviewId(coupon.id);
  };

  const handleDeleteClick = (coupon) => {
    setSelectedCoupon(coupon);
    setDeleteModalOpen(true);
  };

  const handleDeleteCoupon = async () => {
    if (!selectedCoupon) return;
    setDeleteLoading(true);
    removeCoupon(`admin/coupons/${selectedCoupon.id}`)
      .then(() => {
        setDeleteModalOpen(false);
        setSelectedCoupon(null);
        refetchCoupons();
        toast.success(t("commonToast.couponToast.couponDelete"));
      })
      .finally(() => setDeleteLoading(false));
  };

  const canEditWarehouse = (coupon) => true;

  const canDeleteWarehouse = (coupon) => true;

  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },

    {
      header: t("commonTableLabel.title_en"),
      accessor: "title_en",
    },
    {
      header: t("commonTableLabel.title_ar"),
      accessor: "title_ar",
    },
    {
      header: t("commonTableLabel.type"),
      accessor: "type",
    },
    {
      header: t("commonTableLabel.code"),
      accessor: "code",
    },
    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row} fieldName="is_active" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("coupon.previewClassContent")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditCoupon(row)}
            disabled={!canEditWarehouse(row)}
            title={
              !canEditWarehouse(row)
                ? "You don't have permission to edit this Coupon"
                : t("coupon.editAction")
            }
          >
            <FaEdit className="icon-edit" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteWarehouse(row)}
            title={
              !canDeleteWarehouse(row)
                ? "You can't delete this Coupon"
                : t("coupon.deleteAction")
            }
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="page-container">
      {copuponsLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("coupon.title")}
          icon={<RiListCheck3 className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => navigate("/coupon/add")}
            >
              <FaPlus />
              {t("coupon.add")}
            </Button>
          }
        >
          <CouponFilters onChange={handleFilterChange} />
          <Table
            columns={columns}
            data={couponList}
            emptyMessage={t("coupon.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      {/* PreView Page  */}
      <Modal
        isOpen={previewId}
        onClose={() => setPreviewId(null)}
        title={t("coupon.previewClassContent")}
        size="xl"
        showCloseButton={true}
      >
        <PreviewSection
          couponId={previewId}
          onClose={() => setPreviewId(null)}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onDelete={handleDeleteCoupon}
        loading={deleteLoading}
        itemName={t("coupon.coupon")}
        itemValue={selectedCoupon?.title_en}
      />
    </div>
  );
};

export default CouponIndex;
