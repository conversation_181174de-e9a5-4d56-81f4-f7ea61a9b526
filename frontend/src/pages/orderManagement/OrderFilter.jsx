import { useState } from "react";
import SearchInput from "@/components/ui/SearchInput";
import FilterDropdown from "@/components/ui/FilterDropdown";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import {
  orderStatusOptions,
  paymentStatusOptions,
  // paymentMethodOptions,
  orderValueOptions,
  dateRangeOptions,
} from "@/constants/filterOption";

const OrderFilter = ({ onChange }) => {
  const { t } = useTranslation();
  const [filters, setFilters] = useState({
    search: "",
    status: "",
    payment_status: "",
    // payment_method: "",
    order_value: "",
    date_range: "",
  });

  const triggerChange = (updated) => {
    const payload = {};
    if (updated.search.trim()) payload.search = updated.search.trim();
    if (updated.status) payload.status = updated.status;
    if (updated.payment_status) payload.payment_status = updated.payment_status;
    // if (updated.payment_method) payload.payment_method = updated.payment_method;
    if (updated.order_value) payload.order_value = updated.order_value;
    if (updated.date_range) payload.date_range = updated.date_range;
    onChange?.(payload);
  };

  const handleSearchChange = (val) => {
    const updated = { ...filters, search: val };
    setFilters(updated);
    triggerChange(updated);
  };

  const handleFilterChange = (key, values) => {
    const updated = { ...filters, [key]: values };
    setFilters(updated);
    triggerChange(updated);
  };

  const handleReset = () => {
    const reset = {
      search: "",
      status: "",
      payment_status: "",
      // payment_method: "",
      order_value: "",
      date_range: "",
    };
    setFilters(reset);
    onChange?.({});
  };

  return (
    <div className="mb-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
        <div className="flex-1 max-w-md me-1">
          <SearchInput
            value={filters.search}
            onChange={handleSearchChange}
            placeholder={t("order.searchPlaceholder")}
          />
        </div>

        <div className="flex flex-wrap items-center gap-2">
          <FilterDropdown
            label={t("commonTableLabel.orderState")}
            className="w-40"
            options={orderStatusOptions}
            selectedValues={filters.status}
            onChange={(values) => handleFilterChange("status", values)}
          />
          <FilterDropdown
            label={t("commonTableLabel.paymentStatus")}
            className="w-40"
            options={paymentStatusOptions}
            selectedValues={filters.payment_status}
            onChange={(values) => handleFilterChange("payment_status", values)}
          />
          {/* <FilterDropdown
            label={t("commonTableLabel.paymentMethod")}
            className="w-40"
            options={paymentMethodOptions}
            selectedValues={filters.payment_method}
            onChange={(values) => handleFilterChange("payment_method", values)}
          /> */}
          <FilterDropdown
            label={t("commonTableLabel.dateRange")}
            className="w-40"
            options={dateRangeOptions}
            selectedValues={filters.date_range}
            onChange={(values) => handleFilterChange("date_range", values)}
          />
          <FilterDropdown
            label={t("commonTableLabel.orderValue")}
            className="w-40"
            options={orderValueOptions}
            selectedValues={filters.order_value}
            onChange={(values) => handleFilterChange("order_value", values)}
          />
          <Button variant="outline" onClick={handleReset} className="ml-2">
            {t("commonButton.reset")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default OrderFilter;
