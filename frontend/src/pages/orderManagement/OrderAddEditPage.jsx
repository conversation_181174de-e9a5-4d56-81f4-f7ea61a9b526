import { useParams, useNavigate } from "react-router-dom";
import { useState } from "react";
import { toast } from "sonner";
import OrderFormPage from "./OrderForm";
import { useApi } from "@/hooks/useApi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useTranslation } from "react-i18next";
import BackArrowIcon from "@/components/common/BackArrowIcon";

const OrderAddEditPage = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const { fetchData, postMutation, putMutation } = useApi();

  const { data, isLoading, isError, refetch } = id
    ? fetchData(`admin/orders/${id}`)
    : { data: null, isLoading: false, isError: false, refetch: () => {} };
  const orderData = data?.data;

  const handleSubmit = (values) => {
    setLoading(true);
    const dataToSend = { ...values };

    const onSuccess = () => {
      toast.success(
        t(
          id
            ? "commonToast.orderToast.orderUpdate"
            : "commonToast.orderToast.orderCreate"
        )
      );

      navigate("/order/list");
    };

    const onError = (error) => {
      console.error(
        "Order update failed:",
        error?.response?.data || error.message
      );
      toast.error(
        "Order update failed: " +
          (error?.response?.data?.message || error.message)
      );
    };

    const onSettled = () => setLoading(false);

    if (!id) {
      postMutation.mutate(
        { endpoint: "admin/orders", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        {
          endpoint: `admin/orders/${id}`,
          data: dataToSend,
        },
        { onSuccess, onError, onSettled }
      );
    }
  };

  if (isLoading || loading) {
    return (
      <div className="loading-error">
        <LoadingSpinner />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="loading-error">
        <div className="text-red-500">
          Error loading Order data. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="page-container">
      <div className="card-style">
        <div className="title-add-edit-card">
          <div className="title-add-edit-div">
            <button onClick={() => navigate("/order/list")}>
              <BackArrowIcon />
            </button>
            <h3 className="title-add-edit">
              {t(id ? "order.editAction" : "order.add")}
            </h3>
          </div>
        </div>
        <div className="px-6 py-4">
          <OrderFormPage
            order={orderData}
            onSubmit={handleSubmit}
            onCancel={() => navigate("/order/list")}
            loading={loading}
          />
        </div>
      </div>
    </div>
  );
};

export default OrderAddEditPage;
