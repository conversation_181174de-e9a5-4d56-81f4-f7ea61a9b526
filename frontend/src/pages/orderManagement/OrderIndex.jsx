import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import OrderFilterPage from "./OrderFilter";
import { FaEdit, FaTrash, Fa<PERSON><PERSON><PERSON><PERSON>ist, <PERSON>a<PERSON>ye } from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import { capitalizeFirstLetter } from "@/helper/Commonhelper";
import { renderNA } from "@/helper/commonFunctionHelper";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import {
  PackageIcon,
  UserIcon,
  CardItCardIcon,
  DollarIcon,
  CalendarIcon,
  BankIcon,
  WalletIcon,
} from "@/components/common/CustomIcons";
import { onlyForDateFormat, onlyForTimeFormat } from "@/hooks/utility";
import ApprovalStatusBadge from "@/components/common/ApprovalStatusBadge";
import PaymentStatusBadge from "@/components/common/PaymentStatusBadge";
import OrderIndexCardPage from "./OrderIndexCard";

const OrderIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [previewOrder, setPreviewOrder] = useState(null);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: "",
    payment_status: "",
    // payment_method: "",
    order_value: "",
    date_range: "",
  });

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: orderData,
    isLoading,
    isError: orderError,
    refetch,
  } = fetchData("admin/orders", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    status: filterOptions.status || "",
    payment_status: filterOptions.payment_status || "",
    // payment_method: filterOptions.payment_method || "",
    order_value: filterOptions.order_value || "",
    date_range: filterOptions.date_range || "",
  });

  const orderList = orderData?.data?.data || [];
  const paginationInfo = {
    currentPage: orderData?.data?.current_page || 1,
    perPage: orderData?.data?.per_page || itemsPerPage,
    totalItems: orderData?.data?.total_items || 0,
    totalPages: orderData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditOrder = (row) => {
    navigate(`/order/edit/${row.uuid}`);
  };

  const handlePreviewClick = (row) => {
    navigate(`/order/preview/${row.uuid}`);
  };

  const handleDeleteClick = (order) => {
    setSelectedOrder(order);
  };

  const handleDeleteOrder = async () => {
    if (!selectedOrder) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/orders/${selectedOrder.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setSelectedOrder(null);
          refetch();
          toast.success(t("commonToast.orderToast.orderDelete"));
        },
        onError: (error) => {
          console.error(
            "Order deletion failed:",
            error?.response?.data || error.message
          );
          toast.error(
            "Order deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const canEditOrder = (order) => true;

  const canDeleteOrder = (order) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.order"),
      accessor: "order_number",
      render: (row) => (
        <div className="space-y-1">
          <div className="font-semibold text-base">
            {renderNA(row?.order_number)}
          </div>
          <div className="flex items-center gap-2">
            <PackageIcon
              width={15}
              height={15}
              className="text-primary-400"
              viewBox="0 0 24 24"
            />
            <div className="flex items-center gap-1">
              <span className="text-sm font-semibold">
                {renderNA(row?.items_count)}
              </span>
              <span>{t("order.orderItem")}</span>
              <span>(</span>
              <span>{renderNA(row?.total_quantity)}</span>
              <span className="text-sm font-semibold">
                {t("order.orderQty")}
              </span>
              <span>)</span>
            </div>
          </div>
          <div className="flex gap-2">
            <span>{t("order.track")}</span>
            <span>:</span>
            <span className="text-sm font-medium">
              {renderNA(row?.tracking_number)}
            </span>
          </div>
        </div>
      ),
    },
    {
      header: t("commonTableLabel.customer"),
      accessor: "customer_name",
      render: (row) => (
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <UserIcon
              width={15}
              height={15}
              className="text-primary-400"
              viewBox="0 0 24 24"
            />
            <div className="font-semibold text-base">
              {renderNA(row?.customer_name)}
            </div>
          </div>
          <div className="text-sm font-medium">
            <span>{renderNA(row?.customer_email)}</span>
          </div>
        </div>
      ),
    },
    {
      header: t("commonTableLabel.vendor"),
      accessor: "vendor_name",
      render: (row) => renderNA(capitalizeFirstLetter(row?.vendor_name)),
    },
    {
      header: t("commonTableLabel.amount"),
      render: (row) => (
        <div className="space-y-1">
          <div className="font-semibold text-base flex gap-1 items-center">
            <span>{renderNA(row?.currency)}</span>
            <span>{":"}</span>
            <span>{renderNA(row?.total)}</span>
          </div>
          <div className="flex items-center gap-1">
            {row?.payment_method === "cod" ? (
              <DollarIcon
                width={15}
                height={15}
                className="text-primary-400"
                viewBox="0 0 24 24"
              />
            ) : row?.payment_method === "card" ? (
              <CardItCardIcon
                width={15}
                height={15}
                className="text-primary-400"
                viewBox="0 0 24 24"
              />
            ) : row?.payment_method === "bank" ? (
              <BankIcon
                width={15}
                height={15}
                className="text-primary-400"
                viewBox="0 0 24 24"
              />
            ) : row?.payment_method === "wallet" ? (
              <WalletIcon
                width={15}
                height={15}
                className="text-primary-400"
                viewBox="0 0 24 24"
              />
            ) : (
              renderNA
            )}

            <div className="flex items-center gap-1">
              <span className="text-sm font-light">
                {row?.payment_method === "cod"
                  ? "Cash On Delivery"
                  : row?.payment_method === "card"
                  ? "Credit/Debit Card"
                  : row?.payment_method === "bank"
                  ? "Bank Transfer"
                  : row?.payment_method === "wallet"
                  ? "Digital Wallet"
                  : renderNA(capitalizeFirstLetter(row?.payment_method))}
              </span>
            </div>
          </div>
        </div>
      ),
    },
    // {
    //   header: t("commonTableLabel.time"),
    //   render: (row) => (
    //     <div className="space-y-1">
    //       <div className="flex items-center gap-2">
    //         <CalendarIcon
    //           width={15}
    //           height={15}
    //           className="text-primary-400"
    //           viewBox="0 0 24 24"
    //         />
    //         <div className="flex items-center gap-1">
    //           <div className="text-sm font-semibold">
    //             {renderNA(onlyForDateFormat(row?.created_at))}
    //           </div>
    //         </div>
    //       </div>
    //       <div className="flex gap-2">
    //         <div className="text-sm font-medium">
    //           {renderNA(onlyForTimeFormat(row?.created_at))}
    //         </div>
    //       </div>
    //     </div>
    //   ),
    // },
    {
      header: t("commonTableLabel.status"),
      render: (row) => (
        <ApprovalStatusBadge data={row} fieldName="status_display" />
      ),
    },
    {
      header: t("commonTableLabel.paymentStatus"),
      render: (row) => (
        <PaymentStatusBadge data={row} fieldName="payment_status" />
      ),
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("order.previewOrder")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditOrder(row)}
            disabled={!canEditOrder(row)}
            title={
              !canEditOrder(row)
                ? "You don't have permission to edit this Order"
                : t("order.editAction")
            }
          >
            <FaEdit className="icon-edit" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteOrder(row)}
            title={
              !canDeleteOrder(row)
                ? "You can't delete this Order"
                : t("order.deleteAction")
            }
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="page-container">
      <>
        <OrderIndexCardPage />
      </>

      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("order.title")}
          icon={<FaClipboardList className="title-icon" />}
        >
          <OrderFilterPage onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={orderList}
            emptyMessage={t("order.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>
      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={!!selectedOrder}
        onClose={() => setSelectedOrder(null)}
        onDelete={handleDeleteOrder}
        loading={deleteLoading}
        itemName={t("order.order")}
        itemValue={selectedOrder?.name_en}
      />
    </div>
  );
};

export default OrderIndex;
