import { fetchData } from "@/hooks/useApi";
import { renderNA } from "@/helper/commonFunctionHelper";
import { Package, DollarSign, Clock, CheckCircle } from "lucide-react";
import { useTranslation } from "react-i18next";

export default function OrderIndexCard() {
  const { t } = useTranslation();
  const {
    data: orderIndexCardData,
    isLoading,
    isError,
  } = fetchData("admin/orders/analytics");
  const orderData = orderIndexCardData?.data;

  const stats = [
    {
      label: t("order.totalOrders"),
      value: renderNA(orderData?.total_orders),
      icon: <Package className="w-5 h-5 text-white" />,
      iconBg: "bg-purple-500",
      trend: "+12% from last month",
      trendColor: "text-gray-500",
      bg: "bg-white",
      borderBG: "border-purple-500",
    },
    {
      label: t("order.totalRevenue"),
      value: renderNA(
        orderData?.total_revenue ? `AED ${orderData.total_revenue}` : 0
      ),
      icon: <DollarSign className="w-5 h-5 text-white" />,
      iconBg: "bg-green-500",
      trend: "+8% from last month",
      trendColor: "text-gray-500",
      bg: "bg-white",
      borderBG: "border-green-500",
    },
    {
      label: t("order.pendingOrders"),
      value: renderNA(orderData?.pending_orders),
      icon: <Clock className="w-5 h-5 text-white" />,
      iconBg: "bg-yellow-400",
      trend: "-5% from last week",
      trendColor: "text-gray-500",
      bg: "bg-white",
      borderBG: "border-yellow-400",
    },
    {
      label: t("order.deliveredOrders"),
      value: renderNA(orderData?.delivered_orders),
      icon: <CheckCircle className="w-5 h-5 text-white" />,
      iconBg: "bg-blue-500",
      trend: "+15% from last month",
      trendColor: "text-gray-500",
      bg: "bg-white",
      borderBG: "border-blue-500",
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
      {stats.map((stat, i) => (
        <div
          key={i}
          className={`rounded-lg shadow p-5 border-t-4 space-y-2 hover:shadow-lg ${stat.bg} ${stat.borderBG}`}
        >
          <div className="flex items-center justify-between">
            <div className="text-gray-600 font-medium text-base">
              {stat.label}
            </div>
            <div
              className={`${stat.iconBg} rounded-xl p-3 flex items-center justify-center`}
            >
              {stat.icon}
            </div>
          </div>
          <div className="text-xl font-bold">{stat.value}</div>
          {/* <div className={`my-2 text-xs ${stat.trendColor}`}>
            ↗ {stat.trend}
          </div> */}
        </div>
      ))}
    </div>
  );
}
