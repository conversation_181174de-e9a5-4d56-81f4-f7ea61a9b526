import React from "react";
import { CreditCard, Cog, PackageCheck, Truck } from "lucide-react";

function Badge({ children }) {
  return (
    <span className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium leading-none">
      {children}
    </span>
  );
}

function TimelineItem({
  icon: Icon = Cog,
  title,
  badge,
  description,
  actor,
  timestamp,
  isLast,
}) {
  return (
    <div className="grid grid-cols-[48px_1fr_auto] gap-3">
      <div className="relative flex items-start justify-center">
        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-slate-100 ring-1 ring-slate-200">
          <Icon className="h-5 w-5 text-slate-700" />
        </div>
        {!isLast && (
          <span
            className="absolute left-1/2 w-px bg-slate-200"
            style={{ top: 40, bottom: -8, transform: "translateX(-50%)" }}
          />
        )}
      </div>
      <div className="pb-6">
        <div className="flex items-center gap-2">
          <div className="font-semibold text-slate-900">{title}</div>
          {badge && <Badge>{badge}</Badge>}
        </div>
        <div className="mt-1 text-slate-600">{description}</div>
        <div className="mt-2 text-sm text-slate-400">by {actor}</div>
      </div>
      <div className="pt-1 text-right text-sm text-slate-500">{timestamp}</div>
    </div>
  );
}

export const sampleTimelineEvents = [
  {
    icon: CreditCard,
    title: "Payment Authorized",
    badge: "payment",
    description:
      "Credit card payment of $369.34 has been authorized successfully",
    actor: "Customer",
    timestamp: "12/01/2025, 16:30:15",
  },
  {
    icon: Cog,
    title: "Order Created",
    badge: "system",
    description:
      "Order #ORD-2025-000134 has been created and split between 2 vendors",
    actor: "System",
    timestamp: "12/01/2025, 16:30:30",
  },
  {
    icon: PackageCheck,
    title: "Vendor Accepted Order",
    badge: "fulfillment",
    description: "Electronics Pro has accepted their portion of the order",
    actor: "Electronics Pro",
    timestamp: "12/01/2025, 17:00:00",
  },
  {
    icon: Truck,
    title: "Items Shipped",
    badge: "fulfillment",
    description:
      "Package shipped via FedEx Ground with tracking #1234567890123456",
    actor: "Electronics Pro",
    timestamp: "12/01/2025, 22:45:00",
  },
];

export default function OrderTimeline({ events = sampleTimelineEvents }) {
  const safeEvents = Array.isArray(events) ? events : sampleTimelineEvents;
  return (
    <div className="rounded-2xl border border-slate-200 bg-white p-6">
      <div className="text-slate-900 font-bold my-5">Order Timeline</div>
      {safeEvents.length === 0 ? (
        <div className="text-sm text-slate-500">No timeline events yet.</div>
      ) : (
        <div className="space-y-0">
          {safeEvents.map((e, i) => (
            <TimelineItem
              key={i}
              icon={e.icon}
              title={e.title}
              badge={e.badge}
              description={e.description}
              actor={e.actor}
              timestamp={e.timestamp}
              isLast={i === safeEvents.length - 1}
            />
          ))}
        </div>
      )}
    </div>
  );
}
