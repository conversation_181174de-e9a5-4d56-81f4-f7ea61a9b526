import Button from "@/components/ui/Button";
import { CreditCard, Globe, MapPin, MessageSquare, Phone } from "lucide-react";
import React from "react";

const OrderPreviewCard = () => {
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="border border-gray-300 rounded-2xl p-5">
          <div className="flex items-center justify-between mb-3">
            <div className="text-slate-900 font-bold">Customer</div>
            <MessageSquare className="h-5 w-7 text-gray-700" />
          </div>
          <div className="text-lg font-semibold"><PERSON></div>
          <div className="text-sm font-light"><EMAIL></div>
          <div className="flex items-center text-lg font-normal my-3 gap-2">
            <Phone className="h-4 w-4 text-slate-700" />
            <div className="">+****************</div>
          </div>
        </div>
        <div className="border border-gray-300 rounded-2xl p-5">
          <div className="flex items-center justify-between mb-3">
            <div className="text-slate-900 font-bold">Shipping Address</div>
            <MapPin className="h-5 w-7 text-gray-700" />
          </div>
          <div className="text-lg font-semibold">Sarah Johnson</div>
          <div className="text-sm font-light">
            123 Main Street, Apt 4B New York, NY 10001 United States
          </div>
          <div className="my-3">
            <Button className="w-full" type="button" variant="secondary">
              Edit Address
            </Button>
          </div>
        </div>
        <div className="border border-gray-300 rounded-2xl p-5">
          <div className="flex items-center justify-between mb-3">
            <div className="text-slate-900 font-bold">Payment</div>
            <CreditCard className="h-5 w-7 text-gray-700" />
          </div>
          <div className="text-lg font-semibold">Visa •••• 4242</div>
          <div className="text-sm font-light">TX: 90abcdef</div>
        </div>
        <div className="border border-gray-300 rounded-2xl p-5">
          <div className="flex items-center justify-between mb-3">
            <div className="text-slate-900 font-bold">Order Details</div>
            <Globe className="h-5 w-7 text-gray-700" />
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="text-slate-900 font-light">Currency:</div>
              <div className="text-slate-900 font-bold">UCD</div>
            </div>
            <div className="flex items-center justify-between">
              <div className="text-slate-900 font-light">Coupon:</div>
              <div className="text-slate-900 font-bold border border-slate-300 px-2 py-1 rounded-full text-sm">
                SAVE20
              </div>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="text-slate-900 font-light">Channel:</div>
            <div className="text-slate-900 font-bold">Direct</div>
          </div>
          <div className="flex items-center justify-between">
            <div className="text-slate-900 font-light">Device:</div>
            <div className="text-slate-900 font-bold">Desktop Chrome</div>
          </div>
        </div>
      </div>
    </>
  );
};

export default OrderPreviewCard;
