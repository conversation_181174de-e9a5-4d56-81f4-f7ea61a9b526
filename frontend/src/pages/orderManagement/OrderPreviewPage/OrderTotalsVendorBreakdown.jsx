import Button from "@/components/ui/Button";
import { Globe, Package } from "lucide-react";
import React from "react";

const OrderTotalsVendorBreakdown = () => {
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="border border-gray-300 rounded-2xl p-5">
          <div className="flex items-center justify-between mb-3">
            <div className="text-slate-900 font-bold">Order Totals</div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="text-slate-900 font-light">Subtotal:</div>
              <div className="text-slate-900 font-medium">$345.97</div>
            </div>
            <div className="flex items-center justify-between">
              <div className="text-slate-900 font-light">Shipping</div>
              <div className="text-slate-900 font-medium">$12.99</div>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="text-slate-900 font-light">Discount</div>
            <div className="text-slate-900 font-medium">-$5.00</div>
          </div>
          <div className="flex items-center justify-between">
            <div className="text-slate-900 font-light">Tax</div>
            <div className="text-slate-900 font-medium">$12.00</div>
          </div>
          <div className="flex items-center justify-between">
            <div className="text-slate-900 font-light">Processing Fees</div>
            <div className="text-slate-900 font-medium">$2.50</div>
          </div>

          <div className="flex items-center justify-between py-3 my-3 border-gray-300 border-y">
            <div className="text-slate-900 font-semibold">Total</div>
            <div className="text-slate-900 font-bold">$2.50</div>
          </div>

          <div className="flex items-center justify-between my-2">
            <div className="text-slate-900 font-light">Paid</div>
            <div className="text-slate-900 font-medium">$366.50</div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-6 mb-3">
            <Button className="gap-2" variant="secondary" size="md">
              <Package className="text-slate-700" />
              Download Invoice
            </Button>
            <Button className="gap-2" variant="secondary" size="md">
              <Package className="text-slate-700" />
              Print Packing Slip
            </Button>
          </div>
        </div>

        <div className="border border-gray-300 rounded-2xl p-5 space-y-2">
          <div className="flex items-center justify-between mb-3">
            <div className="text-slate-900 font-bold">Vendor Breakdown</div>
          </div>
          <div className="border border-gray-200 p-2 rounded-md">
            <div className="text-slate-900 font-bold my-2">Electronics Pro</div>
            <div className="flex items-center justify-between">
              <div className="text-slate-900 font-light">Subtotal</div>
              <div className="text-slate-900 font-medium">$299.98</div>
            </div>
            <div className="flex items-center justify-between">
              <div className="text-slate-900 font-light">Shipping</div>
              <div className="text-slate-900 font-medium">$8.99</div>
            </div>
            <div className="flex items-center justify-between">
              <div className="text-slate-900 font-light">Tax</div>
              <div className="text-slate-900 font-medium">$9.60</div>
            </div>
            <div className="flex items-center justify-between py-3 my-3 border-gray-300 border-t">
              <div className="text-slate-900 font-semibold">Total</div>
              <div className="text-slate-900 font-medium">$2.50</div>
            </div>
          </div>

          <div className="border border-gray-200 p-2 rounded-md">
            <div className="text-slate-900 font-bold my-2">
              Home & Garden Supply
            </div>
            <div className="flex items-center justify-between">
              <div className="text-slate-900 font-light">Subtotal</div>
              <div className="text-slate-900 font-medium">$299.98</div>
            </div>
            <div className="flex items-center justify-between">
              <div className="text-slate-900 font-light">Shipping</div>
              <div className="text-slate-900 font-medium">$8.99</div>
            </div>
            <div className="flex items-center justify-between">
              <div className="text-slate-900 font-light">Tax</div>
              <div className="text-slate-900 font-medium">$9.60</div>
            </div>
            <div className="flex items-center justify-between py-3 my-3 border-gray-300 border-t">
              <div className="text-slate-900 font-semibold">Total</div>
              <div className="text-slate-900 font-medium">$2.50</div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default OrderTotalsVendorBreakdown;
