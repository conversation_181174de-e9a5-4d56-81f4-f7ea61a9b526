import Button from "@/components/ui/Button";
import Table from "@/components/ui/Table";
import {
  Download,
  Package,
  ChevronDown,
  Image as ImageIcon,
} from "lucide-react";
import React from "react";

const OrderVendorCard = () => {
  const columns = [
    { header: "Product", accessor: "product" },
    { header: "Ordered", accessor: "ordered" },
    { header: "Fulfilled", accessor: "fulfilled" },
    { header: "Unit Price", accessor: "unit_price" },
    { header: "Discount", accessor: "discount" },
    { header: "Tax", accessor: "tax" },
    { header: "Total", accessor: "total" },
  ];

  const data = [
    {
      id: "WBH-001",
      product: (
        <div className="flex items-start gap-4">
          <ImageIcon className="text-slate-700 w-10 h-10" />
          <div>
            <div className="font-semibold text-slate-900">
              Ceramic Plant Pot with Drainage
            </div>
            <div className="text-slate-500">WBH-001</div>
            <div className="text-slate-500">Color: Black, Size: Standard</div>
          </div>
        </div>
      ),
      ordered: 2,
      fulfilled: 2,
      unit_price: "$99.99",
      discount: "-",
      tax: "$8.00",
      total: "$207.98",
    },
  ];

  return (
    <div className="bg-white rounded-lg shadow-xl border border-gray-300">
      <div className="title-add-edit-card flex items-center justify-between w-full">
        <div className="title-add-edit-div flex items-center justify-start w-full">
          <div className="flex gap-2">
            {/* <button>
              <ChevronDown className="text-slate-700" />
            </button> */}

            <div className="flex gap-3 items-center justify-center">
              {/* <ImageIcon className="text-slate-700 w-10 h-10" /> */}
              <div>
                <div className="text-base font-bold text-slate-900 rounded-md">
                  Home & Garden Supply
                </div>
                <div className="text-sm font-light text-slate-900 rounded-md">
                  ID: Vendor-12ASD34
                </div>
              </div>
            </div>
            <div className="flex items-center justify-center">
              <div className="bg-green-100 text-green-500 border border-green-200 rounded-full flex items-center px-2 text-sm">
                Shipped
              </div>
            </div>
            <div className="flex items-center justify-center">
              <div className="px-2 text-sm font-light">2 items</div>
            </div>
            <div className="flex items-center justify-center">
              <div className="px-2 text-sm font-light">Qty: 3</div>
            </div>
            <div className="flex items-center justify-center">
              <div className="px-2 text-sm font-medium">$299.98</div>
            </div>
            <div className="flex items-center justify-center">
              <div className="text-gray-500 border border-gray-200 rounded-full flex items-center px-2 text-sm font-semibold">
                Ship by 01/01/2025
              </div>
            </div>
          </div>
        </div>

        <Button
          className="gap-2 whitespace-nowrap mx-2"
          variant="secondary"
          size="md"
        >
          <Package className="text-slate-700" />
          Issue Refund
        </Button>
        <Button
          className="gap-2 whitespace-nowrap"
          variant="secondary"
          size="md"
        >
          <Download className="text-slate-700" />
          Invoice
        </Button>
      </div>

      <div className="my-4 p-5 border-b border-gray-400">
        {/* <div className="text-slate-900 font-bold mb-4">Line Items</div> */}
        <Table columns={columns} data={data} />
      </div>

      <div className="grid grid-cols-1 gap-2 my-4 p-5">
        <div className="text-slate-900 font-bold mb-4">
          Fulfillment & Tracking
        </div>
        <div className="rounded-2xl border border-slate-200 bg-white p-5">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-slate-100 ring-1 ring-slate-200">
                <Package className="h-5 w-5 text-slate-700" />
              </div>
              <div>
                <div className="text-lg font-semibold text-slate-900">
                  FedEx - Ground
                </div>
                <div className="text-slate-500">123456789</div>
              </div>
            </div>
            <span className=" bg-green-100 text-green-600 border border-green-200 rounded-full px-3 py-1 text-xs font-semibold leading-none whitespace-nowrap">
              In Transit
            </span>
          </div>

          <div className="mt-4 flex items-center gap-8 text-slate-500">
            <div>
              <span className="font-medium text-slate-600">Packages:</span> 1
            </div>
            <div>
              <span className="font-medium text-slate-600">Weight:</span> 2.5
              lbs
            </div>
            <div className="ml-auto">
              <span className="font-medium text-slate-600">ETA:</span>{" "}
              01/01/2026
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderVendorCard;
