// import React, { useMemo, useState } from "react";
// import { Pin, Plus } from "lucide-react";

// function SegmentedTabs({ value, onChange }) {
//   const tabs = [
//     { id: "internal", label: "Internal Notes" },
//     { id: "customer", label: "Customer Messages" },
//     { id: "vendor", label: "Vendor Messages" },
//   ];
//   return (
//     <div className="w-full max-w-3xl">
//       <div className="flex rounded-xl bg-slate-100 p-1">
//         {tabs.map((t) => (
//           <button
//             key={t.id}
//             onClick={() => onChange(t.id)}
//             className={[
//               "flex-1 rounded-lg px-4 py-2 text-sm font-semibold transition",
//               value === t.id
//                 ? "bg-white text-slate-900 shadow-sm"
//                 : "text-slate-500 hover:text-slate-700",
//             ].join(" ")}
//           >
//             {t.label}
//           </button>
//         ))}
//       </div>
//     </div>
//   );
// }

// function NoteItem({ note, onTogglePin }) {
//   return (
//     <div className="rounded-2xl border border-slate-200 bg-white p-4">
//       <div className="flex items-start justify-between">
//         <div className="flex flex-col">
//           <div className="flex items-center gap-2">
//             <span className="font-semibold text-slate-900">{note.author}</span>
//             <span className="text-sm text-slate-500">{note.timestamp}</span>
//             {note.pinned && (
//               <span className="inline-flex items-center gap-1 rounded-full border border-slate-200 px-2 py-0.5 text-xs font-semibold text-slate-600">
//                 <Pin className="h-3 w-3" />
//                 Pinned
//               </span>
//             )}
//           </div>
//           <div className="mt-2 text-slate-600">{note.text}</div>
//         </div>
//         <button
//           onClick={onTogglePin}
//           className={[
//             "rounded-lg p-2 transition",
//             note.pinned
//               ? "text-slate-800 hover:bg-slate-100"
//               : "text-slate-400 hover:text-slate-600 hover:bg-slate-100",
//           ].join(" ")}
//           aria-label="toggle pin"
//         >
//           <Pin className="h-4 w-4" />
//         </button>
//       </div>
//     </div>
//   );
// }

// export default function OrderNotesCommunication({
//   initial = {
//     internal: [
//       {
//         id: "n1",
//         author: "John Admin",
//         timestamp: "12/01/2025, 16:35:00",
//         text: "Customer requested expedited shipping for the electronics items due to upcoming travel",
//         pinned: true,
//       },
//       {
//         id: "n2",
//         author: "Sarah Admin",
//         timestamp: "12/01/2025, 18:00:00",
//         text: "Home & Garden vendor typically ships within 24 hours. Following up if no update by EOD",
//         pinned: false,
//       },
//     ],
//     customer: [],
//     vendor: [],
//   },
// }) {
//   const [tab, setTab] = useState("internal");
//   const [lists, setLists] = useState(initial);
//   const [draft, setDraft] = useState("");

//   const placeholder = useMemo(
//     () =>
//       tab === "internal"
//         ? "Add internal note..."
//         : tab === "customer"
//         ? "Write a message to the customer..."
//         : "Write a message to the vendor...",
//     [tab]
//   );

//   const activeList = lists[tab];

//   function addNote() {
//     if (!draft.trim()) return;
//     const newNote = {
//       id: Math.random().toString(36).slice(2),
//       author: tab === "internal" ? "Admin" : "You",
//       timestamp: new Date().toLocaleString(),
//       text: draft.trim(),
//       pinned: false,
//     };
//     setLists((prev) => ({ ...prev, [tab]: [newNote, ...prev[tab]] }));
//     setDraft("");
//   }

//   function togglePin(id) {
//     setLists((prev) => ({
//       ...prev,
//       [tab]: prev[tab].map((n) =>
//         n.id === id ? { ...n, pinned: !n.pinned } : n
//       ),
//     }));
//   }

//   return (
//     <div className="grid grid-cols-1">
//       <div className="rounded-2xl border border-slate-200 bg-white p-6">
//         <div className="text-xl font-semibold text-slate-900">
//           Notes & Communications
//         </div>

//         <div className="mt-4 flex items-center justify-between gap-4 w-full">
//           <SegmentedTabs value={tab} onChange={setTab} />
//         </div>

//         <div className="mt-4 flex items-start gap-3">
//           <div className="relative w-full">
//             <textarea
//               rows={3}
//               value={draft}
//               onChange={(e) => setDraft(e.target.value)}
//               placeholder={placeholder}
//               className="w-full resize-none rounded-2xl border border-slate-200 bg-white p-4 text-slate-700 outline-none focus:ring-2 focus:ring-slate-200"
//             />
//           </div>
//           <button
//             onClick={addNote}
//             className="flex h-12 w-12 items-center justify-center rounded-xl bg-indigo-400 text-white hover:bg-indigo-500 active:bg-indigo-600"
//             aria-label="add note"
//           >
//             <Plus className="h-5 w-5" />
//           </button>
//         </div>

//         <div className="mt-4 space-y-3">
//           {activeList.length === 0 ? (
//             <div className="rounded-xl border border-dashed border-slate-200 p-6 text-center text-sm text-slate-500">
//               No{" "}
//               {tab === "internal"
//                 ? "internal notes"
//                 : tab === "customer"
//                 ? "customer messages"
//                 : "vendor messages"}{" "}
//               yet.
//             </div>
//           ) : (
//             activeList.map((n) => (
//               <NoteItem
//                 key={n.id}
//                 note={n}
//                 onTogglePin={() => togglePin(n.id)}
//               />
//             ))
//           )}
//         </div>
//       </div>
//     </div>
//   );
// }

import React, { useMemo, useState } from "react";
import { Pin, Plus } from "lucide-react";
import Button from "@/components/ui/Button";

function SegmentedTabs({ value, onChange, className = "" }) {
  const tabs = [
    { id: "internal", label: "Internal Notes" },
    { id: "customer", label: "Customer Messages" },
    { id: "vendor", label: "Vendor Messages" },
  ];
  return (
    <div className={`w-full ${className}`}>
      <div className="flex w-full rounded-xl bg-slate-100 p-1">
        {tabs.map((t) => (
          <button
            key={t.id}
            onClick={() => onChange(t.id)}
            className={[
              "flex-1 rounded-lg px-4 py-2 text-sm font-semibold transition",
              value === t.id
                ? "bg-white text-slate-900 shadow-sm"
                : "text-slate-500 hover:text-slate-700",
            ].join(" ")}
          >
            {t.label}
          </button>
        ))}
      </div>
    </div>
  );
}

function NoteItem({ note, onTogglePin }) {
  return (
    <div className="rounded-2xl border border-slate-200 bg-white p-4">
      <div className="flex items-start justify-between">
        <div className="flex flex-col">
          <div className="flex items-center gap-2">
            <span className="font-semibold text-slate-900">{note.author}</span>
            <span className="text-sm text-slate-500">{note.timestamp}</span>
            {note.pinned && (
              <span className="inline-flex items-center gap-1 rounded-full border border-slate-200 px-2 py-0.5 text-xs font-semibold text-slate-600">
                <Pin className="h-3 w-3" />
                Pinned
              </span>
            )}
          </div>
          <div className="mt-2 text-slate-600">{note.text}</div>
        </div>
        <button
          onClick={onTogglePin}
          className={[
            "rounded-lg p-2 transition",
            note.pinned
              ? "text-slate-800 hover:bg-slate-100"
              : "text-slate-400 hover:text-slate-600 hover:bg-slate-100",
          ].join(" ")}
          aria-label="toggle pin"
        >
          <Pin className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}

export default function NotesCommunications({
  initial = {
    internal: [
      {
        id: "n1",
        author: "John Admin",
        timestamp: "12/01/2025, 16:35:00",
        text: "Customer requested expedited shipping for the electronics items due to upcoming travel",
        pinned: true,
      },
      {
        id: "n2",
        author: "Sarah Admin",
        timestamp: "12/01/2025, 18:00:00",
        text: "Home & Garden vendor typically ships within 24 hours. Following up if no update by EOD",
        pinned: false,
      },
    ],
    customer: [],
    vendor: [],
  },
}) {
  const [tab, setTab] = useState("internal");
  const [lists, setLists] = useState(initial);
  const [draft, setDraft] = useState("");

  const placeholder = useMemo(
    () =>
      tab === "internal"
        ? "Add internal note..."
        : tab === "customer"
        ? "Write a message to the customer..."
        : "Write a message to the vendor...",
    [tab]
  );

  const activeList = lists[tab];

  function addNote() {
    if (!draft.trim()) return;
    const newNote = {
      id: Math.random().toString(36).slice(2),
      author: tab === "internal" ? "Admin" : "You",
      timestamp: new Date().toLocaleString(),
      text: draft.trim(),
      pinned: false,
    };
    setLists((prev) => ({ ...prev, [tab]: [newNote, ...prev[tab]] }));
    setDraft("");
  }

  function togglePin(id) {
    setLists((prev) => ({
      ...prev,
      [tab]: prev[tab].map((n) =>
        n.id === id ? { ...n, pinned: !n.pinned } : n
      ),
    }));
  }

  return (
    <div className="rounded-2xl border border-slate-200 bg-white p-6">
      <div className="text-xl font-semibold text-slate-900">
        Notes & Communications
      </div>

      <div className="mt-4 w-full">
        <SegmentedTabs value={tab} onChange={setTab} className="w-full" />
      </div>

      <div className="mt-4 flex items-start gap-3">
        <div className="relative w-full">
          <textarea
            rows={3}
            value={draft}
            onChange={(e) => setDraft(e.target.value)}
            placeholder={placeholder}
            className="w-full resize-none rounded-2xl border border-slate-200 bg-white p-4 text-slate-700 outline-none focus:ring-2 focus:ring-slate-200"
          />
        </div>
      </div>
      <div className="flex items-center justify-end my-3">
        <Button className="gap-2" variant="primary" size="md" onClick={addNote}>
          <Plus className="text-slate-50" />
          Submit
        </Button>
      </div>

      <div className="mt-4 space-y-3">
        {activeList.length === 0 ? (
          <div className="rounded-xl border border-dashed border-slate-200 p-6 text-center text-sm text-slate-500">
            No{" "}
            {tab === "internal"
              ? "internal notes"
              : tab === "customer"
              ? "customer messages"
              : "vendor messages"}{" "}
            yet.
          </div>
        ) : (
          activeList.map((n) => (
            <NoteItem key={n.id} note={n} onTogglePin={() => togglePin(n.id)} />
          ))
        )}
      </div>
    </div>
  );
}
