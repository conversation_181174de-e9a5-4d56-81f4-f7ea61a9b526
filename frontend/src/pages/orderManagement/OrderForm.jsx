import { Formik, Form } from "formik";
import * as Yup from "yup";
import { FormInput, FormRadioGroup, FormTextarea } from "@/components/ui/form";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import { FaCheckCircle, FaTimesCircle } from "react-icons/fa";
import { statusOptions } from "@/constants/filterOption";

const OrderForm = ({ order, onSubmit, onCancel }) => {
  const { t } = useTranslation();

  const initialValues = order
    ? {
        id: order.id,
        code: order.code || "",
      }
    : {
        code: "",
      };

  const validationSchema = Yup.object({
    name_en: Yup.string().required(t("commonValidation.name_en")),
    // status: Yup.string().required(t("commonValidation.status")),
  });

  const handleSubmit = (values, { setSubmitting }) => {
    const formattedValues = {
      ...values,
      icon: values.icon?.path || values.icon || "",
    };
    onSubmit(formattedValues);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, setFieldValue, values }) => (
        <Form className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-4">
              <div className="gap-4 bg-white p-5 space-y-5">
                <div className="grid grid-cols-2 gap-4">
                  <FormInput
                    name="code"
                    label={t("commonField.code")}
                    placeholder={t("commonPlaceholder.codePlaceholder")}
                  />
                  {/* <FormInput
                    name="fee_text"
                    label={t("commonField.fee_text")}
                    placeholder={t("commonPlaceholder.fee_textPlaceholder")}
                  /> */}
                </div>

                {/* <FormInput
                  name="slug"
                  label={t("commonField.slug")}
                  placeholder={t("commonPlaceholder.slugPlaceholder")}
                  required
                /> */}
              </div>
            </div>

            <div className="space-y-4">
              <div className=" bg-white p-5">
                <div className="grid grid-cols-2 gap-4">
                  <FormRadioGroup
                    name="status"
                    label={t("commonField.status")}
                    options={statusOptions}
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end pt-4 border-t border-gray-200 mt-8 gap-3">
            <Button type="button" variant="outline" onClick={onCancel}>
              {t("commonButton.cancel")}
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {order
                ? t("commonButton.order.updated")
                : t("commonButton.order.create")}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default OrderForm;
