import { fetchData } from "@/hooks/useApi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { getNestedValue } from "@/helper/Commonhelper";
import { renderNA } from "@/helper/commonFunctionHelper";
import BackArrowIcon from "@/components/common/BackArrowIcon";
import { onlyForDateFormat, onlyForTimeFormat } from "@/hooks/utility";
import { RefreshCw, Download, Package, Ellipsis } from "lucide-react";
import OrderPreviewCard from "./OrderPreviewPage/OrderPreviewCard";
import OrderTotalsVendorBreakdown from "./OrderPreviewPage/OrderTotalsVendorBreakdown";
import OrderTimeline from "./OrderPreviewPage/OrderTimeline";
import OrderVendorCard from "./OrderPreviewPage/OrderVendorCard";
import OrderVendorCardTwo from "./OrderPreviewPage/OrderVendorCardTwo";
import NotesCommunications from "./OrderPreviewPage/OrderNotesCommunication";

const Label = ({ label, value }) => (
  <div className="flex items-center gap-2 text-base">
    <strong className="w-60 text-gray-500 font-medium">{label}</strong>
    <strong className="w-4 text-gray-600">:</strong>
    <strong className="w-60 text-gray-700 font-medium">{value}</strong>
  </div>
);

const OrderPreview = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const {
    data: previewData,
    isLoading,
    isError,
  } = fetchData(`admin/orders/${id}`);
  const data = previewData?.data;

  console.log("order Data Response", data);

  if (isLoading) {
    return (
      <div className="loading-error-view">
        <LoadingSpinner />
      </div>
    );
  }
  return (
    <div className="page-container">
      <div className="bg-white rounded-lg shadow-xl border border-gray-300">
        <div className="title-add-edit-card flex items-center justify-between">
          <div className="title-add-edit-div flex items-center justify-between w-full">
            <div className="flex gap-2">
              <button onClick={() => navigate("/order/list")}>
                <BackArrowIcon />
              </button>

              <div className="flex gap-3 items-center justify-center">
                <h3 className="title-add-edit">{t("order.order")}</h3>
                <span className="text-xl font-bold bg-slate-200 p-2 text-slate-900 rounded-md">
                  #{data?.order_number}
                </span>
              </div>
            </div>
            <div className="space-y-1 text-sm font-light">
              <div className="gap-2">
                {onlyForDateFormat(data?.created_at)}{" "}
                {onlyForTimeFormat(data?.created_at)}
              </div>
              <div>{data?.created_at_human}</div>
            </div>
            <div className="flex items-center justify-center">
              <div className="bg-green-100 text-green-500 border border-green-200 rounded-full flex items-center px-2 text-sm">
                Partially Shipped
              </div>
            </div>
            <Button className="gap-2" variant="secondary" size="md">
              <RefreshCw className="text-slate-700" />
              Updated Status
            </Button>

            <Button className="gap-2" variant="secondary" size="md">
              <Package className="text-slate-700" />
              Issue Refund
            </Button>
            <Button className="gap-2" variant="secondary" size="md">
              <Download className="text-slate-700" />
              Download Invoice
            </Button>
            <Button className="gap-2" variant="secondary" size="md">
              <Ellipsis className="text-slate-700" />
            </Button>
          </div>
        </div>

        <div className="space-y-4 mx-auto relative p-4">
          {/* Order Preview main information card  */}
          <OrderPreviewCard />
          {/* order vendor component  */}
          <OrderVendorCard />
          <OrderVendorCardTwo />
          {/* order Total and vendor breakdown component  */}
          <OrderTotalsVendorBreakdown />
          {/* Order Timeline component  */}
          <OrderTimeline />
          {/* Note and Communication component  */}
          <NotesCommunications />
        </div>
      </div>
    </div>
  );
};

export default OrderPreview;
