import { useState } from "react";
import { motion } from "framer-motion";
import { FaEdit, FaTrash, FaUserShield, FaPlus, FaEye } from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import ProductClassFilters from "./ProductClassFilters";
import ProductClassEditForm from "./ProductClassAddUpdate";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";
import { getNestedValue } from "@/helper/Commonhelper";
import StatusBadge from "@/components/common/StatusBadge";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import PreviewSection from "./ProductClassPreview";

const ProductClassIndex = () => {
  const { t } = useTranslation();
  const { deleteMutation, postMutation, putMutation } = useApi();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  // State for filters
  const [filterOptions, setFilterOptions] = useState({
    search: "",
    roles: [],
    statuses: [],
  });

  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedClass, setSelectedClass] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  const [previewId, setPreviewId] = useState(null);

  const {
    data: classesData,
    isLoading,
    isError,
    refetch,
  } = fetchData("admin/product-classes", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    roles: filterOptions.roles || [],
    statuses: filterOptions.statuses || [],
  });

  const classList = classesData?.data?.data || [];
  const paginationInfo = {
    currentPage: classesData?.data?.current_page || 1,
    perPage: classesData?.data?.per_page || itemsPerPage,
    totalItems: classesData?.data?.total_items || 0,
    totalPages: classesData?.data?.total_pages || 1,
  };

  // Handle filter change from ProductClassFilters
  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  // Open edit modal
  const handleEditClass = (classItem) => {
    setSelectedClass(classItem);
    setEditModalOpen(true);
  };

  const handlePreviewClick = (classItem) => {
    setPreviewId(classItem.id);
  };

  // Open delete modal
  const handleDeleteClick = (classItem) => {
    setSelectedClass(classItem);
    setDeleteModalOpen(true);
  };

  const handleDeleteClass = async () => {
    if (!selectedClass) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/product-classes/${selectedClass.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setDeleteModalOpen(false);
          setSelectedClass(null);
          refetch();
          toast.success(t("commonToast.classToast.classDelete"));
        },
        onError: (error) => {
          console.error(
            "Class deletion failed:",
            error?.response?.data || error.message
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const handleUpdateClass = (updatedClass) => {
    setEditLoading(true);
    const dataToSend = { ...updatedClass };
    delete dataToSend.confirmPassword;

    if (updatedClass.id && !dataToSend.password) {
      delete dataToSend.password;
    }

    const onSuccess = () => {
      setEditModalOpen(false);
      setSelectedClass(null);
      refetch();
      toast.success(
        t(
          updatedClass.id
            ? "commonToast.classToast.classUpdate"
            : "commonToast.classToast.classCreate"
        )
      );
    };
    const onError = (error) => {
      console.error(
        "Class update failed:",
        error?.response?.data || error.message
      );
    };
    const onSettled = () => setEditLoading(false);

    if (!updatedClass.id) {
      postMutation.mutate(
        { endpoint: "admin/product-classes", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        {
          endpoint: `admin/product-classes/${updatedClass.id}`,
          data: dataToSend,
        },
        { onSuccess, onError, onSettled }
      );
    }
  };

  const canEditClass = (classItem) => true;

  const canDeleteClass = (classItem) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.name_en"),
      accessor: "name_en",
    },
    {
      header: t("commonTableLabel.name_ar"),
      accessor: "name_ar",
    },
    {
      header: t("commonTableLabel.category"),
      render: (row) => {
        return getNestedValue(row, "category.name_en", "N/A");
      },
    },
    {
      header: t("commonTableLabel.subCategory"),
      render: (row) => {
        return getNestedValue(row, "sub_category.name_en", "N/A");
      },
    },
    {
      header: t("commonTableLabel.code"),
      accessor: "code",
    },
    {
      header: t("commonTableLabel.popular"),
      accessor: "is_popular",
      render: (row) => (row.is_popular ? "Yes" : "No"),
    },
    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row} fieldName="status" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("class.previewClassContent")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditClass(row)}
            disabled={!canEditClass(row)}
            title={
              !canEditClass(row)
                ? "You don't have permission to edit this class"
                : t("productClass.editAction")
            }
          >
            <FaEdit className="icon-edit" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteClass(row)}
            title={
              !canDeleteClass(row)
                ? "You can't delete this class"
                : t("productClass.deleteAction")
            }
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("productClass.title")}
          icon={<FaUserShield className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => {
                setSelectedClass(null);
                setEditModalOpen(true);
              }}
            >
              <FaPlus className="mr-2" /> {t("productClass.add")}
            </Button>
          }
        >
          <ProductClassFilters onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={classList}
            emptyMessage={t("productClass.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>
      {/* Add and Edit Modal  */}
      <Modal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title={
          selectedClass ? t("productClass.editAction") : t("productClass.add")
        }
        size="lg"
      >
        <ProductClassEditForm
          class={selectedClass}
          onSubmit={handleUpdateClass}
          onCancel={() => setEditModalOpen(false)}
          loading={editLoading}
        />
      </Modal>

      {/* PreView Page  */}
      <Modal
        isOpen={previewId}
        onClose={() => setPreviewId(null)}
        title={t("class.previewClassContent")}
        size="xl"
        showCloseButton={true}
      >
        <PreviewSection
          classId={previewId}
          onClose={() => setPreviewId(null)}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onDelete={handleDeleteClass}
        loading={deleteLoading}
        itemName={t("productClass.class")}
        itemValue={selectedClass?.name_en}
      />
    </div>
  );
};

export default ProductClassIndex;
