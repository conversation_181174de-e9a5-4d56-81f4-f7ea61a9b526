import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import AttributeFilter from "./attributeFilters";
import {
  FaEdit,
  FaTrash,
  Fa<PERSON><PERSON><PERSON><PERSON>ist,
  FaPlus,
  <PERSON>aEye,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import Modal from "@/components/ui/Modal";
import { toast } from "sonner";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import PreviewSection from "./attributePreview";

const attributeIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedAttribute, setSelectedAttribute] = useState(null);
  const [previewId, setPreviewId] = useState(null);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: "",
  });

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: attributeData,
    isLoading,
    isError: attributeError,
    refetch,
  } = fetchData("admin/product-attributes", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    status: filterOptions.status || "",
  });

  const attributeList = attributeData?.data?.data || [];
  const paginationInfo = {
    currentPage: attributeData?.data?.current_page || 1,
    perPage: attributeData?.data?.per_page || itemsPerPage,
    totalItems: attributeData?.data?.total_items || 0,
    totalPages: attributeData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditAttribute = (attribute) => {
    navigate(`/products/attribute/edit/${attribute.id}`);
  };

  const handlePreviewClick = (attribute) => {
    setPreviewId(attribute.id);
  };

  const handleDeleteClick = (attribute) => {
    setSelectedAttribute(attribute);
  };

  const handleDeleteAttribute = async () => {
    if (!selectedAttribute) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/product-attributes/${selectedAttribute.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setSelectedAttribute(null);
          refetch();
          toast.success(t("commonToast.attributeToast.attributeDelete"));
        },
        onError: (error) => {
          console.error(
            "= deletion failed:",
            error?.response?.data || error.message
          );
          toast.error(
            "Attribute deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const canEditAttribute = (attribute) => true;

  const canDeleteAttribute = (attribute) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.name_en"),
      accessor: "name",
    },
    {
      header: t("commonTableLabel.name_ar"),
      accessor: "name_ar",
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("attribute.previewClassContent")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditAttribute(row)}
            disabled={!canEditAttribute(row)}
            title={
              !canEditAttribute(row)
                ? "You don't have permission to edit this Attribute"
                : t("attributes.editAction")
            }
          >
            <FaEdit className="icon-edit" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteAttribute(row)}
            title={
              !canDeleteAttribute(row)
                ? "You can't delete this Attribute"
                : t("attributes.deleteAction")
            }
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("attributes.title")}
          icon={<FaClipboardList className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => navigate("/products/attribute/add")}
            >
              <FaPlus className="mr-2" /> {t("attributes.add")}
            </Button>
          }
        >
          <AttributeFilter onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={attributeList}
            emptyMessage={t("attributes.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      {/* PreView Page  */}
      <Modal
        isOpen={previewId}
        onClose={() => setPreviewId(null)}
        title={t("attribute.previewClassContent")}
        size="xl"
        showCloseButton={true}
      >
        <PreviewSection
          attributeId={previewId}
          onClose={() => setPreviewId(null)}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={!!selectedAttribute}
        onClose={() => setSelectedAttribute(null)}
        onDelete={handleDeleteAttribute}
        loading={deleteLoading}
        itemName={t("attributes.attributes")}
        itemValue={selectedAttribute?.name}
      />
    </div>
  );
};

export default attributeIndex;
