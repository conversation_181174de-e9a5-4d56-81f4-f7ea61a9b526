import { Package2, Pencil, XCircle } from "lucide-react";

export default function ReviewCard({
  title,
  completed,
  total,
  percent,
  onEdit,
}) {
  return (
    <div className="w-full max-w-md rounded-lg border border-gray-200 bg-white p-4 flex flex-col shadow-lg">
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gray-50 rounded-md">
            <Package2 className="w-6 h-6 text-gray-500" />
          </div>
          <span className="title-add-edit">{title}</span>
        </div>
        <button
          className="p-1 rounded hover:bg-gray-100"
          onClick={onEdit}
          type="button"
        >
          <Pencil className="w-4 h-4 text-gray-400" />
        </button>
      </div>
      <div className="flex items-center justify-between mt-8">
        <div className="flex items-center gap-2">
          <XCircle className="w-5 h-5 text-red-500" />
          <span className="text-sm text-gray-500">
            {completed} of {total} fields completed
          </span>
        </div>
        <span className="text-sm font-semibold text-gray-700">
          {percent}% Complete
        </span>
      </div>
    </div>
  );
}
