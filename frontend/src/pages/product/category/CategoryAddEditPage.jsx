import { useParams, useNavigate } from "react-router-dom";
import { useState } from "react";
import { toast } from "sonner";
import CategoryFormPage from "./CategoryForm";
import { useApi } from "@/hooks/useApi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useTranslation } from "react-i18next";
import BackArrowIcon from "@/components/common/BackArrowIcon";

const CategoryAddEditPage = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const { fetchData, postMutation, putMutation } = useApi();

  const { data, isLoading, isError, refetch } = id
    ? fetchData(`admin/categories/${id}`)
    : { data: null, isLoading: false, isError: false, refetch: () => {} };
  const categoryData = data?.data;

  const handleSubmit = (values) => {
    setLoading(true);
    const dataToSend = { ...values };

    const onSuccess = () => {
      toast.success(
        t(
          id
            ? "commonToast.categoryToast.categoryUpdate"
            : "commonToast.categoryToast.categoryCreate"
        )
      );

      navigate("/products/categories");
    };

    const onError = (error) => {
      console.error(
        "Category update failed:",
        error?.response?.data || error.message
      );
      toast.error(
        "Category update failed: " +
          (error?.response?.data?.message || error.message)
      );
    };

    const onSettled = () => setLoading(false);

    if (!id) {
      postMutation.mutate(
        { endpoint: "admin/categories", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        {
          endpoint: `admin/categories/${id}`,
          data: dataToSend,
        },
        { onSuccess, onError, onSettled }
      );
    }
  };

  if (isLoading || loading) {
    return (
      <div className="loading-error">
        <LoadingSpinner />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="loading-error">
        <div className="text-red-500">
          Error loading Category data. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="page-container">
      <div className="card-style">
        <div className="title-add-edit-card">
          <div className="title-add-edit-div">
            <button onClick={() => navigate("/products/categories")}>
              <BackArrowIcon />
            </button>
            <h3 className="title-add-edit">
              {t(id ? "category.editAction" : "category.add")}
            </h3>
          </div>
        </div>
        <div className="px-6 py-4">
          <CategoryFormPage
            category={categoryData}
            onSubmit={handleSubmit}
            onCancel={() => navigate("/products/categories")}
            loading={loading}
          />
        </div>
      </div>
    </div>
  );
};

export default CategoryAddEditPage;
