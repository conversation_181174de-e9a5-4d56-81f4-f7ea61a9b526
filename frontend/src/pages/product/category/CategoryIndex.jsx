import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import CategoryFilter from "./CategoryFilters";
import {
  FaEdit,
  FaTrash,
  Fa<PERSON>lip<PERSON><PERSON>ist,
  FaPlus,
  FaEye,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import { getNestedValue, capitalizeFirstLetter } from "@/helper/Commonhelper";
import StatusBadge from "@/components/common/StatusBadge";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import { renderNA } from "@/helper/commonFunctionHelper";
import Modal from "@/components/ui/Modal";
import PreviewSection from "./CategoryPreview";

const CategoryIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [previewCategoryId, setPreviewCategoryId] = useState(null);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: "",
  });

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: categoryData,
    isLoading,
    isError: categoryError,
    refetch,
  } = fetchData("admin/categories", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    status: filterOptions.status || "",
  });

  const categoryList = categoryData?.data?.data || [];
  const paginationInfo = {
    currentPage: categoryData?.data?.current_page || 1,
    perPage: categoryData?.data?.per_page || itemsPerPage,
    totalItems: categoryData?.data?.total_items || 0,
    totalPages: categoryData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditCategory = (category) => {
    navigate(`/products/categories/edit/${category.id}`);
  };

  const handlePreviewClick = (category) => {
    setPreviewCategoryId(category.id);
  };

  const handleDeleteClick = (category) => {
    setSelectedCategory(category);
  };

  const handleDeleteCategory = async () => {
    if (!selectedCategory) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/categories/${selectedCategory.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setSelectedCategory(null);
          refetch();
          toast.success(t("commonToast.categoryToast.categoryDelete"));
        },
        onError: (error) => {
          console.error(
            "Category deletion failed:",
            error?.response?.data || error.message
          );
          toast.error(
            "Category deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const canEditCategory = (category) => true;

  const canDeleteCategory = (category) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.name_en"),
      render: (row) => capitalizeFirstLetter(renderNA(row.name_en)),
    },
    {
      header: t("commonTableLabel.name_ar"),
      render: (row) => renderNA(row.name_ar),
    },
    {
      header: t("commonTableLabel.code"),
      render: (row) => renderNA(row.code),
    },
    {
      header: t("commonField.category"),
      render: (row) => {
        const parentName = getNestedValue(row, "parent.name_en", null);
        return renderNA(parentName);
      },
    },
    {
      header: t("commonTableLabel.type"),
      accessor: "type",
      render: (row) => {
        const typeColors = {
          main: "bg-purple-100 text-purple-800",
          sub: "bg-indigo-100 text-indigo-800",
        };
        return (
          <span
            className={`w-14 text-center px-2 py-1 inline-flex justify-center text-sm leading-5 font-medium rounded-md shadow-md ${
              typeColors[row.type] || "bg-gray-100 text-gray-800"
            }`}
          >
            {capitalizeFirstLetter(row.type)}
          </span>
        );
      },
    },
    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row} fieldName="status" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("category.previewCategoryContent")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditCategory(row)}
            disabled={!canEditCategory(row)}
            title={
              !canEditCategory(row)
                ? "You don't have permission to edit this Category"
                : t("category.editAction")
            }
          >
            <FaEdit className="icon-edit" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteCategory(row)}
            title={
              !canDeleteCategory(row)
                ? "You can't delete this Category"
                : t("category.deleteAction")
            }
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("category.title")}
          icon={<FaClipboardList className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => navigate("/products/categories/add")}
            >
              <FaPlus className="mr-2" /> {t("category.add")}
            </Button>
          }
        >
          <CategoryFilter onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={categoryList}
            emptyMessage={t("category.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      {/* Category PreView  */}
      <Modal
        isOpen={previewCategoryId}
        onClose={() => setPreviewCategoryId(null)}
        title={t("category.previewCategoryContent")}
        size="xl"
        showCloseButton={true}
      >
        <PreviewSection
          categoryId={previewCategoryId}
          onClose={() => setPreviewCategoryId(null)}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={!!selectedCategory}
        onClose={() => setSelectedCategory(null)}
        onDelete={handleDeleteCategory}
        loading={deleteLoading}
        itemName={t("commonField.category")}
        itemValue={selectedCategory?.name_en}
      />
    </div>
  );
};

export default CategoryIndex;
