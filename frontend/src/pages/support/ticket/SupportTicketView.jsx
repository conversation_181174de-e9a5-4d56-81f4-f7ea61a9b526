import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  FaArrowLeft,
  FaExclamationCircle,
  FaStopwatch,
  FaRegUserCircle,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useApi, fetchData } from "@/hooks/useApi";
import { FormSelect, FormTextarea } from "@/components/ui/form";
import { toast } from "sonner";
import { Formik, Form } from "formik";
import { progressStatusOptions } from "@/constants/filterOption";
import DefaultIcon from "@/assets/defaultIcon.png";
import { useTranslation } from "react-i18next";
import BackArrowIcon from "@/components/common/BackArrowIcon";

const SupportTicketView = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { put } = useApi();

  const [ticket, setTicket] = useState(null);
  const [comments, setComments] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);

  const { data: ticketData, isLoading: ticketLoading } = fetchData(
    `admin/support-tickets/${id}`
  );
  const {
    data: messagesData,
    isLoading: messagesLoading,
    refetch: refetchMessages,
  } = fetchData(`admin/support-ticket-messages`, { ticket_id: id });
  const { data: usersData, isLoading: usersLoading } = fetchData(
    "admin/users",
    { pagination: false }
  );
  const { post } = useApi();

  useEffect(() => {
    if (ticketData) {
      setTicket(ticketData.data);
    }
  }, [ticketData]);

  useEffect(() => {
    if (messagesData) {
      setComments(messagesData?.data?.data);
    }
  }, [messagesData]);

  useEffect(() => {
    if (usersData) {
      setUsers(usersData.data);
    }
  }, [usersData]);

  useEffect(() => {
    if (!ticketLoading && !messagesLoading && !usersLoading) {
      setLoading(false);
    }
  }, [ticketLoading, messagesLoading, usersLoading]);

  const userOptions = Array.isArray(users)
    ? users.map((user) => ({
        value: user.id,
        label: user.name,
      }))
    : [];

  const handleUpdateTicket = async (values) => {
    setIsUpdating(true);
    try {
      await put(`admin/support-tickets/${id}`, {
        status: values.status,
        assigned_to: values.assigned_to,
      });
      toast.success("Ticket updated successfully");
    } catch (error) {
      toast.error("Failed to update ticket");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleAddComment = async (values, { setFieldValue }) => {
    if (!values.comment.trim()) {
      toast.error("Comment cannot be empty");
      return;
    }
    setIsSubmittingComment(true);
    try {
      await post("admin/support-ticket-messages", {
        ticket_id: id,
        message: values.comment,
      });
      toast.success("Comment added successfully");
      setFieldValue("comment", "");
      refetchMessages();
    } catch (error) {
      toast.error("Failed to add comment");
    } finally {
      setIsSubmittingComment(false);
    }
  };

  if (loading) {
    return <LoadingSpinner size={64} overlay />;
  }

  const initialValues = {
    status: ticket?.status || "",
    assigned_to: ticket?.assigned_to || "",
    comment: "",
  };

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={() => {}}
      enableReinitialize
    >
      {({ values, setFieldValue }) => (
        <Form>
          <div className="page-container">
            <div className="bg-white rounded-lg shadow-xl border border-gray-300 space-y-3">
              <div className="title-add-edit-card">
                <div className="title-add-edit-div">
                  <button onClick={() => navigate("/support/ticket")}>
                    <BackArrowIcon />
                  </button>
                  <h3 className="title-add-edit">{t("supportTicket.view")}</h3>
                </div>
              </div>
              <div className="m-4">
                <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-4 mb-4 border border-gray-300 rounded-2xl shadow-md">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 py-3">
                    <div className="flex flex-col items-start">
                      <span className="text-sm font-medium text-gray-500  tracking-wide">
                        {t("supportTicket.supportCategory")}
                      </span>
                      <span className="w-full text-base mt-2 font-semibold">
                        {ticket?.category?.name_en || "N/A"}
                      </span>
                    </div>
                    <div className="flex flex-col items-start">
                      <span className="text-sm font-medium text-gray-500  tracking-wide">
                        {t("supportTicket.supportTopic")}
                      </span>
                      <span className="w-full text-base mt-2 font-semibold">
                        {ticket?.topic?.name_en || "N/A"}
                      </span>
                    </div>
                    <div className="flex flex-col items-start">
                      <span className="text-sm font-medium text-gray-500  tracking-wide">
                        {t("supportTicket.supportPriority")}
                      </span>
                      <span className="w-full text-base mt-2 font-semibold">
                        {ticket?.priority || "N/A"}
                      </span>
                    </div>
                    <div className="flex flex-col items-start">
                      <span className="text-sm font-medium text-gray-500  tracking-wide">
                        {t("commonTableLabel.status")}
                      </span>
                      <span className="w-full text-base mt-2 font-semibold">
                        {ticket?.status || "N/A"}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mx-auto mb-4">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                    <div className="lg:col-span-2 space-y-4">
                      <div className="bg-white rounded-xl shadow-md border border-gray-200 overflow-hidden">
                        <div className="bg-gradient-to-r from-gray-500 to-gray-400 px-6 py-4">
                          <h1 className="text-2xl font-bold text-white">
                            {t("supportTicket.supportTicketDetails")}
                          </h1>
                        </div>
                        <div className="p-4 space-y-4">
                          <div className="group">
                            <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                              {t("supportTicket.supportSubject")}
                            </label>
                            <h6 className="text-gray-500  text-base mt-1 font-semibold">
                              {ticket?.subject || "N/A"}
                            </h6>
                          </div>
                          <div className="group">
                            <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                              {t("supportTicket.supportMessage")}
                            </label>
                            <div
                              className="mt-2 p-4 bg-gray-50 rounded-lg border border-gray-200"
                              style={{
                                maxHeight: "200px",
                                overflowY: "auto",
                              }}
                            >
                              <p className="text-gray-700 leading-relaxed whitespace-pre-wrap  text-sm mt-2 font-normal">
                                {ticket?.message || "N/A"}
                              </p>
                            </div>
                          </div>

                          {/* Additional Info */}
                          <div className="pt-4 border-t border-gray-200">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                              <div className="flex items-center">
                                <FaStopwatch className="w-4 h-4 mx-2" />
                                {t("supportTicket.supportCreated")}
                                {ticket?.created_at
                                  ? new Date(
                                      ticket.created_at
                                    ).toLocaleDateString()
                                  : "Unknown"}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* //comment reply */}

                      <div className="mt-2 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-2 p-3">
                          <div className="text-xl font-bold text-slate-950">
                            {t("supportTicket.supportReplySummary")}
                          </div>
                          <div className="flex-grow border-t border-1 border-slate-400"></div>
                        </div>
                        <div
                          className="space-y-2 p-4"
                          style={{
                            maxHeight: "230px",
                            overflowY: "auto",
                          }}
                        >
                          {comments.length > 0 &&
                            comments.map((comment) => (
                              <div
                                key={comment.id}
                                className="p-4 border rounded-md"
                              >
                                <p className="text-gray-800">
                                  {comment.message}
                                </p>
                                <p className="text-xs text-gray-500 mt-2">
                                  By {comment.sender_id} on{" "}
                                  {new Date(
                                    comment.created_at
                                  ).toLocaleString()}
                                </p>
                              </div>
                            ))}
                        </div>
                      </div>

                      <div className="mt-6">
                        <div className="mt-6">
                          <FormTextarea
                            name="comment"
                            label={t("commonField.message")}
                            placeholder={t(
                              "commonPlaceholder.messagePlaceholder"
                            )}
                          />
                          <Button
                            type="button"
                            variant="secondary"
                            onClick={() =>
                              handleAddComment(values, { setFieldValue })
                            }
                            disabled={isSubmittingComment}
                            className="mt-2"
                          >
                            {isSubmittingComment ? (
                              <LoadingSpinner size={20} />
                            ) : (
                              t("commonButton.supportTicket.addMessage")
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Actions Panel */}

                    <div className="lg:col-span-1 space-y-3">
                      <div className="bg-white rounded-xl shadow-md border border-gray-200 overflow-hidden sticky top-6">
                        {/* Header */}
                        <div className="bg-gradient-to-r from-gray-500 to-gray-400 px-6 py-4">
                          <h2 className="text-xl font-bold text-white flex items-center">
                            {t("supportTicket.supportTicketOverview")}
                          </h2>
                        </div>
                        <div className="p-4 space-y-4">
                          <div className="flex items-start gap-3">
                            <span className="text-sm font-medium text-gray-500">
                              {t("supportTicket.supportVendor")}
                            </span>
                            <span className="text-base font-semibold">
                              {ticket?.vendor?.name_en || "N/A"}
                            </span>
                          </div>
                          <div className="flex items-start gap-3">
                            <span className="text-sm font-medium text-gray-500">
                              {t("supportTicket.supportOrder")}
                            </span>
                            <span className="text-base font-semibold">
                              {ticket?.order?.name_en || "N/A"}
                            </span>
                          </div>
                          <div className="flex items-start gap-3">
                            <span className="text-sm font-medium text-gray-500 ">
                              {t("commonTableLabel.code")}
                            </span>
                            <span className="text-base font-semibold">
                              {ticket?.code || "N/A"}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white rounded-xl shadow-md border border-gray-200 overflow-hidden sticky top-6">
                        {/* Header */}
                        <div className="bg-gradient-to-r from-gray-500 to-gray-400 px-6 py-4">
                          <h2 className="text-xl font-bold text-white flex items-center">
                            {t("supportTicket.supportQuickActions")}
                          </h2>
                        </div>
                        {/* Actions Form */}
                        <div className="p-4 space-y-4">
                          <FormSelect
                            label={t("commonField.status")}
                            name="status"
                            options={progressStatusOptions}
                          />
                          <FormSelect
                            label={t("commonField.assignTo")}
                            name="assigned_to"
                            options={userOptions}
                            placeholder={
                              usersLoading ? "Loading..." : "Select User"
                            }
                            disabled={usersLoading}
                          />
                          <Button
                            type="button"
                            variant="secondary"
                            onClick={() => handleUpdateTicket(values)}
                            disabled={isUpdating}
                          >
                            {isUpdating ? (
                              <LoadingSpinner size={20} />
                            ) : (
                              t("commonButton.supportTicket.saveChanges")
                            )}
                          </Button>
                        </div>
                      </div>

                      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden sticky top-6">
                        {/* Header */}
                        <div className="bg-gradient-to-r from-gray-500 to-gray-400 px-6 py-4">
                          <h2 className="text-xl font-bold text-white flex items-center">
                            {t("commonField.assignTo")}
                          </h2>
                        </div>
                        <div className="p-4 space-y-4">
                          <div className="grid grid-cols-1 gap-3">
                            <div className="flex gap-3 items-center">
                              <div className="w-20 h-20 border-2 rounded-full border-gray-500">
                                <img
                                  src={DefaultIcon}
                                  alt="Name"
                                  className="w-full h-full p-1 rounded-full"
                                />
                              </div>
                              <div className="space-y-1">
                                <div className="text-base font-semibold">
                                  {ticket?.assigned_to?.name || "N/A"}
                                </div>
                                <div className="text-base font-semibold">
                                  {ticket?.assigned_to?.phone || "N/A"}
                                </div>
                                <div className="text-base font-semibold">
                                  {ticket?.assigned_to?.email || "N/A"}
                                </div>
                              </div>
                            </div>
                            <div className="space-y-1">
                              <div className="flex items-start gap-3">
                                <span className="text-sm font-medium text-gray-500 ">
                                  {t("supportTicket.supportVendor")}
                                </span>
                                <span className="text-base font-semibold">
                                  {ticket?.assigned_to?.vendor_id || "N/A"}
                                </span>
                              </div>
                              <div className="flex items-start gap-3">
                                <span className="text-sm font-medium text-gray-500 ">
                                  {t("supportTicket.supportTPL")}
                                </span>
                                <span className="text-base font-semibold">
                                  {ticket?.assigned_to?.tpl_id || "N/A"}
                                </span>
                              </div>
                              <div className="flex items-start gap-3">
                                <span className="text-sm font-medium text-gray-500 ">
                                  {t("commonField.status")}
                                </span>
                                <span className="text-base font-semibold">
                                  {ticket?.assigned_to?.status || "N/A"}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* <div className="mt-6">
                <div className="mt-6">
                  <FormTextarea
                    name="comment"
                    label={t("commonField.message")}
                    placeholder={t("commonPlaceholder.messagePlaceholder")}
                  />
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={() => handleAddComment(values, { setFieldValue })}
                    disabled={isSubmittingComment}
                    className="mt-2"
                  >
                    {isSubmittingComment ? (
                      <LoadingSpinner size={20} />
                    ) : (
                      t("commonButton.supportTicket.addMessage")
                    )}
                  </Button>
                </div>
              </div> */}
                {/* </Card>
              </div> */}
              </div>
            </div>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default SupportTicketView;
