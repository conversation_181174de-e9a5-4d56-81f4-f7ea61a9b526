import { useState, useCallback, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { toast } from "sonner";
import { FaPlus, FaEdit, FaTrash, FaEye } from "react-icons/fa";
import { RiListCheck3 } from "react-icons/ri";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import TicketFilters from "./SupportTicketFilters";
import { useTranslation } from "react-i18next";
import { getNestedValue } from "@/helper/Commonhelper";
import WorkflowStatusBadge from "@/components/common/WorkflowStatusBadge";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import SupportTicketCard from "./SupportTicketCard";

const SupportTicketList = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { delete: removeTicket } = useApi();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
  } = usePagination(1, 10);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: "",
    priority: "",
    category_id: "",
    topic_id: "",
  });

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const apiParams = useMemo(
    () => ({
      pagination: true,
      per_page: itemsPerPage,
      page: currentPage,
      search: filterOptions.search || "",
      status: filterOptions.status || "",
      priority: filterOptions.priority || "",
      category_id: filterOptions.category_id || "",
      topic_id: filterOptions.topic_id || "",
    }),
    [itemsPerPage, currentPage, filterOptions]
  );

  const {
    data: ticketsData,
    isLoading: ticketsLoading,
    refetch: refetchTickets,
  } = fetchData("admin/support-tickets", apiParams);

  const ticketList = ticketsData?.data?.data?.data || [];
  const statusCounts = ticketsData?.data?.status_counts || {};
  const paginationInfo = {
    currentPage: ticketsData?.data?.current_page || 1,
    perPage: ticketsData?.data?.per_page || itemsPerPage,
    totalItems: ticketsData?.data?.total || 0,
    totalPages: ticketsData?.data?.last_page || 1,
  };

  const handleFilterChange = useCallback(
    (newFilters) => {
      setFilterOptions((prevFilters) => {
        const searchChanged = prevFilters.search !== newFilters.search;
        const statusesChanged =
          JSON.stringify(prevFilters.status) !==
          JSON.stringify(newFilters.status);
        const prioritiesChanged =
          JSON.stringify(prevFilters.priority) !==
          JSON.stringify(newFilters.priority);
        const categoriesChanged =
          JSON.stringify(prevFilters.category_id) !==
          JSON.stringify(newFilters.category_id);
        const topicsChanged =
          JSON.stringify(prevFilters.topic_id) !==
          JSON.stringify(newFilters.topic_id);
        if (
          searchChanged ||
          statusesChanged ||
          prioritiesChanged ||
          categoriesChanged ||
          topicsChanged
        ) {
          setCurrentPage(1);
          return newFilters;
        }

        return prevFilters;
      });
    },
    [setCurrentPage]
  );

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditTicket = (ticket) => {
    navigate(`/support/ticket/edit/${ticket.id}`);
  };

  const handleDeleteClick = (ticket) => {
    setSelectedTicket(ticket);
    setDeleteModalOpen(true);
  };

  const handleDeleteTicket = async () => {
    if (!selectedTicket) return;
    setDeleteLoading(true);
    removeTicket(`admin/support-tickets/${selectedTicket.id}`)
      .then(() => {
        setDeleteModalOpen(false);
        setSelectedTicket(null);
        refetchTickets();
        toast.success(t("commonToast.supportTicketToast.supportTicketDelete"));
      })
      .finally(() => setDeleteLoading(false));
  };

  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.subject"),
      accessor: "subject",
    },
    {
      header: t("commonTableLabel.priority"),
      accessor: "priority",
    },
    {
      header: t("commonTableLabel.category"),
      render: (row) => {
        return getNestedValue(row, "category.name_en", "N/A");
      },
    },
    {
      header: t("commonTableLabel.topic"),
      render: (row) => {
        return getNestedValue(row, "topic.name_en", "N/A");
      },
    },
    {
      header: t("commonTableLabel.assignTo"),
      render: (row) => {
        return getNestedValue(row, "assigned_to.name", "N/A");
      },
    },
    {
      header: t("commonTableLabel.status"),
      accessor: "status",
      render: (row) => <WorkflowStatusBadge data={row} fieldName="status" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="success"
            size="sm"
            onClick={() => navigate(`/support/ticket/view/${row.id}`)}
          >
            <FaEye className="text-green-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditTicket(row)}
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
          >
            <FaTrash className="text-red-600" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="page-container">
      <SupportTicketCard />
      {ticketsLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("supportTicket.title")}
          icon={<RiListCheck3 className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => navigate("/support/ticket/add")}
            >
              <FaPlus className="mr-2" />
              {t("supportTicket.add")}
            </Button>
          }
        >
          <TicketFilters onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={ticketList}
            emptyMessage={t("supportTicket.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onDelete={handleDeleteTicket}
        loading={deleteLoading}
        itemName={t("supportTicket.supportTicket")}
        itemValue={selectedTicket?.subject}
      />
    </div>
  );
};

export default SupportTicketList;
