import { fetchData } from "@/hooks/useApi";
import { renderNA } from "@/helper/commonFunctionHelper";
import { FolderO<PERSON>, Loader2, CheckCircle2, XCircle } from "lucide-react";
import { useTranslation } from "react-i18next";

export default function SupportTicketCard() {
  const { t } = useTranslation();
  const {
    data: supportTicketCardData,
    isLoading,
    isError,
  } = fetchData("admin/support-tickets");
  const supportCount = supportTicketCardData?.data?.status_counts;

  const stats = [
    {
      label: t("commonOptions.progressStatusOptions.open"),
      value: renderNA(supportCount?.open),
      icon: <FolderOpen className="w-5 h-5 text-white" />,
      iconBg: "bg-blue-500",
      trend: "+12% from last month",
      trendColor: "text-gray-500",
      bg: "bg-white",
      borderBG: "border-purple-500",
    },

    {
      label: t("commonOptions.progressStatusOptions.progress"),
      value: renderNA(supportCount?.in_progress),
      icon: <Loader2 className="w-5 h-5 text-white" />,
      iconBg: "bg-yellow-500",
      trend: "+8% from last month",
      trendColor: "text-gray-500",
      bg: "bg-white",
      borderBG: "border-green-500",
    },
    {
      label: t("commonOptions.progressStatusOptions.resolved"),
      value: renderNA(supportCount?.resolved),
      icon: <CheckCircle2 className="w-5 h-5 text-white" />,
      iconBg: "bg-green-500",
      trend: "-5% from last week",
      trendColor: "text-gray-500",
      bg: "bg-white",
      borderBG: "border-yellow-400",
    },
    {
      label: t("commonOptions.progressStatusOptions.closed"),
      value: renderNA(supportCount?.closed),
      icon: <XCircle className="w-5 h-5 text-white" />,
      iconBg: "bg-gray-500",
      trend: "+15% from last month",
      trendColor: "text-gray-500",
      bg: "bg-white",
      borderBG: "border-blue-500",
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
      {stats.map((stat, i) => (
        <div
          key={i}
          className={`rounded-lg shadow p-5 border-t-4 space-y-2 hover:shadow-lg ${stat.bg} ${stat.borderBG}`}
        >
          <div className="flex items-center justify-between">
            <div className="text-gray-600 font-medium text-base">
              {stat.label}
            </div>
            <div
              className={`${stat.iconBg} rounded-xl p-3 flex items-center justify-center`}
            >
              {stat.icon}
            </div>
          </div>
          <div className="text-xl font-bold">{stat.value}</div>
          {/* <div className={`my-2 text-xs ${stat.trendColor}`}>
            ↗ {stat.trend}
          </div> */}
        </div>
      ))}
    </div>
  );
}
