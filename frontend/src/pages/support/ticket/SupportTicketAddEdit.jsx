import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Formik, Form } from "formik";
import * as yup from "yup";
import { toast } from "sonner";
import {
  FormInput,
  FormSelect,
  FormTextarea,
  FormRadioGroup,
} from "@/components/ui/form";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useApi, fetchData } from "@/hooks/useApi";
import useSupportCategoryList from "@/hooks/list/useSupportCategoryList";
import useSupportTopicList from "@/hooks/list/useSupportTopicList";
import { priorityOptions } from "@/constants/filterOption";
import { statusOptions } from "@/constants/filterOption";
import { useTranslation } from "react-i18next";
import useVendorList from "@/hooks/list/useVendorList";
import BackArrowIcon from "@/components/common/BackArrowIcon";

const SupportTicketAddEdit = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { post, put } = useApi();
  const [loading, setLoading] = useState(false);

  const { options: SupportCategoryOptions, loading: SupportCategoriesLoading } =
    useSupportCategoryList();
  const { options: VendorOptions, loading: VendorLoading } = useVendorList();

  const { data: ticketData, isLoading: ticketLoading } = id
    ? fetchData(`admin/support-tickets/${id}`)
    : { data: null, isLoading: false };

  const ticket = ticketData?.data;

  const schema = yup.object().shape({
    subject: yup.string().required(t("commonValidation.subject")),
    message: yup.string().required(t("commonValidation.message")),
    priority: yup.string().required(t("commonValidation.priority")),
    category_id: yup.string().required(t("commonValidation.category_type")),
    topic_id: yup.string().required(t("commonValidation.topic")),
    vendor_id: yup.string().optional(),
    order_id: yup.string().optional(),
  });

  const initialValues = {
    subject: ticket?.subject || "",
    message: ticket?.message || "",
    priority: ticket?.priority || "medium",
    category_id: ticket?.category_id || "",
    topic_id: ticket?.topic_id || "",
    vendor_id: ticket?.vendor_id || "",
    order_id: ticket?.order_id || "",
  };

  const handleSubmit = (values) => {
    setLoading(true);

    const processAndSubmit = async () => {
      try {
        const payload = { ...values };

        const promise = id
          ? put(`admin/support-tickets/${id}`, payload)
          : post("admin/support-tickets", payload);

        await promise;
        toast.success(
          id
            ? t("commonToast.supportTicketToast.supportTicketUpdate")
            : t("commonToast.supportTicketToast.supportTicketCreate")
        );

        navigate("/support/ticket");
      } catch (error) {
        const errorMessage =
          error?.response?.data?.message ||
          `Failed to ${id ? "update" : "create"} ticket.`;
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    processAndSubmit();
  };

  if (ticketLoading) {
    return <LoadingSpinner size={64} overlay />;
  }

  return (
    <div className="page-container">
      <div className="card-style">
        <div className="title-add-edit-card">
          <div className="title-add-edit-div">
            <button onClick={() => navigate("/support/ticket")}>
              <BackArrowIcon />
            </button>
            <h3 className="title-add-edit">
              {t(id ? "supportTicket.editAction" : "supportTicket.add")}
            </h3>
          </div>
        </div>
        <Formik
          initialValues={initialValues}
          validationSchema={schema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, errors, touched, setFieldValue }) => {
            const { options: SupportTopicOptions, loading: topicsLoading } =
              useSupportTopicList(values.category_id);
            return (
              <Form className="space-y-6 p-5">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormSelect
                    name="category_id"
                    label={t("commonField.category_type")}
                    options={SupportCategoryOptions}
                    disabled={SupportCategoriesLoading}
                    onChange={(e) => {
                      setFieldValue("category_id", e.target.value);
                      setFieldValue("topic_id", "");
                    }}
                    required
                  />
                  <FormSelect
                    name="topic_id"
                    label={t("commonField.topicType")}
                    options={SupportTopicOptions}
                    disabled={!values.category_id || topicsLoading}
                    required
                  />
                  <FormSelect
                    name="priority"
                    label={t("commonField.priority")}
                    options={priorityOptions}
                    required
                  />
                  <FormSelect
                    name="vendor_id"
                    label={t("commonField.vendor")}
                    options={VendorOptions}
                    disabled={VendorLoading}
                  />

                  {/* <FormInput
                    name="order_id"
                    label={t("commonField.order")}
                    placeholder={t("commonPlaceholder.orderPlaceholder")}
                  /> */}
                  <div className="col-span-2">
                    <FormInput
                      name="subject"
                      label={t("commonField.subject")}
                      placeholder={t("commonPlaceholder.subjectPlaceholder")}
                      required
                    />
                  </div>
                </div>
                <FormTextarea
                  name="message"
                  label={t("commonField.message")}
                  placeholder={t("commonPlaceholder.messagePlaceholder")}
                  required
                />
                {/* <div className="grid grid-cols-2 md:grid-cols-4 gap-6 pt-4">
                  <FormRadioGroup
                    name="status"
                    label="status"
                    options={statusOptions}
                    required
                  />
                </div> */}
                <div className="flex justify-end pt-4 border-t border-gray-200 mt-8 gap-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate("/support/ticket")}
                  >
                    {t("commonButton.cancel")}
                  </Button>
                  <Button type="submit" variant="primary" disabled={loading}>
                    {loading ? (
                      <LoadingSpinner size={20} />
                    ) : id ? (
                      t("commonButton.supportTicket.updated")
                    ) : (
                      t("commonButton.supportTicket.create")
                    )}
                  </Button>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
    </div>
  );
};

export default SupportTicketAddEdit;
