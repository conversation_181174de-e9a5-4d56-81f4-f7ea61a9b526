import { useState, useCallback, useMemo } from "react";
import { motion } from "framer-motion";
import { toast } from "sonner";
import { FaPlus, FaEdit, FaTrash, FaEye } from "react-icons/fa";
import { RiListCheck3 } from "react-icons/ri";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import SupportTopicFilters from "./SupportTopicFilters";
import SupportTopicForm from "./SupportTopicForm";
import { useTranslation } from "react-i18next";
import { getNestedValue } from "@/helper/Commonhelper";
import StatusBadge from "@/components/common/StatusBadge";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import PreviewSection from "./SupportTopicPreview";

const SupportTopicList = () => {
  const { t } = useTranslation();
  const { delete: removeTopic } = useApi();
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [previewId, setPreviewId] = useState(null);

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
  } = usePagination(1, 10);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: "",
    category_id: "",
  });

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const apiParams = useMemo(
    () => ({
      pagination: true,
      per_page: itemsPerPage,
      page: currentPage,
      search: filterOptions.search || "",
      status: filterOptions.status || "",
      category_id: filterOptions.category_id || "",
    }),
    [itemsPerPage, currentPage, filterOptions]
  );

  const {
    data: topicsData,
    isLoading: topicsLoading,
    refetch: refetchTopics,
  } = fetchData("admin/support-topics", apiParams);

  const topicList = topicsData?.data?.data || [];
  const paginationInfo = {
    currentPage: topicsData?.data?.current_page || 1,
    perPage: topicsData?.data?.per_page || itemsPerPage,
    totalItems: topicsData?.data?.total_items || 0,
    totalPages: topicsData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleAddTopic = () => {
    setSelectedTopic(null);
    setIsFormModalOpen(true);
  };

  const handleEditTopic = (topic) => {
    setSelectedTopic(topic);
    setIsFormModalOpen(true);
  };

  const handlePreviewClick = (topic) => {
    setPreviewId(topic.id);
  };

  const handleFormSuccess = () => {
    setIsFormModalOpen(false);
    refetchTopics();
  };

  const handleDeleteClick = (topic) => {
    setSelectedTopic(topic);
    setDeleteModalOpen(true);
  };

  const handleDeleteTopic = async () => {
    if (!selectedTopic) return;
    setDeleteLoading(true);
    removeTopic(`admin/support-topics/${selectedTopic.id}`)
      .then(() => {
        setDeleteModalOpen(false);
        setSelectedTopic(null);
        refetchTopics();
        toast.success(t("commonToast.supportTopicToast.supportTopicDelete"));
      })
      .finally(() => setDeleteLoading(false));
  };

  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.name_en"),
      accessor: "name_en",
    },
    {
      header: t("commonTableLabel.name_ar"),
      accessor: "name_ar",
    },
    {
      header: t("commonTableLabel.category"),
      render: (row) => {
        return getNestedValue(row, "category.name_en", "N/A");
      },
    },
    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row} fieldName="status" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("supportTopic.previewClassContent")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditTopic(row)}
          >
            <FaEdit className="icon-edit" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <>
      <div className="page-container">
        {topicsLoading && <LoadingSpinner size={64} overlay />}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card
            title={t("supportTopic.title")}
            icon={<RiListCheck3 className="title-icon" />}
            action={
              <Button
                variant="primary"
                className="gap-2"
                onClick={handleAddTopic}
              >
                <FaPlus className="mr-2" />
                {t("supportTopic.add")}
              </Button>
            }
          >
            <SupportTopicFilters onChange={handleFilterChange} />
            <Table
              columns={columns}
              data={topicList}
              emptyMessage={t("supportTopic.emptyMessage")}
            />

            <PaginationInfo
              itemsPerPage={itemsPerPage}
              onItemsPerPageChange={handleItemsPerPageChange}
              currentPage={paginationInfo.currentPage}
              totalItems={paginationInfo.totalItems}
              totalPages={paginationInfo.totalPages}
              onPageChange={setCurrentPage}
              className="pagination-info"
            />
          </Card>
        </motion.div>

        {/* PreView Page  */}
        <Modal
          isOpen={previewId}
          onClose={() => setPreviewId(null)}
          title={t("supportTopic.previewClassContent")}
          size="xl"
          showCloseButton={true}
        >
          <PreviewSection
            supportTopicId={previewId}
            onClose={() => setPreviewId(null)}
          />
        </Modal>

        {/* Delete Confirmation Modal */}
        <DeleteConfirmationModal
          isOpen={deleteModalOpen}
          onClose={() => setDeleteModalOpen(false)}
          onDelete={handleDeleteTopic}
          loading={deleteLoading}
          itemName={t("supportTopic.supportTopic")}
          itemValue={selectedTopic?.name_en}
        />
      </div>
      <SupportTopicForm
        isOpen={isFormModalOpen}
        onClose={() => setIsFormModalOpen(false)}
        topic={selectedTopic}
        onSuccess={handleFormSuccess}
      />
    </>
  );
};

export default SupportTopicList;
