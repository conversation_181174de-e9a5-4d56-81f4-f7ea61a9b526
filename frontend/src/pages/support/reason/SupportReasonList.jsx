import { useState, useCallback, useMemo } from "react";
import { motion } from "framer-motion";
import { toast } from "sonner";
import { FaPlus, FaEdit, FaTrash } from "react-icons/fa";
import { RiListCheck3 } from "react-icons/ri";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import SupportReasonFilters from "./SupportReasonFilters.jsx";
import SupportReasonForm from "./SupportReasonForm.jsx";

const SupportReasonList = () => {
  const { delete: removeReason } = useApi();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
  } = usePagination(1, 10);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    statuses: [],
  });

  const [isFormModalOpen, setFormModalOpen] = useState(false);
  const [editingReason, setEditingReason] = useState(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedReason, setSelectedReason] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const apiParams = useMemo(
    () => ({
      pagination: true,
      per_page: itemsPerPage,
      page: currentPage,
      search: filterOptions.search,
      statuses: filterOptions.statuses,
    }),
    [itemsPerPage, currentPage, filterOptions]
  );

  const {
    data: reasonsData,
    isLoading: reasonsLoading,
    refetch: refetchReasons,
  } = fetchData("admin/support-reasons", apiParams);

  const reasonList = reasonsData?.data?.data || [];
  const paginationInfo = {
    currentPage: reasonsData?.data?.current_page || 1,
    perPage: reasonsData?.data?.per_page || itemsPerPage,
    totalItems: reasonsData?.data?.total_items || 0,
    totalPages: reasonsData?.data?.total_pages || 1,
  };

  const handleFilterChange = useCallback(
    (newFilters) => {
      setFilterOptions((prevFilters) => {
        const searchChanged = prevFilters.search !== newFilters.search;
        const statusesChanged =
          JSON.stringify(prevFilters.statuses) !==
          JSON.stringify(newFilters.statuses);

        if (searchChanged || statusesChanged) {
          setCurrentPage(1);
          return newFilters;
        }

        return prevFilters;
      });
    },
    [setCurrentPage]
  );

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleAddReason = () => {
    setEditingReason(null);
    setFormModalOpen(true);
  };

  const handleEditReason = (reason) => {
    setEditingReason(reason);
    setFormModalOpen(true);
  };

  const handleDeleteClick = (reason) => {
    setSelectedReason(reason);
    setDeleteModalOpen(true);
  };

  const handleDeleteReason = async () => {
    if (!selectedReason) return;
    setDeleteLoading(true);
    removeReason(`admin/support-reasons/${selectedReason.id}`)
      .then(() => {
        setDeleteModalOpen(false);
        setSelectedReason(null);
        refetchReasons();
        toast.success("Reason deleted successfully.");
      })
      .finally(() => setDeleteLoading(false));
  };

  const handleSuccess = () => {
    refetchReasons();
  };

  const columns = [
    {
      header: "ID",
      accessor: "id",
    },
    {
      header: "Label",
      accessor: "label",
    },
    {
      header: "Code Prefix",
      accessor: "code_prefix",
    },
    {
      header: "Status",
      accessor: "status",
      render: (row) => {
        const statusColors = {
          active: "bg-green-100 text-green-800",
          inactive: "bg-gray-100 text-gray-800",
        };

        return (
          <span
            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
              statusColors[row.status]
            }`}
          >
            {row.status.charAt(0).toUpperCase() + row.status.slice(1)}
          </span>
        );
      },
    },
    {
      header: "Actions",
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditReason(row)}
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(row)}
          >
            <FaTrash className="text-red-600" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="page-container">
      {reasonsLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title="Reason List"
          icon={<RiListCheck3 className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="mt-4"
              onClick={handleAddReason}
            >
              <FaPlus className="mr-2" /> Add Reason
            </Button>
          }
        >
          <SupportReasonFilters onChange={handleFilterChange} />
          <Table
            columns={columns}
            data={reasonList}
            emptyMessage="No reasons found."
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      <SupportReasonForm
        isOpen={isFormModalOpen}
        onClose={() => setFormModalOpen(false)}
        reasonData={editingReason}
        onSuccess={handleSuccess}
      />

      <Modal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="Confirm Delete"
        size="sm"
      >
        <div className="py-4">
          <p className="text-gray-700">
            Are you sure you want to delete the reason{" "}
            <span className="font-semibold">{selectedReason?.label}</span>? This
            action cannot be undone.
          </p>
        </div>
        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={() => setDeleteModalOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleDeleteReason}
            disabled={deleteLoading}
          >
            {deleteLoading ? <LoadingSpinner size={20} /> : "Delete"}
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default SupportReasonList;
