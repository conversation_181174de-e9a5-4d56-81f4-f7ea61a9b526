import { useState, useCallback, useMemo } from "react";
import { motion } from "framer-motion";
import { toast } from "sonner";
import { FaPlus, FaEdit, FaTrash, FaEye } from "react-icons/fa";
import { RiListCheck3 } from "react-icons/ri";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import SupportCategoryFilters from "./SupportCategoryFilters";
import SupportCategoryForm from "./SupportCategoryForm";
import { useTranslation } from "react-i18next";
import StatusBadge from "@/components/common/StatusBadge";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import PreviewSection from "./SupportCategoryPreview";

const SupportCategoryList = () => {
  const { t } = useTranslation();
  const { delete: removeCategory } = useApi();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentCategory, setCurrentCategory] = useState(null);

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
  } = usePagination(1, 10);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: "",
  });

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [previewId, setPreviewId] = useState(null);

  const apiParams = useMemo(
    () => ({
      pagination: true,
      per_page: itemsPerPage,
      page: currentPage,
      search: filterOptions.search || "",
      status: filterOptions.status || "",
    }),
    [itemsPerPage, currentPage, filterOptions]
  );

  const {
    data: categoriesData,
    isLoading: categoriesLoading,
    refetch: refetchCategories,
  } = fetchData("admin/support-categories", apiParams);

  const categoryList = categoriesData?.data?.data || [];
  const paginationInfo = {
    currentPage: categoriesData?.data?.current_page || 1,
    perPage: categoriesData?.data?.per_page || itemsPerPage,
    totalItems: categoriesData?.data?.total_items || 0,
    totalPages: categoriesData?.data?.total_pages || 1,
  };

  const handleFilterChange = useCallback(
    (newFilters) => {
      setFilterOptions((prevFilters) => {
        const searchChanged = prevFilters.search !== newFilters.search;
        const statusesChanged =
          JSON.stringify(prevFilters.status) !==
          JSON.stringify(newFilters.status);

        if (searchChanged || statusesChanged) {
          setCurrentPage(1);
          return newFilters;
        }

        return prevFilters;
      });
    },
    [setCurrentPage]
  );

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditCategory = (category) => {
    setCurrentCategory(category);
    setIsModalOpen(true);
  };

  const handleAddCategory = () => {
    setCurrentCategory(null);
    setIsModalOpen(true);
  };

  const handlePreviewClick = (category) => {
    setPreviewId(category.id);
  };

  const handleFormSuccess = () => {
    refetchCategories();
  };

  const handleDeleteClick = (category) => {
    setCurrentCategory(category);
    setDeleteModalOpen(true);
  };

  const handleDeleteCategory = async () => {
    if (!currentCategory) return;
    setDeleteLoading(true);
    removeCategory(`admin/support-categories/${currentCategory.id}`)
      .then(() => {
        setDeleteModalOpen(false);
        setCurrentCategory(null);
        refetchCategories();
        toast.success(t("commonToast.categoryToast.categoryDelete"));
      })
      .finally(() => setDeleteLoading(false));
  };

  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.name_en"),
      accessor: "name_en",
    },
    {
      header: t("commonTableLabel.name_ar"),
      accessor: "name_ar",
    },
    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row} fieldName="status" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("category.previewClassContent")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditCategory(row)}
          >
            <FaEdit className="icon-edit" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="page-container">
      {categoriesLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("category.titleSupport")}
          icon={<RiListCheck3 className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={handleAddCategory}
            >
              <FaPlus className="mr-2" /> {t("category.add")}
            </Button>
          }
        >
          <SupportCategoryFilters onChange={handleFilterChange} />
          <Table
            columns={columns}
            data={categoryList}
            emptyMessage={t("category.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={currentCategory ? t("category.editAction") : t("category.add")}
        size="md"
      >
        <SupportCategoryForm
          category={currentCategory}
          onSuccess={handleFormSuccess}
          onClose={() => setIsModalOpen(false)}
        />
      </Modal>

      {/* PreView Page  */}
      <Modal
        isOpen={previewId}
        onClose={() => setPreviewId(null)}
        title={t("category.previewClassContent")}
        size="xl"
        showCloseButton={true}
      >
        <PreviewSection
          supportCategoryId={previewId}
          onClose={() => setPreviewId(null)}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onDelete={handleDeleteCategory}
        loading={deleteLoading}
        itemName={t("commonField.category")}
        itemValue={currentCategory?.name_en}
      />
    </div>
  );
};

export default SupportCategoryList;
