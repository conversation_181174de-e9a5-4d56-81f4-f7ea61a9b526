import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import OfferAndDealFilter from "./offerAndDealFilter";
import {
  FaEdit,
  FaTrash,
  FaClipboardList,
  FaPlus,
  FaEye,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import NoImage from "@/assets/NoImage.png";
import StatusBadge from "@/components/common/StatusBadge";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import PreviewSection from "./offerAndDealPreview";

const OfferAndDealIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedOfferDeal, setSelectedOfferDeal] = useState(null);
  const [previewId, setPreviewId] = useState(null);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    is_active: "",
    type: "",
  });

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: offerDealData,
    isLoading,
    isError: offerDealError,
    refetch,
  } = fetchData("admin/offer-and-deals", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    is_active: filterOptions.is_active || "",
    type: filterOptions.type || "",
  });

  const offerDealList = offerDealData?.data?.data || [];
  const paginationInfo = {
    currentPage: offerDealData?.data?.current_page || 1,
    perPage: offerDealData?.data?.per_page || itemsPerPage,
    totalItems: offerDealData?.data?.total_items || 0,
    totalPages: offerDealData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditOfferDeal = (offerDeal) => {
    navigate(`/OfferDeal/edit/${offerDeal.id}`);
  };

  const handlePreviewClick = (offerDeal) => {
    setPreviewId(offerDeal.id);
  };

  const handleDeleteClick = (offerDeal) => {
    setSelectedOfferDeal(offerDeal);
  };

  const handleDeleteCategory = async () => {
    if (!selectedOfferDeal) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/offer-and-deals/${selectedOfferDeal.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setSelectedOfferDeal(null);
          refetch();
          toast.success(t("commonToast.offerDealToast.offerDealDelete"));
        },
        onError: (error) => {
          console.error(
            "Offer and Deal deletion failed:",
            error?.response?.data || error.message
          );
          toast.error(
            "Offer and Deal deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const canEditOfferDeal = (offerDeal) => true;

  const canDeleteOfferDeal = (offerDeal) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.image"),
      accessor: "image_url",
      render: (row) =>
        row.image_url ? (
          <img
            src={row.image_url}
            alt={row.title_en}
            className="w-20 h-10 object-contain rounded-sm shadow"
          />
        ) : (
          <img
            src={NoImage}
            alt={row.title_en}
            className="w-20 h-10 object-contain rounded-sm shadow"
          />
        ),
    },
    {
      header: t("commonTableLabel.title_en"),
      accessor: "title_en",
    },
    {
      header: t("commonTableLabel.title_ar"),
      accessor: "title_ar",
    },
    {
      header: t("commonField.regularPrice"),
      accessor: "regular_price",
    },
    {
      header: t("commonField.offerPrice"),
      accessor: "offer_price",
    },
    {
      header: t("commonField.discountPrice"),
      accessor: "discount_percentage",
    },
    {
      header: t("commonTableLabel.type"),
      accessor: "type",
      render: (row) => {
        const typeColors = {
          product: "bg-purple-100 text-purple-800",
          shipping: "bg-indigo-100 text-indigo-800",
        };

        return (
          <span
            className={`w-18 text-center px-2 py-1 inline-flex justify-center text-sm leading-5 font-medium rounded-md shadow-md ${
              typeColors[row.type] || "bg-gray-100 text-gray-800"
            }`}
          >
            {row.type?.toUpperCase()}
          </span>
        );
      },
    },
    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row} fieldName="is_active" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("offerDeal.previewClassContent")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditOfferDeal(row)}
            disabled={!canEditOfferDeal(row)}
            title={
              !canEditOfferDeal(row)
                ? "You don't have permission to edit this Offer & Deal"
                : t("offerDeal.editAction")
            }
          >
            <FaEdit className="icon-edit" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteOfferDeal(row)}
            title={
              !canDeleteOfferDeal(row)
                ? "You can't delete this Offer & Deal"
                : t("offerDeal.deleteAction")
            }
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("offerDeal.title")}
          icon={<FaClipboardList className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => navigate("/OfferDeal/add")}
            >
              <FaPlus className="mr-2" /> {t("offerDeal.add")}
            </Button>
          }
        >
          <OfferAndDealFilter onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={offerDealList}
            emptyMessage={t("offerDeal.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      {/* PreView Page  */}
      <Modal
        isOpen={previewId}
        onClose={() => setPreviewId(null)}
        title={t("offerDeal.previewClassContent")}
        size="xl"
        showCloseButton={true}
      >
        <PreviewSection
          offerDealId={previewId}
          onClose={() => setPreviewId(null)}
        />
      </Modal>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationModal
        isOpen={!!selectedOfferDeal}
        onClose={() => setSelectedOfferDeal(null)}
        onDelete={handleDeleteCategory}
        loading={deleteLoading}
        itemName={t("offerDeal.offerDeal")}
        itemValue={selectedOfferDeal?.title_en}
      />
    </div>
  );
};

export default OfferAndDealIndex;
