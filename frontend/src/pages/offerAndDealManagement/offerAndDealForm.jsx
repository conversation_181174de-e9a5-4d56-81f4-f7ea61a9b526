import { Formik, Form } from "formik";
import * as Yup from "yup";
import {
  FormInput,
  FormRadioGroup,
  FormTextarea,
  FormDatePicker,
  FormSelect,
} from "@/components/ui/form";
import ImageUploader from "@/components/ui/ImageUploader";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import {
  statusBooleanOptions,
  offerDealTypeStatusOptions,
} from "@/constants/filterOption";

const OfferAndDealForm = ({ offerDeal, onSubmit, onCancel }) => {
  const { t } = useTranslation();

  const initialValues = offerDeal
    ? {
        title_en: offerDeal.title_en || "",
        title_ar: offerDeal.title_ar || "",
        description_en: offerDeal.description_en || "",
        description_ar: offerDeal.description_ar || "",
        tag: offerDeal.tag || "",
        image: offerDeal.image_url || "",
        link: offerDeal.link || "",
        type: offerDeal.type || "",
        regular_price: offerDeal.regular_price || "",
        offer_price: offerDeal.offer_price || "",
        discount_percentage: offerDeal.discount_percentage || "",
        start_time: offerDeal.start_time || "",
        end_time: offerDeal.end_time || "",
        is_active: offerDeal.is_active || false,
      }
    : {
        title_en: "",
        title_ar: "",
        description_en: "",
        description_ar: "",
        tag: "",
        image: "",
        link: "",
        type: "",
        regular_price: "",
        offer_price: "",
        discount_percentage: "",
        start_time: "",
        end_time: "",
        is_active: false,
      };

  const validationSchema = Yup.object({
    title_en: Yup.string().required(t("commonValidation.title_en")),
    type: Yup.string().required(t("commonValidation.type")),
    regular_price: Yup.number()
      .typeError(t("commonValidation.validNumber"))
      .positive(t("commonValidation.positiveNumber"))
      .nullable(),
    discount_percentage: Yup.number()
      .typeError(t("commonValidation.validNumber"))
      .positive(t("commonValidation.positiveNumber"))
      .nullable(),
    offer_price: Yup.number()
      .typeError(t("commonValidation.validNumber"))
      .positive(t("commonValidation.positiveNumber"))
      .nullable(),
  });

  const toUploadPathIfUrl = (val) => {
    if (!val || typeof val !== "string") return val;
    const m = val.match(/\/uploads\/.+$/);
    return m ? m[0].replace(/^\//, "") : val;
  };

  const handleSubmit = (values, { setSubmitting }) => {
    const formattedValues = {
      ...values,
      // image: values.image_url || "",
      image: values.image?.path || values.image || "",
    };
    if (formattedValues.image) {
      formattedValues.image = toUploadPathIfUrl(formattedValues.image);
    }
    onSubmit(formattedValues);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, setFieldValue, values }) => (
        <Form className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormInput
                  name="title_en"
                  label={t("commonField.title_en")}
                  placeholder={t("commonPlaceholder.title_enPlaceholder")}
                  required
                />
                <FormInput
                  name="title_ar"
                  label={t("commonField.title_ar")}
                  placeholder={t("commonPlaceholder.title_arPlaceholder")}
                />
                <FormSelect
                  name="type"
                  label={t("commonTableLabel.type")}
                  options={offerDealTypeStatusOptions}
                  required
                />
                <FormInput
                  name="tag"
                  label={t("commonField.tag")}
                  placeholder={t("commonPlaceholder.tagPlaceholder")}
                />
                <FormInput
                  name="link"
                  label={t("commonField.link")}
                  placeholder={t("commonPlaceholder.linkUrlPlaceholder")}
                />
                <FormInput
                  name="regular_price"
                  label={t("commonField.regularPrice")}
                  placeholder={t("commonPlaceholder.regularPricePlaceholder")}
                />
                <FormInput
                  name="discount_percentage"
                  label={t("commonField.discountPrice")}
                  placeholder={t("commonPlaceholder.discountPricePlaceholder")}
                />
                <FormInput
                  name="offer_price"
                  label={t("commonField.offerPrice")}
                  placeholder={t("commonPlaceholder.offerPricePlaceholder")}
                />
                <FormDatePicker
                  name="start_time"
                  label={t("commonField.start_date")}
                />
                <FormDatePicker
                  name="end_time"
                  label={t("commonField.end_date")}
                />
              </div>
              <FormRadioGroup
                name="is_active"
                label={t("commonField.status")}
                options={statusBooleanOptions}
              />
            </div>

            <div className="p-3 space-y-4">
              <FormTextarea
                name="description_en"
                label={t("commonField.description_en")}
                placeholder={t("commonPlaceholder.description_enPlaceholder")}
              />
              <FormTextarea
                name="description_ar"
                label={t("commonField.description_ar")}
                placeholder={t("commonPlaceholder.description_arPlaceholder")}
              />
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  {t("commonField.bannerImage")}
                </label>
                <ImageUploader
                  value={values.image}
                  onUploadSuccess={(url) => setFieldValue("image", url)}
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end pt-4 border-t border-gray-200 mt-8 gap-3">
            <Button type="button" variant="outline" onClick={onCancel}>
              {t("commonButton.cancel")}
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {offerDeal
                ? t("commonButton.offerDeal.updated")
                : t("commonButton.offerDeal.create")}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default OfferAndDealForm;
