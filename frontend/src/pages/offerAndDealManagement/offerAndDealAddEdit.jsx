import { useParams, useNavigate } from "react-router-dom";
import { useState } from "react";
import { toast } from "sonner";
import OfferDealFormPage from "./offerAndDealForm";
import { useApi } from "@/hooks/useApi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useTranslation } from "react-i18next";
import BackArrowIcon from "@/components/common/BackArrowIcon";

const OfferAndDealAddEdit = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const { fetchData, postMutation, putMutation } = useApi();

  const { data, isLoading, isError, refetch } = id
    ? fetchData(`admin/offer-and-deals/${id}`)
    : { data: null, isLoading: false, isError: false, refetch: () => {} };
  const offerDealData = data?.data;

  const handleSubmit = (values) => {
    setLoading(true);
    const dataToSend = { ...values };

    const onSuccess = () => {
      toast.success(
        t(
          id
            ? "commonToast.offerDealToast.offerDealUpdate"
            : "commonToast.offerDealToast.offerDealCreate"
        )
      );
      navigate("/OfferDeal/list");
    };

    const onError = (error) => {
      console.error(
        "Offer & Deal update failed:",
        error?.response?.data || error.message
      );
      toast.error(
        "Offer & Deal update failed: " +
          (error?.response?.data?.message || error.message)
      );
    };

    const onSettled = () => setLoading(false);

    if (!id) {
      postMutation.mutate(
        { endpoint: "admin/offer-and-deals", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        {
          endpoint: `admin/offer-and-deals/${id}`,
          data: dataToSend,
        },
        { onSuccess, onError, onSettled }
      );
    }
  };

  if (isLoading || loading) {
    return (
      <div className="loading-error">
        <LoadingSpinner />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="loading-error">
        <div className="text-red-500">
          Error loading Offer & Deal data. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="page-container">
      <div className="card-style">
        <div className="title-add-edit-card">
          <div className="title-add-edit-div">
            <button onClick={() => navigate("/OfferDeal/list")}>
              <BackArrowIcon />
            </button>
            <h3 className="title-add-edit">
              {t(id ? "offerDeal.editAction" : "offerDeal.add")}
            </h3>
          </div>
        </div>
        <div className="px-6 py-4">
          <OfferDealFormPage
            offerDeal={offerDealData}
            onSubmit={handleSubmit}
            onCancel={() => navigate("/OfferDeal/list")}
            loading={loading}
          />
        </div>
      </div>
    </div>
  );
};

export default OfferAndDealAddEdit;
