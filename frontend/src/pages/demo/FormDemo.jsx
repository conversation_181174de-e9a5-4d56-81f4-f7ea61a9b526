import { useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import Card from "../../components/ui/Card";
import {
  FormInput,
  FormCheckbox,
  FormRadioGroup,
  FormSelect,
  FormDatePicker,
  FormTextarea,
  FormSwitch,
  RichTextEditor,
} from "../../components/ui/form";
import Button from "../../components/ui/Button";
import Tabs from "../../components/ui/Tabs";

const FormDemo = () => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState(null);

  const initialValues = {
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    bio: "",
    birthDate: "",
    gender: "",
    country: "",
    agreeTerms: false,
    receiveNotifications: true,
    employmentStatus: "",
    interests: [],
    richTextContent: "<p>Enter your rich content here</p>",
  };

  const validationSchema = Yup.object({
    firstName: Yup.string().required("First name is required"),
    lastName: Yup.string().required("Last name is required"),
    email: Yup.string()
      .email("Invalid email address")
      .required("Email is required"),
    password: Yup.string()
      .min(8, "Password must be at least 8 characters")
      .required("Password is required"),
    bio: Yup.string().max(200, "Bio must be at most 200 characters"),
    birthDate: Yup.date().nullable(),
    gender: Yup.string().required("Gender is required"),
    country: Yup.string().required("Country is required"),
    agreeTerms: Yup.boolean().oneOf([true], "You must agree to the terms"),
    employmentStatus: Yup.string().required("Employment status is required"),
    richTextContent: Yup.string().max(
      5000,
      "Content must be less than 5000 characters"
    ),
  });

  const handleSubmit = (values, { setSubmitting }) => {
    setTimeout(() => {
      setFormData(values);
      setSubmitting(false);
      // resetForm();
    }, 1000);
  };

  const genderOptions = [
    { value: "male", label: t("demo.form.genders.male") },
    { value: "female", label: t("demo.form.genders.female") },
    { value: "other", label: t("demo.form.genders.other") },
    {
      value: "prefer_not_to_say",
      label: t("demo.form.genders.preferNotToSay"),
    },
  ];

  const countryOptions = [
    { value: "us", label: t("demo.form.countries.us") },
    { value: "ca", label: t("demo.form.countries.ca") },
    { value: "uk", label: t("demo.form.countries.uk") },
    { value: "au", label: t("demo.form.countries.au") },
    { value: "de", label: t("demo.form.countries.de") },
    { value: "fr", label: t("demo.form.countries.fr") },
    { value: "jp", label: t("demo.form.countries.jp") },
    { value: "in", label: t("demo.form.countries.in") },
    { value: "br", label: t("demo.form.countries.br") },
    { value: "ae", label: t("demo.form.countries.ae") },
  ];

  const employmentOptions = [
    { value: "full_time", label: t("demo.form.employment.fullTime") },
    { value: "part_time", label: t("demo.form.employment.partTime") },
    { value: "self_employed", label: t("demo.form.employment.selfEmployed") },
    { value: "freelance", label: t("demo.form.employment.freelance") },
    { value: "unemployed", label: t("demo.form.employment.unemployed") },
    { value: "student", label: t("demo.form.employment.student") },
    { value: "retired", label: t("demo.form.employment.retired") },
  ];

  const tabs = [
    {
      label: t("demo.form.title"),
      content: (
        <div className="py-4">
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            {({ values, isSubmitting, errors, setFieldValue }) => (
              <Form className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormInput
                    name="firstName"
                    label={t("demo.form.fields.firstName")}
                    placeholder="John"
                    required
                  />

                  <FormInput
                    name="lastName"
                    label={t("demo.form.fields.lastName")}
                    placeholder="Doe"
                    required
                  />
                </div>

                <FormInput
                  name="email"
                  type="email"
                  label={t("demo.form.fields.email")}
                  placeholder="<EMAIL>"
                  helperText={t("demo.form.fields.emailHelper")}
                  required
                />

                <FormInput
                  name="password"
                  type="password"
                  label={t("demo.form.fields.password")}
                  placeholder="••••••••"
                  helperText={t("demo.form.fields.passwordHelper")}
                  required
                />

                <FormTextarea
                  name="bio"
                  label={t("demo.form.fields.bio")}
                  placeholder={t("demo.form.fields.bioPlaceholder")}
                  helperText={t("demo.form.fields.bioHelper")}
                  rows={4}
                />

                <FormDatePicker
                  name="birthDate"
                  label={t("demo.form.fields.birthDate")}
                  helperText={t("demo.form.fields.birthDateHelper")}
                />

                <FormRadioGroup
                  name="gender"
                  label={t("demo.form.fields.gender")}
                  options={genderOptions}
                  required
                />

                <FormSelect
                  name="country"
                  label={t("demo.form.fields.country")}
                  options={countryOptions}
                  placeholder={t("demo.form.fields.countryPlaceholder")}
                  required
                />

                <FormRadioGroup
                  name="employmentStatus"
                  label={t("demo.form.fields.employmentStatus")}
                  options={employmentOptions}
                  required
                />

                <FormSwitch
                  name="receiveNotifications"
                  label={t("demo.form.fields.notifications")}
                  helperText={t("demo.form.fields.notificationsHelper")}
                />
                <div>{JSON.stringify(values?.richTextContent)}</div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    {t("demo.form.fields.richText")}
                  </label>
                  <RichTextEditor
                    name="richTextContent"
                    value={values.richTextContent}
                    onChange={(content) =>
                      setFieldValue("richTextContent", content)
                    }
                    placeholder={t("demo.form.fields.richTextPlaceholder")}
                  />
                  {/* {errors.richTextContent && touched.richTextContent ? (
                    <p className="mt-1 text-sm text-red-600">{errors.richTextContent}</p>
                  ) : null} */}
                </div>

                <FormCheckbox
                  name="agreeTerms"
                  label={t("demo.form.fields.terms")}
                  required
                />

                <div className="flex justify-end rtl-justify-start space-x-4 rtl-space-x-reverse">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => console.log("Cancel clicked")}
                  >
                    {t("demo.form.cancel")}
                  </Button>

                  <Button
                    type="submit"
                    variant="primary"
                    loading={isSubmitting}
                    disabled={isSubmitting}
                  >
                    {isSubmitting
                      ? t("demo.form.submitting")
                      : t("demo.form.submit")}
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      ),
    },
    {
      label: t("demo.form.formData"),
      content: (
        <div className="py-4">
          {formData ? (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="title-add-edit mb-4 rtl-text-right text-left">
                {t("demo.form.submittedData")}
              </h3>
              <pre className="bg-white p-4 rounded border overflow-auto max-h-96">
                {JSON.stringify(formData, null, 2)}
              </pre>
            </div>
          ) : (
            <div className="py-8 text-gray-500 rtl-text-right text-left">
              <p>{t("demo.form.noDataMessage")}</p>
            </div>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card title={t("demo.form.title")}>
          <Tabs tabs={tabs} />
        </Card>
      </motion.div>
    </div>
  );
};

export default FormDemo;
