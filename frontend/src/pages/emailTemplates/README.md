# Email Template Management System

A comprehensive email template management system for the UAE E-commerce admin portal.

## Features

### Core Functionality
- **Template Management**: Create, edit, delete, and duplicate email templates
- **Category Organization**: Organize templates into categories for better management
- **Variable System**: Dynamic variables with validation and type checking
- **Version Control**: Track changes and restore previous versions
- **Preview System**: Live preview with variable substitution
- **Test Email**: Send test emails to verify templates

### Template Features
- Rich text editor with HTML support
- Plain text fallback for email clients
- Variable insertion with auto-completion
- Template validation and syntax checking
- Multi-language support (English/Arabic)
- Priority levels and tagging system

### Category Management
- Hierarchical organization
- Custom icons and descriptions
- Sort ordering
- Template count tracking

### Variable System
- Categorized variables (user, order, vendor, site, auth)
- Data type validation (string, number, boolean, array, object)
- Required/optional flags
- Default values and examples
- Regular expression validation

### Version Control
- Complete change history
- Diff tracking for modifications
- Restore to previous versions
- Change reason tracking
- User attribution

## Components Structure

```
emailTemplates/
├── EmailTemplateIndex.jsx          # Main listing page
├── EmailTemplateCreateEdit.jsx     # Create/Edit form page
├── EmailTemplateForm.jsx           # Template form component
├── EmailTemplateFilters.jsx        # Search and filter component
├── EmailTemplatePreviewModal.jsx   # Preview modal with test email
├── categories/
│   ├── CategoryIndex.jsx           # Category listing
│   ├── CategoryDetail.jsx          # Category detail view
│   └── CategoryForm.jsx            # Category form
├── variables/
│   ├── VariableIndex.jsx           # Variable listing
│   └── VariableForm.jsx            # Variable form
├── history/
│   └── TemplateHistory.jsx         # Version history
├── index.js                        # Component exports
└── README.md                       # This file
```

## API Integration

The system integrates with the following API endpoints:

### Templates
- `GET /admin/email-templates/templates` - List templates
- `GET /admin/email-templates/templates/{uuid}` - Get template
- `POST /admin/email-templates/templates` - Create template
- `PUT /admin/email-templates/templates/{uuid}` - Update template
- `DELETE /admin/email-templates/templates/{uuid}` - Delete template
- `POST /admin/email-templates/templates/{uuid}/preview` - Preview template
- `POST /admin/email-templates/templates/{uuid}/test` - Send test email

### Categories
- `GET /admin/email-templates/categories` - List categories
- `GET /admin/email-templates/categories/{slug}` - Get category
- `POST /admin/email-templates/categories` - Create category
- `PUT /admin/email-templates/categories/{slug}` - Update category
- `DELETE /admin/email-templates/categories/{slug}` - Delete category

### Variables
- `GET /admin/email-templates/variables` - List variables
- `GET /admin/email-templates/variables/{id}` - Get variable
- `POST /admin/email-templates/variables` - Create variable
- `PUT /admin/email-templates/variables/{id}` - Update variable
- `DELETE /admin/email-templates/variables/{id}` - Delete variable

### History
- `GET /admin/email-templates/templates/{uuid}/history` - Get history
- `GET /admin/email-templates/templates/{uuid}/history/{version}` - Get version
- `POST /admin/email-templates/templates/{uuid}/history/{version}/restore` - Restore version

## Permissions

The system uses the following permissions:
- `BROWSE_EMAIL_TEMPLATES` - View template list
- `VIEW_EMAIL_TEMPLATE` - View template details
- `CREATE_EMAIL_TEMPLATE` - Create new templates
- `EDIT_EMAIL_TEMPLATE` - Edit existing templates
- `DELETE_EMAIL_TEMPLATE` - Delete templates
- `MANAGE_EMAIL_TEMPLATE_CATEGORIES` - Manage categories
- `MANAGE_EMAIL_TEMPLATE_VARIABLES` - Manage variables

## Navigation

The system is accessible through the sidebar menu:
- Email Templates
  - All Templates
  - Categories
  - Variables

## Routing

The following routes are available:
- `/email-templates` - Main template listing
- `/email-templates/create` - Create new template
- `/email-templates/edit/:uuid` - Edit template
- `/email-templates/history/:uuid` - Template history
- `/email-templates/categories` - Category management
- `/email-templates/categories/:slug` - Category detail
- `/email-templates/variables` - Variable management

## Variable Syntax

Templates support Handlebars-style variable syntax:

### Basic Variables
```handlebars
{{user.name}}
{{order.total}}
{{site.name}}
```

### Conditional Logic
```handlebars
{{#if user.is_premium}}
  Premium content here
{{else}}
  Regular content here
{{/if}}
```

### Loops
```handlebars
{{#each order.items}}
  <li>{{name}} - ${{price}}</li>
{{/each}}
```

## Available Variables

### User Variables
- `user.name` - User's full name
- `user.email` - User's email address
- `user.phone` - User's phone number
- `user.is_premium` - Premium status

### Order Variables
- `order.number` - Order number
- `order.total` - Order total amount
- `order.status` - Order status
- `order.items` - Array of order items
- `order.date` - Order date

### Site Variables
- `site.name` - Site name
- `site.url` - Site URL
- `site.logo` - Site logo URL
- `site.support_email` - Support email

### Authentication Variables
- `auth.otp_code` - OTP verification code
- `auth.reset_token` - Password reset token
- `auth.login_url` - Login URL

## Styling

The system follows the existing design patterns:
- Tailwind CSS for styling
- Framer Motion for animations
- Consistent color scheme and spacing
- Responsive design for all screen sizes
- RTL support for Arabic language

## Dependencies

- React 18+
- React Router DOM
- Formik & Yup for forms
- TanStack Query for API calls
- Framer Motion for animations
- React Icons
- TipTap for rich text editing
- Sonner for notifications
- React i18next for translations
