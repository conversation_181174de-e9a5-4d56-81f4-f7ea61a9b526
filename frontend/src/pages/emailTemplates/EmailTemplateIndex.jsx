import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import EmailTemplateFilters from "./EmailTemplateFilters";
import {
  FaEdit,
  FaTrash,
  FaPlus,
  FaEye,
  FaEnvelope,
  FaList,
  FaCog,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Pagination from "@/components/ui/Pagination";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import EmailTemplatePreviewModal from "./EmailTemplatePreviewModal";

const EmailTemplateIndex = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [filterOptions, setFilterOptions] = useState({});
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);
  const { deleteMutation } = useApi();

  const {
    data: templateData,
    isLoading,
    isError: templateError,
    refetch,
  } = fetchData("admin/email-templates/templates", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    category_id: filterOptions.category_id || "",
    language: filterOptions.language || "en",
    is_active: filterOptions.is_active || "",
  });

  // Handle nested API response structure: {status: true, data: {data: [...], meta: {...}}}
  const templateList = templateData?.data?.data || [];
  const paginationInfo = {
    currentPage: templateData?.data?.current_page || 1,
    perPage: templateData?.data?.per_page || itemsPerPage,
    totalItems: templateData?.data?.total_items || 0,
    totalPages: templateData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleDeleteClick = (template) => {
    setSelectedTemplate(template);
    setShowDeleteModal(true);
  };

  const handlePreviewClick = (template) => {
    setSelectedTemplate(template);
    setShowPreviewModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteMutation.mutateAsync({
        endpoint: `admin/email-templates/templates/${selectedTemplate.uuid}`,
      });
      toast.success(t("emailTemplate.deleteSuccess"));
      refetch();
      setShowDeleteModal(false);
      setSelectedTemplate(null);
    } catch (error) {
      toast.error(t("emailTemplate.deleteError"));
    }
  };



  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.templateName"),
      accessor: "name",
      render: (template) => (
        <div className="flex items-center">
          <div>
            <div className="text-sm font-medium text-gray-900">
              {template.name}
            </div>
            <div className="text-sm text-gray-500">{template.description}</div>
          </div>
        </div>
      ),
    },
    {
      header: t("emailTemplate.subject"),
      accessor: "subject",
      render: (template) => (
        <div className="text-sm text-gray-900 max-w-xs truncate">
          {template.subject}
        </div>
      ),
    },
    {
      header: t("commonTableLabel.category"),
      accessor: "category",
      render: (template) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {template.category?.name || t("commonButton.notAvailable")}
        </span>
      ),
    },
    {
      header: t("commonTableLabel.language"),
      accessor: "language",
      render: (template) => (
        <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
          {template.language?.toUpperCase()}
        </span>
      ),
    },
    {
      header: t("commonTableLabel.status"),
      accessor: "is_active",
      render: (template) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            template.is_active
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {template.is_active ? t("commonOptions.status.active") : t("commonOptions.status.inactive")}
        </span>
      ),
    },
    {
      header: t("commonTableLabel.actions"),
      accessor: "actions",
      render: (template) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePreviewClick(template)}
            title={t("emailTemplate.preview")}
          >
            <FaEye className="text-blue-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate(`/email-templates/edit/${template.uuid}`)}
            title={t("emailTemplate.edit")}
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(template)}
            title={t("emailTemplate.delete")}
          >
            <FaTrash className="text-red-600" />
          </Button>
        </div>
      ),
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner size={40} />
      </div>
    );
  }

  if (templateError) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{t("emailTemplate.loadError")}</p>
        <Button onClick={() => refetch()} className="mt-4">
          {t("common.retry")}
        </Button>
      </div>
    );
  }

  return (
    <div className="mx-auto relative p-5">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("emailTemplate.title")}
          icon={<FaEnvelope className="text-indigo-600 me-1" />}
          action={
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
              <Button
                onClick={() => navigate("/email-templates/categories")}
                variant="outline"
                className="flex items-center justify-center sm:justify-start space-x-2 w-full sm:w-auto"
                size="sm"
              >
                <FaList className="w-4 h-4" />
                <span className="hidden sm:inline">{t("emailTemplate.manageCategories")}</span>
                <span className="sm:hidden">Categories</span>
              </Button>
              <Button
                onClick={() => navigate("/email-templates/variables")}
                variant="outline"
                className="flex items-center justify-center sm:justify-start space-x-2 w-full sm:w-auto"
                size="sm"
              >
                <FaCog className="w-4 h-4" />
                <span className="hidden sm:inline">{t("emailTemplate.manageVariables")}</span>
                <span className="sm:hidden">Variables</span>
              </Button>
              <Button
                variant="primary"
                className="flex items-center justify-center sm:justify-start gap-2 w-full sm:w-auto"
                onClick={() => navigate("/email-templates/create")}
                size="sm"
              >
                <FaPlus className="w-4 h-4" />
                <span className="hidden sm:inline">{t("emailTemplate.createNew")}</span>
                <span className="sm:hidden">Create</span>
              </Button>
            </div>
          }
        >
          <EmailTemplateFilters
            onFilterChange={handleFilterChange}
            filterOptions={filterOptions}
          />

          <Table
            columns={columns}
            data={templateList}
            emptyMessage={t("emailTemplate.noTemplates")}
          />

          <PaginationInfo
            currentPage={paginationInfo.currentPage}
            totalPages={paginationInfo.totalPages}
            totalItems={paginationInfo.totalItems}
            itemsPerPage={paginationInfo.perPage}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        </Card>
      </motion.div>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title={t("emailTemplate.deleteConfirmTitle")}
      >
        <div className="p-6">
          <p className="text-gray-600 mb-4">
            {t("emailTemplate.deleteConfirmMessage", {
              name: selectedTemplate?.name,
            })}
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
            >
              {t("commonButton.cancel")}
            </Button>
            <Button
              variant="danger"
              onClick={handleDeleteConfirm}
              disabled={deleteMutation.isLoading}
            >
              {deleteMutation.isLoading ? (
                <LoadingSpinner size={16} />
              ) : (
                t("commonButton.delete")
              )}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Preview Modal */}
      {showPreviewModal && selectedTemplate && (
        <EmailTemplatePreviewModal
          template={selectedTemplate}
          onClose={() => setShowPreviewModal(false)}
        />
      )}
    </div>
  );
};

export default EmailTemplateIndex;
