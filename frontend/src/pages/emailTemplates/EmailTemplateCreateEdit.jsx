import { use<PERSON>ara<PERSON>, useNavigate, useSearchParams } from "react-router-dom";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";
import EmailTemplateForm from "./EmailTemplateForm";
import { useApi, fetchData } from "@/hooks/useApi";
import { get } from "@/services/apiService";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { FaArrowLeft, FaCode, FaPlus, FaTimes } from "react-icons/fa";

// Variables Panel Component
const VariablesPanel = ({ selectedVariables = [], onVariableAdd, onVariableRemove }) => {
  const { t } = useTranslation();

  // Fetch variables grouped by category
  const { data: variablesData } = fetchData("admin/email-templates/variables", {
    group_by_category: true,
  });

  const variables = variablesData?.data?.data || variablesData?.data || {};

  const handleVariableClick = (variableKey) => {
    if (onVariableAdd && !selectedVariables.includes(variableKey)) {
      // Add to selection if callback provided
      onVariableAdd(variableKey);
      toast.success(t("emailTemplate.variableAdded"));
    } else {
      // Copy to clipboard as fallback
      navigator.clipboard.writeText(`{{${variableKey}}}`);
      toast.success(t("emailTemplate.variableCopied"));
    }
  };

  if (!variables || Object.keys(variables).length === 0) {
    return (
      <Card title={t("emailTemplate.variables")} className="h-fit [&>div:last-child]:p-3 [&>div:last-child]:pt-2">
        <div className="text-center py-6">
          <FaCode className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-sm text-gray-500">{t("emailTemplate.noVariables")}</p>
        </div>
      </Card>
    );
  }

  return (
    <Card title={t("emailTemplate.variables")} className="h-fit [&>div:last-child]:p-3 [&>div:last-child]:pt-2">
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {Object.entries(variables).map(([category, categoryVariables]) => (
          <div key={category} className="border-b border-gray-200 last:border-b-0 pb-3 last:pb-0">
            <h4 className="text-sm font-semibold text-gray-900 mb-2 capitalize">
              {category}
            </h4>
            <div className="space-y-1">
              {Array.isArray(categoryVariables) && categoryVariables.map((variable) => {
                const isSelected = selectedVariables.includes(variable.key);
                return (
                  <button
                    key={variable.id}
                    onClick={() => handleVariableClick(variable.key)}
                    className={`w-full text-left p-2 rounded-md border transition-colors group ${
                      isSelected
                        ? 'bg-blue-50 border-blue-200 hover:bg-blue-100'
                        : 'hover:bg-gray-50 border-gray-200'
                    }`}
                    title={isSelected ? t("emailTemplate.variableSelected") : t("emailTemplate.clickToAdd")}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className={`text-sm font-medium truncate ${
                          isSelected ? 'text-blue-900' : 'text-gray-900'
                        }`}>
                          {variable.name}
                        </p>
                        <p className="text-xs text-gray-500 truncate">
                          <code>{"{{" + variable.key + "}}"}</code>
                        </p>
                      </div>
                      {isSelected ? (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onVariableRemove && onVariableRemove(variable.key);
                          }}
                          className="w-3 h-3 text-blue-600 hover:text-red-600 flex-shrink-0 ml-2"
                          title={t("emailTemplate.removeVariable")}
                        >
                          <FaTimes className="w-3 h-3" />
                        </button>
                      ) : (
                        <FaPlus className="w-3 h-3 text-gray-400 group-hover:text-blue-500 flex-shrink-0 ml-2" />
                      )}
                    </div>
                    {variable.description && (
                      <p className="text-xs text-gray-400 mt-1 truncate">
                        {variable.description}
                      </p>
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};

const EmailTemplateCreateEdit = () => {
  const { uuid } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [templateData, setTemplateData] = useState(null);
  const [isLoadingTemplate, setIsLoadingTemplate] = useState(false);
  const [selectedVariables, setSelectedVariables] = useState([]);



  const { postMutation, putMutation } = useApi();
  const duplicateUuid = searchParams.get("duplicate");
  const isEditing = !!uuid;
  const isDuplicating = !!duplicateUuid;

  // Variable management functions
  const handleVariableAdd = (variableKey) => {
    if (!selectedVariables.includes(variableKey)) {
      setSelectedVariables([...selectedVariables, variableKey]);
    }
  };

  const handleVariableRemove = (variableKey) => {
    setSelectedVariables(selectedVariables.filter((v) => v !== variableKey));
  };

  // Fetch template data for editing or duplicating
  useEffect(() => {
    const loadTemplate = async () => {
      const targetUuid = uuid || duplicateUuid;
      if (!targetUuid) return;

      setIsLoadingTemplate(true);
      try {
        const response = await get(`admin/email-templates/templates/${targetUuid}`);
        if (response?.data?.data) {
          let template = response.data.data;

          // If duplicating, modify the template data
          if (isDuplicating) {
            template = {
              ...template,
              name: `${template.name} (Copy)`,
              is_default: false,
              uuid: undefined, // Remove UUID for new template
            };
          }
          setTemplateData(template);
          // Set selected variables if they exist
          if (template.variables) {
            setSelectedVariables(template.variables);
          }
        }
      } catch (error) {
        toast.error(t("emailTemplate.loadError"));
        navigate("/email-templates");
      } finally {
        setIsLoadingTemplate(false);
      }
    };

    loadTemplate();
  }, [uuid, duplicateUuid, isDuplicating, t, navigate]);

  const handleSubmit = async (formData) => {
    setLoading(true);
    try {
      // Include selected variables in form data
      const dataWithVariables = {
        ...formData,
        variables: selectedVariables,
      };

      if (isEditing) {
        // Update existing template
        await putMutation.mutateAsync({
          endpoint: `admin/email-templates/templates/${uuid}`,
          data: dataWithVariables,
        });
        toast.success(t("emailTemplate.updateSuccess"));
      } else {
        // Create new template
        await postMutation.mutateAsync({
          endpoint: "admin/email-templates/templates",
          data: dataWithVariables,
        });
        toast.success(t("emailTemplate.createSuccess"));
      }
      navigate("/email-templates");
    } catch (error) {
      const errorMessage = error.response?.data?.message || 
        (isEditing ? t("emailTemplate.updateError") : t("emailTemplate.createError"));
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate("/email-templates");
  };

  const getPageTitle = () => {
    if (isDuplicating) return t("emailTemplate.duplicateTitle");
    if (isEditing) return t("emailTemplate.editTitle");
    return t("emailTemplate.createTitle");
  };

  const getPageSubtitle = () => {
    if (isDuplicating) return t("emailTemplate.duplicateSubtitle");
    if (isEditing) return t("emailTemplate.editSubtitle");
    return t("emailTemplate.createSubtitle");
  };

  if (isLoadingTemplate) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner size={40} />
      </div>
    );
  }

  return (
    <div className="mx-auto relative p-5">
      {loading && <LoadingSpinner size={64} overlay />}

      {/* Simplified Header */}
      <div className="mb-6">
        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/email-templates")}
            className="text-gray-600 hover:text-gray-800"
          >
            <FaArrowLeft className="w-5 h-5" />
          </Button>
          <h1 className="text-2xl font-bold text-gray-900">
            {getPageTitle()}
          </h1>
        </div>
      </div>

      {/* Two-Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:items-start">
        {/* Left Column - Form (2/3 width) */}
        <div className="lg:col-span-2">
          <Card className="[&>div:last-child]:p-3 [&>div:last-child]:pt-2">
            <EmailTemplateForm
              template={templateData}
              onSubmit={handleSubmit}
              onCancel={handleCancel}
              loading={loading}
            />
          </Card>
        </div>

        {/* Right Column - Variables and Help (1/3 width) */}
        <div className="space-y-4">
          {/* Variables Section */}
          <VariablesPanel
            selectedVariables={selectedVariables}
            onVariableAdd={handleVariableAdd}
            onVariableRemove={handleVariableRemove}
          />

          {/* Template Help Section */}
          <Card title={t("emailTemplate.helpTitle")} className="h-fit [&>div:last-child]:p-3 [&>div:last-child]:pt-2">
            <div className="prose prose-sm max-w-none">
              <h4 className="text-sm font-semibold text-gray-900 mb-2">{t("emailTemplate.variableUsage")}</h4>
              <p className="text-sm text-gray-600 mb-4">{t("emailTemplate.variableUsageDescription")}</p>

              <h4 className="text-sm font-semibold text-gray-900 mb-2">{t("emailTemplate.examples")}</h4>
              <ul className="text-sm text-gray-600 space-y-1 mb-4">
                <li>
                  <code className="bg-gray-100 px-1 rounded text-xs">{"{{user.name}}"}</code> - {t("emailTemplate.exampleUserName")}
                </li>
                <li>
                  <code className="bg-gray-100 px-1 rounded text-xs">{"{{site.name}}"}</code> - {t("emailTemplate.exampleSiteName")}
                </li>
                <li>
                  <code className="bg-gray-100 px-1 rounded text-xs">{"{{order.number}}"}</code> - {t("emailTemplate.exampleOrderNumber")}
                </li>
              </ul>

              <h4 className="text-sm font-semibold text-gray-900 mb-2">{t("emailTemplate.conditionalLogic")}</h4>
              <pre className="bg-gray-100 p-2 rounded text-xs mb-4 overflow-x-auto">
                {'{{#if user.is_premium}}\n  Premium content\n{{else}}\n  Regular content\n{{/if}}'}
              </pre>

              <h4 className="text-sm font-semibold text-gray-900 mb-2">{t("emailTemplate.loops")}</h4>
              <pre className="bg-gray-100 p-2 rounded text-xs overflow-x-auto">
                {'{{#each order.items}}\n  <li>{{name}} - ${{price}}</li>\n{{/each}}'}
              </pre>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default EmailTemplateCreateEdit;
