import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import {
  FaArrowLeft,
  FaHistory,
  FaEye,
  FaUndo,
  FaUser,
  FaClock,
  FaCode,
  FaFileAlt,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import { toast } from "sonner";

const TemplateHistory = () => {
  const { uuid } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [selectedVersion, setSelectedVersion] = useState(null);
  const [showVersionModal, setShowVersionModal] = useState(false);
  const [showRestoreModal, setShowRestoreModal] = useState(false);
  const [restoreReason, setRestoreReason] = useState("");

  const { postMutation } = useApi();

  // Fetch template info
  const {
    data: templateData,
    isLoading: isLoadingTemplate,
  } = fetchData(`admin/email-templates/templates/${uuid}`);

  // Fetch template history
  const {
    data: historyData,
    isLoading: isLoadingHistory,
    isError,
    refetch,
  } = fetchData(`admin/email-templates/templates/${uuid}/history`);

  // Handle nested API response structure: {status: true, data: {data: {...}}}
  const template = templateData?.data?.data || templateData?.data;
  const history = historyData?.data?.data || historyData?.data || [];

  const handleViewVersion = async (version) => {
    try {
      const response = await fetchData(
        `admin/email-templates/templates/${uuid}/history/${version.version_number}`
      );
      setSelectedVersion(response.data);
      setShowVersionModal(true);
    } catch (error) {
      toast.error(t("emailTemplate.history.loadVersionError"));
    }
  };

  const handleRestoreClick = (version) => {
    setSelectedVersion(version);
    setShowRestoreModal(true);
  };

  const handleRestoreConfirm = async () => {
    if (!restoreReason.trim()) {
      toast.error(t("emailTemplate.history.restoreReasonRequired"));
      return;
    }

    try {
      await postMutation.mutateAsync({
        endpoint: `admin/email-templates/templates/${uuid}/history/${selectedVersion.version_number}/restore`,
        data: { change_reason: restoreReason },
      });
      toast.success(t("emailTemplate.history.restoreSuccess"));
      setShowRestoreModal(false);
      setRestoreReason("");
      setSelectedVersion(null);
      refetch();
    } catch (error) {
      toast.error(t("emailTemplate.history.restoreError"));
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const getChangeTypeColor = (changes) => {
    if (!changes) return "bg-gray-100 text-gray-800";
    
    const hasAdditions = changes.variables?.added?.length > 0;
    const hasRemovals = changes.variables?.removed?.length > 0;
    const hasModifications = Object.keys(changes).some(key => 
      key !== "variables" && changes[key]
    );

    if (hasRemovals) return "bg-red-100 text-red-800";
    if (hasAdditions) return "bg-green-100 text-green-800";
    if (hasModifications) return "bg-blue-100 text-blue-800";
    return "bg-gray-100 text-gray-800";
  };

  const renderChangeSummary = (changes) => {
    if (!changes) return t("emailTemplate.history.noChanges");

    const summaryItems = [];
    
    Object.keys(changes).forEach(key => {
      if (key === "variables") {
        if (changes.variables.added?.length > 0) {
          summaryItems.push(
            t("emailTemplate.history.variablesAdded", { 
              count: changes.variables.added.length 
            })
          );
        }
        if (changes.variables.removed?.length > 0) {
          summaryItems.push(
            t("emailTemplate.history.variablesRemoved", { 
              count: changes.variables.removed.length 
            })
          );
        }
      } else if (changes[key]) {
        summaryItems.push(
          t(`emailTemplate.history.${key}Changed`, { 
            field: t(`emailTemplate.${key}`) 
          })
        );
      }
    });

    return summaryItems.length > 0 
      ? summaryItems.join(", ") 
      : t("emailTemplate.history.minorChanges");
  };

  if (isLoadingTemplate || isLoadingHistory) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner size={40} />
      </div>
    );
  }

  if (isError || !template) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{t("emailTemplate.history.loadError")}</p>
        <Button onClick={() => navigate("/email-templates")} className="mt-4">
          {t("common.back")}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => navigate("/email-templates")}
            className="flex items-center space-x-2"
          >
            <FaArrowLeft className="w-4 h-4" />
            <span>{t("common.back")}</span>
          </Button>
          <div className="flex items-center space-x-3">
            <FaHistory className="w-6 h-6 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {t("emailTemplate.history.title")}
              </h1>
              <p className="text-gray-600">{template.name}</p>
            </div>
          </div>
        </div>
        <Button
          onClick={() => navigate(`/email-templates/edit/${uuid}`)}
          className="flex items-center space-x-2"
        >
          <FaFileAlt className="w-4 h-4" />
          <span>{t("emailTemplate.editTemplate")}</span>
        </Button>
      </div>

      {/* Breadcrumb */}
      <nav className="flex" aria-label="Breadcrumb">
        <ol className="inline-flex items-center space-x-1 md:space-x-3">
          <li className="inline-flex items-center">
            <button
              onClick={() => navigate("/email-templates")}
              className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
            >
              {t("emailTemplate.title")}
            </button>
          </li>
          <li>
            <div className="flex items-center">
              <span className="mx-2 text-gray-400">/</span>
              <span className="text-sm font-medium text-gray-500">
                {t("emailTemplate.history.title")}
              </span>
            </div>
          </li>
        </ol>
      </nav>

      {/* Current Template Info */}
      <Card title={t("emailTemplate.history.currentVersion")}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {t("emailTemplate.history.current")}
            </div>
            <div className="text-sm text-gray-500">
              {t("emailTemplate.version")}
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {template.is_active ? t("commonOptions.status.active") : t("commonOptions.status.inactive")}
            </div>
            <div className="text-sm text-gray-500">
              {t("emailTemplate.status")}
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {formatDate(template.updated_at)}
            </div>
            <div className="text-sm text-gray-500">
              {t("emailTemplate.lastModified")}
            </div>
          </div>
        </div>
      </Card>

      {/* Version History */}
      <Card title={t("emailTemplate.history.versionHistory")}>
        {history.length > 0 ? (
          <div className="space-y-4">
            {history.map((version, index) => (
              <motion.div
                key={version.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
                      <span className="text-sm font-bold text-blue-600">
                        v{version.version_number}
                      </span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getChangeTypeColor(
                            version.changes_summary
                          )}`}
                        >
                          {renderChangeSummary(version.changes_summary)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        {version.change_reason || t("emailTemplate.history.noReason")}
                      </p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <div className="flex items-center space-x-1">
                          <FaUser className="w-3 h-3" />
                          <span>{version.created_by?.name}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <FaClock className="w-3 h-3" />
                          <span>{formatDate(version.created_at)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewVersion(version)}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      <FaEye className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRestoreClick(version)}
                      className="text-green-600 hover:text-green-800"
                    >
                      <FaUndo className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <FaHistory className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t("emailTemplate.history.noHistory")}
            </h3>
            <p className="text-gray-500">
              {t("emailTemplate.history.noHistoryDescription")}
            </p>
          </div>
        )}
      </Card>

      {/* Version Detail Modal */}
      <Modal
        isOpen={showVersionModal}
        onClose={() => setShowVersionModal(false)}
        title={t("emailTemplate.history.versionDetails")}
        size="xl"
      >
        {selectedVersion && (
          <div className="p-6 space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">
                  {t("emailTemplate.version")}:
                </span>
                <span className="ml-2">v{selectedVersion.version_number}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">
                  {t("emailTemplate.history.changedBy")}:
                </span>
                <span className="ml-2">{selectedVersion.changed_by?.name}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">
                  {t("emailTemplate.history.changeDate")}:
                </span>
                <span className="ml-2">{formatDate(selectedVersion.created_at)}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">
                  {t("emailTemplate.history.changeReason")}:
                </span>
                <span className="ml-2">
                  {selectedVersion.change_reason || t("emailTemplate.history.noReason")}
                </span>
              </div>
            </div>

            <div className="border-t pt-4">
              <h4 className="font-medium text-gray-900 mb-2">
                {t("emailTemplate.subject")}
              </h4>
              <p className="text-gray-700 bg-gray-50 p-3 rounded">
                {selectedVersion.subject}
              </p>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-2">
                {t("emailTemplate.htmlContent")}
              </h4>
              <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto max-h-64">
                {selectedVersion.body_html}
              </pre>
            </div>

            {selectedVersion.variables && selectedVersion.variables.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">
                  {t("emailTemplate.variables")}
                </h4>
                <div className="flex flex-wrap gap-2">
                  {selectedVersion.variables.map((variable) => (
                    <span
                      key={variable}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {variable}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* Restore Confirmation Modal */}
      <Modal
        isOpen={showRestoreModal}
        onClose={() => setShowRestoreModal(false)}
        title={t("emailTemplate.history.restoreVersion")}
      >
        <div className="p-6">
          <p className="text-gray-600 mb-4">
            {t("emailTemplate.history.restoreConfirmMessage", {
              version: selectedVersion?.version_number,
            })}
          </p>
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t("emailTemplate.history.restoreReason")} *
            </label>
            <textarea
              value={restoreReason}
              onChange={(e) => setRestoreReason(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
              placeholder={t("emailTemplate.history.restoreReasonPlaceholder")}
            />
          </div>
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowRestoreModal(false)}
            >
              {t("common.cancel")}
            </Button>
            <Button
              onClick={handleRestoreConfirm}
              disabled={postMutation.isLoading || !restoreReason.trim()}
              className="flex items-center space-x-2"
            >
              {postMutation.isLoading && <LoadingSpinner size={16} />}
              <span>{t("emailTemplate.history.restore")}</span>
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default TemplateHistory;
