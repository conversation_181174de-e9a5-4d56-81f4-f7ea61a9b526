import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { I18nextProvider } from 'react-i18next';
import { vi } from 'vitest';
import CategoryIndex from '../CategoryIndex';
import i18n from '@/i18n';

// Mock the API hook
vi.mock('@/hooks/useApi', () => ({
  fetchData: vi.fn(),
  useApi: () => ({
    deleteMutation: {
      mutateAsync: vi.fn(),
      isLoading: false,
    },
  }),
}));

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock sonner
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18n}>
        <BrowserRouter>
          {children}
        </BrowserRouter>
      </I18nextProvider>
    </QueryClientProvider>
  );
};

describe('CategoryIndex', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const mockCategories = [
    {
      id: 1,
      name: 'Authentication',
      slug: 'authentication',
      description: 'Email templates for user authentication',
      is_active: true,
      templates_count: 5,
      sort_order: 1,
    },
    {
      id: 2,
      name: 'Orders',
      slug: 'orders',
      description: 'Email templates for order confirmations',
      is_active: true,
      templates_count: 3,
      sort_order: 2,
    },
  ];

  it('renders category list correctly', async () => {
    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: { data: mockCategories },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    });

    render(<CategoryIndex />, { wrapper: createWrapper() });

    expect(screen.getByText('Template Categories')).toBeInTheDocument();
    expect(screen.getByText('Authentication')).toBeInTheDocument();
    expect(screen.getByText('Orders')).toBeInTheDocument();
  });

  it('shows loading state', async () => {
    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: null,
      isLoading: true,
      isError: false,
      refetch: vi.fn(),
    });

    render(<CategoryIndex />, { wrapper: createWrapper() });

    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('shows error state', async () => {
    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: null,
      isLoading: false,
      isError: true,
      refetch: vi.fn(),
    });

    render(<CategoryIndex />, { wrapper: createWrapper() });

    expect(screen.getByText('Failed to load categories')).toBeInTheDocument();
  });

  it('opens create modal when create button is clicked', async () => {
    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: { data: mockCategories },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    });

    render(<CategoryIndex />, { wrapper: createWrapper() });

    const createButton = screen.getByText('Create Category');
    fireEvent.click(createButton);

    await waitFor(() => {
      expect(screen.getByText('Create Category')).toBeInTheDocument();
    });
  });

  it('filters categories by search term', async () => {
    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: { data: mockCategories },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    });

    render(<CategoryIndex />, { wrapper: createWrapper() });

    const searchInput = screen.getByPlaceholderText('Search categories...');
    fireEvent.change(searchInput, { target: { value: 'auth' } });

    expect(searchInput.value).toBe('auth');
  });

  it('opens edit modal when edit button is clicked', async () => {
    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: { data: mockCategories },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    });

    render(<CategoryIndex />, { wrapper: createWrapper() });

    const editButtons = screen.getAllByTitle('Edit Category');
    fireEvent.click(editButtons[0]);

    await waitFor(() => {
      expect(screen.getByText('Edit Category')).toBeInTheDocument();
    });
  });

  it('disables delete button for categories with templates', async () => {
    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: { data: mockCategories },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    });

    render(<CategoryIndex />, { wrapper: createWrapper() });

    const deleteButtons = screen.getAllByTitle(/Delete/);
    expect(deleteButtons[0]).toBeDisabled(); // First category has 5 templates
  });

  it('opens delete confirmation for empty categories', async () => {
    const emptyCategory = {
      ...mockCategories[0],
      templates_count: 0,
    };

    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: { data: [emptyCategory] },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    });

    render(<CategoryIndex />, { wrapper: createWrapper() });

    const deleteButton = screen.getByTitle('Delete Category');
    fireEvent.click(deleteButton);

    await waitFor(() => {
      expect(screen.getByText('Delete Category')).toBeInTheDocument();
    });
  });

  it('displays template count correctly', async () => {
    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: { data: mockCategories },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    });

    render(<CategoryIndex />, { wrapper: createWrapper() });

    expect(screen.getByText('5')).toBeInTheDocument(); // Templates count for Authentication
    expect(screen.getByText('3')).toBeInTheDocument(); // Templates count for Orders
  });

  it('shows active/inactive status correctly', async () => {
    const categoriesWithInactive = [
      ...mockCategories,
      {
        id: 3,
        name: 'Inactive Category',
        slug: 'inactive',
        description: 'An inactive category',
        is_active: false,
        templates_count: 0,
        sort_order: 3,
      },
    ];

    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: { data: categoriesWithInactive },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    });

    render(<CategoryIndex />, { wrapper: createWrapper() });

    expect(screen.getAllByText('Active')).toHaveLength(2);
    expect(screen.getByText('Inactive')).toBeInTheDocument();
  });
});
