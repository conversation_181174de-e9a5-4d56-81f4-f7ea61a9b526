import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { I18nextProvider } from 'react-i18next';
import { vi } from 'vitest';
import CategoryForm from '../CategoryForm';
import i18n from '@/i18n';

// Mock the API hook
const mockPostMutation = {
  mutateAsync: vi.fn(),
  isLoading: false,
};

const mockPutMutation = {
  mutateAsync: vi.fn(),
  isLoading: false,
};

vi.mock('@/hooks/useApi', () => ({
  useApi: () => ({
    postMutation: mockPostMutation,
    putMutation: mockPutMutation,
  }),
}));

// Mock useSlugSync hook
const mockHandleSlugSync = vi.fn();
vi.mock('@/hooks/useSlugSync', () => ({
  useSlugSync: () => mockHandleSlugSync,
}));

// Mock sonner
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18n}>
        {children}
      </I18nextProvider>
    </QueryClientProvider>
  );
};

describe('CategoryForm', () => {
  const mockOnSuccess = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const mockCategory = {
    id: 1,
    name: 'Authentication',
    slug: 'authentication',
    description: 'Email templates for user authentication',
    icon: '🔐',
    sort_order: 1,
    is_active: true,
  };

  it('renders create form correctly', () => {
    render(
      <CategoryForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    expect(screen.getByLabelText('Category Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Icon')).toBeInTheDocument();
    expect(screen.getByLabelText('Description')).toBeInTheDocument();
    expect(screen.getByText('Create')).toBeInTheDocument();
  });

  it('renders edit form with pre-filled values', () => {
    render(
      <CategoryForm 
        category={mockCategory} 
        onSuccess={mockOnSuccess} 
        onCancel={mockOnCancel} 
      />,
      { wrapper: createWrapper() }
    );

    expect(screen.getByDisplayValue('Authentication')).toBeInTheDocument();
    expect(screen.getByDisplayValue('🔐')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Email templates for user authentication')).toBeInTheDocument();
    expect(screen.getByText('Update')).toBeInTheDocument();
  });

  it('validates required fields', async () => {
    render(
      <CategoryForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    const submitButton = screen.getByText('Create');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Category name is required')).toBeInTheDocument();
    });
  });

  it('validates name length', async () => {
    render(
      <CategoryForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    const nameInput = screen.getByLabelText('Category Name');
    fireEvent.change(nameInput, { target: { value: 'A' } });
    fireEvent.blur(nameInput);

    await waitFor(() => {
      expect(screen.getByText('Category name must be at least 2 characters')).toBeInTheDocument();
    });
  });

  it('validates sort order range', async () => {
    render(
      <CategoryForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    const sortOrderInput = screen.getByLabelText('Sort Order');
    fireEvent.change(sortOrderInput, { target: { value: '0' } });
    fireEvent.blur(sortOrderInput);

    await waitFor(() => {
      expect(screen.getByText('Sort order must be at least 1')).toBeInTheDocument();
    });
  });

  it('calls slug sync when name changes', async () => {
    render(
      <CategoryForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    const nameInput = screen.getByLabelText('Category Name');
    fireEvent.change(nameInput, { target: { value: 'Test Category' } });

    expect(mockHandleSlugSync).toHaveBeenCalled();
  });

  it('submits create form successfully', async () => {
    mockPostMutation.mutateAsync.mockResolvedValue({});

    render(
      <CategoryForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    // Fill form
    fireEvent.change(screen.getByLabelText('Category Name'), {
      target: { value: 'Test Category' },
    });
    fireEvent.change(screen.getByLabelText('Description'), {
      target: { value: 'Test description' },
    });
    fireEvent.change(screen.getByLabelText('Icon'), {
      target: { value: '📧' },
    });

    // Submit form
    fireEvent.click(screen.getByText('Create'));

    await waitFor(() => {
      expect(mockPostMutation.mutateAsync).toHaveBeenCalledWith({
        endpoint: 'admin/email-templates/categories',
        data: expect.objectContaining({
          name: 'Test Category',
          description: 'Test description',
          icon: '📧',
          sort_order: 1,
          is_active: true,
        }),
      });
      expect(mockOnSuccess).toHaveBeenCalled();
    });
  });

  it('submits edit form successfully', async () => {
    mockPutMutation.mutateAsync.mockResolvedValue({});

    render(
      <CategoryForm 
        category={mockCategory} 
        onSuccess={mockOnSuccess} 
        onCancel={mockOnCancel} 
      />,
      { wrapper: createWrapper() }
    );

    // Update name
    fireEvent.change(screen.getByLabelText('Category Name'), {
      target: { value: 'Updated Authentication' },
    });

    // Submit form
    fireEvent.click(screen.getByText('Update'));

    await waitFor(() => {
      expect(mockPutMutation.mutateAsync).toHaveBeenCalledWith({
        endpoint: `admin/email-templates/categories/${mockCategory.slug}`,
        data: expect.objectContaining({
          name: 'Updated Authentication',
          icon: '🔐',
        }),
      });
      expect(mockOnSuccess).toHaveBeenCalled();
    });
  });

  it('calls onCancel when cancel button is clicked', () => {
    render(
      <CategoryForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    fireEvent.click(screen.getByText('Cancel'));
    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('shows preview when name is entered', async () => {
    render(
      <CategoryForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    fireEvent.change(screen.getByLabelText('Category Name'), {
      target: { value: 'Test Category' },
    });
    fireEvent.change(screen.getByLabelText('Icon'), {
      target: { value: '📧' },
    });

    await waitFor(() => {
      expect(screen.getByText('Preview')).toBeInTheDocument();
      expect(screen.getByText('Test Category')).toBeInTheDocument();
      expect(screen.getByText('📧')).toBeInTheDocument();
    });
  });

  it('handles status radio buttons correctly', async () => {
    render(
      <CategoryForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    const inactiveRadio = screen.getByLabelText('Inactive');
    fireEvent.click(inactiveRadio);

    expect(inactiveRadio).toBeChecked();
  });

  it('handles sort order input correctly', async () => {
    render(
      <CategoryForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    const sortOrderInput = screen.getByLabelText('Sort Order');
    fireEvent.change(sortOrderInput, { target: { value: '5' } });

    expect(sortOrderInput.value).toBe('5');
  });
});
