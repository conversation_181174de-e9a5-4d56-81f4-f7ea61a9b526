import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import SearchInput from "@/components/ui/SearchInput";
import FilterDropdown from "@/components/ui/FilterDropdown";
import Button from "@/components/ui/Button";

const CategoryFilters = ({ onFilterChange }) => {
  const { t } = useTranslation();
  const [filters, setFilters] = useState({
    search: "",
    is_active: "",
  });

  const statusOptions = [
    { value: "", label: t("emailTemplate.allStatuses") },
    { value: "1", label: t("commonOptions.status.active") },
    { value: "0", label: t("commonOptions.status.inactive") },
  ];

  const triggerChange = (updated) => {
    const payload = {};
    if (updated.search?.trim()) payload.search = updated.search.trim();
    if (updated.is_active) payload.is_active = updated.is_active;
    onFilterChange?.(payload);
  };

  const handleSearchChange = (val) => {
    const updated = { ...filters, search: val };
    setFilters(updated);
    triggerChange(updated);
  };

  const handleFilterChange = (key, values) => {
    const updated = { ...filters, [key]: values };
    setFilters(updated);
    triggerChange(updated);
  };

  const handleReset = () => {
    const reset = { search: "", is_active: "" };
    setFilters(reset);
    onFilterChange?.({});
  };

  return (
    <div className="mb-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
        <div className="flex-1 max-w-md">
          <SearchInput
            value={filters.search}
            onChange={handleSearchChange}
            placeholder={t("emailTemplate.categoryManagement.searchPlaceholder")}
          />
        </div>

        <div className="flex flex-wrap items-center gap-2">
          <FilterDropdown
            label={t("commonTableLabel.status")}
            className="w-40"
            options={statusOptions}
            selectedValues={filters.is_active}
            onChange={(values) => handleFilterChange("is_active", values)}
          />
          <Button variant="outline" onClick={handleReset} className="ml-2">
            {t("commonButton.reset")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CategoryFilters;
