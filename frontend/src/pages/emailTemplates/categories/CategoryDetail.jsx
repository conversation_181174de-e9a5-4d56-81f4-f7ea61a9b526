import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import {
  FaArrowLeft,
  FaEdit,
  FaPlus,
  FaEye,
  FaTrash,
  FaFolder,
  FaEnvelope,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData } from "@/hooks/useApi";
import CategoryForm from "./CategoryForm";

const CategoryDetail = () => {
  const { slug } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [showEditModal, setShowEditModal] = useState(false);

  const {
    data: categoryData,
    isLoading,
    isError,
    refetch,
  } = fetchData(`admin/email-templates/categories/${slug}`);

  // Handle nested API response structure: {status: true, data: {data: {...}}}
  const category = categoryData?.data?.data || categoryData?.data;
  const templates = category?.templates || [];

  const handleEditSuccess = () => {
    refetch();
    setShowEditModal(false);
  };

  const templateColumns = [
    {
      header: t("emailTemplate.name"),
      accessor: "name",
      render: (template) => (
        <div className="flex items-center space-x-3">
          <FaEnvelope className="w-4 h-4 text-blue-500" />
          <div>
            <div className="font-medium text-gray-900">{template.name}</div>
            <div className="text-sm text-gray-500">{template.slug}</div>
          </div>
        </div>
      ),
    },
    {
      header: t("emailTemplate.subject"),
      accessor: "subject",
      render: (template) => (
        <span className="text-sm text-gray-600 truncate max-w-xs">
          {template.subject}
        </span>
      ),
    },
    {
      header: t("emailTemplate.language"),
      accessor: "language",
      render: (template) => (
        <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
          {template.language?.toUpperCase()}
        </span>
      ),
    },
    {
      header: t("emailTemplate.status"),
      accessor: "is_active",
      render: (template) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            template.is_active
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {template.is_active ? t("commonOptions.status.active") : t("commonOptions.status.inactive")}
        </span>
      ),
    },
    {
      header: t("emailTemplate.default"),
      accessor: "is_default",
      render: (template) => (
        template.is_default && (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            {t("emailTemplate.default")}
          </span>
        )
      ),
    },
    {
      header: t("common.actions"),
      accessor: "actions",
      render: (template) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/email-templates/preview/${template.uuid}`)}
            className="text-blue-600 hover:text-blue-800"
          >
            <FaEye className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/email-templates/edit/${template.uuid}`)}
            className="text-green-600 hover:text-green-800"
          >
            <FaEdit className="w-4 h-4" />
          </Button>
        </div>
      ),
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner size={40} />
      </div>
    );
  }

  if (isError || !category) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{t("emailTemplate.category.loadError")}</p>
        <Button onClick={() => navigate("/email-templates/categories")} className="mt-4">
          {t("common.back")}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => navigate("/email-templates/categories")}
            className="flex items-center space-x-2"
          >
            <FaArrowLeft className="w-4 h-4" />
            <span>{t("common.back")}</span>
          </Button>
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg">
              {category.icon ? (
                <span className="text-xl">{category.icon}</span>
              ) : (
                <FaFolder className="w-6 h-6 text-blue-600" />
              )}
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {category.name}
              </h1>
              <p className="text-gray-600">{category.description}</p>
            </div>
          </div>
        </div>
        <div className="flex space-x-3">
          <Button
            onClick={() => setShowEditModal(true)}
            variant="outline"
            className="flex items-center space-x-2"
          >
            <FaEdit className="w-4 h-4" />
            <span>{t("common.edit")}</span>
          </Button>
          <Button
            onClick={() => navigate(`/email-templates/create?category=${category.id}`)}
            className="flex items-center space-x-2"
          >
            <FaPlus className="w-4 h-4" />
            <span>{t("emailTemplate.createNew")}</span>
          </Button>
        </div>
      </div>

      {/* Breadcrumb */}
      <nav className="flex" aria-label="Breadcrumb">
        <ol className="inline-flex items-center space-x-1 md:space-x-3">
          <li className="inline-flex items-center">
            <button
              onClick={() => navigate("/email-templates")}
              className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
            >
              {t("emailTemplate.title")}
            </button>
          </li>
          <li>
            <div className="flex items-center">
              <span className="mx-2 text-gray-400">/</span>
              <button
                onClick={() => navigate("/email-templates/categories")}
                className="text-sm font-medium text-gray-700 hover:text-blue-600"
              >
                {t("emailTemplate.categoryManagement.title")}
              </button>
            </div>
          </li>
          <li>
            <div className="flex items-center">
              <span className="mx-2 text-gray-400">/</span>
              <span className="text-sm font-medium text-gray-500">
                {category.name}
              </span>
            </div>
          </li>
        </ol>
      </nav>

      {/* Category Info */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {category.templates_count || 0}
            </div>
            <div className="text-sm text-gray-500">
              {t("emailTemplate.templates")}
            </div>
          </div>
        </Card>
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {category.sort_order}
            </div>
            <div className="text-sm text-gray-500">
              {t("emailTemplate.category.sortOrder")}
            </div>
          </div>
        </Card>
        <Card>
          <div className="text-center">
            <div className={`text-2xl font-bold ${category.is_active ? 'text-green-600' : 'text-red-600'}`}>
              {category.is_active ? t("commonOptions.status.active") : t("commonOptions.status.inactive")}
            </div>
            <div className="text-sm text-gray-500">
              {t("emailTemplate.status")}
            </div>
          </div>
        </Card>
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {category.slug}
            </div>
            <div className="text-sm text-gray-500">
              {t("emailTemplate.category.slug")}
            </div>
          </div>
        </Card>
      </div>

      {/* Templates */}
      <Card title={t("emailTemplate.templatesInCategory")}>
        {templates.length > 0 ? (
          <Table
            columns={templateColumns}
            data={templates}
            emptyMessage={t("emailTemplate.noTemplatesInCategory")}
          />
        ) : (
          <div className="text-center py-12">
            <FaEnvelope className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t("emailTemplate.noTemplatesInCategory")}
            </h3>
            <p className="text-gray-500 mb-4">
              {t("emailTemplate.noTemplatesInCategoryDescription")}
            </p>
            <Button
              onClick={() => navigate(`/email-templates/create?category=${category.id}`)}
              className="flex items-center space-x-2"
            >
              <FaPlus className="w-4 h-4" />
              <span>{t("emailTemplate.createFirst")}</span>
            </Button>
          </div>
        )}
      </Card>

      {/* Edit Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title={t("emailTemplate.category.editTitle")}
        size="lg"
      >
        <CategoryForm
          category={category}
          onSuccess={handleEditSuccess}
          onCancel={() => setShowEditModal(false)}
        />
      </Modal>
    </div>
  );
};

export default CategoryDetail;
