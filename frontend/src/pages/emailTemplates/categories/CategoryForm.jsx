import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import {
  FormInput,
  FormTextarea,
  FormRadioGroup,
} from "@/components/ui/form";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useApi } from "@/hooks/useApi";
import { useSlugSync } from "@/hooks/useSlugSync";

const CategoryForm = ({ category, onSuccess, onCancel }) => {
  const { t } = useTranslation();
  const { postMutation, putMutation } = useApi();
  const handleSlugSync = useSlugSync();
  const isEditing = !!category;

  const initialValues = category
    ? {
        name: category.name || "",
        description: category.description || "",
        icon: category.icon || "",
        sort_order: category.sort_order || 1,
        is_active: category.is_active ? "1" : "0",
      }
    : {
        name: "",
        description: "",
        icon: "",
        sort_order: 1,
        is_active: "1",
      };

  const validationSchema = Yup.object({
    name: Yup.string()
      .required(t("emailTemplate.category.validation.nameRequired"))
      .min(2, t("emailTemplate.category.validation.nameMin"))
      .max(100, t("emailTemplate.category.validation.nameMax")),
    description: Yup.string()
      .max(500, t("emailTemplate.category.validation.descriptionMax")),
    icon: Yup.string()
      .max(50, t("emailTemplate.category.validation.iconMax")),
    sort_order: Yup.number()
      .required(t("emailTemplate.category.validation.sortOrderRequired"))
      .min(1, t("emailTemplate.category.validation.sortOrderMin"))
      .max(999, t("emailTemplate.category.validation.sortOrderMax")),
  });

  const statusOptions = [
    { value: "1", label: t("commonOptions.status.active") },
    { value: "0", label: t("commonOptions.status.inactive") },
  ];

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      const formData = {
        ...values,
        sort_order: parseInt(values.sort_order),
        is_active: values.is_active === "1",
      };

      if (isEditing) {
        await putMutation.mutateAsync({
          endpoint: `admin/email-templates/categories/${category.slug}`,
          data: formData,
        });
        toast.success(t("emailTemplate.category.updateSuccess"));
      } else {
        await postMutation.mutateAsync({
          endpoint: "admin/email-templates/categories",
          data: formData,
        });
        toast.success(t("emailTemplate.category.createSuccess"));
      }

      onSuccess();
    } catch (error) {
      const errorMessage = error.response?.data?.message || 
        (isEditing 
          ? t("emailTemplate.category.updateError") 
          : t("emailTemplate.category.createError"));
      toast.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const isLoading = postMutation.isLoading || putMutation.isLoading;

  return (
    <div className="p-6">
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting, setFieldValue, values }) => (
          <Form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormInput
                name="name"
                label={t("emailTemplate.categoryManagement.name")}
                placeholder={t("emailTemplate.categoryManagement.namePlaceholder")}
                onChange={(e) =>
                  handleSlugSync(e, setFieldValue, "name", "slug")
                }
                required
              />
              <FormInput
                name="icon"
                label={t("emailTemplate.categoryManagement.icon")}
                placeholder={t("emailTemplate.categoryManagement.iconPlaceholder")}
                help={t("emailTemplate.categoryManagement.iconHelp")}
              />
            </div>

            <FormTextarea
              name="description"
              label={t("emailTemplate.categoryManagement.description")}
              placeholder={t("emailTemplate.categoryManagement.descriptionPlaceholder")}
              rows={3}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormInput
                name="sort_order"
                type="number"
                label={t("emailTemplate.categoryManagement.sortOrder")}
                placeholder="1"
                min="1"
                max="999"
                help={t("emailTemplate.categoryManagement.sortOrderHelp")}
                required
              />
              <div className="mt-3">
                <FormRadioGroup
                  name="is_active"
                  label={t("emailTemplate.status")}
                  options={statusOptions}
                  required
                />
              </div>
            </div>

            {/* Preview */}
            {values.name && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 mb-2">
                  {t("emailTemplate.category.preview")}
                </h4>
                <div className="flex items-center space-x-3">
                  {values.icon && (
                    <span className="text-lg">{values.icon}</span>
                  )}
                  <div>
                    <div className="font-medium text-gray-900">{values.name}</div>
                    {values.description && (
                      <div className="text-sm text-gray-500">{values.description}</div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
              >
                {t("common.cancel")}
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || isLoading}
                className="flex items-center space-x-2"
              >
                {isLoading && <LoadingSpinner size={16} />}
                <span>
                  {isEditing ? t("common.update") : t("common.create")}
                </span>
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default CategoryForm;
