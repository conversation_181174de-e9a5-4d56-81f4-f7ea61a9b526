import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { I18nextProvider } from 'react-i18next';
import { vi } from 'vitest';
import VariableForm from '../VariableForm';
import i18n from '@/i18n';

// Mock the API hook
const mockPostMutation = {
  mutateAsync: vi.fn(),
  isLoading: false,
};

const mockPutMutation = {
  mutateAsync: vi.fn(),
  isLoading: false,
};

vi.mock('@/hooks/useApi', () => ({
  useApi: () => ({
    postMutation: mockPostMutation,
    putMutation: mockPutMutation,
  }),
}));

// Mock sonner
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18n}>
        {children}
      </I18nextProvider>
    </QueryClientProvider>
  );
};

describe('VariableForm', () => {
  const mockOnSuccess = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const mockVariable = {
    id: 1,
    key: 'user.name',
    name: 'User Name',
    description: 'The full name of the user',
    category: 'user',
    data_type: 'string',
    is_required: true,
    example_value: 'John Doe',
    default_value: '',
  };

  it('renders create form correctly', () => {
    render(
      <VariableForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    expect(screen.getByLabelText('Variable Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Variable Key')).toBeInTheDocument();
    expect(screen.getByLabelText('Category')).toBeInTheDocument();
    expect(screen.getByText('Create')).toBeInTheDocument();
  });

  it('renders edit form with pre-filled values', () => {
    render(
      <VariableForm 
        variable={mockVariable} 
        onSuccess={mockOnSuccess} 
        onCancel={mockOnCancel} 
      />,
      { wrapper: createWrapper() }
    );

    expect(screen.getByDisplayValue('User Name')).toBeInTheDocument();
    expect(screen.getByDisplayValue('user.name')).toBeInTheDocument();
    expect(screen.getByText('Update')).toBeInTheDocument();
  });

  it('validates required fields', async () => {
    render(
      <VariableForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    const submitButton = screen.getByText('Create');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Variable name is required')).toBeInTheDocument();
      expect(screen.getByText('Variable key is required')).toBeInTheDocument();
    });
  });

  it('validates variable key format', async () => {
    render(
      <VariableForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    const keyInput = screen.getByLabelText('Variable Key');
    fireEvent.change(keyInput, { target: { value: 'Invalid Key!' } });
    fireEvent.blur(keyInput);

    await waitFor(() => {
      expect(screen.getByText(/Variable key must use lowercase letters/)).toBeInTheDocument();
    });
  });

  it('auto-generates key from name in create mode', async () => {
    render(
      <VariableForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    const nameInput = screen.getByLabelText('Variable Name');
    fireEvent.change(nameInput, { target: { value: 'User Full Name' } });

    await waitFor(() => {
      const keyInput = screen.getByLabelText('Variable Key');
      expect(keyInput.value).toBe('user_full_name');
    });
  });

  it('does not auto-generate key in edit mode', async () => {
    render(
      <VariableForm 
        variable={mockVariable} 
        onSuccess={mockOnSuccess} 
        onCancel={mockOnCancel} 
      />,
      { wrapper: createWrapper() }
    );

    const nameInput = screen.getByLabelText('Variable Name');
    fireEvent.change(nameInput, { target: { value: 'Updated Name' } });

    const keyInput = screen.getByLabelText('Variable Key');
    expect(keyInput.value).toBe('user.name'); // Should remain unchanged
    expect(keyInput).toBeDisabled();
  });

  it('submits create form successfully', async () => {
    mockPostMutation.mutateAsync.mockResolvedValue({});

    render(
      <VariableForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    // Fill form
    fireEvent.change(screen.getByLabelText('Variable Name'), {
      target: { value: 'Test Variable' },
    });
    fireEvent.change(screen.getByLabelText('Variable Key'), {
      target: { value: 'test.variable' },
    });
    fireEvent.change(screen.getByLabelText('Description'), {
      target: { value: 'Test description' },
    });

    // Submit form
    fireEvent.click(screen.getByText('Create'));

    await waitFor(() => {
      expect(mockPostMutation.mutateAsync).toHaveBeenCalledWith({
        endpoint: 'admin/email-templates/variables',
        data: expect.objectContaining({
          name: 'Test Variable',
          key: 'test.variable',
          description: 'Test description',
          category: 'user',
          data_type: 'string',
          is_required: false,
        }),
      });
      expect(mockOnSuccess).toHaveBeenCalled();
    });
  });

  it('submits edit form successfully', async () => {
    mockPutMutation.mutateAsync.mockResolvedValue({});

    render(
      <VariableForm 
        variable={mockVariable} 
        onSuccess={mockOnSuccess} 
        onCancel={mockOnCancel} 
      />,
      { wrapper: createWrapper() }
    );

    // Update name
    fireEvent.change(screen.getByLabelText('Variable Name'), {
      target: { value: 'Updated User Name' },
    });

    // Submit form
    fireEvent.click(screen.getByText('Update'));

    await waitFor(() => {
      expect(mockPutMutation.mutateAsync).toHaveBeenCalledWith({
        endpoint: `admin/email-templates/variables/${mockVariable.id}`,
        data: expect.objectContaining({
          name: 'Updated User Name',
          key: 'user.name',
        }),
      });
      expect(mockOnSuccess).toHaveBeenCalled();
    });
  });

  it('calls onCancel when cancel button is clicked', () => {
    render(
      <VariableForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    fireEvent.click(screen.getByText('Cancel'));
    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('shows preview when key is entered', async () => {
    render(
      <VariableForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    fireEvent.change(screen.getByLabelText('Variable Key'), {
      target: { value: 'test.key' },
    });

    await waitFor(() => {
      expect(screen.getByText('{{test.key}}')).toBeInTheDocument();
    });
  });
});
