import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { I18nextProvider } from 'react-i18next';
import { vi } from 'vitest';
import VariableIndex from '../VariableIndex';
import i18n from '@/i18n';

// Mock the API hook
vi.mock('@/hooks/useApi', () => ({
  fetchData: vi.fn(),
  useApi: () => ({
    deleteMutation: {
      mutateAsync: vi.fn(),
      isLoading: false,
    },
  }),
}));

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock sonner
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18n}>
        <BrowserRouter>
          {children}
        </BrowserRouter>
      </I18nextProvider>
    </QueryClientProvider>
  );
};

describe('VariableIndex', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const mockVariables = [
    {
      id: 1,
      key: 'user.name',
      name: 'User Name',
      description: 'The full name of the user',
      category: 'user',
      data_type: 'string',
      is_required: true,
      example_value: 'John Doe',
    },
    {
      id: 2,
      key: 'order.total',
      name: 'Order Total',
      description: 'Total amount of the order',
      category: 'order',
      data_type: 'number',
      is_required: false,
      example_value: '99.99',
    },
  ];

  it('renders variable list correctly', async () => {
    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: { data: mockVariables },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    });

    render(<VariableIndex />, { wrapper: createWrapper() });

    expect(screen.getByText('Template Variables')).toBeInTheDocument();
    expect(screen.getByText('user.name')).toBeInTheDocument();
    expect(screen.getByText('order.total')).toBeInTheDocument();
  });

  it('shows loading state', async () => {
    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: null,
      isLoading: true,
      isError: false,
      refetch: vi.fn(),
    });

    render(<VariableIndex />, { wrapper: createWrapper() });

    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('shows error state', async () => {
    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: null,
      isLoading: false,
      isError: true,
      refetch: vi.fn(),
    });

    render(<VariableIndex />, { wrapper: createWrapper() });

    expect(screen.getByText('Failed to load variables')).toBeInTheDocument();
  });

  it('opens create modal when create button is clicked', async () => {
    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: { data: mockVariables },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    });

    render(<VariableIndex />, { wrapper: createWrapper() });

    const createButton = screen.getByText('Create Variable');
    fireEvent.click(createButton);

    await waitFor(() => {
      expect(screen.getByText('Create Variable')).toBeInTheDocument();
    });
  });

  it('filters variables by search term', async () => {
    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: { data: mockVariables },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    });

    render(<VariableIndex />, { wrapper: createWrapper() });

    const searchInput = screen.getByPlaceholderText('Search variables...');
    fireEvent.change(searchInput, { target: { value: 'user' } });

    expect(searchInput.value).toBe('user');
  });

  it('opens edit modal when edit button is clicked', async () => {
    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: { data: mockVariables },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    });

    render(<VariableIndex />, { wrapper: createWrapper() });

    const editButtons = screen.getAllByTitle('Edit');
    fireEvent.click(editButtons[0]);

    await waitFor(() => {
      expect(screen.getByText('Edit Variable')).toBeInTheDocument();
    });
  });

  it('opens delete confirmation when delete button is clicked', async () => {
    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: { data: mockVariables },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    });

    render(<VariableIndex />, { wrapper: createWrapper() });

    const deleteButtons = screen.getAllByTitle('Delete');
    fireEvent.click(deleteButtons[0]);

    await waitFor(() => {
      expect(screen.getByText('Delete Variable')).toBeInTheDocument();
    });
  });

  it('navigates back when back button is clicked', async () => {
    const { fetchData } = await import('@/hooks/useApi');
    fetchData.mockReturnValue({
      data: { data: mockVariables },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    });

    render(<VariableIndex />, { wrapper: createWrapper() });

    const backButton = screen.getByText('Back');
    fireEvent.click(backButton);

    expect(mockNavigate).toHaveBeenCalledWith('/email-templates');
  });
});
