import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import SearchInput from "@/components/ui/SearchInput";
import FilterDropdown from "@/components/ui/FilterDropdown";
import Button from "@/components/ui/Button";

const VariableFilters = ({ onFilterChange }) => {
  const { t } = useTranslation();
  const [filters, setFilters] = useState({
    search: "",
    category: "",
    data_type: "",
    is_required: "",
  });

  const categoryOptions = [
    { value: "", label: t("emailTemplate.variable.allCategories") },
    { value: "user", label: t("emailTemplate.variable.category.user") },
    { value: "order", label: t("emailTemplate.variable.category.order") },
    { value: "vendor", label: t("emailTemplate.variable.category.vendor") },
    { value: "site", label: t("emailTemplate.variable.category.site") },
    { value: "auth", label: t("emailTemplate.variable.category.auth") },
  ];

  const dataTypeOptions = [
    { value: "", label: t("emailTemplate.variable.allDataTypes") },
    { value: "string", label: t("emailTemplate.variable.dataType.string") },
    { value: "number", label: t("emailTemplate.variable.dataType.number") },
    { value: "boolean", label: t("emailTemplate.variable.dataType.boolean") },
    { value: "array", label: t("emailTemplate.variable.dataType.array") },
    { value: "object", label: t("emailTemplate.variable.dataType.object") },
  ];

  const requiredOptions = [
    { value: "", label: t("emailTemplate.variable.allRequired") },
    { value: "1", label: t("commonOptions.required") },
    { value: "0", label: t("commonOptions.optional") },
  ];

  const triggerChange = (updated) => {
    const payload = {};
    if (updated.search?.trim()) payload.search = updated.search.trim();
    if (updated.category) payload.category = updated.category;
    if (updated.data_type) payload.data_type = updated.data_type;
    if (updated.is_required) payload.is_required = updated.is_required;
    onFilterChange?.(payload);
  };

  const handleSearchChange = (val) => {
    const updated = { ...filters, search: val };
    setFilters(updated);
    triggerChange(updated);
  };

  const handleFilterChange = (key, values) => {
    const updated = { ...filters, [key]: values };
    setFilters(updated);
    triggerChange(updated);
  };

  const handleReset = () => {
    const reset = { search: "", category: "", data_type: "", is_required: "" };
    setFilters(reset);
    onFilterChange?.({});
  };

  return (
    <div className="mb-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
        <div className="flex-1 max-w-md">
          <SearchInput
            value={filters.search}
            onChange={handleSearchChange}
            placeholder={t("emailTemplate.variable.searchPlaceholder")}
          />
        </div>

        <div className="flex flex-wrap items-center gap-2">
          <FilterDropdown
            label={t("emailTemplate.variable.category")}
            className="w-40"
            options={categoryOptions}
            selectedValues={filters.category}
            onChange={(values) => handleFilterChange("category", values)}
          />
          <FilterDropdown
            label={t("emailTemplate.variable.dataType")}
            className="w-40"
            options={dataTypeOptions}
            selectedValues={filters.data_type}
            onChange={(values) => handleFilterChange("data_type", values)}
          />
          <FilterDropdown
            label={t("emailTemplate.variable.required")}
            className="w-40"
            options={requiredOptions}
            selectedValues={filters.is_required}
            onChange={(values) => handleFilterChange("is_required", values)}
          />
          <Button variant="outline" onClick={handleReset} className="ml-2">
            {t("commonButton.reset")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default VariableFilters;
