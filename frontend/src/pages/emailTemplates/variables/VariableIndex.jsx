import React, { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import VariableFilters from "./VariableFilters";
import {
  FaEdit,
  FaTrash,
  FaPlus,
  FaArrowLeft,
  FaCode,
  FaSearch,
  FaFilter,
  FaTimes,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Pagination from "@/components/ui/Pagination";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import VariableForm from "./VariableForm";

const VariableIndex = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [filterOptions, setFilterOptions] = useState({});
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedVariable, setSelectedVariable] = useState(null);
  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const { deleteMutation } = useApi();

  const {
    data: variablesData,
    isLoading,
    isError,
    refetch,
  } = fetchData("admin/email-templates/variables", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    category: filterOptions.category || "",
    data_type: filterOptions.data_type || "",
    is_required: filterOptions.is_required || "",
  });

  // Handle nested API response structure: {status: true, data: {data: [...], meta: {...}}}
  const variables = variablesData?.data?.data || [];
  const paginationInfo = {
    currentPage: variablesData?.data?.current_page || 1,
    perPage: variablesData?.data?.per_page || itemsPerPage,
    totalItems: variablesData?.data?.total_items || 0,
    totalPages: variablesData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleCreateClick = () => {
    setSelectedVariable(null);
    setShowCreateModal(true);
  };

  const handleEditClick = (variable) => {
    setSelectedVariable(variable);
    setShowEditModal(true);
  };

  const handleDeleteClick = (variable) => {
    setSelectedVariable(variable);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteMutation.mutateAsync({
        endpoint: `admin/email-templates/variables/${selectedVariable.id}`,
      });
      toast.success(t("emailTemplate.variable.deleteSuccess"));
      refetch();
      setShowDeleteModal(false);
      setSelectedVariable(null);
    } catch (error) {
      const errorMessage = error.response?.data?.message || t("emailTemplate.variable.deleteError");
      toast.error(errorMessage);
    }
  };

  const handleFormSuccess = () => {
    refetch();
    setShowCreateModal(false);
    setShowEditModal(false);
    setSelectedVariable(null);
  };



  const categoryOptions = [
    { value: "", label: t("emailTemplate.variable.allCategories") },
    { value: "user", label: t("emailTemplate.variable.category.user") },
    { value: "order", label: t("emailTemplate.variable.category.order") },
    { value: "vendor", label: t("emailTemplate.variable.category.vendor") },
    { value: "site", label: t("emailTemplate.variable.category.site") },
    { value: "auth", label: t("emailTemplate.variable.category.auth") },
  ];

  const dataTypeOptions = [
    { value: "", label: t("emailTemplate.variable.allDataTypes") },
    { value: "string", label: t("emailTemplate.variable.dataType.string") },
    { value: "number", label: t("emailTemplate.variable.dataType.number") },
    { value: "boolean", label: t("emailTemplate.variable.dataType.boolean") },
    { value: "array", label: t("emailTemplate.variable.dataType.array") },
    { value: "object", label: t("emailTemplate.variable.dataType.object") },
  ];

  const requiredOptions = [
    { value: "", label: t("emailTemplate.variable.allRequired") },
    { value: "1", label: t("commonOptions.required") },
    { value: "0", label: t("commonOptions.optional") },
  ];

  const columns = [
    {
      header: t("emailTemplate.variable.key"),
      accessor: "key",
      render: (variable) => (
        <div className="flex items-center space-x-3">
          <FaCode className="w-4 h-4 text-blue-500" />
          <div>
            <div className="font-mono text-sm text-blue-600">{variable.key}</div>
            <div className="text-xs text-gray-500">{variable.name}</div>
          </div>
        </div>
      ),
    },
    {
      header: t("emailTemplate.variable.description"),
      accessor: "description",
      render: (variable) => (
        <span className="text-sm text-gray-600">
          {variable.description || "—"}
        </span>
      ),
    },
    {
      header: t("emailTemplate.variable.category"),
      accessor: "category",
      render: (variable) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {variable.category}
        </span>
      ),
    },
    {
      header: t("emailTemplate.variable.dataType"),
      accessor: "data_type",
      render: (variable) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          {variable.data_type}
        </span>
      ),
    },
    {
      header: t("emailTemplate.variable.required"),
      accessor: "is_required",
      render: (variable) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            variable.is_required
              ? "bg-red-100 text-red-800"
              : "bg-green-100 text-green-800"
          }`}
        >
          {variable.is_required ? t("commonOptions.required") : t("commonOptions.optional")}
        </span>
      ),
    },
    {
      header: t("emailTemplate.variable.example"),
      accessor: "example_value",
      render: (variable) => (
        <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
          {variable.example_value || "—"}
        </span>
      ),
    },
    {
      header: t("common.actions"),
      accessor: "actions",
      render: (variable) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditClick(variable)}
            className="text-green-600 hover:text-green-800"
          >
            <FaEdit className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteClick(variable)}
            className="text-red-600 hover:text-red-800"
          >
            <FaTrash className="w-4 h-4" />
          </Button>
        </div>
      ),
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner size={40} />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{t("emailTemplate.variable.loadError")}</p>
        <Button onClick={() => refetch()} className="mt-4">
          {t("common.retry")}
        </Button>
      </div>
    );
  }

  return (
    <div className="mx-auto relative p-5">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header with Back Button */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={() => navigate("/email-templates")}
              className="flex items-center space-x-2"
            >
              <FaArrowLeft className="w-4 h-4" />
              <span>{t("common.back")}</span>
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {t("emailTemplate.variable.title")}
              </h1>
              <p className="text-gray-600 mt-1">
                {t("emailTemplate.variable.subtitle")}
              </p>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
            <Button
              onClick={handleCreateClick}
              className="flex items-center justify-center sm:justify-start space-x-2 w-full sm:w-auto"
            >
              <FaPlus className="w-4 h-4" />
              <span>{t("emailTemplate.variable.createNew")}</span>
            </Button>
          </div>
        </div>

        <Card
          title={t("emailTemplate.variable.list")}
          icon={<FaCode className="text-indigo-600 me-1" />}
        >
          <VariableFilters
            onFilterChange={handleFilterChange}
            filterOptions={filterOptions}
          />

          <Table
            columns={columns}
            data={variables}
            emptyMessage={t("emailTemplate.variable.noVariables")}
          />

          <PaginationInfo
            currentPage={paginationInfo.currentPage}
            totalPages={paginationInfo.totalPages}
            totalItems={paginationInfo.totalItems}
            itemsPerPage={paginationInfo.perPage}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        </Card>
      </motion.div>

      {/* Create Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title={t("emailTemplate.variable.createTitle")}
        size="lg"
      >
        <VariableForm
          onSuccess={handleFormSuccess}
          onCancel={() => setShowCreateModal(false)}
        />
      </Modal>

      {/* Edit Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title={t("emailTemplate.variable.editTitle")}
        size="lg"
      >
        <VariableForm
          variable={selectedVariable}
          onSuccess={handleFormSuccess}
          onCancel={() => setShowEditModal(false)}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title={t("emailTemplate.variable.deleteConfirmTitle")}
        size="sm"
      >
        <div className="p-6">
          <p className="text-gray-600 mb-6">
            {t("emailTemplate.variable.deleteConfirmMessage", {
              key: selectedVariable?.key,
            })}
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
            >
              {t("common.cancel")}
            </Button>
            <Button
              variant="danger"
              onClick={handleDeleteConfirm}
              loading={deleteMutation.isPending}
            >
              {t("common.delete")}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default VariableIndex;
