import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  FormInput,
  FormSelect,
  FormTextarea,
  FormRadioGroup,
  RichTextEditor,
} from "@/components/ui/form";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { fetchData } from "@/hooks/useApi";
import { FaPlus, FaTimes, FaInfo } from "react-icons/fa";
import { useSlugSync } from "@/hooks/useSlugSync";

const EmailTemplateForm = ({ template, onSubmit, onCancel }) => {
  const { t } = useTranslation();
  const handleSlugSync = useSlugSync();


  // Fetch categories and variables
  const { data: categoriesData, isLoading: categoriesLoading, isError: categoriesError } = fetchData("admin/email-templates/categories", {
    is_active: true,
  });

  const { data: variablesData, isLoading: variablesLoading, isError: variablesError } = fetchData("admin/email-templates/variables", {
    group_by_category: true,
  });

  // Handle nested API response structure: {status: true, data: {data: [...]}}
  const categories = categoriesData?.data?.data || categoriesData?.data || [];
  const variables = variablesData?.data?.data || variablesData?.data || {};

  // Debug logging for categories
  useEffect(() => {
    if (categoriesData) {
      console.log("Categories API Response:", categoriesData);
      console.log("Extracted categories:", categories);
      console.log("Categories array length:", categories.length);
    }
    if (categoriesError) {
      console.error("Categories API Error:", categoriesError);
    }
  }, [categoriesData, categories, categoriesError]);





  const initialValues = template
    ? {
        name: template.name || "",
        subject: template.subject || "",
        body_html: template.body_html || "",
        body_text: template.body_text || "",
        category_id: template.category_id?.toString() || "",
        language: template.language || "en",
        is_active: template.is_active ? "1" : "0",
        is_default: template.is_default ? "1" : "0",

      }
    : {
        name: "",
        subject: "",
        body_html: "",
        body_text: "",
        category_id: "",
        language: "en",
        is_active: "1",
        is_default: "0",

      };

  const validationSchema = Yup.object({
    name: Yup.string().required(t("emailTemplate.validation.nameRequired")),
    subject: Yup.string().required(t("emailTemplate.validation.subjectRequired")),
    body_html: Yup.string().required(t("emailTemplate.validation.bodyRequired")),
    category_id: Yup.string().required(t("emailTemplate.validation.categoryRequired")),
    language: Yup.string().required(t("emailTemplate.validation.languageRequired")),
  });

  const categoryOptions = [
    {
      value: "",
      label: categoriesLoading
        ? t("common.loading")
        : categoriesError
        ? "Error loading categories"
        : categories.length === 0
        ? "No categories available"
        : t("emailTemplate.selectCategory")
    },
    ...(Array.isArray(categories) ? categories.map((category) => ({
      value: category.id.toString(),
      label: category.name,
    })) : []),
  ];

  // Show error message if categories failed to load
  if (categoriesError) {
    console.error("Failed to load categories:", categoriesError);
  }

  const languageOptions = [
    { value: "en", label: "English" },
    { value: "ar", label: "العربية" },
  ];

  const statusOptions = [
    { value: "1", label: t("commonOptions.status.active") },
    { value: "0", label: t("commonOptions.status.inactive") },
  ];

  const defaultOptions = [
    { value: "1", label: t("commonOptions.yesNo.Yes") },
    { value: "0", label: t("commonOptions.yesNo.No") },
  ];



  const handleSubmit = (values, { setSubmitting }) => {
    const formattedValues = {
      ...values,
      category_id: parseInt(values.category_id),
      is_active: values.is_active === "1",
      is_default: values.is_default === "1",


    };
    onSubmit(formattedValues);
    setSubmitting(false);
  };



  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, setFieldValue, values }) => (
        <Form className="space-y-4">
          {/* Basic Information */}
          <Card title={t("emailTemplate.basicInformation")} className="mb-3 [&>div:last-child]:p-3 [&>div:last-child]:pt-2">
            {/* First Row - Template Name (Full Width) */}
            <div className="mb-4">
              <FormInput
                name="name"
                label={t("emailTemplate.name")}
                placeholder={t("emailTemplate.namePlaceholder")}
                onChange={(e) =>
                  handleSlugSync(e, setFieldValue, "name", "slug")
                }
                required
              />
            </div>

            {/* Second Row - Category and Language (Two Columns) */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormSelect
                name="category_id"
                label={t("emailTemplate.category")}
                options={categoryOptions}
                required
              />
              <FormSelect
                name="language"
                label={t("emailTemplate.language")}
                options={languageOptions}
                required
              />
            </div>
            
            <div className="mt-4">
              <FormInput
                name="subject"
                label={t("emailTemplate.subject")}
                placeholder={t("emailTemplate.subjectPlaceholder")}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <FormRadioGroup
                name="is_active"
                label={t("emailTemplate.status")}
                options={statusOptions}
                required
              />
              <FormRadioGroup
                name="is_default"
                label={t("emailTemplate.setAsDefault")}
                options={defaultOptions}
                help={t("emailTemplate.defaultHelp")}
              />
            </div>
          </Card>

          {/* Content */}
          <Card title={t("emailTemplate.content")} className="mb-3 [&>div:last-child]:p-3 [&>div:last-child]:pt-2">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t("emailTemplate.htmlContent")} *
                </label>
                <RichTextEditor
                  name="body_html"
                  value={values.body_html}
                  onChange={(content) => setFieldValue("body_html", content)}
                  placeholder={t("emailTemplate.htmlPlaceholder")}
                />
              </div>

              <div>
                <FormTextarea
                  name="body_text"
                  label={t("emailTemplate.textContent")}
                  placeholder={t("emailTemplate.textPlaceholder")}
                  rows={6}
                  help={t("emailTemplate.textHelp")}
                />
              </div>
            </div>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3">
            <Button type="button" variant="outline" onClick={onCancel}>
              {t("common.cancel")}
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting
                ? t("common.saving")
                : template
                ? t("common.update")
                : t("common.create")}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default EmailTemplateForm;
