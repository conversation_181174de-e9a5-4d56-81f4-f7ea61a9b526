import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { FaCode, FaEye, FaTimes } from "react-icons/fa";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

import { useApi } from "@/hooks/useApi";

const EmailTemplatePreviewModal = ({ template, onClose }) => {
  const { t } = useTranslation();
  const { postMutation } = useApi();
  const [activeTab, setActiveTab] = useState("preview");
  const [previewData, setPreviewData] = useState(null);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);

  const [variables, setVariables] = useState({});

  // Default sample variables
  const defaultVariables = {
    "user.name": "<PERSON>",
    "user.email": "<EMAIL>",
    "site.name": "Vitamins.ae",
    "site.url": "https://vitamins.ae",
    "auth.otp_code": "123456",
    "order.number": "ORD-123456",
    "order.total": "$99.99",
  };

  useEffect(() => {
    if (template) {
      // Initialize variables with defaults for any variables found in the template
      const templateVariables = template.variables || [];
      const initialVars = {};
      templateVariables.forEach((varKey) => {
        initialVars[varKey] = defaultVariables[varKey] || `[${varKey}]`;
      });
      setVariables(initialVars);
      generatePreview(initialVars);
    }
  }, [template]);

  const generatePreview = async (vars = variables) => {
    setIsLoadingPreview(true);
    try {
      const response = await postMutation.mutateAsync({
        endpoint: `admin/email-templates/templates/${template.uuid}/preview`,
        data: { variables: vars },
      });
      setPreviewData(response.data);
    } catch (error) {
      toast.error(t("emailTemplate.previewError"));
      console.error("Preview error:", error);
    } finally {
      setIsLoadingPreview(false);
    }
  };

  const handleVariableChange = (key, value) => {
    const updatedVars = { ...variables, [key]: value };
    setVariables(updatedVars);
    // Debounce preview generation
    clearTimeout(window.previewTimeout);
    window.previewTimeout = setTimeout(() => {
      generatePreview(updatedVars);
    }, 500);
  };



  const tabs = [
    { id: "preview", label: t("emailTemplate.preview"), icon: FaEye },
    { id: "html", label: t("emailTemplate.htmlSource"), icon: FaCode },
  ];

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title={t("emailTemplate.previewTitle", { name: template.name })}
      size="xl"
    >
      <div className="flex flex-col h-[80vh]">
        {/* Tab Navigation */}
        <div className="flex border-b border-gray-200">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700"
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Variables Panel */}
          {(template.variables?.length > 0) && (
            <div className="w-80 border-r border-gray-200 p-4 overflow-y-auto">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {t("emailTemplate.variables")}
              </h3>
              <div className="space-y-3">
                {template.variables.map((varKey) => (
                  <div key={varKey}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t(`emailTemplate.variableLabels.${varKey}`, { defaultValue: varKey })}
                    </label>
                    <input
                      type="text"
                      value={variables[varKey] || ""}
                      onChange={(e) => handleVariableChange(varKey, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                      placeholder={`Enter ${varKey}`}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Content Panel */}
          <div className="flex-1 overflow-hidden">
            {activeTab === "preview" && (
              <div className="h-full overflow-y-auto p-6">
                {isLoadingPreview ? (
                  <div className="flex justify-center items-center h-64">
                    <LoadingSpinner size={40} />
                  </div>
                ) : previewData ? (
                  <div className="space-y-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-2">
                        {t("emailTemplate.subject")}
                      </h4>
                      <p className="text-gray-700">{previewData.subject}</p>
                    </div>
                    <div className="bg-white border rounded-lg">
                      <div className="p-4 border-b">
                        <h4 className="font-medium text-gray-900">
                          {t("emailTemplate.emailContent")}
                        </h4>
                      </div>
                      <div className="p-4">
                        <iframe
                          srcDoc={previewData.body_html}
                          className="w-full h-96 border-0"
                          title="Email Preview"
                        />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-8">
                    {t("emailTemplate.noPreviewData")}
                  </div>
                )}
              </div>
            )}

            {activeTab === "html" && (
              <div className="h-full overflow-y-auto p-6">
                {previewData ? (
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">
                        {t("emailTemplate.htmlContent")}
                      </h4>
                      <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
                        <code>{previewData.body_html}</code>
                      </pre>
                    </div>
                    {previewData.body_text && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">
                          {t("emailTemplate.textContent")}
                        </h4>
                        <pre className="bg-gray-100 p-4 rounded-lg text-sm whitespace-pre-wrap">
                          {previewData.body_text}
                        </pre>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-8">
                    {t("emailTemplate.noPreviewData")}
                  </div>
                )}
              </div>
            )}


          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-4 border-t border-gray-200">
          <Button variant="outline" onClick={onClose}>
            {t("common.close")}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default EmailTemplatePreviewModal;
