import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import PageFilterPage from "./PageFilters";
import {
  FaEdit,
  FaTrash,
  Fa<PERSON>lip<PERSON><PERSON>ist,
  FaPlus,
  <PERSON>aEye,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import PagePreviewModal from "./PagePreviewModal";
import StatusBadge from "@/components/common/StatusBadge";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";

const PageIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedPage, setSelectedPage] = useState(null);
  const [selectedPageId, setSelectedPageId] = useState(null);
  const [previewModalOpen, setPreviewModalOpen] = useState(false);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: "",
  });

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: pageData,
    isLoading,
    isError: pageError,
    refetch,
  } = fetchData("admin/pages", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    status: filterOptions.status || "",
  });

  const pageList = pageData?.data?.data || [];
  const paginationInfo = {
    currentPage: pageData?.data?.current_page || 1,
    perPage: pageData?.data?.per_page || itemsPerPage,
    totalItems: pageData?.data?.total_items || 0,
    totalPages: pageData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditOfferDeal = (page) => {
    navigate(`/pages/edit/${page.id}`);
  };

  const handlePreviewClick = (id) => {
    setSelectedPageId(id);
    setPreviewModalOpen(true);
  };

  const handleDeleteClick = (page) => {
    setSelectedPage(page);
  };

  const handleDeleteCategory = async () => {
    if (!selectedPage) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/pages/${selectedPage.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setSelectedPage(null);
          refetch();
          toast.success(t("commonToast.pageToast.pageDelete"));
        },
        onError: (error) => {
          console.error(
            "Pages deletion failed:",
            error?.response?.data || error.message
          );
          toast.error(
            "Pages deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const canEditPage = (page) => true;

  const canDeletePage = (page) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.title_en"),
      accessor: "title_en",
    },
    {
      header: t("commonTableLabel.title_ar"),
      accessor: "title_ar",
    },
    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row} fieldName="status" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="success"
            size="sm"
            onClick={() => handlePreviewClick(row.id)}
          >
            <FaEye className="text-green-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditOfferDeal(row)}
            disabled={!canEditPage(row)}
            title={
              !canEditPage(row)
                ? "You don't have permission to edit this Pages"
                : t("page.editAction")
            }
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeletePage(row)}
            title={
              !canDeletePage(row)
                ? "You can't delete this Pages"
                : t("page.deleteAction")
            }
          >
            <FaTrash className="text-red-600" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("page.title")}
          icon={<FaClipboardList className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => navigate("/pages/add")}
            >
              <FaPlus className="mr-2" /> {t("page.add")}
            </Button>
          }
        >
          <PageFilterPage onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={pageList}
            emptyMessage={t("page.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationModal
        isOpen={!!selectedPage}
        onClose={() => setSelectedPage(null)}
        onDelete={handleDeleteCategory}
        loading={deleteLoading}
        itemName={t("page.page")}
        itemValue={selectedPage?.title_en}
      />

      {/* Preview modal  */}
      <Modal
        isOpen={previewModalOpen}
        onClose={() => setPreviewModalOpen(false)}
        title={t("page.title")}
        size="xl"
      >
        <PagePreviewModal
          id={selectedPageId}
          onClose={() => setPreviewModalOpen(false)}
        />
      </Modal>
    </div>
  );
};

export default PageIndex;
