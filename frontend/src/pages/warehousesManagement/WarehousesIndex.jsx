import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import WarehouseFilter from "./WarehouseFilter";
import {
  FaEdit,
  FaTrash,
  FaClip<PERSON><PERSON>ist,
  FaPlus,
  <PERSON>aEye,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import Modal from "@/components/ui/Modal";
import { toast } from "sonner";
import StatusBadge from "@/components/common/StatusBadge";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import PreviewSection from "./WarehousePreview";

const WarehousesIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);
  const [previewId, setPreviewId] = useState(null);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: "",
  });

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: warehouseData,
    isLoading,
    isError: warehouseError,
    refetch,
  } = fetchData("admin/warehouses", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    status: filterOptions.status || "",
  });

  const warehouseList = warehouseData?.data?.data || [];
  const paginationInfo = {
    currentPage: warehouseData?.data?.current_page || 1,
    perPage: warehouseData?.data?.per_page || itemsPerPage,
    totalItems: warehouseData?.data?.total_items || 0,
    totalPages: warehouseData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditWarehouse = (warehouse) => {
    navigate(`/warehouse/edit/${warehouse.id}`);
  };

  const handlePreviewClick = (warehouse) => {
    setPreviewId(warehouse.id);
  };

  const handleDeleteClick = (warehouse) => {
    setSelectedWarehouse(warehouse);
  };

  const handleDeleteCategory = async () => {
    if (!selectedWarehouse) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/warehouses/${selectedWarehouse.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setSelectedWarehouse(null);
          refetch();
          toast.success(t("commonToast.couponToast.couponDelete"));
        },
        onError: (error) => {
          console.error(
            "Warehouse deletion failed:",
            error?.response?.data || error.message
          );
          toast.error(
            "Warehouse deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const canEditCategory = (warehouse) => true;

  const canDeleteCategory = (warehouse) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.name_en"),
      accessor: "name_en",
    },
    {
      header: t("commonTableLabel.code"),
      accessor: "code",
    },
    {
      header: t("commonTableLabel.contactPerson"),
      accessor: "contact_person",
    },
    {
      header: t("commonTableLabel.contactNumber"),
      accessor: "contact_number",
    },
    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row} fieldName="status" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("warehouse.previewClassContent")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditWarehouse(row)}
            disabled={!canEditCategory(row)}
            title={
              !canEditCategory(row)
                ? "You don't have permission to edit this Warehouse"
                : t("warehouse.editAction")
            }
          >
            <FaEdit className="icon-edit" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteCategory(row)}
            title={
              !canDeleteCategory(row)
                ? "You can't delete this Warehouse"
                : t("warehouse.deleteAction")
            }
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("warehouse.title")}
          icon={<FaClipboardList className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => navigate("/warehouse/add")}
            >
              <FaPlus className="mr-2" /> {t("warehouse.add")}
            </Button>
          }
        >
          <WarehouseFilter onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={warehouseList}
            emptyMessage={t("warehouse.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      {/* PreView Page  */}
      <Modal
        isOpen={previewId}
        onClose={() => setPreviewId(null)}
        title={t("warehouse.previewClassContent")}
        size="xl"
        showCloseButton={true}
      >
        <PreviewSection
          warehouseId={previewId}
          onClose={() => setPreviewId(null)}
        />
      </Modal>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationModal
        isOpen={!!selectedWarehouse}
        onClose={() => setSelectedWarehouse(null)}
        onDelete={handleDeleteCategory}
        loading={deleteLoading}
        itemName={t("warehouse.warehouse")}
        itemValue={selectedWarehouse?.name_en}
      />
    </div>
  );
};

export default WarehousesIndex;
