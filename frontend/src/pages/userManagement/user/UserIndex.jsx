import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { toast } from "sonner";
import {
  FaUserPlus,
  FaEdit,
  FaTrash,
  FaUserShield,
  Fa<PERSON><PERSON>,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { rolePermissions } from "@/constants/roles";
import UserEditForm from "./UserAddEdit";
import UserFilters from "./UserFilters";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { useTranslation } from "react-i18next";
import StatusBadge from "@/components/common/StatusBadge";
import ApprovalStatusBadge from "@/components/common/ApprovalStatusBadge";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import { renderNA } from "@/helper/commonFunctionHelper";
import { capitalizeLetter, humanize } from "@/helper/Commonhelper";

const UserIndex = () => {
  const { t } = useTranslation();
  const { postForm, putForm, delete: removeUser } = useApi();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  // Filter state
  const [filterOptions, setFilterOptions] = useState({
    search: "",
    role_id: "",
    is_active: "",
    is_verified: "",
    status: "",
  });

  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const {
    data: usersData,
    isLoading: usersLoading,
    isError: usersError,
    refetch: refetchUsers,
  } = fetchData("admin/users", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    role_id: filterOptions.role_id || "",
    // is_active: filterOptions.is_active || "",
    is_active: filterOptions.is_active ?? "",
    is_verified: filterOptions.is_verified || "",
    status: filterOptions.status || "",
  });

  const userList = usersData?.data?.data || [];
  const paginationInfo = {
    currentPage: usersData?.data?.current_page || 1,
    perPage: usersData?.data?.per_page || itemsPerPage,
    totalItems: usersData?.data?.total_items || 0,
    totalPages: usersData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditUser = (user) => {
    setSelectedUser(user);
    setEditModalOpen(true);
  };

  const handleDeleteClick = (user) => {
    setSelectedUser(user);
    setDeleteModalOpen(true);
  };

  const handleDeleteUser = async () => {
    if (!selectedUser) return;
    setDeleteLoading(true);
    removeUser(`admin/users/${selectedUser.id}`)
      .then(() => {
        setDeleteModalOpen(false);
        setSelectedUser(null);
        refetchUsers();
        toast.success(t("commonToast.userToast.userDelete"));
      })
      .finally(setDeleteLoading(false));
  };

  // Use postMutation and putMutation for user add/update
  const [editLoading, setEditLoading] = useState(false);

  const handleUpdateUser = (formData) => {
    setEditLoading(true);

    const onSuccess = () => {
      setEditModalOpen(false);
      setSelectedUser(null);
      refetchUsers();
      toast.success(
        t(
          formData.get("id")
            ? "commonToast.userToast.userUpdate"
            : "commonToast.userToast.userCreate"
        )
      );
    };

    const onSettled = () => setEditLoading(false);

    if (!formData.get("id")) {
      postForm(`admin/users`, formData).then(onSuccess).finally(onSettled);
    } else {
      putForm(`admin/users/${formData.get("id")}`, formData)
        .then(onSuccess)
        .finally(onSettled);
    }
  };

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.name"),
      accessor: "name",
    },
    {
      header: t("commonTableLabel.phone"),
      accessor: "phone",
    },
    {
      header: t("commonTableLabel.email"),
      accessor: "email",
    },
    {
      header: t("role.role"),
      accessor: "roles",
      render: (row) => {
        const roleName = row?.roles?.[0]?.name;
        return roleName ? capitalizeLetter(humanize(roleName)) : renderNA;
      },
    },

    {
      header: t("commonTableLabel.verified"),
      accessor: "is_verified",
      render: (row) => (
        <span
          className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
            row.is_verified
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {row.is_verified ? "Yes" : "No"}
        </span>
      ),
    },
    {
      header: t("commonField.approvalStatus"),
      render: (row) => <ApprovalStatusBadge data={row} fieldName="status" />,
    },
    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row} fieldName="is_active" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditUser(row)}
            // disabled={!canEditUser(row)}
            // title={
            //   !canEditUser(row)
            //     ? "You don't have permission to edit this user"
            //     : "Edit user"
            // }
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            // disabled={!canDeleteUser(row)}
            // title={
            //   !canDeleteUser(row) ? "You can't delete this user" : "Delete user"
            // }
          >
            <FaTrash className="text-red-600" />
          </Button>
          {row.permissions && (
            <div className="relative group">
              <Button
                variant="outline"
                size="sm"
                title="User has custom permissions"
                className="cursor-default"
              >
                <FaKey
                  className={
                    row.permissions.length !== rolePermissions[row.role]?.length
                      ? "text-yellow-500"
                      : "text-gray-400"
                  }
                />
              </Button>
              <div className="absolute z-10 hidden group-hover:block bg-white border border-gray-200 rounded-md shadow-lg p-2 w-48 text-xs right-0 mt-1">
                <p className="font-semibold mb-1">Custom Permissions:</p>
                <p className="text-gray-600">
                  {row.permissions &&
                  row.permissions.length !== rolePermissions[row.role]?.length
                    ? `${row.permissions.length} permissions (${
                        rolePermissions[row.role]?.length || 0
                      } in role)`
                    : "Standard role permissions"}
                </p>
              </div>
            </div>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="page-container">
      {usersLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("user.title")}
          icon={<FaUserShield className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => {
                setSelectedUser(null);
                setEditModalOpen(true);
              }}
            >
              <FaUserPlus /> {t("user.add")}
            </Button>
          }
        >
          <UserFilters onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={userList}
            emptyMessage={t("user.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      {/* Edit User Modal */}
      <Modal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title={t(selectedUser ? "user.editAction" : "user.add")}
        size="xl"
      >
        <UserEditForm
          user={selectedUser}
          onSubmit={handleUpdateUser}
          onCancel={() => setEditModalOpen(false)}
          loading={editLoading}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onDelete={handleDeleteUser}
        loading={deleteLoading}
        itemName={t("user.user")}
        itemValue={selectedUser?.name}
      />
    </div>
  );
};

export default UserIndex;
