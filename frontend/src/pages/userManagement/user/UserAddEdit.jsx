import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useState, useEffect } from "react";
import useRoleList from "@/hooks/list/useRoleList";
import { useSelector } from "react-redux";
import {
  FormInput,
  FormSelect,
  FormRadioGroup,
  FormPassword,
} from "@/components/ui/form";
import FormPhoneNumberInput from "@/components/ui/form/FormPhoneNumberInput";
import Button from "@/components/ui/Button";
import {
  getAllRoles,
  getRoleDisplayName,
  rolePermissions,
} from "@/constants/roles";
import usePermissionList from "@/hooks/list/usePermissionList";
import PermissionManager from "../../staff/PermissionManager";
import ImageUploader from "@/components/ui/ImageUploader";
import { fetchData } from "@/hooks/useApi";
import { useTranslation } from "react-i18next";
import { statusOptions } from "@/constants/filterOption";

const UserAddEdit = ({ user, onSubmit, onCancel }) => {
  const { t } = useTranslation();
  const { user: currentUser } = useSelector((state) => state.auth);
  const [customPermissions, setCustomPermissions] = useState([]);
  const [defaultRoles, setDefaultRoles] = useState([]);
  const [selectedRole, setSelectedRole] = useState(
    user ? user.role || (Array.isArray(user.roles) ? user.roles[0] : "") : ""
  );

  const { data: singleUser } = user
    ? fetchData(`admin/users/${user?.id}`)
    : { data: null };

  const { roles, loading: rolesLoading, error: rolesError } = useRoleList();

  useEffect(() => {
    if (singleUser && roles) {
      handleRoleChange(singleUser.data.roles[0]);
      setCustomPermissions(singleUser.data.permissions);
    }
  }, [singleUser, roles]);

  // Ensure roles is always an array before filtering
  const availableRoles = (Array.isArray(roles) ? roles : []).filter((role) => {
    // Super admin can assign any role
    if (currentUser.role === "super_admin") return true;
    // Admin can't assign super admin role
    if (currentUser.role === "admin" && role.key === "super_admin")
      return false;
    // Others can only assign roles with less privilege
    return true;
  });

  // Role options
  const roleOptions = availableRoles.map((role) => ({
    label: role.name,
    value: role.name,
  }));

  // Handle role change (single select)
  const handleRoleChange = (roleVal) => {
    setSelectedRole(roleVal);

    // Find the selected role object from availableRoles
    const selectedRoleObj = availableRoles.find(
      (r) => r.name === roleVal || r.key === roleVal
    );

    // Get permissions for the selected role from rolePermissions (if available)
    let permissionsForRole = [];
    if (selectedRoleObj?.permissions) {
      permissionsForRole = selectedRoleObj?.permissions;
    }

    // If permissions are objects, map to IDs; if strings, use as is
    setDefaultRoles(
      permissionsForRole.map((p) =>
        typeof p === "object" && p.name ? p.name : p
      )
    );
  };

  // Initial values for the form
  const initialValues = user
    ? {
        id: user.id,
        name: user.name,
        email: user.email,
        role:
          user.role ||
          (Array.isArray(user.roles) && user.roles[0]
            ? user.roles[0].name
            : ""),
        status: user.status,
        password: "",
        password_confirmation: "",
        phone: user.phone || "",
        avatar: user.avatar_url,
      }
    : {
        name: "",
        email: "",
        role: "",
        status: "active",
        password: "",
        password_confirmation: "",
        phone: " ",
        avatar: null,
      };

  // Validation schema
  const validationSchema = Yup.object({
    name: Yup.string().required(t("commonValidation.name")),
    email: Yup.string()
      .email(t("commonValidation.invalidEmail"))
      .required(t("commonValidation.email")),
    role: Yup.string().required(t("commonValidation.role")),
    status: Yup.string().required(t("commonValidation.status")),
    password: user
      ? Yup.string()
      : Yup.string()
          .min(8, t("commonValidation.passwordLength"))
          .required(t("commonValidation.password")),
    password_confirmation: user
      ? Yup.string().oneOf(
          [Yup.ref("password"), null],
          t("commonValidation.passwordMatch")
        )
      : Yup.string()
          .oneOf(
            [Yup.ref("password"), null],
            t("commonValidation.passwordMatch")
          )
          .required(t("commonValidation.confirmPassword")),
  });

  const handleSubmit = (values, { setSubmitting }) => {
    const formData = new FormData();

    Object.entries(values).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        formData.append(key, value);
      }
    });

    if (user && !values.password) {
      formData.delete("password");
      formData.delete("password_confirmation");
    }

    if (user) {
      formData.append("_method", "PUT");
    }

    // Append roles and permissions
    customPermissions.map((permission, index) =>
      formData.append(`permissions[${index}]`, permission)
    );

    if (values.avatar instanceof File) {
      formData.append("avatar", values.avatar);
    } else formData.delete("avatar");

    formData.delete("role");
    formData.append("roles[0]", values.role);

    onSubmit(formData);
    setSubmitting(false);
  };

  // Handle permission changes
  const handlePermissionChange = (permissions) => {
    setCustomPermissions(permissions);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
    >
      {({ isSubmitting, values, setFieldValue, errors, touched }) => (
        <Form className="space-y-4">
          {/* Avatar Upload */}
          <div className="flex justify-center">
            <div className="mb">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t("commonTableLabel.profilePicture")}
              </label>
              <ImageUploader
                value={initialValues.avatar}
                onUploadSuccess={(url) => setFieldValue("avatar", url)}
                // width="w-30"
                // height="h-30"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInput
              name="name"
              label={t("commonTableLabel.name")}
              placeholder={t("commonPlaceholder.namePlaceholder")}
              required
            />

            <FormInput
              name="email"
              type="email"
              label={t("commonTableLabel.email")}
              placeholder={t("commonPlaceholder.emailPlaceholder")}
              required
            />

            {/* <FormInput
              name="phone"
              label="Phone"
              placeholder="Enter phone number"
              // required
            /> */}
            <FormPhoneNumberInput
              name="phone"
              label={t("commonTableLabel.phone")}
              required={false}
            />

            <FormSelect
              name="role"
              label={t("role.role")}
              options={roleOptions}
              required
              value={values.role}
              disabled={
                user &&
                user.role === "super_admin" &&
                currentUser.role !== "super_admin"
              }
              onChange={(e) => {
                handleRoleChange(e.target.value);
                setFieldValue("role", e.target.value);
              }}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* <FormInput
              name="password"
              type="password"
              label={
                user
                  ? "New Password (leave blank to keep current)"
                  : t("commonTableLabel.password")
              }
              placeholder="••••••••"
              required={!user}
            /> */}

            <FormPassword
              name="password"
              type="password"
              label={
                user
                  ? "New Password (leave blank to keep current)"
                  : t("commonTableLabel.password")
              }
              placeholder="••••••••"
              strongCheck={false}
              required={!user}
            />
            <FormPassword
              name="password_confirmation"
              type="password"
              label={t("commonTableLabel.confirmPassword")}
              placeholder="••••••••"
              strongCheck={false}
              required={!user}
            />

            {/* <FormInput
              name="password_confirmation"
              type="password"
              label={t("commonTableLabel.confirmPassword")}
              placeholder="••••••••"
              required={!user}
            /> */}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormRadioGroup
              name="status"
              label={t("commonTableLabel.status")}
              options={statusOptions}
              required
              disabled={
                user &&
                user.role === "super_admin" &&
                currentUser.role !== "super_admin"
              }
            />
          </div>
          {/* Permission Manager */}
          <div className="border-t border-gray-200 pt-6 mt-6">
            {(() => {
              const {
                permissions,
                loading: permissionsLoading,
                error: permissionsError,
              } = usePermissionList();
              if (permissionsLoading) return <div>Loading permissions...</div>;
              if (permissionsError)
                return (
                  <div className="text-red-500">
                    Failed to load permissions.
                  </div>
                );
              return (
                <PermissionManager
                  userPermissions={customPermissions}
                  onChange={handlePermissionChange}
                  defaultPermissions={defaultRoles}
                  permissions={permissions}
                />
              );
            })()}
          </div>
          <div className="flex justify-end pt-4 border-t border-gray-200 mt-6 gap-3">
            <Button type="button" variant="outline" onClick={onCancel}>
              {t("commonButton.cancel")}
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {user
                ? t("commonButton.user.updated")
                : t("commonButton.user.create")}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default UserAddEdit;
