import { useState } from "react";
import SearchInput from "@/components/ui/SearchInput";
import FilterDropdown from "@/components/ui/FilterDropdown";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import useRoleActiveList from "@/hooks/list/useRoleActiveList";
import {
  booleanStatusOptions,
  userBooleanStatusOptions,
  userApprovalStatusOptions,
} from "@/constants/filterOption";

const UserFilters = ({ onChange }) => {
  const { t } = useTranslation();
  const [filters, setFilters] = useState({
    search: "",
    is_active: "",
    is_verified: "",
    status: "",
    role_id: "",
  });

  const { options: roleOptions, loading: roleLoading } = useRoleActiveList();

  const triggerChange = (updated) => {
    const payload = {};
    if (updated.search.trim()) payload.search = updated.search.trim();
    if (updated.is_active !== "" && updated.is_active !== undefined)
      payload.is_active = updated.is_active;
    if (updated.is_verified !== "" && updated.is_verified !== undefined)
      payload.is_verified = updated.is_verified;
    if (updated.status) payload.status = updated.status;
    if (updated.role_id) payload.role_id = updated.role_id;
    onChange?.(payload);
  };

  const handleSearchChange = (val) => {
    const updated = { ...filters, search: val };
    setFilters(updated);
    triggerChange(updated);
  };

  const handleFilterChange = (key, values) => {
    const updated = { ...filters, [key]: values };
    setFilters(updated);
    triggerChange(updated);
  };

  const handleReset = () => {
    const reset = {
      search: "",
      is_active: "",
      is_verified: "",
      status: "",
      role_id: "",
    };
    setFilters(reset);
    onChange?.({});
  };

  return (
    <div className="mb-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
        <div className="flex-1 max-w-md">
          <SearchInput
            value={filters.search}
            onChange={handleSearchChange}
            placeholder={t("user.searchPlaceholder")}
          />
        </div>

        <div className="flex flex-wrap items-center gap-2">
          <FilterDropdown
            label={t("role.role")}
            className="w-40"
            options={roleOptions}
            selectedValues={filters.role_id}
            onChange={(values) => handleFilterChange("role_id", values)}
          />
          <FilterDropdown
            label={t("commonField.approvalState")}
            className="w-40"
            options={userApprovalStatusOptions}
            selectedValues={filters.status}
            onChange={(values) => handleFilterChange("status", values)}
          />
          <FilterDropdown
            label={t("commonField.verify")}
            className="w-40"
            options={userBooleanStatusOptions}
            selectedValues={filters.is_verified}
            onChange={(values) => handleFilterChange("is_verified", values)}
          />
          <FilterDropdown
            label={t("commonField.status")}
            className="w-40"
            options={booleanStatusOptions}
            selectedValues={filters.is_active}
            onChange={(values) => handleFilterChange("is_active", values)}
          />
          <Button variant="outline" onClick={handleReset} className="ml-2">
            {t("commonButton.reset")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default UserFilters;
