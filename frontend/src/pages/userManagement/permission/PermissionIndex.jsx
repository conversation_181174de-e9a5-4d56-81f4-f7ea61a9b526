import { useState } from "react";
import { motion } from "framer-motion";
import { FaUserPlus, FaEdit, FaTrash, FaUserShield } from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import PermissionEditForm from "./PermissionAddEdit";
import PermissionFilters from "./PermissionFilters";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import { capitalizeFirstLetter, humanize } from "@/helper/Commonhelper";
import { renderNA } from "@/helper/commonFunctionHelper";

const PermissionIndex = () => {
  const { t } = useTranslation();
  const { deleteMutation, postMutation, putMutation } = useApi();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  // Filter state
  const [filterOptions, setFilterOptions] = useState({
    search: "",
    role_id: "",
    // statuses: [],
  });

  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedPermission, setSelectedPermission] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);

  const {
    data: permissionData,
    isLoading,
    isError,
    refetch,
  } = fetchData("admin/permissions", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    role_id: filterOptions.role_id || "",
    // statuses: filterOptions.statuses,
  });

  const permissionList = permissionData?.data?.data || [];
  const paginationInfo = {
    currentPage: permissionData?.data?.current_page || 1,
    perPage: permissionData?.data?.per_page || itemsPerPage,
    totalItems: permissionData?.data?.total_items || 0,
    totalPages: permissionData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  // Open edit modal
  const handleEditPermission = (permission) => {
    setSelectedPermission(permission);
    setEditModalOpen(true);
  };

  // Open delete modal
  const handleDeleteClick = (permission) => {
    setSelectedPermission(permission);
    setDeleteModalOpen(true);
  };

  const handleDeletePermission = async () => {
    if (!selectedPermission) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/permissions/${selectedPermission.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setDeleteModalOpen(false);
          setSelectedPermission(null);
          refetch();
          toast.success(t("commonToast.permissionToast.permissionDelete"));
        },
        onError: (error) => {
          console.error(
            "Permission deletion failed:",
            error?.response?.data || error.message
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const handleUpdatePermission = (updatedPermission) => {
    setEditLoading(true);
    const dataToSend = { ...updatedPermission };
    delete dataToSend.confirmPassword;

    if (updatedPermission.id && !dataToSend.password) {
      delete dataToSend.password;
    }

    const onSuccess = () => {
      setEditModalOpen(false);
      setSelectedPermission(null);
      refetch();
      toast.success(
        t(
          updatedPermission.id
            ? "commonToast.permissionToast.permissionUpdate"
            : "commonToast.permissionToast.permissionCreate"
        )
      );
    };
    const onError = (error) => {
      console.error(
        "Permission update failed:",
        error?.response?.data || error.message
      );
    };
    const onSettled = () => setEditLoading(false);

    if (!updatedPermission.id) {
      postMutation.mutate(
        { endpoint: "admin/permissions", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        {
          endpoint: `admin/permissions/${updatedPermission.id}`,
          data: dataToSend,
        },
        { onSuccess, onError, onSettled }
      );
    }
  };

  const canEditPermission = (permission) => true;

  const canDeletePermission = (permission) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.name"),
      accessor: "name",
      render: (row) =>
        row?.name ? capitalizeFirstLetter(humanize(row.name)) : renderNA,
    },
    {
      header: t("commonTableLabel.guardName"),
      accessor: "guard_name",
    },
    {
      header: t("role.role"),
      accessor: "roles",
      render: (row) =>
        row.roles && row.roles.length > 0 ? (
          <span className="font-bold bg-slate-100 px-2 py-1 rounded inline-block">
            {row.roles.map((role, idx) => (
              <span key={idx}>
                {role.name.toUpperCase()}
                {idx !== row.roles.length - 1 && <br />}
              </span>
            ))}
          </span>
        ) : (
          "N/A"
        ),
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditPermission(row)}
            disabled={!canEditPermission(row)}
            title={
              !canEditPermission(row)
                ? "You don't have permission to edit this permission"
                : t("permission.editAction")
            }
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeletePermission(row)}
            title={
              !canDeletePermission(row)
                ? "You can't delete this permission"
                : t("permission.deleteAction")
            }
          >
            <FaTrash className="text-red-600" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("permission.title")}
          icon={<FaUserShield className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => {
                setSelectedPermission(null);
                setEditModalOpen(true);
              }}
            >
              <FaUserPlus /> {t("permission.add")}
            </Button>
          }
        >
          <PermissionFilters onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={permissionList}
            emptyMessage={t("permission.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      <Modal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title={t(
          selectedPermission ? "permission.editAction" : "permission.add"
        )}
        size="lg"
      >
        <PermissionEditForm
          permission={selectedPermission}
          onSubmit={handleUpdatePermission}
          onCancel={() => setEditModalOpen(false)}
          loading={editLoading}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onDelete={handleDeletePermission}
        loading={deleteLoading}
        itemName={t("permission.permission")}
        itemValue={selectedPermission?.name}
      />
    </div>
  );
};

export default PermissionIndex;
