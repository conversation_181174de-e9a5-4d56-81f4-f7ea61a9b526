import { useState, useEffect } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { motion } from "framer-motion";
import { FormInput } from "@/components/ui/form";
import Button from "@/components/ui/Button";
import usePermissionList from "@/hooks/list/usePermissionList";
import { useTranslation } from "react-i18next";

const RoleAddEdit = ({ role, onSubmit, onCancel }) => {
  const { t } = useTranslation();
  const {
    permissions: permissionOptions,
    loading,
    error,
  } = usePermissionList();
  const [permissionGroups, setPermissionGroups] = useState({});

  useEffect(() => {
    if (permissionOptions.length) {
      const groups = permissionOptions.reduce((acc, perm) => {
        if (!acc[perm.group_name]) acc[perm.group_name] = [];
        acc[perm.group_name].push({
          label: perm.name,
          value: perm.name, // Store permission name instead of ID
          name: perm.name,
        });
        return acc;
      }, {});

      // Sort groups alphabetically
      const sortedGroups = Object.keys(groups)
        .sort()
        .reduce((acc, key) => {
          acc[key] = groups[key];
          return acc;
        }, {});
      setPermissionGroups(sortedGroups);
    }
  }, [permissionOptions]);

  const initialValues = role
    ? {
        id: role.id,
        name: role.name || "",
        permissions: role.permissions
          ? role.permissions.map((p) => (typeof p === "object" ? p.name : p))
          : [],
      }
    : {
        name: "",
        permissions: [],
      };

  const validationSchema = Yup.object({
    name: Yup.string().required(t("commonValidation.name")),
    permissions: Yup.array().min(1, t("commonValidation.leastOnePermission")),
  });

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      enableReinitialize
      onSubmit={(values, { setSubmitting }) => {
        const payload = {
          ...values,
          permissions: values.permissions.filter(Boolean),
        };

        onSubmit(payload);
        setSubmitting(false);
      }}
    >
      {({ values, setFieldValue, errors, touched, isSubmitting }) => {
        const selectedPermissions = new Set(values.permissions);

        const handlePermissionToggle = (permValue) => {
          const newPermissions = new Set(selectedPermissions);
          if (newPermissions.has(permValue)) {
            newPermissions.delete(permValue);
          } else {
            newPermissions.add(permValue);
          }
          setFieldValue("permissions", Array.from(newPermissions));
        };

        return (
          <Form className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="col-span-2">
                <FormInput
                  name="name"
                  label={t("commonTableLabel.name")}
                  placeholder={t("commonPlaceholder.namePlaceholder")}
                  required
                />
              </div>

              <div className="md:col-span-2 space-y-4">
                <h3 className="title-add-edit">{t("role.rolePermissions")}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-h-96 overflow-auto border border-gray-300 rounded p-4 bg-white">
                  {Object.entries(permissionGroups).map(
                    ([category, permissions]) => (
                      <motion.div
                        key={category}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.2 }}
                        className="border border-gray-200 rounded-md p-4"
                      >
                        <h4 className="font-medium text-gray-900 capitalize mb-3">
                          {category}
                        </h4>
                        <div className="space-y-2">
                          {permissions.map(({ label, value }) => (
                            <div key={value} className="flex items-center">
                              <input
                                type="checkbox"
                                id={`permission-${value}`}
                                checked={selectedPermissions.has(value)}
                                onChange={() => handlePermissionToggle(value)}
                                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                              />
                              <label
                                htmlFor={`permission-${value}`}
                                className="mx-2 block text-sm text-gray-700"
                              >
                                {label.replace(/_/g, " ")}
                              </label>
                            </div>
                          ))}
                        </div>
                      </motion.div>
                    )
                  )}
                </div>
                {errors.permissions && touched.permissions && (
                  <div className="text-red-600 text-sm mt-1">
                    {errors.permissions}
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end pt-4 border-t border-gray-200 mt-6 gap-3">
              <Button type="button" variant="outline" onClick={onCancel}>
                {t("commonButton.cancel")}
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {role
                  ? t("commonButton.role.updated")
                  : t("commonButton.role.create")}
              </Button>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};

export default RoleAddEdit;
