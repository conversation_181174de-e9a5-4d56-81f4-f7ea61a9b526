import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { FaUserPlus, FaEdit, FaTrash, FaUserShield } from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import RoleEditForm from "./RoleAddEdit";
import RoleFilters from "./RoleFilters";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import { capitalizeFirstLetter, humanize } from "@/helper/Commonhelper";
import { renderNA } from "@/helper/commonFunctionHelper";

const RoleIndex = () => {
  const { t } = useTranslation();
  const { deleteMutation, postMutation, putMutation } = useApi();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  // Filter state
  const [filterOptions, setFilterOptions] = useState({
    search: "",
  });

  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);

  const {
    data: roleData,
    isLoading,
    isError,
    refetch,
  } = fetchData("admin/roles", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search,
  });

  const roleList = roleData?.data?.data || [];
  const paginationInfo = {
    currentPage: roleData?.data?.current_page || 1,
    perPage: roleData?.data?.per_page || itemsPerPage,
    totalItems: roleData?.data?.total_items || 0,
    totalPages: roleData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  // Open edit modal
  const handleEditRole = (role) => {
    setSelectedRole(role);
    setEditModalOpen(true);
  };

  // Open delete modal
  const handleDeleteClick = (role) => {
    setSelectedRole(role);
    setDeleteModalOpen(true);
  };

  const handleDeleteRole = async () => {
    if (!selectedRole) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/roles/${selectedRole.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setDeleteModalOpen(false);
          setSelectedRole(null);
          refetch();
          toast.success(t("commonToast.roleToast.roleDelete"));
        },
        onError: (error) => {
          console.error(
            "Role deletion failed:",
            error?.response?.data || error.message
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const handleUpdateRole = (updatedRole) => {
    setEditLoading(true);
    const dataToSend = { ...updatedRole };
    delete dataToSend.confirmPassword;

    if (updatedRole.id && !dataToSend.password) {
      delete dataToSend.password;
    }

    const onSuccess = () => {
      setEditModalOpen(false);
      setSelectedRole(null);
      refetch();
      toast.success(
        t(
          updatedRole.id
            ? "commonToast.roleToast.roleUpdate"
            : "commonToast.roleToast.roleCreate"
        )
      );
    };
    const onError = (error) => {
      console.error("update failed:", error?.response?.data || error.message);
    };
    const onSettled = () => setEditLoading(false);

    if (!updatedRole.id) {
      postMutation.mutate(
        { endpoint: "admin/roles", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        { endpoint: `admin/roles/${updatedRole.id}`, data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    }
  };

  const canEditRole = (role) => {
    return true;
  };

  const canDeleteRole = (role) => {
    return true;
  };

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.name"),
      accessor: "name",
      render: (row) =>
        row?.name ? capitalizeFirstLetter(humanize(row.name)) : renderNA,
    },
    {
      header: t("commonTableLabel.guardName"),
      accessor: "guard_name",
    },

    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditRole(row)}
            disabled={!canEditRole(row)}
            title={
              !canEditRole(row)
                ? "You don't have permission to edit this role"
                : t("role.editAction")
            }
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteRole(row)}
            title={
              !canDeleteRole(row)
                ? "You can't delete this role"
                : t("role.deleteAction")
            }
          >
            <FaTrash className="text-red-600" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("role.title")}
          icon={<FaUserShield className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => {
                setSelectedRole(null);
                setEditModalOpen(true);
              }}
            >
              <FaUserPlus />
              {t("role.add")}
            </Button>
          }
        >
          <RoleFilters onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={roleList}
            emptyMessage={t("role.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      <Modal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title={t(selectedRole ? "role.editAction" : "role.add")}
        size="xl"
      >
        <RoleEditForm
          role={selectedRole}
          onSubmit={handleUpdateRole}
          onCancel={() => setEditModalOpen(false)}
          loading={editLoading}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onDelete={handleDeleteRole}
        loading={deleteLoading}
        itemName={t("role.role")}
        itemValue={selectedRole?.name}
      />
    </div>
  );
};

export default RoleIndex;
