import { Formik, Form } from "formik";
import * as yup from "yup";
import { toast } from "sonner";
import { FormSwitchBadge } from "@/components/ui/form";
import { useApi } from "@/hooks/useApi";
import { useTranslation } from "react-i18next";

const CustomerBanFormPage = ({ data, active = 0, onUpdated }) => {
  const id = data;
  const { t } = useTranslation();
  const { putMutation } = useApi();

  const schema = yup.object().shape({
    is_active: yup.boolean().required(),
  });

  const initialValues = { is_active: !!active };

  const loading = putMutation.isPending || putMutation.isLoading;

  const handleToggle = (next) => {
    if (!id || loading) return;
    putMutation.mutate(
      {
        endpoint: `general/users/ban-unban/${id}`,
        data: { is_active: next ? 1 : 0 },
      },
      {
        onSuccess: () => {
          if (typeof onUpdated === "function") onUpdated();
          toast.success(t("commonToast.banToast.banUpdate"));
        },
        onError: (error) => {
          toast.error(
            error?.response?.data?.message || "Failed to update ban status"
          );
        },
      }
    );
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={schema}
      onSubmit={() => {}}
      enableReinitialize
    >
      {({ setFieldValue }) => (
        <Form>
          <FormSwitchBadge
            name="is_active"
            disabled={loading || !id}
            onChange={(next) => {
              setFieldValue("is_active", next, false);
              handleToggle(next);
            }}
          />
        </Form>
      )}
    </Formik>
  );
};

export default CustomerBanFormPage;
