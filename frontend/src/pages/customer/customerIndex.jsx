import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import CustomerFilter from "./customerFilter";
import {
  FaEdit,
  FaTrash,
  FaClip<PERSON><PERSON>ist,
  FaPlus,
  FaEye,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import Modal from "@/components/ui/Modal";
import { toast } from "sonner";
import StatusBadge from "@/components/common/StatusBadge";
import { capitalizeFirstLetter, getNestedValue } from "@/helper/Commonhelper";
import ApprovalStatusBadge from "@/components/common/ApprovalStatusBadge";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import PreviewSection from "./customerPreview";
import CustomerBanFormPage from "./customerBanForm";
import { renderNA } from "@/helper/commonFunctionHelper";

const CustomerIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [previewId, setPreviewId] = useState(null);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: "",
    is_active: "",
  });

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: customerData,
    isLoading,
    isError: customerError,
    refetch,
  } = fetchData("admin/customers", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    status: filterOptions.status || "",
    is_active: filterOptions.is_active || "",
  });

  const customerList = customerData?.data?.data || [];
  const paginationInfo = {
    currentPage: customerData?.data?.current_page || 1,
    perPage: customerData?.data?.per_page || itemsPerPage,
    totalItems: customerData?.data?.total_items || 0,
    totalPages: customerData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditCustomer = (customer) => {
    navigate(`/customers/edit/${customer.id}`);
  };

  const handlePreviewClick = (customer) => {
    setPreviewId(customer.id);
  };

  const handleDeleteClick = (customer) => {
    setSelectedCustomer(customer);
  };

  const handleDeleteCustomer = async () => {
    if (!selectedCustomer) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/customers/${selectedCustomer.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setSelectedCustomer(null);
          refetch();
          toast.success(t("commonToast.customerToast.customerDelete"));
        },
        onError: (error) => {
          console.error(
            "Customer deletion failed:",
            error?.response?.data || error.message
          );
          toast.error(
            "Customer deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const canEditCustomer = (customer) => true;

  const canDeleteCustomer = (customer) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.name_en"),
      render: (row) => {
        return getNestedValue(row, "user.name", "N/A");
      },
    },
    {
      header: t("commonTableLabel.phone"),
      render: (row) => {
        return getNestedValue(row, "user.phone", "N/A");
      },
    },
    {
      header: t("commonTableLabel.email"),
      render: (row) => {
        return getNestedValue(row, "user.email", "N/A");
      },
    },
    {
      header: t("commonField.gender"),
      render: (row) => capitalizeFirstLetter(row.gender),
    },
    {
      header: t("commonTableLabel.ban"),
      render: (row) => (
        <CustomerBanFormPage
          data={getNestedValue(row, "user.id", renderNA)}
          active={getNestedValue(row, "user.is_active", 0)}
          onUpdated={refetch}
        />
      ),
    },
    {
      header: t("commonField.approvalStatus"),
      render: (row) => (
        <ApprovalStatusBadge data={row?.user} fieldName="status" />
      ),
    },
    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row?.user} fieldName="is_active" />,
    },

    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("customer.previewClassContent")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditCustomer(row)}
            disabled={!canEditCustomer(row)}
            title={
              !canEditCustomer(row)
                ? "You don't have permission to edit this Customer"
                : t("customer.editAction")
            }
          >
            <FaEdit className="icon-edit" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteCustomer(row)}
            title={
              !canDeleteCustomer(row)
                ? "You can't delete this Customer"
                : t("customer.deleteAction")
            }
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("customer.title")}
          icon={<FaClipboardList className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => navigate("/customers/add")}
            >
              <FaPlus className="mr-2" /> {t("customer.add")}
            </Button>
          }
        >
          <CustomerFilter onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={customerList}
            emptyMessage={t("customer.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      {/* PreView Page  */}
      <Modal
        isOpen={previewId}
        onClose={() => setPreviewId(null)}
        title={t("customer.previewClassContent")}
        size="xl"
        showCloseButton={true}
      >
        <PreviewSection
          customerId={previewId}
          onClose={() => setPreviewId(null)}
        />
      </Modal>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationModal
        isOpen={!!selectedCustomer}
        onClose={() => setSelectedCustomer(null)}
        onDelete={handleDeleteCustomer}
        loading={deleteLoading}
        itemName={t("customer.customer")}
        itemValue={getNestedValue(selectedCustomer, "user.name", "")}
      />
    </div>
  );
};

export default CustomerIndex;
