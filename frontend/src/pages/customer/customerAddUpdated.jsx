import { useParams, useNavigate } from "react-router-dom";
import { useState } from "react";
import { toast } from "sonner";
import CustomerFormPage from "./customerForm";
import { useApi } from "@/hooks/useApi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useTranslation } from "react-i18next";
import BackArrowIcon from "@/components/common/BackArrowIcon";

const CustomerAddUpdated = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const { fetchData, postMutation, putMutation } = useApi();

  const { data, isLoading, isError, refetch } = id
    ? fetchData(`admin/customers/${id}`)
    : { data: null, isLoading: false, isError: false, refetch: () => {} };
  const customerData = data?.data;

  const handleSubmit = (values) => {
    setLoading(true);
    const dataToSend = { ...values };

    const onSuccess = () => {
      toast.success(
        t(
          id
            ? "commonToast.customerToast.customerUpdate"
            : "commonToast.customerToast.customerCreate"
        )
      );

      navigate("/customers/list");
    };

    const onError = (error) => {
      console.error(
        "Customer update failed:",
        error?.response?.data || error.message
      );
      toast.error(
        "Customer update failed: " +
          (error?.response?.data?.message || error.message)
      );
    };

    const onSettled = () => setLoading(false);

    if (!id) {
      postMutation.mutate(
        { endpoint: "admin/customers", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        {
          endpoint: `admin/customers/${id}`,
          data: dataToSend,
        },
        { onSuccess, onError, onSettled }
      );
    }
  };

  if (isLoading || loading) {
    return (
      <div className="loading-error">
        <LoadingSpinner />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="loading-error">
        <div className="text-red-500">
          Error loading Customers data. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="page-container">
      <div className="card-style">
        <div className="title-add-edit-card">
          <div className="title-add-edit-div">
            <button onClick={() => navigate("/customers/list")}>
              <BackArrowIcon />
            </button>
            <h3 className="title-add-edit">
              {t(id ? "customer.editAction" : "customer.add")}
            </h3>
          </div>
        </div>
        <div className="px-6 py-4">
          <CustomerFormPage
            customer={customerData}
            onSubmit={handleSubmit}
            onCancel={() => navigate("/customers/list")}
            loading={loading}
          />
        </div>
      </div>
    </div>
  );
};

export default CustomerAddUpdated;
