import { useState } from "react";
import SearchInput from "@/components/ui/SearchInput";
import FilterDropdown from "@/components/ui/FilterDropdown";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import {
  statusBooleanOptions,
  userApprovalStatusOptions,
} from "@/constants/filterOption";

const CustomerFilter = ({ onChange }) => {
  const { t } = useTranslation();
  const [filters, setFilters] = useState({
    search: "",
    status: "",
    is_active: "",
  });

  const triggerChange = (updated) => {
    const payload = {};
    if (updated.search.trim()) payload.search = updated.search.trim();
    payload.is_active = String(updated.is_active);
    if (updated.status) payload.status = updated.status;
    onChange?.(payload);
  };

  const handleSearchChange = (val) => {
    const updated = { ...filters, search: val };
    setFilters(updated);
    triggerChange(updated);
  };

  const handleFilterChange = (key, values) => {
    const updated = { ...filters, [key]: values };
    setFilters(updated);
    triggerChange(updated);
  };

  const handleReset = () => {
    const reset = { search: "", is_active: "", status: "" };
    setFilters(reset);
    onChange?.({});
  };

  return (
    <div className="mb-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
        <div className="flex-1 max-w-md">
          <SearchInput
            value={filters.search}
            onChange={handleSearchChange}
            placeholder={t("customer.searchPlaceholder")}
          />
        </div>

        <div className="flex flex-wrap items-center gap-2">
          <FilterDropdown
            label={t("commonField.approvalState")}
            className="w-40"
            options={userApprovalStatusOptions}
            selectedValues={filters.status}
            onChange={(values) => handleFilterChange("status", values)}
          />
          <FilterDropdown
            label={t("commonField.status")}
            className="w-40"
            options={statusBooleanOptions}
            selectedValues={filters.is_active}
            onChange={(values) => handleFilterChange("is_active", values)}
          />
          <Button variant="outline" onClick={handleReset} className="ml-2">
            {t("commonButton.reset")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CustomerFilter;
