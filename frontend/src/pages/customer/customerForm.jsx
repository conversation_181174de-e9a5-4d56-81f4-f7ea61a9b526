import { Formik, Form } from "formik";
import * as Yup from "yup";
import {
  FormInput,
  FormRadioGroup,
  FormSelect,
  FormDatePicker,
  FormRadioButtonGroup,
  FormPassword,
} from "@/components/ui/form";
import ImageUploader from "@/components/ui/ImageUploader";
import FileUploader from "@/components/ui/FileUploader";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import { useSlugSync } from "@/hooks/useSlugSync";
import {
  genderOptions,
  preferredLanguageOptions,
  kycDocumentTypeOptions,
  kycVerifiedOptions,
  userApprovalStatusOptions,
  preferredCurrencyOptions,
  statusBooleanOptions,
} from "@/constants/filterOption";
import { toDateShowInputValue } from "@/hooks/utility";

const CustomerForm = ({ customer, onSubmit, onCancel }) => {
  const { t } = useTranslation();
  const handleSlugSync = useSlugSync();

  const initialValues = customer
    ? {
        id: customer.id || "",
        name: customer?.user?.name || "",
        email: customer?.user?.email || "",
        password: "",
        password_confirmation: "",
        phone: customer?.user?.phone || "",
        avatar: customer?.user?.avatar_url || "",
        status: customer?.user?.status || "pending",
        is_active: customer?.user?.is_active || false,
        gender: customer.gender || "male",
        date_of_birth: toDateShowInputValue(customer.date_of_birth),
        loyalty_points: customer.loyalty_points || "0",
        preferred_language: customer.preferred_language || "en",
        preferred_currency: customer.preferred_currency || "AED",
        kyc_document_type: customer.kyc_document_type || "",
        kyc_file: customer.kyc_file || "",
        kyc_verified: customer.kyc_verified || false,
        referral_code: customer.referral_code || "",
        referred_by: customer.referred_by || "",
        loyalty_points_awarded: customer.loyalty_points_awarded || false,
        newsletter_consent: customer.newsletter_consent || false,
        is_vrps: customer.is_vrps || false,
      }
    : {
        name: "",
        email: "",
        password: "",
        password_confirmation: "",
        phone: "",
        avatar: "",
        status: "pending",
        is_active: false,
        gender: "male",
        date_of_birth: "",
        loyalty_points: "0",
        preferred_language: "en",
        preferred_currency: "AED",
        kyc_document_type: "",
        kyc_file: "",
        kyc_verified: false,
        referral_code: "",
        referred_by: "",
        loyalty_points_awarded: false,
        newsletter_consent: false,
        is_vrps: false,
      };

  const validationSchema = Yup.object({
    name: Yup.string().required(t("commonValidation.name")),
    phone: Yup.string()
      .matches(/^[0-9]*$/, t("commonValidation.phoneInvalid"))
      .required(t("commonValidation.phone")),
    email: Yup.string()
      .email(t("commonValidation.emailInvalid"))
      .required(t("commonValidation.email")),
    loyalty_points: Yup.string().matches(
      /^[0-9]*$/,
      t("commonValidation.loyaltyPointsInvalid")
    ),
    status: Yup.string().required(t("commonValidation.status")),
    password: Yup.string().when("id", {
      is: (val) => !val,
      then: (schema) =>
        schema
          .min(8, t("commonValidation.passwordLength"))
          .required(t("commonValidation.password")),
      otherwise: (schema) =>
        schema.min(8, t("commonValidation.passwordLength")),
    }),

    password_confirmation: Yup.string().when("password", {
      is: (password) => password && password.length > 0,
      then: (schema) =>
        schema
          .oneOf([Yup.ref("password")], t("commonValidation.passwordMatch"))
          .required(t("commonValidation.confirmPassword")),
      otherwise: (schema) => schema.notRequired(),
    }),
  });

  const toUploadPathIfUrl = (val) => {
    if (!val || typeof val !== "string") return val;
    const m = val.match(/\/uploads\/.+$/);
    return m ? m[0].replace(/^\//, "") : val;
  };

  const handleSubmit = (values, { setSubmitting }) => {
    const formattedValues = {
      ...values,
      avatar: values.avatar?.path || values.avatar || "",
      kyc_file: values.kyc_file?.path || values.kyc_file || "",
    };

    if (formattedValues.avatar) {
      formattedValues.avatar = toUploadPathIfUrl(formattedValues.avatar);
    }
    if (formattedValues.kyc_file) {
      formattedValues.kyc_file = toUploadPathIfUrl(formattedValues.kyc_file);
    }

    onSubmit(formattedValues);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, setFieldValue, values }) => {
        const selectedDocLabel = kycDocumentTypeOptions.find(
          (opt) => opt.value === values.kyc_document_type
        )?.label;
        return (
          <Form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="gap-4 bg-white p-5 space-y-5">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        {t("commonField.avatar")}
                      </label>
                      <ImageUploader
                        value={initialValues.avatar}
                        onUploadSuccess={(url) => setFieldValue("avatar", url)}
                        width="w-full"
                        height="h-[200px]"
                      />
                    </div>
                    <div className="space-y-4 mt-2">
                      <FormInput
                        name="name"
                        label={t("commonTableLabel.name")}
                        placeholder={t("commonPlaceholder.namePlaceholder")}
                        required
                      />
                      <FormInput
                        name="phone"
                        label={t("commonTableLabel.phone")}
                        placeholder={t(
                          "commonPlaceholder.ContactPersonNumberPlaceholder"
                        )}
                        required
                      />
                      <FormInput
                        name="email"
                        type="email"
                        label={t("commonField.emailAddress")}
                        placeholder={t("commonPlaceholder.emailPlaceholder")}
                        required
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <FormDatePicker
                      name="date_of_birth"
                      label={t("commonField.birthDate")}
                    />
                    <FormSelect
                      name="gender"
                      label={t("commonField.gender")}
                      options={genderOptions}
                    />
                    {/* <FormInput
                      name="password"
                      type="password"
                      label={t("auth.password")}
                      placeholder={t("commonPlaceholder.passwordPlaceholder")}
                      required={!values.id}
                    /> */}

                    <FormPassword
                      name="password"
                      type="password"
                      label={t("auth.password")}
                      // placeholder="••••••••"
                      placeholder={t("commonPlaceholder.passwordPlaceholder")}
                      strongCheck={false}
                      required={!values.id}
                    />
                    <FormPassword
                      name="password_confirmation"
                      label={t("auth.confirmPassword")}
                      type="password"
                      // placeholder="••••••••"
                      placeholder={t(
                        "commonPlaceholder.confirmationPasswordPlaceholder"
                      )}
                      strongCheck={false}
                      required={!values.id}
                    />

                    {/* <FormInput
                      name="password_confirmation"
                      label={t("auth.confirmPassword")}
                      type="password"
                      placeholder={t(
                        "commonPlaceholder.confirmationPasswordPlaceholder"
                      )}
                      required={!values.id}
                    /> */}

                    <FormSelect
                      name="preferred_language"
                      label={t("commonField.preferredLanguage")}
                      options={preferredLanguageOptions}
                    />
                    <FormSelect
                      name="preferred_currency"
                      label={t("commonField.preferredCurrency")}
                      options={preferredCurrencyOptions}
                    />
                  </div>
                  <div className="grid grid-cols-1 gap-4">
                    <FormInput
                      name="loyalty_points"
                      label={t("commonField.loyaltyPoints")}
                      placeholder={t(
                        "commonPlaceholder.loyaltyPointsPlaceholder"
                      )}
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-4 bg-white p-5">
                <div className="grid grid-cols-1 gap-4">
                  <span className="text-lg font-semibold">
                    {t("customer.knowYourCustomer")}
                  </span>

                  <FormRadioButtonGroup
                    name="kyc_document_type"
                    label={t("commonField.kycDocument")}
                    options={kycDocumentTypeOptions}
                  />

                  {values.kyc_document_type && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t("commonField.kycFile")} [
                        <span className="font-semibold text-primary-500 px-1">
                          {selectedDocLabel}
                        </span>
                        ]
                      </label>
                      <FileUploader
                        value={values.kyc_file}
                        onUploadSuccess={(url) =>
                          setFieldValue("kyc_file", url)
                        }
                      />
                    </div>
                  )}

                  <FormRadioGroup
                    name="kyc_verified"
                    label={t("commonField.kycVerify")}
                    options={kycVerifiedOptions}
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormInput
                    name="referred_by"
                    label={t("commonField.referredBy")}
                    placeholder={t("commonPlaceholder.referredByPlaceholder")}
                  />
                  <FormInput
                    name="referral_code"
                    label={t("commonField.referralCode")}
                    placeholder={t(
                      "commonPlaceholder.referredByCodePlaceholder"
                    )}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <FormRadioGroup
                    name="is_vrps"
                    label={t("commonField.vrps")}
                    options={kycVerifiedOptions}
                  />
                  <FormRadioGroup
                    name="newsletter_consent"
                    label={t("commonField.newsletterConsent")}
                    options={kycVerifiedOptions}
                  />
                  <FormRadioGroup
                    name="loyalty_points_awarded"
                    label={t("commonField.loyaltyPointsAwarded")}
                    options={kycVerifiedOptions}
                  />
                  <FormRadioGroup
                    name="is_active"
                    label={t("commonTableLabel.status")}
                    options={statusBooleanOptions}
                  />
                </div>
                <div className="grid grid-cols-1 gap-4">
                  <FormSelect
                    name="status"
                    label={t("commonTableLabel.approval_status")}
                    options={userApprovalStatusOptions}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end pt-4 border-t border-gray-200 mt-8 gap-3">
              <Button type="button" variant="outline" onClick={onCancel}>
                {t("commonButton.cancel")}
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {customer
                  ? t("commonButton.customer.updated")
                  : t("commonButton.customer.create")}
              </Button>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};

export default CustomerForm;
