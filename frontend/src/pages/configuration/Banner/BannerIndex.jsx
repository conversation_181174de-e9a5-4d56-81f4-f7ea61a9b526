import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { FaUserPlus, FaEdit, FaTrash, Fa<PERSON>ey, FaBlog } from "react-icons/fa";
import Card from "../../../components/ui/Card";
import Table from "../../../components/ui/Table";
import PaginationInfo from "../../../components/ui/PaginationInfo";
import SearchInput from "../../../components/ui/SearchInput";
import FilterDropdown from "../../../components/ui/FilterDropdown";
import Button from "../../../components/ui/Button";
import Modal from "../../../components/ui/Modal";
import LoadingSpinner from "../../../components/ui/LoadingSpinner";
import { filterUsers } from "../../../hooks/filter/filterUsers";
import {
  getAllRoles,
  getRoleDisplayName,
  rolePermissions,
} from "../../../constants/roles";
import BannerForm from "./BannerAddUpdated";
//call api service
import { fetchData, useApi } from "../../../hooks/useApi";
import usePagination from "../../../hooks/usePagination";

const BannerIndex = () => {
  const [currentUser, setCurrentUser] = useState({ id: 1, role: "admin" });

  // Pagination state using usePagination
  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 5);

  // State for search and filters
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState({
    roles: [],
    statuses: [],
  });

  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  // Use fetchData from useApi.js for fetching users
  const {
    data: usersData,
    isLoading: usersLoading,
    isError: usersError,
    refetch: refetchUsers,
  } = fetchData("admin/blog-categories", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
  });

  // Extract user list and pagination info from API response
  const userList = usersData?.data?.data || [];
  const paginationInfo = {
    currentPage: usersData?.data?.current_page || 1,
    perPage: usersData?.data?.per_page || itemsPerPage,
    totalItems: usersData?.data?.total_items || 0,
    totalPages: usersData?.data?.total_pages || 1,
  };

  // Filtered users for client-side filtering (if needed)
  const filteredUsers = filterUsers(userList, searchTerm, filters);

  useEffect(() => {
    setCurrentPage(1); // Reset to first page when filters change
  }, [searchTerm, filters]);

  // Handle page change
  // Handle search change
  const handleSearchChange = (value) => {
    setSearchTerm(value);
  };

  // Handle filter change
  const handleFilterChange = (filterType, values) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: values,
    }));
  };

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1); // Reset to first page when items per page changes
  };

  // Reset all filters
  const handleResetFilters = () => {
    setSearchTerm("");
    setFilters({
      roles: [],
      statuses: [],
    });
  };

  // Open edit modal
  const handleEditUser = (user) => {
    setSelectedUser(user);
    setEditModalOpen(true);
  };

  // Open delete modal
  const handleDeleteClick = (user) => {
    setSelectedUser(user);
    setDeleteModalOpen(true);
  };

  // Handle user delete
  const { deleteMutation: deleteUserMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);

  const handleDeleteUser = async () => {
    if (!selectedUser) return;
    setDeleteLoading(true);
    deleteUserMutation.mutate(
      {
        endpoint: `admin/blog-categories/${selectedUser.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setDeleteModalOpen(false);
          setSelectedUser(null);
          refetchUsers();
          alert("blog category deleted successfully.");
        },
        onError: (error) => {
          console.error(
            "User deletion failed:",
            error?.response?.data || error.message
          );
          alert(
            "User deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  // Use postMutation and putMutation for user add/update
  const { postMutation, putMutation } = useApi();
  const [editLoading, setEditLoading] = useState(false);

  const handleUpdateUser = (updatedUser) => {
    setEditLoading(true);
    const dataToSend = { ...updatedUser };
    delete dataToSend.confirmPassword;

    if (updatedUser.id && !dataToSend.password) {
      delete dataToSend.password;
    }

    const onSuccess = () => {
      setEditModalOpen(false);
      setSelectedUser(null);
      refetchUsers();
      alert(`User ${updatedUser.id ? "updated" : "created"} successfully.`);
    };
    const onError = (error) => {
      console.error(
        "User update failed:",
        error?.response?.data || error.message
      );
      alert(
        "User update failed: " +
          (error?.response?.data?.message || error.message)
      );
    };
    const onSettled = () => setEditLoading(false);

    if (!updatedUser.id) {
      postMutation.mutate(
        { endpoint: "admin/blog-categories", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        {
          endpoint: `admin/blog-categories/${updatedUser.id}`,
          data: dataToSend,
        },
        { onSuccess, onError, onSettled }
      );
    }
  };

  // Check if user can be edited (can't edit super admin if you're not super admin)
  const canEditUser = (user) => {
    if (currentUser.role === "super_admin") return true;
    return user.role !== "super_admin";
  };

  // Check if user can be deleted (can't delete yourself or super admin if you're not super admin)
  const canDeleteUser = (user) => {
    if (user.id === currentUser.id) return false;
    if (currentUser.role === "super_admin") return true;
    return user.role !== "super_admin";
  };

  // Table columns configuration
  const columns = [
    {
      header: "ID",
      accessor: "id",
    },
    {
      header: "title_en",
      accessor: "title_en",
    },
    {
      header: "title_ar",
      accessor: "title_ar",
    },
    {
      header: "slug",
      accessor: "slug",
    },
    {
      header: "Role",
      accessor: "roles",
      render: (row) => row.roles?.[0]?.name || "N/A",
    },

    {
      header: "Status",
      accessor: "status",
      render: (row) => {
        const statusColors = {
          active: "bg-green-100 text-green-800",
          inactive: "bg-gray-100 text-gray-800",
          suspended: "bg-red-100 text-red-800",
        };

        return (
          <span
            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
              statusColors[row.status]
            }`}
          >
            {row.status.charAt(0).toUpperCase() + row.status.slice(1)}
          </span>
        );
      },
    },
    {
      header: "Actions",
      render: (row) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditUser(row)}
            disabled={!canEditUser(row)}
            title={
              !canEditUser(row)
                ? "You don't have permission to edit this user"
                : "Edit user"
            }
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteUser(row)}
            title={
              !canDeleteUser(row) ? "You can't delete this user" : "Delete user"
            }
          >
            <FaTrash className="text-red-600" />
          </Button>
          {row.permissions && (
            <div className="relative group">
              <Button
                variant="outline"
                size="sm"
                title="User has custom permissions"
                className="cursor-default"
              >
                <FaKey
                  className={
                    row.permissions.length !== rolePermissions[row.role]?.length
                      ? "text-yellow-500"
                      : "text-gray-400"
                  }
                />
              </Button>
              <div className="absolute z-10 hidden group-hover:block bg-white border border-gray-200 rounded-md shadow-lg p-2 w-48 text-xs right-0 mt-1">
                <p className="font-semibold mb-1">Custom Permissions:</p>
                <p className="text-gray-600">
                  {row.permissions &&
                  row.permissions.length !== rolePermissions[row.role]?.length
                    ? `${row.permissions.length} permissions (${
                        rolePermissions[row.role]?.length || 0
                      } in role)`
                    : "Standard role permissions"}
                </p>
              </div>
            </div>
          )}
        </div>
      ),
    },
  ];

  // Filter options
  const roleOptions = getAllRoles().map((role) => ({
    label: getRoleDisplayName(role.key),
    value: role.key,
  }));

  const statusOptions = [
    { label: "Active", value: "active" },
    { label: "Inactive", value: "inactive" },
    { label: "Suspended", value: "suspended" },
  ];

  // Calculate total pages
  const totalPages = paginationInfo.totalPages;

  return (
    <div className="max-w-7xl mx-auto relative">
      {usersLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card title="Banner List" icon={<FaBlog className="text-indigo-600" />}>
          <div className="mb-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
              <div className="flex-1 max-w-md">
                <SearchInput
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder="Search users..."
                />
              </div>

              <div className="flex flex-wrap items-center space-x-2">
                <FilterDropdown
                  label="Role"
                  options={roleOptions}
                  selectedValues={filters.roles}
                  onChange={(values) => handleFilterChange("roles", values)}
                />

                <FilterDropdown
                  label="Status"
                  options={statusOptions}
                  selectedValues={filters.statuses}
                  onChange={(values) => handleFilterChange("statuses", values)}
                />

                <Button
                  variant="outline"
                  onClick={handleResetFilters}
                  className="ml-2"
                >
                  Reset
                </Button>

                <Button
                  variant="primary"
                  className="ml-2"
                  onClick={() => {
                    setSelectedUser(null);
                    setEditModalOpen(true);
                  }}
                >
                  <FaUserPlus className="mr-2" /> Add Banner
                </Button>
              </div>
            </div>
          </div>

          <Table
            columns={columns}
            data={filteredUsers}
            emptyMessage="No users found matching your criteria."
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      {/* Edit User Modal */}
      <Modal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title={selectedUser ? "Edit Banner" : "Add New Banner"}
        size="lg"
      >
        <BannerForm
          user={selectedUser}
          onSubmit={handleUpdateUser}
          onCancel={() => setEditModalOpen(false)}
          loading={editLoading}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="Confirm Delete"
        size="sm"
      >
        <div className="py-4">
          <p className="text-gray-700">
            Are you sure you want to delete the user{" "}
            <span className="font-semibold">{selectedUser?.name}</span>? This
            action cannot be undone.
          </p>
        </div>
        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={() => setDeleteModalOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleDeleteUser}
            disabled={deleteLoading}
          >
            {deleteLoading ? <LoadingSpinner size={20} /> : "Delete"}
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default BannerIndex;
