import { fetchData } from "@/hooks/useApi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { capitalizeFirstLetter, getNestedValue } from "@/helper/Commonhelper";
import { renderNA } from "@/helper/commonFunctionHelper";
import BackArrowIcon from "@/components/common/BackArrowIcon";

const Label = ({ label, value }) => (
  <div className="flex items-center gap-2 text-base">
    <strong className="w-60 text-gray-500 font-medium">{label}</strong>
    <strong className="w-4 text-gray-600">:</strong>
    <strong className="w-60 text-gray-700 font-medium">{value}</strong>
  </div>
);

const BrandPreview = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const {
    data: previewData,
    isLoading,
    isError,
  } = fetchData(`admin/brands/${id}`);
  const data = previewData?.data;

  if (isLoading) {
    return (
      <div className="loading-error-view">
        <LoadingSpinner />
      </div>
    );
  }
  return (
    <div className="page-container">
      <div className="bg-white rounded-lg shadow-xl border border-gray-300">
        <div className="title-add-edit-card">
          <div className="title-add-edit-div">
            <button onClick={() => navigate("/products/brands")}>
              <BackArrowIcon />
            </button>
            <h3 className="title-add-edit">{t("brand.title")}</h3>
          </div>
        </div>
        <div className="space-y-4 mx-auto relative p-4">
          <Card titleLabel={t("brandPreview.information")}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-4 text-sm">
              <Label
                label={t("commonTableLabel.title_en")}
                value={capitalizeFirstLetter(data.name_en || renderNA)}
              />
              <Label
                label={t("commonTableLabel.name_ar")}
                value={renderNA(data.name_ar)}
              />
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default BrandPreview;
