import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Formik, Form, Field } from "formik";
import * as yup from "yup";
import { toast } from "sonner";
import {
  FormInput,
  FormTextarea,
  FormSwitch,
  FormCheckbox,
  FormRadioGroup,
} from "@/components/ui/form";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import ImageUpload from "@/components/ui/ImageUploader";
import { useSlugSync } from "@/hooks/useSlugSync";
import { useApi, fetchData } from "@/hooks/useApi";
import { useTranslation } from "react-i18next";
import BackArrowIcon from "@/components/common/BackArrowIcon";
import { statusBooleanOptions, regOptions } from "@/constants/filterOption";

const BrandAddEdit = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const handleSlugSync = useSlugSync();
  const navigate = useNavigate();
  const { post, put } = useApi();
  const [loading, setLoading] = useState(false);

  const { data: brandData, isLoading: brandLoading } = id
    ? fetchData(`admin/brands/${id}`)
    : { data: null, isLoading: false };

  const brand = brandData?.data;

  const schema = yup.object().shape({
    name_en: yup.string().required(t("commonValidation.name_en")),
    name_ar: yup.string().optional(),
    slug: yup.string().required(t("commonValidation.slug")),
    country_of_origin: yup.string().optional(),
    is_trademark_registered: yup.boolean().optional(),
    website: yup
      .string()
      .url(t("commonValidation.mustBeURL"))
      .optional()
      .nullable(),
    instagram: yup
      .string()
      .url(t("commonValidation.mustBeURL"))
      .optional()
      .nullable(),
    facebook: yup
      .string()
      .url(t("commonValidation.mustBeURL"))
      .optional()
      .nullable(),
    manufacturer: yup.boolean().optional(),
    brand_owner: yup.boolean().optional(),
    marketing_auth_holder: yup.boolean().optional(),
    exclusive_distributor: yup.boolean().optional(),
    authorized_distributor: yup.boolean().optional(),
    wholesaler: yup.boolean().optional(),
    authorized_retailer: yup.boolean().optional(),
    direct_importer: yup.boolean().optional(),
    parallel_importer: yup.boolean().optional(),
    drop_shipper: yup.boolean().optional(),
    // skus_on_brand_website: yup.number().optional().nullable(),
    skus_on_brand_website: yup
      .number()
      .typeError(t("commonValidation.skuBrandWebsite"))
      .min(0, t("commonValidation.notNegativeNumber"))
      .optional()
      .nullable(),
    skus_on_amazon: yup
      .number()
      .typeError(t("commonValidation.skuAmazon"))
      .min(0, t("commonValidation.notNegativeNumber"))
      .optional()
      .nullable(),

    // skus_on_amazon: yup.number().optional().nullable(),
    // skus_on_noon: yup.number().optional().nullable(),
    // skus_on_other_marketplaces: yup.number().optional().nullable(),
    skus_on_noon: yup
      .number()
      //.transform((value) => (isNaN(value) ? undefined : value)) // Handle NaN values
      // .min(0, "The skus on noon field must be at least 0")
      .min(0, t("commonValidation.notNegativeNumber"))
      .typeError(t("commonValidation.skuNoon"))
      .optional()
      .nullable(),

    skus_on_other_marketplaces: yup
      .number()
      .min(0, t("commonValidation.notNegativeNumber"))
      .typeError(t("commonValidation.skuOtherMarketplaces"))
      .optional()
      .nullable(),
    skus_on_own_website: yup
      .number()
      .min(0, t("commonValidation.notNegativeNumber"))
      .typeError(t("commonValidation.skuOwnWebsite"))
      .optional()
      .nullable(),
    // sold_in_hypermarkets: yup.boolean().optional(),
    // sold_in_pharmacies: yup.boolean().optional(),
    // sold_in_specialty_stores: yup.boolean().optional(),
    // mohap_registration: yup.string().optional().nullable(),
    // dubai_municipality_registration: yup.string().optional().nullable(),
    // dubai_municipality_registration: yup
    //   .string()
    //   .oneOf(
    //     ["ValidOption1", "ValidOption2", "ValidOption3"],
    //     "The selected Dubai Municipality Registration is invalid."
    //   )
    //   .optional()
    //   .nullable(),

    brand_usp: yup.string().optional().nullable(),
    top_products: yup.string().optional().nullable(),
    // logo: yup.mixed().optional(),
    // trademark_document: yup.mixed().optional(),
    // relationship_proof: yup.mixed().optional(),
    // purchase_proof: yup.mixed().optional(),
    // self_declaration: yup.mixed().optional(),
    // product_pictures: yup.mixed().optional(),
    // product_pictures: yup
    //   .array()
    //   .of(yup.string().required("Each picture is required"))
    //   .optional(),
    // status: yup.string(),
    // description: yup.string().optional(),
    // status: yup
    //   .string()
    //   .nullable()
    //   .oneOf(["pending", "approved", "rejected"], "Invalid status value")
    //   .default("pending"),
  });

  const initialValues = {
    name_en: brand?.name_en || "",
    name_ar: brand?.name_ar || "",
    slug: brand?.slug || "",
    country_of_origin: brand?.country_of_origin || "",
    is_trademark_registered: brand?.is_trademark_registered || false,
    website: brand?.website || "",
    instagram: brand?.instagram || "",
    facebook: brand?.facebook || "",
    manufacturer: brand?.manufacturer || false,
    brand_owner: brand?.brand_owner || false,
    marketing_auth_holder: brand?.marketing_auth_holder || false,
    exclusive_distributor: brand?.exclusive_distributor || false,
    authorized_distributor: brand?.authorized_distributor || false,
    wholesaler: brand?.wholesaler || false,
    authorized_retailer: brand?.authorized_retailer || false,
    direct_importer: brand?.direct_importer || false,
    parallel_importer: brand?.parallel_importer || false,
    drop_shipper: brand?.drop_shipper || false,
    skus_on_brand_website: brand?.skus_on_brand_website ?? "",
    skus_on_amazon: brand?.skus_on_amazon ?? "",
    // skus_on_noon: brand?.skus_on_noon ?? "",
    skus_on_noon: brand?.skus_on_noon ?? "",
    skus_on_other_marketplaces: brand?.skus_on_other_marketplaces ?? "",
    skus_on_own_website: brand?.skus_on_own_website ?? "",
    // sold_in_hypermarkets: brand?.sold_in_hypermarkets || false,
    // sold_in_pharmacies: brand?.sold_in_pharmacies || false,
    // sold_in_specialty_stores: brand?.sold_in_specialty_stores || false,
    sold_in_hypermarkets: brand?.sold_in_hypermarkets || "",
    sold_in_pharmacies: brand?.sold_in_pharmacies || "",
    sold_in_specialty_stores: brand?.sold_in_specialty_stores || "",
    // mohap_registration: brand?.mohap_registration || "",
    // dubai_municipality_registration:
    //   brand?.dubai_municipality_registration || "",
    // dubai_municipality_registration:
    //   brand?.dubai_municipality_registration || "",
    mohap_registration: brand?.mohap_registration || "no",
    dubai_municipality_registration:
      brand?.dubai_municipality_registration || "no",

    brand_usp: brand?.brand_usp || "",
    top_products: brand?.top_products?.join(", ") || "",
    // logo: brand?.logo || "",
    logo: brand?.logo_url || "",
    trademark_document: brand?.trademark_document_url || "",
    relationship_proof: brand?.relationship_proof_url || "",
    purchase_proof: brand?.purchase_proof_url || "",
    self_declaration: brand?.self_declaration_url || "",
    // product_pictures: brand?.product_pictures || "",
    product_pictures: brand?.product_pictures_with_url || [],
    // status: brand?.is_active ?? "active",
    status: brand?.status ? "approved" : "pending",
    description: brand?.description || "",
    // is_active: brand?.is_active || "",
    // is_active: brand.is_active || "inactive",
    // is_active: brand?.is_active ?? "inactive",
    is_active: brand?.is_active === true,
  };

  // const statusOptions = [
  //   { label: t("commonOptions.status.active"), value: true },
  //   { label: t("commonOptions.status.inactive"), value: false },
  // ];

  // const regOptions = [
  //   { label: t("commonOptions.yesNo.Yes"), value: "yes" },
  //   { label: t("commonOptions.yesNo.No"), value: "no" },
  // ];

  const toUploadPathIfUrl = (val) => {
    if (!val || typeof val !== "string") return val;
    const m = val.match(/\/uploads\/.+$/);
    return m ? m[0].replace(/^\//, "") : val;
  };

  const handleSubmit = (values) => {
    setLoading(true);

    const processAndSubmit = async () => {
      try {
        const payload = { ...values };

        // payload.status = payload.status ? "active" : "pending";
        payload.status = payload.status || "pending";
        if (payload.top_products && typeof payload.top_products === "string") {
          payload.top_products = payload.top_products
            .split(",")
            .map((p) => p.trim())
            .filter((p) => p);
        } else if (!Array.isArray(payload.top_products)) {
          payload.top_products = [];
        }

        const booleanFields = [
          "is_trademark_registered",
          "manufacturer",
          "brand_owner",
          "marketing_auth_holder",
          "exclusive_distributor",
          "authorized_distributor",
          "wholesaler",
          "authorized_retailer",
          "direct_importer",
          "parallel_importer",
          "drop_shipper",
          // "sold_in_hypermarkets",
          // "sold_in_pharmacies",
          // "sold_in_specialty_stores",
        ];

        for (const key of booleanFields) {
          payload[key] = payload[key] ? 1 : 0;
        }

        if (payload.logo) {
          payload.logo = toUploadPathIfUrl(payload.logo);
        }
        if (payload.trademark_document) {
          payload.trademark_document = toUploadPathIfUrl(
            payload.trademark_document
          );
        }
        if (payload.relationship_proof) {
          payload.relationship_proof = toUploadPathIfUrl(
            payload.relationship_proof
          );
        }
        if (payload.purchase_proof) {
          payload.purchase_proof = toUploadPathIfUrl(payload.purchase_proof);
        }
        if (payload.self_declaration) {
          payload.self_declaration = toUploadPathIfUrl(
            payload.self_declaration
          );
        }
        if (payload.product_pictures) {
          payload.product_pictures = toUploadPathIfUrl(
            payload.product_pictures
          );
        }

        const promise = id
          ? put(`admin/brands/${id}`, payload)
          : post("admin/brands", payload);

        await promise;
        toast.success(
          id
            ? t("commonToast.brandToast.brandUpdate")
            : t("commonToast.brandToast.brandCreate")
        );
        navigate("/products/brands");
      } catch (error) {
        const errorMessage =
          error?.response?.data?.message ||
          `Failed to ${id ? "update" : "create"} brand.`;
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    processAndSubmit();
  };

  if (brandLoading) {
    return <LoadingSpinner size={64} overlay />;
  }

  return (
    <div className="page-container">
      <div className="card-style">
        <div className="title-add-edit-card">
          <div className="title-add-edit-div">
            <button onClick={() => navigate("/products/brands")}>
              <BackArrowIcon />
            </button>
            <h3 className="title-add-edit">
              {t(id ? "brand.editAction" : "brand.add")}
            </h3>
          </div>
        </div>
        <Formik
          initialValues={initialValues}
          validationSchema={schema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, setFieldValue, errors, touched }) => {
            return (
              <Form className="space-y-6 p-5">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormInput
                    name="name_en"
                    label={t("commonField.name_en")}
                    error={touched.name_en && errors.name_en}
                    placeholder={t("commonPlaceholder.name_enPlaceholder")}
                    onChange={(e) =>
                      handleSlugSync(e, setFieldValue, "name_en", "slug")
                    }
                  />
                  <FormInput
                    name="name_ar"
                    label={t("commonField.name_ar")}
                    error={touched.name_ar && errors.name_ar}
                    placeholder={t("commonPlaceholder.name_arPlaceholder")}
                  />
                  <FormInput
                    name="slug"
                    label={t("commonField.slug")}
                    error={touched.slug && errors.slug}
                    placeholder={t("commonPlaceholder.slugPlaceholder")}
                  />
                  <FormInput
                    name="country_of_origin"
                    label={t("commonField.countryOrigin")}
                    error={
                      touched.country_of_origin && errors.country_of_origin
                    }
                    placeholder={t(
                      "commonPlaceholder.countryOriginPlaceholder"
                    )}
                  />
                  <FormInput
                    name="website"
                    label={t("commonField.website")}
                    error={touched.website && errors.website}
                    placeholder={t("commonPlaceholder.websitePlaceholder")}
                  />
                  <FormInput
                    name="instagram"
                    label={t("commonField.instagramPage")}
                    error={touched.instagram && errors.instagram}
                    placeholder={t("commonPlaceholder.instagramPlaceholder")}
                  />
                  <FormInput
                    name="facebook"
                    label={t("commonField.facebookPage")}
                    error={touched.facebook && errors.facebook}
                    placeholder={t("commonPlaceholder.facebookPlaceholder")}
                  />
                </div>
                <h3 className="text-lg font-semibold pt-4">
                  {t("brand.businessType")}
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  <FormSwitch
                    name="manufacturer"
                    label={t("commonField.manufacturer")}
                  />
                  <FormSwitch
                    name="brand_owner"
                    label={t("commonField.brandOwner")}
                  />
                  <FormSwitch
                    name="marketing_auth_holder"
                    label={t("commonField.marketingAuthHolder")}
                  />
                  <FormSwitch
                    name="exclusive_distributor"
                    label={t("commonField.exclusiveDistributor")}
                  />
                  <FormSwitch
                    name="authorized_distributor"
                    label={t("commonField.authorizedDistributor")}
                  />
                  <FormSwitch
                    name="wholesaler"
                    label={t("commonField.wholesaler")}
                  />
                  <FormSwitch
                    name="authorized_retailer"
                    label={t("commonField.authorizedRetailer")}
                  />

                  <FormSwitch
                    name="direct_importer"
                    label={t("commonField.directImporter")}
                  />
                  <FormSwitch
                    name="parallel_importer"
                    label={t("commonField.parallelImporter")}
                  />
                  <FormSwitch
                    name="drop_shipper"
                    label={t("commonField.dropShipper")}
                  />
                </div>
                <h3 className="text-lg font-semibold pt-4">
                  {t("brand.skuInformation")}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <FormInput
                    name="skus_on_brand_website"
                    label={t("commonField.skuBrandWebsite")}
                    // type="number"
                  />
                  <FormInput
                    name="skus_on_amazon"
                    label={t("commonField.skuAmazon")}
                    // type="number"
                  />
                  <FormInput
                    name="skus_on_noon"
                    label={t("commonField.skuNoon")}
                    // type="number"
                  />
                  <FormInput
                    name="skus_on_other_marketplaces"
                    label={t("commonField.skuOtherMarketplaces")}
                    // type="number"
                  />
                  <FormInput
                    name="skus_on_own_website"
                    label={t("commonField.skuOwnWebsite")}
                    // type="number"
                  />
                </div>
                <h3 className="text-lg font-semibold pt-4">
                  {t("brand.salesChannels")}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <FormInput
                    name="sold_in_hypermarkets"
                    label={t("commonField.soldHypermarkets")}
                    placeholder={t(
                      "commonPlaceholder.soldHypermarketPlaceholder"
                    )}
                  />
                  <FormInput
                    name="sold_in_pharmacies"
                    label={t("commonField.soldPharmacies")}
                    placeholder={t(
                      "commonPlaceholder.soldPharmaciesPlaceholder"
                    )}
                  />
                  <FormInput
                    name="sold_in_specialty_stores"
                    label={t("commonField.soldSpecialtyStores")}
                    placeholder={t(
                      "commonPlaceholder.soldSpecialtyStoresPlaceholder"
                    )}
                  />
                  {/* <FormSwitch
                    name="sold_in_hypermarkets"
                    label="Sold in Hypermarkets"
                  />
                  <FormSwitch
                    name="sold_in_pharmacies"
                    label="Sold in Pharmacies"
                  />
                  <FormSwitch
                    name="sold_in_specialty_stores"
                    label="Sold in Specialty Stores"
                  /> */}
                </div>
                <h3 className="text-lg font-semibold pt-4">
                  {t("brand.registrationUSP")}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* <FormInput
                    name="mohap_registration"
                    label="MOHAP Registration"
                  /> */}
                  {/* <FormCheckbox
                    name="mohap_registration"
                    label="MOHAP Registration"
                    helperText="Check if the brand is registered with MOHAP."
                  /> */}
                  {/* <FormInput
                    name="dubai_municipality_registration"
                    label="Dubai Municipality Registration"
                  /> */}
                  {/* <FormRadioGroup
                    name="mohap_registration"
                    label="MOHAP Registration"
                    options={regOptions}
                  /> */}
                  <FormRadioGroup
                    name="mohap_registration"
                    label={t("commonField.MOHAPRegistration")}
                    options={regOptions}
                    onChange={(value) =>
                      setFieldValue("mohap_registration", value)
                    }
                  />
                  <FormRadioGroup
                    name="dubai_municipality_registration"
                    label={t("commonField.dubaiMunicipalityRegistration")}
                    options={regOptions}
                    onChange={(value) =>
                      setFieldValue("dubai_municipality_registration", value)
                    }
                  />

                  {/* <FormRadioGroup
                    name="dubai_municipality_registration"
                    label="Dubai Municipality Registration"
                    options={regOptions}.
                  /> */}
                </div>
                <FormTextarea
                  name="brand_usp"
                  label={t("commonField.brandUSP")}
                  placeholder={t(
                    "commonPlaceholder.brandUniqueSellingPropositionPlaceholder"
                  )}
                />
                <FormTextarea
                  name="top_products"
                  label={t("commonField.topProducts")}
                  placeholder={t(
                    "commonPlaceholder.egProductOneProductTwoProductThree"
                  )}
                />
                <FormTextarea
                  name="description"
                  label={t("commonField.description")}
                  error={touched.description && errors.description}
                  placeholder={t("commonPlaceholder.descriptionPlaceholder")}
                />
                <h3 className="text-lg font-semibold pt-4">
                  {t("commonField.document")}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t("commonField.brandLogo")}
                    </label>
                    <ImageUpload
                      value={values.logo}
                      onUploadSuccess={(path) => setFieldValue("logo", path)}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t("commonField.trademarkDocument")}
                    </label>
                    <ImageUpload
                      value={values.trademark_document}
                      onUploadSuccess={(path) =>
                        setFieldValue("trademark_document", path)
                      }
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t("commonField.relationshipProof")}
                    </label>
                    <ImageUpload
                      value={values.relationship_proof}
                      onUploadSuccess={(path) =>
                        setFieldValue("relationship_proof", path)
                      }
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t("commonField.purchaseProof")}
                    </label>
                    <ImageUpload
                      value={values.purchase_proof}
                      onUploadSuccess={(path) =>
                        setFieldValue("purchase_proof", path)
                      }
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t("commonField.selfDeclaration")}
                    </label>
                    <ImageUpload
                      value={values.self_declaration}
                      onUploadSuccess={(path) =>
                        setFieldValue("self_declaration", path)
                      }
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t("commonField.productPictures")}
                    </label>
                    {/* <ImageUpload
                      value={values.product_pictures}
                      onUploadSuccess={(path) =>
                        setFieldValue("product_pictures", path)
                      }
                    /> */}
                    <ImageUpload
                      value={values.product_pictures}
                      onUploadSuccess={(path) =>
                        setFieldValue("product_pictures", [
                          ...values.product_pictures,
                          path,
                        ])
                      }
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6 pt-4">
                  <FormSwitch
                    name="is_trademark_registered"
                    label={t("commonField.isTrademarkRegistered")}
                  />
                  {/* <FormSwitch
                    name="status"
                    label="Status"
                    onLabel="Active"
                    offLabel="Pending"
                  /> */}

                  <FormRadioGroup
                    name="is_active"
                    label={t("commonField.status")}
                    options={statusBooleanOptions}
                    required
                  />
                </div>
                {/* <div className="flex justify-end space-x-4 pt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate("/products/brands")}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" variant="primary" disabled={loading}>
                    {loading ? (
                      <LoadingSpinner size={20} />
                    ) : id ? (
                      "Update Brand"
                    ) : (
                      "Create Brand"
                    )}
                  </Button>
                </div> */}
                <div className="flex justify-end pt-4 border-t border-gray-200 mt-8 gap-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate("/products/brands")}
                  >
                    {t("commonButton.cancel")}
                  </Button>
                  <Button type="submit" variant="primary" disabled={loading}>
                    {loading ? (
                      <LoadingSpinner size={20} />
                    ) : id ? (
                      t("commonButton.brand.updated")
                    ) : (
                      t("commonButton.brand.create")
                    )}
                  </Button>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
    </div>
  );
};

export default BrandAddEdit;
