import { useState, useCallback, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { toast } from "sonner";
import { FaPlus, FaEdit, FaTrash, FaEye } from "react-icons/fa";
import { RiListCheck3 } from "react-icons/ri";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import BrandFilters from "./BrandFilters";
import NoImage from "@/assets/NoImage.png";
import { useTranslation } from "react-i18next";
import StatusBadge from "@/components/common/StatusBadge";
import ApprovalStatusBadge from "@/components/common/ApprovalStatusBadge";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";

const BrandList = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { delete: removeBrand } = useApi();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
  } = usePagination(1, 10);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: "",
    is_active: "",
  });

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedBrand, setSelectedBrand] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const apiParams = useMemo(
    () => ({
      pagination: true,
      per_page: itemsPerPage,
      page: currentPage,
      search: filterOptions.search || "",
      status: filterOptions.status || "",
      is_active: filterOptions.is_active ?? "",
    }),
    [itemsPerPage, currentPage, filterOptions]
  );

  const {
    data: brandsData,
    isLoading: brandsLoading,
    refetch: refetchBrands,
  } = fetchData("admin/brands", apiParams);

  const brandList = brandsData?.data?.data || [];
  const paginationInfo = {
    currentPage: brandsData?.data?.current_page || 1,
    perPage: brandsData?.data?.per_page || itemsPerPage,
    totalItems: brandsData?.data?.total || 0,
    totalPages: brandsData?.data?.last_page || 1,
  };

  const handleFilterChange = useCallback(
    (newFilters) => {
      setFilterOptions((prevFilters) => {
        const searchChanged = prevFilters.search !== newFilters.search;
        const statusesChanged =
          JSON.stringify(prevFilters.is_active) !==
          JSON.stringify(newFilters.is_active);
        const approvalChanged =
          JSON.stringify(prevFilters.status) !==
          JSON.stringify(newFilters.status);

        if (searchChanged || statusesChanged || approvalChanged) {
          setCurrentPage(1);
          return newFilters;
        }

        return prevFilters;
      });
    },
    [setCurrentPage]
  );

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditBrand = (brand) => {
    navigate(`/products/brands/edit/${brand.id}`);
  };

  const handlePreviewClick = (brand) => {
    navigate(`/products/brands/preview/${brand.id}`);
  };

  const handleDeleteClick = (brand) => {
    setSelectedBrand(brand);
    setDeleteModalOpen(true);
  };

  const handleDeleteBrand = async () => {
    if (!selectedBrand) return;
    setDeleteLoading(true);
    removeBrand(`admin/brands/${selectedBrand.id}`)
      .then(() => {
        setDeleteModalOpen(false);
        setSelectedBrand(null);
        refetchBrands();
        toast.success(t("commonToast.brandToast.brandDelete"));
      })
      .finally(() => setDeleteLoading(false));
  };

  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.image"),
      accessor: "logo_url",
      render: (row) =>
        row.logo_url ? (
          <img
            src={row.logo_url}
            alt={row.title_en || "Brand Image"}
            className="w-12 h-12 object-cover rounded"
          />
        ) : (
          <img
            src={NoImage}
            alt={row.title_en || "Brand Image"}
            className="w-12 h-12 object-cover rounded"
          />
        ),
    },

    {
      header: t("commonTableLabel.name_en"),
      accessor: "name_en",
    },
    {
      header: t("commonTableLabel.name_ar"),
      accessor: "name_ar",
    },
    {
      header: t("commonTableLabel.country"),
      accessor: "country_of_origin",
    },
    {
      header: t("commonField.approvalStatus"),
      render: (row) => <ApprovalStatusBadge data={row} fieldName="status" />,
    },
    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row} fieldName="is_active" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("brand.previewOrder")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditBrand(row)}
          >
            <FaEdit className="icon-edit" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="page-container">
      {brandsLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("brand.title")}
          icon={<RiListCheck3 className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => navigate("/products/brands/add")}
            >
              <FaPlus className="mr-2" /> {t("brand.add")}
            </Button>
          }
        >
          <BrandFilters onChange={handleFilterChange} />
          <Table
            columns={columns}
            data={brandList}
            emptyMessage={t("brand.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onDelete={handleDeleteBrand}
        loading={deleteLoading}
        itemName={t("brand.brand")}
        itemValue={selectedBrand?.name_en}
      />
    </div>
  );
};

export default BrandList;
