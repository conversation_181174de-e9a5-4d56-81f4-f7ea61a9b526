import { useState } from "react";
import SearchInput from "@/components/ui/SearchInput";
import FilterDropdown from "@/components/ui/FilterDropdown";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import {
  booleanStatusOptions,
  approvalOptions,
} from "@/constants/filterOption";

const BrandFilters = ({ onChange }) => {
  const { t } = useTranslation();
  const [filters, setFilters] = useState({
    search: "",
    is_active: "",
    status: "",
  });

  const triggerChange = (updated) => {
    const payload = {};
    if (updated.search.trim()) payload.search = updated.search.trim();
    if (updated.is_active !== "" && updated.is_active !== undefined)
      payload.is_active = updated.is_active;
    if (updated.status) payload.status = updated.status;
    onChange?.(payload);
  };

  const handleSearchChange = (val) => {
    const updated = { ...filters, search: val };
    setFilters(updated);
    triggerChange(updated);
  };

  const handleFilterChange = (key, val) => {
    const updated = { ...filters, [key]: val };
    setFilters(updated);
    triggerChange(updated);
  };

  const handleReset = () => {
    const reset = { search: "", is_active: "", status: "" };
    setFilters(reset);
    onChange?.({});
  };

  return (
    <div className="mb-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
        <div className="flex-1 max-w-md">
          <SearchInput
            value={filters.search}
            onChange={handleSearchChange}
            placeholder={t("brand.searchPlaceholder")}
          />
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <FilterDropdown
            label={t("commonField.approvalState")}
            className="w-40"
            options={approvalOptions}
            value={filters.status}
            onChange={(val) => handleFilterChange("status", val)}
          />
          <FilterDropdown
            label={t("commonField.status")}
            className="w-40"
            options={booleanStatusOptions}
            value={filters.is_active}
            onChange={(val) => handleFilterChange("is_active", val)}
          />

          <Button variant="outline" onClick={handleReset} className="ml-2">
            {t("commonButton.reset")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BrandFilters;
