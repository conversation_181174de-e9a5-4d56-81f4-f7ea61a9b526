import { useState } from "react";
import { motion } from "framer-motion";
import {
  FaUserPlus,
  FaEdit,
  FaTrash,
  FaUserShield,
  FaArrowLeft,
  FaEye,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import BannerItemsFilters from "./BannerItemsFilter";
import BannerItemsEditForm from "./BannerItemAddUpdated";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import { useParams, useNavigate } from "react-router-dom";
import NoImage from "@/assets/NoImage.png";
import { useTranslation } from "react-i18next";
import StatusBadge from "@/components/common/StatusBadge";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import PreviewSection from "./BannerItemPreview";

const BannerItemIndex = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { deleteMutation, postMutation, putMutation } = useApi();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  // State for filters
  const [filterOptions, setFilterOptions] = useState({
    search: "",
    roles: [],
    status: "",
  });

  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedBannerItem, setSelectedBannerItem] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  const [previewId, setPreviewId] = useState(null);

  const {
    data: bannerItemData,
    isLoading,
    isError,
    refetch,
  } = fetchData(`admin/banners/${id}`, {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    roles: filterOptions.roles || [],
    status: filterOptions.status || "",
  });

  //API response object..
  const itemsBanner = bannerItemData?.data || {};
  const bannerItemList = itemsBanner?.items || [];

  const paginationInfo = {
    currentPage: bannerItemData?.data?.current_page || 1,
    perPage: bannerItemData?.data?.per_page || itemsPerPage,
    totalItems: bannerItemData?.data?.total_items || 0,
    totalPages: bannerItemData?.data?.total_pages || 1,
  };

  // Handle filter change from ProductClassFilters
  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  // Open edit modal
  const handleEditClass = (BannerItem) => {
    setSelectedBannerItem(BannerItem);
    setEditModalOpen(true);
  };

  const handlePreviewClick = (BannerItem) => {
    setPreviewId(BannerItem.id);
  };

  // Open delete modal
  const handleDeleteClick = (BannerItem) => {
    setSelectedBannerItem(BannerItem);
    setDeleteModalOpen(true);
  };

  const handleDeleteBanner = async () => {
    if (!selectedBannerItem) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/banner-items/${selectedBannerItem.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setDeleteModalOpen(false);
          setSelectedBannerItem(null);
          refetch();
          toast.success(t("commonToast.bannerItemsToast.bannerItemDelete"));
        },
        onError: (error) => {
          console.error(
            "Banner Items deletion failed:",
            error?.response?.data || error.message
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const handleUpdateBanner = (updatedBanner) => {
    setEditLoading(true);
    const dataToSend = { ...updatedBanner };
    delete dataToSend.confirmPassword;

    if (updatedBanner.id && !dataToSend.password) {
      delete dataToSend.password;
    }

    const onSuccess = () => {
      setEditModalOpen(false);
      setSelectedBannerItem(null);
      refetch();
      toast.success(
        updatedBanner.id
          ? t("commonToast.bannerItemsToast.bannerItemUpdate")
          : t("commonToast.bannerItemsToast.bannerItemCreate")
      );
    };
    const onError = (error) => {
      console.error(
        "Banner Items update failed:",
        error?.response?.data || error.message
      );
    };
    const onSettled = () => setEditLoading(false);

    if (!updatedBanner.id) {
      postMutation.mutate(
        { endpoint: "admin/banner-items", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        {
          endpoint: `admin/banner-items/${updatedBanner.id}`,
          data: dataToSend,
        },
        { onSuccess, onError, onSettled }
      );
    }
  };

  const canEditBannerItems = (BannerItem) => true;

  const canDeleteBannerItems = (BannerItem) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.image"),
      accessor: "media_path",
      render: (row) =>
        row.media_path ? (
          <img
            src={`${import.meta.env.VITE_IMAGE_URL}/${row.media_path}`}
            alt={row.title_en || "Banner Items Image"}
            className="w-12 h-12 object-cover rounded"
          />
        ) : (
          <img
            src={NoImage}
            alt={row.title_en || "Banner Items Image"}
            className="w-12 h-12 object-cover rounded"
          />
        ),
    },
    {
      header: t("commonTableLabel.title_en"),
      accessor: "title_en",
    },
    {
      header: t("commonTableLabel.title_ar"),
      accessor: "title_ar",
    },
    {
      header: t("commonTableLabel.alt_text"),
      accessor: "alt_text",
    },
    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row} fieldName="is_active" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("bannerItems.previewClassContent")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditClass(row)}
            disabled={!canEditBannerItems(row)}
            title={
              !canEditBannerItems(row)
                ? "You don't have permission to edit this Banner"
                : t("bannerItems.editAction")
            }
          >
            <FaEdit className="icon-edit" />
          </Button>

          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteBannerItems(row)}
            title={
              !canDeleteBannerItems(row)
                ? "You can't delete this Banner Items"
                : t("bannerItems.deleteAction")
            }
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <div className="py-2">
        <Button
          variant="secondary"
          className="gap-2"
          onClick={() => navigate("/setting/banner")}
        >
          <FaArrowLeft />
          {t("banner.title")}
        </Button>
      </div>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("bannerItems.title")}
          icon={<FaUserShield className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => {
                setSelectedBannerItem(null);
                setEditModalOpen(true);
              }}
            >
              <FaUserPlus /> {t("bannerItems.add")}
            </Button>
          }
        >
          <BannerItemsFilters onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={bannerItemList}
            emptyMessage={t("bannerItems.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      <Modal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title={
          selectedBannerItem
            ? t("bannerItems.editAction")
            : t("bannerItems.add")
        }
        size="lg"
      >
        <BannerItemsEditForm
          banner={selectedBannerItem}
          onSubmit={handleUpdateBanner}
          onCancel={() => setEditModalOpen(false)}
          loading={editLoading}
        />
      </Modal>

      {/* PreView Page  */}
      <Modal
        isOpen={previewId}
        onClose={() => setPreviewId(null)}
        title={t("bannerItems.previewClassContent")}
        size="xl"
        showCloseButton={true}
      >
        <PreviewSection
          bannerItemId={previewId}
          onClose={() => setPreviewId(null)}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onDelete={handleDeleteBanner}
        loading={deleteLoading}
        itemName={t("bannerItems.bannerItem")}
        itemValue={selectedBannerItem?.title_en}
      />
    </div>
  );
};

export default BannerItemIndex;
