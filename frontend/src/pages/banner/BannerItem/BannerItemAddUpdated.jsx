import { Formik, Form } from "formik";
import * as Yup from "yup";
import { FormInput, FormRadioGroup } from "@/components/ui/form";
import Button from "@/components/ui/Button";
import ImageUploader from "@/components/ui/ImageUploader";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { booleanStatusOptions } from "@/constants/filterOption";
import { ErrorMessage } from "formik";

const BannerItemAddUpdated = ({ banner: bannerData, onSubmit, onCancel }) => {
  const { t } = useTranslation();
  const { id: banner_id } = useParams();

  const initialValues = bannerData
    ? {
        id: bannerData.id,
        banner_id: bannerData?.banner_id || banner_id,
        title_en: bannerData?.title_en || "",
        title_ar: bannerData?.title_ar || "",
        alt_text: bannerData.alt_text || "",
        media_path: bannerData?.media_path_url || "",
        position: bannerData?.position || "",
        link_url: bannerData?.link_url || "",
        is_active: Number(bannerData?.is_active ?? 0),
      }
    : {
        banner_id,
        title_en: "",
        title_ar: "",
        alt_text: "",
        media_path: "",
        position: "",
        link_url: "",
        is_active: 0,
      };

  const validationSchema = Yup.object({
    title_en: Yup.string().required(t("commonValidation.title_en")),
    media_path: Yup.string().required(t("commonValidation.imageRequired")),
    link_url: Yup.string()
      .url(t("commonValidation.mustBeURL"))
      .optional()
      .nullable(),
    position: Yup.number()
      .typeError(t("commonValidation.validNumber"))
      .min(0, t("commonValidation.notNegativeNumber"))
      .optional()
      .nullable(),
  });

  const toUploadPathIfUrl = (val) => {
    if (!val || typeof val !== "string") return val;
    const m = val.match(/\/uploads\/.+$/);
    return m ? m[0].replace(/^\//, "") : val;
  };

  const handleSubmit = (values, { setSubmitting }) => {
    const submitValues = {
      ...values,
      media_path: values.media_path?.path || values.media_path || "",
      position:
        values.position === "" ||
        values.position === null ||
        values.position === undefined
          ? 0
          : Number(values.position),
    };

    if (submitValues.media_path) {
      submitValues.media_path = toUploadPathIfUrl(submitValues.media_path);
    }
    onSubmit(submitValues);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, values, setFieldValue }) => {
        return (
          <Form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
              <FormInput
                name="title_en"
                label={t("commonField.title_en")}
                placeholder={t("commonPlaceholder.title_enPlaceholder")}
                required
              />
              <FormInput
                name="title_ar"
                label={t("commonField.title_ar")}
                placeholder={t("commonPlaceholder.title_arPlaceholder")}
              />
              <FormInput
                name="link_url"
                label={t("commonField.link")}
                placeholder={t("commonPlaceholder.linkUrlPlaceholder")}
              />
              <FormInput
                name="position"
                label={t("commonField.position")}
                placeholder={t("commonPlaceholder.positionPlaceholder")}
              />
              <FormInput
                name="alt_text"
                label={t("commonField.alt_text")}
                placeholder={t("commonPlaceholder.altTextPlaceholder")}
              />

              <div className="mb">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t("commonField.itemsImage")}
                  <span className="text-red-500 ml-1">*</span>
                </label>

                <ImageUploader
                  value={values.media_path}
                  onUploadSuccess={(url) => setFieldValue("media_path", url)}
                  required
                />
                <ErrorMessage name="media_path">
                  {(msg) => (
                    <div className="text-red-500 text-sm mt-1">{msg}</div>
                  )}
                </ErrorMessage>
              </div>
              <FormRadioGroup
                name="is_active"
                label={t("commonField.status")}
                options={booleanStatusOptions}
              />
            </div>
            <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 mt-6">
              <Button type="button" variant="outline" onClick={onCancel}>
                {t("commonButton.cancel")}
              </Button>
              <Button type="submit" variant="primary" loading={isSubmitting}>
                {bannerData
                  ? t("commonButton.bannerItem.updated")
                  : t("commonButton.bannerItem.create")}
              </Button>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};

export default BannerItemAddUpdated;
