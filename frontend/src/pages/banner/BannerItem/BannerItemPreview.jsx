import { fetchData } from "@/hooks/useApi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { useTranslation } from "react-i18next";
import { getNestedValue, capitalizeFirstLetter } from "@/helper/Commonhelper";
import { renderNA } from "@/helper/commonFunctionHelper";

const Label = ({ label, value }) => (
  <div className="flex items-center gap-2 text-base">
    <strong className="w-60 text-gray-500 font-medium">{label}</strong>
    <strong className="w-4 text-gray-600">:</strong>
    <strong className="w-60 text-gray-700 font-medium">{value}</strong>
  </div>
);

const BannerItemPreview = ({ bannerItemId, onClose }) => {
  const { t } = useTranslation();
  const {
    data: previewData,
    isLoading,
    isError,
  } = fetchData(`admin/banner-items/${bannerItemId}`);
  const data = previewData?.data;

  if (isLoading) {
    return (
      <div className="loading-error-view">
        <LoadingSpinner />
      </div>
    );
  }
  return (
    <div className="mx-auto">
      <div className="bg-white rounded-lg shadow-xl border border-gray-300">
        <div className="space-y-4 mx-auto relative">
          <Card titleLabel={t("customer.classInformation")}>
            <div className="grid grid-cols-1 md:grid-cols-2">
              <Label
                label={t("commonTableLabel.title_en")}
                value={capitalizeFirstLetter(data.title_en || renderNA)}
              />
              <Label
                label={t("commonTableLabel.title_ar")}
                value={capitalizeFirstLetter(data.title_ar || renderNA)}
              />
            </div>
          </Card>
        </div>
      </div>
      <div className="flex justify-end gap-3 pt-4 border-t border-gray-300 mt-4">
        <Button type="button" variant="outline" onClick={onClose}>
          {t("commonButton.cancel")}
        </Button>
      </div>
    </div>
  );
};

export default BannerItemPreview;
