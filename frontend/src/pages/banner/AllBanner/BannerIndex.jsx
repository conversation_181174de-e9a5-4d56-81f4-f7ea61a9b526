import { useState } from "react";
import { motion } from "framer-motion";
import {
  FaUserPlus,
  FaEdit,
  FaTrash,
  FaUserShield,
  FaFileImport,
  FaEye,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import BannerFilters from "./BannerFilters";
import BannerEditForm from "./BannerAddUpdated";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import StatusBadge from "@/components/common/StatusBadge";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import PreviewSection from "./BannerPreview";

const BannerIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { deleteMutation, postMutation, putMutation } = useApi();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  // State for filters
  const [filterOptions, setFilterOptions] = useState({
    search: "",
    roles: [],
    // statuses: [],
    status: "",
  });

  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedBanner, setSelectedBanner] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  const [previewId, setPreviewId] = useState(null);

  const {
    data: bannersData,
    isLoading,
    isError,
    refetch,
  } = fetchData("admin/banners", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    roles: filterOptions.roles || [],
    status: filterOptions.status || "",
  });

  const bannerList = bannersData?.data?.data || [];

  const paginationInfo = {
    currentPage: bannersData?.data?.current_page || 1,
    perPage: bannersData?.data?.per_page || itemsPerPage,
    totalItems: bannersData?.data?.total_items || 0,
    totalPages: bannersData?.data?.total_pages || 1,
  };

  // Handle filter change from ProductClassFilters
  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  // Open edit modal
  const handleEditClass = (BannerItem) => {
    setSelectedBanner(BannerItem);
    setEditModalOpen(true);
  };

  const handlePreviewClick = (BannerItem) => {
    setPreviewId(BannerItem.id);
  };

  // Open delete modal
  const handleDeleteClick = (BannerItem) => {
    setSelectedBanner(BannerItem);
    setDeleteModalOpen(true);
  };

  const handleBannerItem = (BannerItem) => {
    navigate(`/setting/banner/items/list/${BannerItem.id}`);
  };

  const handleDeleteBanner = async () => {
    if (!selectedBanner) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/banners/${selectedBanner.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setDeleteModalOpen(false);
          setSelectedBanner(null);
          refetch();
          toast.success(t("commonToast.bannerToast.bannerDelete"));
        },
        onError: (error) => {
          console.error(
            "Banner deletion failed:",
            error?.response?.data || error.message
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const handleUpdateBanner = (updatedBanner) => {
    setEditLoading(true);
    const dataToSend = { ...updatedBanner };
    delete dataToSend.confirmPassword;

    if (updatedBanner.id && !dataToSend.password) {
      delete dataToSend.password;
    }

    const onSuccess = () => {
      setEditModalOpen(false);
      setSelectedBanner(null);
      refetch();
      toast.success(
        updatedBanner.id
          ? t("commonToast.bannerToast.bannerUpdate")
          : t("commonToast.bannerToast.bannerCreate")
      );
    };
    const onError = (error) => {
      console.error(
        "Banner update failed:",
        error?.response?.data || error.message
      );
    };
    const onSettled = () => setEditLoading(false);

    if (!updatedBanner.id) {
      postMutation.mutate(
        { endpoint: "admin/banners", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        {
          endpoint: `admin/banners/${updatedBanner.id}`,
          data: dataToSend,
        },
        { onSuccess, onError, onSettled }
      );
    }
  };

  const canEditBanner = (BannerItem) => true;

  const canBannerItems = (BannerItem) => true;

  const canDeleteBanner = (BannerItem) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.title"),
      accessor: "title",
    },
    {
      header: t("commonTableLabel.type"),
      accessor: "type",
    },
    {
      header: t("commonTableLabel.description"),
      accessor: "description",
    },
    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row} fieldName="is_active" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("banner.previewClassContent")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditClass(row)}
            disabled={!canEditBanner(row)}
            title={
              !canEditBanner(row)
                ? "You don't have permission to edit this Banner"
                : t("banner.editAction")
            }
          >
            <FaEdit className="icon-edit" />
          </Button>

          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteBanner(row)}
            title={
              !canDeleteBanner(row)
                ? "You can't delete this Banner"
                : t("banner.deleteAction")
            }
          >
            <FaTrash className="icon-danger" />
          </Button>

          <Button
            variant="view"
            size="sm"
            onClick={() => handleBannerItem(row)}
            disabled={!canBannerItems(row)}
            title={
              !canBannerItems(row)
                ? "You don't have permission to edit this Banner"
                : t("banner.itemsAction")
            }
          >
            <FaFileImport className="text-yellow-600" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("banner.title")}
          icon={<FaUserShield className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => {
                setSelectedBanner(null);
                setEditModalOpen(true);
              }}
            >
              <FaUserPlus /> {t("banner.add")}
            </Button>
          }
        >
          <BannerFilters onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={bannerList}
            emptyMessage={t("banner.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>
      {/* edit component page  */}
      <Modal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title={selectedBanner ? t("banner.editAction") : t("banner.add")}
        size="md"
      >
        <BannerEditForm
          banner={selectedBanner}
          onSubmit={handleUpdateBanner}
          onCancel={() => setEditModalOpen(false)}
          loading={editLoading}
        />
      </Modal>

      {/* PreView Page  */}
      <Modal
        isOpen={previewId}
        onClose={() => setPreviewId(null)}
        title={t("banner.previewClassContent")}
        size="xl"
        showCloseButton={true}
      >
        <PreviewSection
          bannerId={previewId}
          onClose={() => setPreviewId(null)}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onDelete={handleDeleteBanner}
        loading={deleteLoading}
        itemName={t("banner.banner")}
        itemValue={selectedBanner?.title}
      />
    </div>
  );
};

export default BannerIndex;
