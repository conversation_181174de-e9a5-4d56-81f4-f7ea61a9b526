import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { FaEdit, FaTrash, FaKey, FaBlog, FaPlus, FaEye } from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import BlogCategoryFilters from "./blogCategoryFilters";
import BlogCategoryForm from "./blogCategoryAddUpdate";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import StatusBadge from "@/components/common/StatusBadge";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import PreviewSection from "./blogCategoryPreview";

const blogCategoryIndex = () => {
  const { postMutation, putMutation, deleteMutation } = useApi();

  const { t } = useTranslation();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  // State for filters
  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: "",
  });

  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedBlogCategory, setSelectedBlogCategory] = useState(null);
  const [editLoading, setEditLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [previewId, setPreviewId] = useState(null);

  const {
    data: blogCategoryData,
    isLoading,
    isError,
    refetch,
  } = fetchData("admin/blog-categories", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    status: filterOptions.status || "",
  });

  const blogCategoryList = blogCategoryData?.data?.data || [];
  const paginationInfo = {
    currentPage: blogCategoryData?.data?.current_page || 1,
    perPage: blogCategoryData?.data?.per_page || itemsPerPage,
    totalItems: blogCategoryData?.data?.total_items || 0,
    totalPages: blogCategoryData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  // Open edit modal
  const handleEditBlogCategory = (blogCategory) => {
    setSelectedBlogCategory(blogCategory);
    setEditModalOpen(true);
  };

  const handlePreviewClick = (blogCategory) => {
    setPreviewId(blogCategory.id);
  };

  // Open delete modal
  const handleDeleteClick = (blogCategory) => {
    setSelectedBlogCategory(blogCategory);
    setDeleteModalOpen(true);
  };

  const handleDelete = async () => {
    if (!selectedBlogCategory) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/blog-categories/${selectedBlogCategory.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setDeleteModalOpen(false);
          setSelectedBlogCategory(null);
          refetch();
          toast.success(t("commonToast.blogCategoryToast.blogCategoryDelete"));
        },
        onError: (error) => {
          console.error(
            "Category deletion failed:",
            error?.response?.data || error.message
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const handleUpdateBlogCategory = (updatedBlogCategory) => {
    setEditLoading(true);
    const dataToSend = { ...updatedBlogCategory };
    delete dataToSend.confirmPassword;

    if (updatedBlogCategory.id && !dataToSend.password) {
      delete dataToSend.password;
    }

    const onSuccess = () => {
      setEditModalOpen(false);
      setSelectedBlogCategory(null);
      refetch();
      toast.success(
        t(
          updatedBlogCategory.id
            ? "commonToast.blogCategoryToast.blogCategoryUpdate"
            : "commonToast.blogCategoryToast.blogCategoryCreate"
        )
      );
    };
    const onError = (error) => {
      console.error(
        "Category update failed:",
        error?.response?.data || error.message
      );
    };
    const onSettled = () => setEditLoading(false);

    if (!updatedBlogCategory.id) {
      postMutation.mutate(
        { endpoint: "admin/blog-categories", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        {
          endpoint: `admin/blog-categories/${updatedBlogCategory.id}`,
          data: dataToSend,
        },
        { onSuccess, onError, onSettled }
      );
    }
  };

  const canEditBlogCategory = (blogCategory) => true;

  const canDeleteBlogCategory = (blogCategory) => true;

  // Table columns configuration
  const columns = [
    {
      // header: "ID",
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.title_en"),
      accessor: "title_en",
    },
    {
      header: t("commonTableLabel.title_ar"),
      accessor: "title_ar",
    },
    {
      header: t("commonTableLabel.slug"),
      accessor: "slug",
    },
    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row} fieldName="status" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("blogCategory.previewClassContent")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditBlogCategory(row)}
            disabled={!canEditBlogCategory(row)}
            title={
              !canEditBlogCategory(row)
                ? "You don't have permission to edit this blog category"
                : t("blogCategory.editAction")
            }
          >
            <FaEdit className="icon-edit" />
          </Button>

          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteBlogCategory(row)}
            title={
              !canDeleteBlogCategory(row)
                ? "You can't delete this blog category"
                : t("blogCategory.deleteAction")
            }
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("blogCategory.title")}
          icon={<FaBlog className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => {
                setSelectedBlogCategory(null);
                setEditModalOpen(true);
              }}
            >
              <FaPlus className="mr-2" /> {t("blogCategory.add")}
            </Button>
          }
        >
          <BlogCategoryFilters onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={blogCategoryList}
            emptyMessage={t("blogCategory.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      <Modal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title={
          selectedBlogCategory
            ? t("blogCategory.editAction")
            : t("blogCategory.add")
        }
        size="xl"
      >
        <BlogCategoryForm
          blogCategory={selectedBlogCategory}
          onSubmit={handleUpdateBlogCategory}
          onCancel={() => setEditModalOpen(false)}
          loading={editLoading}
        />
      </Modal>

      {/* PreView Page  */}
      <Modal
        isOpen={previewId}
        onClose={() => setPreviewId(null)}
        title={t("blogCategory.previewClassContent")}
        size="xl"
        showCloseButton={true}
      >
        <PreviewSection
          blogCategoryId={previewId}
          onClose={() => setPreviewId(null)}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onDelete={handleDelete}
        loading={deleteLoading}
        itemName={t("blogCategory.blogCategory")}
        itemValue={selectedBlogCategory?.title_en}
      />
    </div>
  );
};

export default blogCategoryIndex;
