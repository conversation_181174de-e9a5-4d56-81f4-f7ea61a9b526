import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import BlogFilters from "./blogFilters";
import PreviewSection from "./PreviewModal";
import {
  FaEdit,
  FaTrash,
  FaClipboardList,
  FaEye,
  FaPlus,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import Modal from "@/components/ui/Modal";
import { toast } from "sonner";
import { getNestedValue } from "@/helper/Commonhelper";
import WorkflowStatusBadge from "@/components/common/WorkflowStatusBadge";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";

const blogIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: "",
    blog_category_id: "",
  });

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedBlog, setSelectedBlog] = useState(null);
  const [previewBlog, setPreviewBlog] = useState(null);
  const [previewBlogId, setPreviewBlogId] = useState(null);

  const {
    data: blogsData,
    isLoading,
    isError: blogsError,
    refetch,
  } = fetchData("admin/blogs", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    status: filterOptions.status || "",
    blog_category_id: filterOptions.blog_category_id || "",
  });

  const blogList = blogsData?.data?.data || [];
  const paginationInfo = {
    currentPage: blogsData?.data?.current_page || 1,
    perPage: blogsData?.data?.per_page || itemsPerPage,
    totalItems: blogsData?.data?.total_items || 0,
    totalPages: blogsData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditBlog = (blog) => {
    navigate(`/blog/posts/edit/${blog.id}`);
  };

  const handleDeleteClick = (blog) => {
    setSelectedBlog(blog);
  };

  // const handlePreviewClick = (blog) => {
  //   setPreviewBlog(blog);
  // };
  const handlePreviewClick = (blog) => {
    setPreviewBlogId(blog.id);
  };

  const handleDeleteBlog = async () => {
    if (!selectedBlog) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/blogs/${selectedBlog.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setSelectedBlog(null);
          refetch();
          toast.success(t("commonToast.blogToast.blogDelete"));
        },
        onError: (error) => {
          console.error(
            "Blog deletion failed:",
            error?.response?.data || error.message
          );
          toast.error(
            "Blog deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const canEditBlog = (blog) => true;

  const canDeleteBlog = (blog) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.title_en"),
      accessor: "title_en",
    },
    {
      header: t("commonTableLabel.title_ar"),
      accessor: "title_ar",
    },
    {
      header: t("commonTableLabel.metaTitle"),
      accessor: "meta_title",
    },
    {
      header: t("commonTableLabel.blogCategory"),
      render: (row) => {
        const value = getNestedValue(row, "category.title_en", "N/A");
        return <span>{value}</span>;
      },
    },
    {
      header: t("commonTableLabel.status"),
      accessor: "status",
      render: (row) => <WorkflowStatusBadge data={row} fieldName="status" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("blog.previewBlogContent")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditBlog(row)}
            disabled={!canEditBlog(row)}
            title={
              !canEditBlog(row)
                ? "You don't have permission to edit this blog"
                : t("blog.editAction")
            }
          >
            <FaEdit className="icon-edit" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteBlog(row)}
            title={
              !canDeleteBlog(row)
                ? "You can't delete this blog"
                : t("blog.deleteAction")
            }
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("blog.title")}
          icon={<FaClipboardList className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => navigate("/blog/posts/add")}
            >
              <FaPlus className="mr-1" /> {t("blog.add")}
            </Button>
          }
        >
          <BlogFilters onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={blogList}
            emptyMessage={t("blog.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationModal
        isOpen={!!selectedBlog}
        onClose={() => setSelectedBlog(null)}
        onDelete={handleDeleteBlog}
        loading={deleteLoading}
        itemName={t("blog.blog")}
        itemValue={selectedBlog?.title_en}
      />

      {/* Blog View  */}
      <Modal
        isOpen={previewBlogId}
        onClose={() => setPreviewBlogId(null)}
        title={t("blog.previewBlogContent")}
        size="xl"
        showCloseButton={true}
      >
        <PreviewSection
          blogId={previewBlogId}
          onClose={() => setPreviewBlogId(null)}
        />
      </Modal>
    </div>
  );
};

export default blogIndex;
