import { fetchData } from "@/hooks/useApi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import Button from "@/components/ui/Button";

const PreviewData = ({ blogId, onClose }) => {
  const { data, isLoading } = fetchData(`admin/blogs/${blogId}`);
  const blogData = data?.data || {};
  if (isLoading) {
    return (
      <div className="loading-error-view">
        <LoadingSpinner />
      </div>
    );
  }
  return (
    <div>
      <div className="space-y-4 m-5">
        <div className="flex items-center space-x-2">
          <div>TITLE:: {blogData.title_en}</div>
        </div>
      </div>
      <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 mt-4">
        <Button type="button" variant="outline" onClick={onClose}>
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default PreviewData;
