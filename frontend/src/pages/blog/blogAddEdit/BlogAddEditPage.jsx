import { useParams, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import BlogForm from "./BlogForm";
import { useApi } from "@/hooks/useApi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useTranslation } from "react-i18next";
import BackArrowIcon from "@/components/common/BackArrowIcon";

const BlogAddEditPage = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const { fetchData, postMutation, putMutation } = useApi();

  const { data, isLoading, isError, refetch } = id
    ? fetchData(`admin/blogs/${id}`)
    : { data: null, isLoading: false, isError: false, refetch: () => {} };
  const blogData = data?.data;

  const handleSubmit = (values) => {
    setLoading(true);
    const dataToSend = { ...values };

    const onSuccess = () => {
      toast.success(
        t(
          id
            ? "commonToast.blogToast.blogUpdate"
            : "commonToast.blogToast.blogCreate"
        )
      );
      navigate("/blog/posts");
    };

    const onError = (error) => {
      console.error(
        "Blog update failed:",
        error?.response?.data || error.message
      );
      toast.error(
        "Blog update failed: " +
          (error?.response?.data?.message || error.message)
      );
    };

    const onSettled = () => setLoading(false);

    if (!id) {
      postMutation.mutate(
        { endpoint: "admin/blogs", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        {
          endpoint: `admin/blogs/${id}`,
          data: dataToSend,
        },
        { onSuccess, onError, onSettled }
      );
    }
  };

  if (isLoading || loading) {
    return (
      <div className="loading-error">
        <LoadingSpinner />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="loading-error">
        <div className="text-red-500">
          Error loading blog data. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="page-container">
      <div className="card-style">
        <div className="title-add-edit-card">
          <div className="title-add-edit-div">
            <button onClick={() => navigate("/blog/posts")}>
              <BackArrowIcon />
            </button>
            <h3 className="title-add-edit">
              {id ? t("blog.editAction") : t("blog.add")}
            </h3>
          </div>
        </div>
        <div className="px-6 py-4">
          <BlogForm
            blog={blogData}
            onSubmit={handleSubmit}
            onCancel={() => navigate("/blog/posts")}
            loading={loading}
          />
        </div>
      </div>
    </div>
  );
};

export default BlogAddEditPage;
