import { Formik, Form } from "formik";
import * as Yup from "yup";
import {
  FormInput,
  FormRadioGroup,
  FormSelect,
  FormTextarea,
  RichTextEditor,
} from "@/components/ui/form";
import ImageUploader from "@/components/ui/ImageUploader";
import Button from "@/components/ui/Button";
import { toast } from "sonner";
import useBlogCategoryList from "@/hooks/list/useBlogCategoryList";
import { useSlugSync } from "@/hooks/useSlugSync";
import { useTranslation } from "react-i18next";

const BlogAddUpdate = ({ blog, onSubmit, onCancel }) => {
  const { t } = useTranslation();
  const handleSlugSync = useSlugSync();
  const {
    options: blogCategoryOptions,
    loading: blogCategoryLoading,
    error: blogCategoryError,
  } = useBlogCategoryList();

  const initialValues = blog
    ? {
        id: blog.id,
        title_en: blog.title_en || "",
        title_ar: blog.title_ar || "",
        blog_category_id: blog.blog_category_id || "",
        slug: blog.slug || "",
        content_en: blog.content_en || "",
        content_ar: blog.content_ar || "",
        summary_en: blog.summary_en || "",
        summary_ar: blog.summary_ar || "",
        meta_title: blog.meta_title || "",
        keywords: blog.keywords || "",
        meta_description: blog.meta_description || "",
        featured_image: blog.featured_image_url || "",
        status: blog.status || "draft",
      }
    : {
        title_en: "",
        title_ar: "",
        blog_category_id: "",
        slug: "",
        content_en: "",
        content_ar: "",
        summary_en: "",
        summary_ar: "",
        meta_title: "",
        keywords: "",
        meta_description: "",
        featured_image: "",
        status: "draft",
      };

  const validationSchema = Yup.object({
    blog_category_id: Yup.string().required(t("commonValidation.blogCategory")),
    title_en: Yup.string().required(t("commonValidation.title_en")),
    slug: Yup.string().required(t("commonValidation.slug")),
    status: Yup.string().required(t("commonValidation.status")),
  });
  const statusOptions = [
    { label: "Draft", value: "draft" },
    { label: "Published", value: "published" },
  ];

  const toUploadPathIfUrl = (val) => {
    if (!val || typeof val !== "string") return val;
    const m = val.match(/\/uploads\/.+$/);
    return m ? m[0].replace(/^\//, "") : val;
  };

  const handleSubmit = (values, { setSubmitting }) => {
    const formattedValues = {
      ...values,
      featured_image:
        values.featured_image?.path || values.featured_image || "",
    };
    if (formattedValues.featured_image) {
      formattedValues.featured_image = toUploadPathIfUrl(
        formattedValues.featured_image
      );
    }
    onSubmit(formattedValues);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, setFieldValue, values }) => (
        <Form className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-[1fr_30%] gap-8 max-w-full">
            <div className="space-y-4">
              <FormInput
                name="title_en"
                label={t("commonField.title_en")}
                placeholder={t("commonPlaceholder.title_enPlaceholder")}
                onChange={(e) =>
                  handleSlugSync(e, setFieldValue, "title_en", "slug")
                }
                required
              />
              <FormInput
                name="title_ar"
                label={t("commonField.title_ar")}
                placeholder={t("commonPlaceholder.title_arPlaceholder")}
              />
              <FormInput
                name="slug"
                label={t("commonField.slug")}
                placeholder={t("commonPlaceholder.slugPlaceholder")}
                required
              />
              <div>
                <label className="block text-sm font-medium text-gray-700 my-2">
                  {t("commonField.content_en")}
                </label>
                <RichTextEditor
                  name="content_en"
                  value={values.content_en}
                  onChange={(content) => setFieldValue("content_en", content)}
                  placeholder={t("commonPlaceholder.content_enPlaceholder")}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 my-2">
                  {t("commonField.content_ar")}
                </label>
                <RichTextEditor
                  name="content_ar"
                  value={values.content_ar}
                  onChange={(content) => setFieldValue("content_ar", content)}
                  placeholder={t("commonPlaceholder.content_arPlaceholder")}
                />
              </div>
              <FormTextarea
                name="meta_description"
                label={t("commonField.meta_description")}
                placeholder={t("commonPlaceholder.meta_descriptionPlaceholder")}
              />
            </div>

            <div className="space-y-4">
              <FormSelect
                name="blog_category_id"
                label={t("commonTableLabel.blogCategory")}
                options={blogCategoryOptions}
                loading={blogCategoryLoading}
                required
              />
              <FormTextarea
                name="summary_en"
                label={t("commonField.summary_en")}
                placeholder={t("commonPlaceholder.summary_enPlaceholder")}
              />
              <FormTextarea
                name="summary_ar"
                label={t("commonField.summary_ar")}
                placeholder={t("commonPlaceholder.summary_arPlaceholder")}
              />
              <FormInput
                name="meta_title"
                label={t("commonField.title_Meta")}
                placeholder={t("commonPlaceholder.meta_titlePlaceholder")}
              />
              <FormInput
                name="keywords"
                label={t("commonField.keyword")}
                placeholder={t("commonPlaceholder.keywordPlaceholder")}
              />

              <div className="mb">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t("commonField.featuredImage")}
                </label>
                <ImageUploader
                  value={initialValues.featured_image}
                  onUploadSuccess={(url) =>
                    setFieldValue("featured_image", url)
                  }
                />
              </div>
              <FormRadioGroup
                name="status"
                label={t("commonField.status")}
                options={statusOptions}
                required
              />
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 mt-8">
            <Button type="button" variant="outline" onClick={onCancel}>
              {t("commonButton.cancel")}
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {blog
                ? t("commonButton.blog.updated")
                : t("commonButton.blog.create")}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default BlogAddUpdate;
