import { useParams, useNavigate } from "react-router-dom";
import { useState } from "react";
import { toast } from "sonner";
import FulfillmentFormPage from "./FulfillmentForm";
import { useApi } from "@/hooks/useApi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useTranslation } from "react-i18next";
import BackArrowIcon from "@/components/common/BackArrowIcon";

const FulfillmentAddEdit = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const { fetchData, postMutation, putMutation } = useApi();

  const { data, isLoading, isError, refetch } = id
    ? fetchData(`admin/fulfilments/${id}`)
    : { data: null, isLoading: false, isError: false, refetch: () => {} };
  const fulfillmentData = data?.data;

  const handleSubmit = (values) => {
    setLoading(true);
    const dataToSend = { ...values };

    const onSuccess = () => {
      toast.success(
        t(
          id
            ? "commonToast.fulfillmentToast.fulfillmentUpdate"
            : "commonToast.fulfillmentToast.fulfillmentCreate"
        )
      );

      navigate("/fulfillment/list");
    };

    const onError = (error) => {
      console.error(
        "Fulfillment update failed:",
        error?.response?.data || error.message
      );
      toast.error(
        "Fulfillment update failed: " +
          (error?.response?.data?.message || error.message)
      );
    };

    const onSettled = () => setLoading(false);

    if (!id) {
      postMutation.mutate(
        { endpoint: "admin/fulfilments", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        {
          endpoint: `admin/fulfilments/${id}`,
          data: dataToSend,
        },
        { onSuccess, onError, onSettled }
      );
    }
  };

  if (isLoading || loading) {
    return (
      <div className="loading-error">
        <LoadingSpinner />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="loading-error">
        <div className="text-red-500">
          Error loading Fulfillments data. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="page-container">
      <div className="card-style">
        <div className="title-add-edit-card">
          <div className="title-add-edit-div">
            <button onClick={() => navigate("/fulfillment/list")}>
              <BackArrowIcon />
            </button>
            <h3 className="title-add-edit">
              {t(id ? "fulfillment.editAction" : "fulfillment.add")}
            </h3>
          </div>
        </div>
        <div className="px-6 py-4">
          <FulfillmentFormPage
            fulfillment={fulfillmentData}
            onSubmit={handleSubmit}
            onCancel={() => navigate("/fulfillment/list")}
            loading={loading}
          />
        </div>
      </div>
    </div>
  );
};

export default FulfillmentAddEdit;
