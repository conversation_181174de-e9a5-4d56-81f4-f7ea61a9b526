import { Formik, Form } from "formik";
import * as Yup from "yup";
import { FormInput, FormRadioGroup, FormTextarea } from "@/components/ui/form";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import { statusBooleanOptions } from "@/constants/filterOption";

const FulfillmentForm = ({ fulfillment, onSubmit, onCancel }) => {
  const { t } = useTranslation();

  const initialValues = fulfillment
    ? {
        id: fulfillment.id || "",
        name_en: fulfillment.name_en || "",
        name_ar: fulfillment.name_ar || "",
        description_en: fulfillment.description_en || "",
        description_ar: fulfillment.description_ar || "",
        is_active: fulfillment.is_active || false,
      }
    : {
        name_en: "",
        name_ar: "",
        description_en: "",
        description_ar: "",
        is_active: false,
      };

  const validationSchema = Yup.object({
    name_en: Yup.string().required(t("commonValidation.name_en")),
    name_ar: Yup.string().required(t("commonValidation.name_ar")),
    is_active: Yup.string().required(t("commonValidation.status")),
  });

  const handleSubmit = (values, { setSubmitting }) => {
    onSubmit(values);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting }) => {
        return (
          <Form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-5">
              <div className="space-y-4 bg-white gap-3">
                <FormInput
                  name="name_en"
                  label={t("commonField.name_en")}
                  placeholder={t("commonPlaceholder.name_enPlaceholder")}
                  required
                />
                <FormTextarea
                  name="description_en"
                  label={t("commonField.description_en")}
                  placeholder={t("commonPlaceholder.description_enPlaceholder")}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormRadioGroup
                    name="is_active"
                    label={t("commonTableLabel.status")}
                    options={statusBooleanOptions}
                  />
                </div>
              </div>
              <div className="space-y-4 bg-white gap-3">
                <FormInput
                  name="name_ar"
                  label={t("commonField.name_ar")}
                  placeholder={t("commonPlaceholder.name_arPlaceholder")}
                  required
                />

                <FormTextarea
                  name="description_ar"
                  label={t("commonField.description_ar")}
                  placeholder={t("commonPlaceholder.description_arPlaceholder")}
                />
              </div>
            </div>

            <div className="flex justify-end pt-4 border-t border-gray-200 mt-8 gap-3">
              <Button type="button" variant="outline" onClick={onCancel}>
                {t("commonButton.cancel")}
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {fulfillment
                  ? t("commonButton.fulfillment.updated")
                  : t("commonButton.fulfillment.create")}
              </Button>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};

export default FulfillmentForm;
