import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import FulfillmentFilter from "./FulfillmentFilter";
import {
  FaEdit,
  FaTrash,
  FaClip<PERSON><PERSON>ist,
  FaPlus,
  <PERSON>aEye,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import StatusBadge from "@/components/common/StatusBadge";
import {
  capitalizeFirstLetter,
  capitalizeLetter,
  getNestedValue,
} from "@/helper/Commonhelper";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import { renderNA } from "@/helper/commonFunctionHelper";
import Modal from "@/components/ui/Modal";
import PreviewSection from "./FulfillmentPreview";

const FulfillmentIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedFulfillment, setSelectedFulfillment] = useState(null);
  const [previewFulfillmentId, setPreviewFulfillmentId] = useState(null);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    is_active: "",
  });

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: fulfillmentData,
    isLoading,
    isError: fulfillmentError,
    refetch,
  } = fetchData("admin/fulfilments", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    is_active: filterOptions.is_active || "",
  });

  const fulfillmentList = fulfillmentData?.data?.data || [];
  const paginationInfo = {
    currentPage: fulfillmentData?.data?.current_page || 1,
    perPage: fulfillmentData?.data?.per_page || itemsPerPage,
    totalItems: fulfillmentData?.data?.total_items || 0,
    totalPages: fulfillmentData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditFulfillment = (fulfillment) => {
    navigate(`/fulfillment/edit/${fulfillment.id}`);
  };

  const handlePreviewClick = (fulfillment) => {
    setPreviewFulfillmentId(fulfillment.id);
  };

  const handleDeleteClick = (fulfillment) => {
    setSelectedFulfillment(fulfillment);
  };

  const handleDeleteFulfillment = async () => {
    if (!selectedFulfillment) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/fulfilments/${selectedFulfillment.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setSelectedFulfillment(null);
          refetch();
          toast.success(t("commonToast.fulfillmentToast.fulfillmentDelete"));
        },
        onError: (error) => {
          console.error(
            "Fulfillment deletion failed:",
            error?.response?.data || error.message
          );
          toast.error(
            "Fulfillment deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const canEditFulfillment = (fulfillment) => true;

  const canDeleteFulfillment = (fulfillment) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },

    {
      header: t("commonTableLabel.name_en"),
      render: (row) => capitalizeFirstLetter(renderNA(row.name_en)),
    },
    {
      header: t("commonTableLabel.name_ar"),
      render: (row) => capitalizeFirstLetter(renderNA(row.name_ar)),
    },

    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row} fieldName="is_active" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("fulfillment.previewFulfillmentContent")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditFulfillment(row)}
            disabled={!canEditFulfillment(row)}
            title={
              !canEditFulfillment(row)
                ? "You don't have permission to edit this Fulfillment"
                : t("fulfillment.editAction")
            }
          >
            <FaEdit className="icon-edit" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteFulfillment(row)}
            title={
              !canDeleteFulfillment(row)
                ? "You can't delete this Fulfillment"
                : t("fulfillment.deleteAction")
            }
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("fulfillment.title")}
          icon={<FaClipboardList className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => navigate("/fulfillment/add")}
            >
              <FaPlus className="mr-2" /> {t("fulfillment.add")}
            </Button>
          }
        >
          <FulfillmentFilter onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={fulfillmentList}
            emptyMessage={t("fulfillment.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      {/* Fulfillment View  */}
      <Modal
        isOpen={previewFulfillmentId}
        onClose={() => setPreviewFulfillmentId(null)}
        title={t("fulfillment.previewFulfillmentContent")}
        size="xl"
        showCloseButton={true}
      >
        <PreviewSection
          fulfillmentId={previewFulfillmentId}
          onClose={() => setPreviewFulfillmentId(null)}
        />
      </Modal>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationModal
        isOpen={!!selectedFulfillment}
        onClose={() => setSelectedFulfillment(null)}
        onDelete={handleDeleteFulfillment}
        loading={deleteLoading}
        itemName={t("fulfillment.fulfillment")}
        itemValue={getNestedValue(selectedFulfillment, "name_en")}
      />
    </div>
  );
};

export default FulfillmentIndex;
