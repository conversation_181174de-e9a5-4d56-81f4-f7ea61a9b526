import { useParams, useNavigate } from "react-router-dom";
import { useState } from "react";
import { toast } from "sonner";
import PopUpFormPage from "./PopUpForm";
import { useApi } from "@/hooks/useApi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useTranslation } from "react-i18next";
import BackArrowIcon from "@/components/common/BackArrowIcon";

const PopUpAddEdit = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const { fetchData, postMutation, putMutation } = useApi();

  const { data, isLoading, isError, refetch } = id
    ? fetchData(`admin/popups/${id}`)
    : { data: null, isLoading: false, isError: false, refetch: () => {} };
  const popUpData = data?.data;

  const handleSubmit = (values) => {
    setLoading(true);
    const dataToSend = { ...values };

    const onSuccess = () => {
      toast.success(
        t(
          id
            ? "commonToast.popupToast.popupUpdate"
            : "commonToast.popupToast.popupCreate"
        )
      );

      navigate("/popup/list");
    };

    const onError = (error) => {
      console.error(
        "PopUp update failed:",
        error?.response?.data || error.message
      );
      toast.error(
        "PopUp update failed: " +
          (error?.response?.data?.message || error.message)
      );
    };

    const onSettled = () => setLoading(false);

    if (!id) {
      postMutation.mutate(
        { endpoint: "admin/popups", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        {
          endpoint: `admin/popups/${id}`,
          data: dataToSend,
        },
        { onSuccess, onError, onSettled }
      );
    }
  };

  if (isLoading || loading) {
    return (
      <div className="loading-error">
        <LoadingSpinner />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="loading-error">
        <div className="text-red-500">
          Error loading PopUp data. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="page-container">
      <div className="card-style">
        <div className="title-add-edit-card">
          <div className="title-add-edit-div">
            <button onClick={() => navigate("/popup/list")}>
              <BackArrowIcon />
            </button>
            <h3 className="title-add-edit">
              {t(id ? "popup.editAction" : "popup.add")}
            </h3>
          </div>
        </div>
        <div className="px-6 py-4">
          <PopUpFormPage
            popup={popUpData}
            onSubmit={handleSubmit}
            onCancel={() => navigate("/popup/list")}
            loading={loading}
          />
        </div>
      </div>
    </div>
  );
};

export default PopUpAddEdit;
