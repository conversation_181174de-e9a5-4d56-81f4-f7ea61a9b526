import { Formik, Form } from "formik";
import * as Yup from "yup";
import {
  FormInput,
  FormRadioGroup,
  FormSelect,
  RichTextEditor,
} from "@/components/ui/form";
import ImageUploader from "@/components/ui/ImageUploader";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import { testOptions, statusOptions } from "@/constants/filterOption";

const PopUpForm = ({ popup, onSubmit, onCancel }) => {
  const { t } = useTranslation();

  const initialValues = popup
    ? {
        id: popup.id || "",
        title_en: popup.title_en || "",
        title_ar: popup.title_ar || "",
        content_en: popup.content_en || "",
        content_ar: popup.content_ar || "",
        type: popup.type || "",
        button_text: popup.button_text || "",
        button_link: popup.button_link || "",
        status: popup.status || "inactive",
        image: popup.image_url || "",
      }
    : {
        title_en: "",
        title_ar: "",
        content_en: "",
        content_ar: "",
        type: "",
        button_text: "",
        button_link: "",
        image: "",
        status: "inactive",
      };

  const validationSchema = Yup.object({
    title_en: Yup.string().required(t("commonValidation.title_en")),
    title_ar: Yup.string().required(t("commonValidation.title_ar")),
    status: Yup.string().required(t("commonValidation.status")),
  });

  const toUploadPathIfUrl = (val) => {
    if (!val || typeof val !== "string") return val;
    const m = val.match(/\/uploads\/.+$/);
    return m ? m[0].replace(/^\//, "") : val;
  };

  const handleSubmit = (values, { setSubmitting }) => {
    const formattedValues = {
      ...values,
      image: values.image?.path || values.image || "",
    };

    if (formattedValues.image) {
      formattedValues.image = toUploadPathIfUrl(formattedValues.image);
    }
    onSubmit(formattedValues);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, setFieldValue, values }) => {
        return (
          <Form className="space-y-4">
            <div className="bg-white space-y-5">
              <div className="grid grid-cols-1">
                <label className="block text-sm font-medium text-gray-700">
                  {t("commonField.uploadImage")}
                </label>
                <ImageUploader
                  value={initialValues.image}
                  onUploadSuccess={(url) => setFieldValue("image", url)}
                  width="w-full"
                  height="h-[350px]"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-5">
              <div className="space-y-4 bg-white">
                <div className="grid grid-cols-1 gap-4">
                  <FormInput
                    name="title_en"
                    label={t("commonField.title_en")}
                    placeholder={t("commonPlaceholder.title_enPlaceholder")}
                    required
                  />
                  <FormInput
                    name="title_ar"
                    label={t("commonField.title_ar")}
                    placeholder={t("commonPlaceholder.title_arPlaceholder")}
                    required
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <FormSelect
                    name="type"
                    label={t("commonTableLabel.type")}
                    options={testOptions}
                  />
                  <FormInput
                    name="button_text"
                    label={t("commonField.buttonText")}
                    placeholder={t("commonPlaceholder.buttonTextPlaceholder")}
                  />
                </div>
                <div className="grid grid-cols-1 gap-4">
                  <FormInput
                    name="button_link"
                    label={t("commonField.buttonLink")}
                    placeholder={t("commonPlaceholder.buttonLinkPlaceholder")}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormRadioGroup
                    name="status"
                    label={t("commonTableLabel.status")}
                    options={statusOptions}
                  />
                </div>
              </div>
              <div className="space-y-4 bg-white">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 my-2">
                      {t("commonField.content_en")}
                    </label>
                    <RichTextEditor
                      name="content_en"
                      value={values.content_en}
                      onChange={(content) =>
                        setFieldValue("content_en", content)
                      }
                      placeholder={t("commonPlaceholder.content_enPlaceholder")}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 my-2">
                      {t("commonField.content_ar")}
                    </label>
                    <RichTextEditor
                      name="content_ar"
                      value={values.content_ar}
                      onChange={(content) =>
                        setFieldValue("content_ar", content)
                      }
                      placeholder={t("commonPlaceholder.content_arPlaceholder")}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end pt-4 border-t border-gray-200 mt-8 gap-3">
              <Button type="button" variant="outline" onClick={onCancel}>
                {t("commonButton.cancel")}
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {popup
                  ? t("commonButton.popup.updated")
                  : t("commonButton.popup.create")}
              </Button>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};

export default PopUpForm;
