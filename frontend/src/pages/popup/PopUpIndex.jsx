import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import PopUpFilter from "./PopUpFilters";
import {
  FaEdit,
  FaTrash,
  FaClip<PERSON><PERSON>ist,
  FaPlus,
  <PERSON>aEye,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";
import StatusBadge from "@/components/common/StatusBadge";
import {
  capitalizeFirstLetter,
  getNestedValue,
  capitalizeLetter,
} from "@/helper/Commonhelper";
import DeleteConfirmationModal from "@/components/common/DeleteConfirmationModal";
import NoImage from "@/assets/NoImage.png";
import { renderNA } from "@/helper/commonFunctionHelper";
import Modal from "@/components/ui/Modal";
import PreviewSection from "./PopUpPreview";

const PopUpIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedPopUp, setSelectedPopUp] = useState(null);
  const [previewPopUpId, setPreviewPopUpId] = useState(null);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: "",
  });

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: popUpData,
    isLoading,
    isError: popUpError,
    refetch,
  } = fetchData("admin/popups", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search || "",
    status: filterOptions.status || "",
  });

  const popUpList = popUpData?.data?.data || [];
  const paginationInfo = {
    currentPage: popUpData?.data?.current_page || 1,
    perPage: popUpData?.data?.per_page || itemsPerPage,
    totalItems: popUpData?.data?.total_items || 0,
    totalPages: popUpData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditPopUp = (popup) => {
    navigate(`/popup/edit/${popup.id}`);
  };

  const handlePreviewClick = (popup) => {
    setPreviewPopUpId(popup.id);
  };

  const handleDeleteClick = (popup) => {
    setSelectedPopUp(popup);
  };

  const handleDeletePopUp = async () => {
    if (!selectedPopUp) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/popups/${selectedPopUp.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setSelectedPopUp(null);
          refetch();
          toast.success(t("commonToast.popupToast.popupDelete"));
        },
        onError: (error) => {
          console.error(
            "PopUp deletion failed:",
            error?.response?.data || error.message
          );
          toast.error(
            "PopUp deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const canEditPopUp = (popup) => true;

  const canDeletePopUp = (popup) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.image"),
      accessor: "image_url",
      render: (row) =>
        row.image_url ? (
          <img
            src={row.image_url}
            alt={row.title_en}
            className="w-20 h-10 object-contain rounded-sm shadow"
          />
        ) : (
          <img
            src={NoImage}
            alt={row.title_en}
            className="w-20 h-10 object-contain rounded-sm shadow"
          />
        ),
    },
    {
      header: t("commonTableLabel.title_en"),
      render: (row) => capitalizeFirstLetter(renderNA(row.title_en)),
    },
    {
      header: t("commonTableLabel.type"),
      render: (row) => capitalizeLetter(renderNA(row.type)),
    },
    {
      header: t("commonTableLabel.status"),
      render: (row) => <StatusBadge data={row} fieldName="status" />,
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="action-button-group">
          <Button
            variant="viewIcon"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("popup.previewBlogContent")}
          >
            <FaEye className="icon-view" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditPopUp(row)}
            disabled={!canEditPopUp(row)}
            title={
              !canEditPopUp(row)
                ? "You don't have permission to edit this PopUp"
                : t("popup.editAction")
            }
          >
            <FaEdit className="icon-edit" />
          </Button>
          <Button
            variant="dangerIcon"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeletePopUp(row)}
            title={
              !canDeletePopUp(row)
                ? "You can't delete this PopUp"
                : t("popup.deleteAction")
            }
          >
            <FaTrash className="icon-danger" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="page-container">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("popup.title")}
          icon={<FaClipboardList className="title-icon" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => navigate("/popup/add")}
            >
              <FaPlus className="mr-2" /> {t("popup.add")}
            </Button>
          }
        >
          <PopUpFilter onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={popUpList}
            emptyMessage={t("popup.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="pagination-info"
          />
        </Card>
      </motion.div>

      {/* PopUp View  */}
      <Modal
        isOpen={previewPopUpId}
        onClose={() => setPreviewPopUpId(null)}
        title={t("popup.previewBlogContent")}
        size="xl"
        showCloseButton={true}
      >
        <PreviewSection
          popupId={previewPopUpId}
          onClose={() => setPreviewPopUpId(null)}
        />
      </Modal>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationModal
        isOpen={!!selectedPopUp}
        onClose={() => setSelectedPopUp(null)}
        onDelete={handleDeletePopUp}
        loading={deleteLoading}
        itemName={t("popup.popup")}
        itemValue={getNestedValue(selectedPopUp, "title_en")}
      />
    </div>
  );
};

export default PopUpIndex;
