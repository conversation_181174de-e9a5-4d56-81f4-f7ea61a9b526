

// Converts "YYYY-MM-DD HH:mm:ss" or "YYYY-MM-DDTHH:mm:ss" to "YYYY-MM-DD"
export function toDateInputValue(dateTimeStr) {
    if (!dateTimeStr) return "";
    return dateTimeStr.split(" ")[0];
}

// Split by "T" if there is no space
// Converts "YYYY-MM-DD HH:mm:ss" or "YYYY-MM-DDTHH:mm:ss" to "YYYY-MM-DD"
export function toDateShowInputValue(dateTimeStr) {
    if (!dateTimeStr) return "";
    return dateTimeStr.includes("T")
        ? dateTimeStr.split("T")[0]
        : dateTimeStr.split(" ")[0];
}


// Returns today's date in "YYYY-MM-DD" format
export function getTodayStr() {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0');
    const dd = String(today.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
}

// Checks if the given date (string or Date object) is before today (returns true/false)
export function isPastDate(date) {
    const dateStr = typeof date === "string"
        ? toDateInputValue(date)
        : date.toISOString().slice(0, 10);
    return dateStr < getTodayStr();
}

// Adds a number of days to a date string ("YYYY-MM-DD"), returns new date as "YYYY-MM-DD"
export function addDays(dateStr, days) {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    date.setDate(date.getDate() + days);
    return date.toISOString().slice(0, 10);
}

// Converts "YYYY-MM-DD" to "DD MMM, YYYY" (example: "05 Jul, 2025")
export function formatDate(dateStr) {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', {
        day: '2-digit',
        month: 'short',
        year: 'numeric'
    });
}


// Utility function to format date as example "Jul 28, 2025"
export const onlyForDateFormat = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
        month: "short",
        day: "2-digit",
        year: "numeric",
    }).format(date);
};

// Utility function to format time as example "11:42 AM"
export const onlyForTimeFormat = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
    }).format(date);
};