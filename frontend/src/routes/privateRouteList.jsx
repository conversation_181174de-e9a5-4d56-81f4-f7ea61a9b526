import { lazy } from "react";
import React from "react";
import * as permissions from "../constants/permissions";

// Lazy loaded pages
const Dashboard = lazy(() => import("../pages/dashboard/Dashboard"));
const FormDemo = lazy(() => import("../pages/demo/FormDemo"));
const ListDemo = lazy(() => import("../pages/demo/ListDemo"));

// user management component start
// const UserManagement = lazy(() => import("../pages/staff/UserManagement"));
const UserIndex = lazy(() => import("../pages/userManagement/user/UserIndex"));
const RoleIndex = lazy(() => import("../pages/userManagement/role/RoleIndex"));
const PermissionIndex = lazy(() =>
  import("../pages/userManagement/permission/PermissionIndex")
);
// user management component end

//page components start
const PageIndex = lazy(() => import("../pages/page/PageIndex"));
const PageAddEditPage = lazy(() => import("../pages/page/PageEditUpdate"));
//page components end

// Email Template components start
const EmailTemplateIndex = lazy(() => import("../pages/emailTemplates/EmailTemplateIndex"));
const EmailTemplateCreateEdit = lazy(() => import("../pages/emailTemplates/EmailTemplateCreateEdit"));
const CategoryIndex = lazy(() => import("../pages/emailTemplates/categories/CategoryIndex"));
const CategoryDetail = lazy(() => import("../pages/emailTemplates/categories/CategoryDetail"));
const VariableIndex = lazy(() => import("../pages/emailTemplates/variables/VariableIndex"));
const TemplateHistory = lazy(() => import("../pages/emailTemplates/history/TemplateHistory"));
// Email Template components end

// Customers components start
const CustomersPage = lazy(() => import("../pages/customer/customerIndex"));
const CustomersAddEditPage = lazy(() =>
  import("../pages/customer/customerAddUpdated")
);
// Customers components end

// Configuration components start
const BannerPage = lazy(() =>
  import("../pages/configuration/Banner/BannerIndex")
);
const BannerItemPage = lazy(() =>
  import("../pages/configuration/BannerItem/BannerItemIndex")
);
// Configuration components end

// Product components start

// Category
const ProductCategoriesPage = lazy(() =>
  import("../pages/product/category/CategoryIndex")
);
const ProductCategoriesAddEditPage = lazy(() =>
  import("../pages/product/category/CategoryAddEditPage")
);
// product class
const ProductClassPage = lazy(() =>
  import("../pages/product/productClass/ProductClassIndex")
);

// Attribute
const ProductAttributePage = lazy(() =>
  import("../pages/product/productAttribute/attributeIndex")
);
const ProductAttributeAddEditPage = lazy(() =>
  import("../pages/product/productAttribute/attributeAddEditPage")
);

// Product components end

// Vendor Management start
//vendor eoi
const VendorEOIPage = lazy(() =>
  import("../pages/vendorManagement/vendorEoi/eoiIndex")
);
const VendorEOIPreviewPage = lazy(() =>
  import("../pages/vendorManagement/vendorEoi/eoiPreview")
);
const VendorIndexPage = lazy(() =>
  import("../pages/vendorManagement/vendors/vendorIndex")
);
const VendorPreviewPage = lazy(() =>
  import("../pages/vendorManagement/vendors/vendorPreview")
);
// Vendor Management end

// settings Menu start

const DropDownOptionPage = lazy(() =>
  import("../pages/setting/dropdownMenu/DropDownIndex")
);

const DropDownAddEditPage = lazy(() =>
  import("../pages/setting/dropdownMenu/dropDownAddEditPage")
);

// settings Menu end.

// Brand Start
// const BrandPage = lazy(() =>
//   import("../pages/brandManagement/brand/brandIndex")
// );
// Brand End

// Banner Start
const BannerIndexPage = lazy(() =>
  import("../pages/banner/AllBanner/BannerIndex")
);
// Banner end

// Banner Items start
const BannerItemsIndexPage = lazy(() =>
  import("../pages/banner/BannerItem/BannerItemIndex")
);
// Banner Items end

// warehouses start

const WarehouseIndexPage = lazy(() =>
  import("../pages/warehousesManagement/WarehousesIndex")
);
const WareHouseAddEditPage = lazy(() =>
  import("../pages/warehousesManagement/WarehouseAddEdit")
);

// warehouses end

// Coupon start
const CouponIndexPage = lazy(() =>
  import("../pages/couponManagement/CouponIndex")
);
const CouponAddEditPage = lazy(() =>
  import("../pages/couponManagement/CouponAddEdit")
);
// Coupon end

// Simple page components

// Brand start
const BrandListPage = lazy(() => import("../pages/brands/BrandList"));
const BrandAddEditPage = lazy(() => import("../pages/brands/BrandAddEdit"));
const BrandPreviewPage = lazy(() => import("../pages/brands/BrandPreview"));
// Brand end

// Products
const AllProductsPage = () => <div>All Products Page</div>;

const ProductReviewsPage = () => <div>Product Reviews Page</div>;
const DigitalProductsPage = () => <div>Digital Products Page</div>;
const PhysicalProductsPage = () => <div>Physical Products Page</div>;

// Vendors
const VendorsPage = () => <div>Partner Vendors Page</div>;

// Reports
const SalesReportsPage = () => <div>Sales Reports Page</div>;
const InventoryReportsPage = () => <div>Inventory Reports Page</div>;
const CustomerReportsPage = () => <div>Customer Reports Page</div>;
const VendorReportsPage = () => <div>Vendor Reports Page</div>;

// Products
const ProductListPage = lazy(() =>
  import("../pages/products/allProducts/ProductIndex")
);
const ProductWizardPage = lazy(() =>
  import("../pages/products/allProducts/ProductWizardPage.jsx")
);

// Product Circle start
const ProductCircleIndexPage = lazy(() =>
  import("../pages/product/productCircle/ProductCircleIndex")
);
const ProductCircleAddEditPage = lazy(() =>
  import("../pages/product/productCircle/ProductCircleAddEditPage")
);

// Product Circle end

// Blog
// const BlogPostsPage = () => <div>Blog Posts Page</div>;
const BlogPostsPage = lazy(() => import("../pages/blog/allBlog/blogIndex"));
const BlogCategoriesPage = lazy(() =>
  import("../pages/blog/blogCategory/blogCategoryIndex")
);
const BlogAddEditPage = lazy(() =>
  import("../pages/blog/blogAddEdit/BlogAddEditPage")
);
//Offer And Deal

const OfferDealIndexPage = lazy(() =>
  import("../pages/offerAndDealManagement/offerAndDealIndex")
);
const OfferDealAddEditPage = lazy(() =>
  import("../pages/offerAndDealManagement/offerAndDealAddEdit")
);

//  Order Start
const OrderIndexPage = lazy(() =>
  import("../pages/orderManagement/OrderIndex")
);
const OrderAddEditPage = lazy(() =>
  import("../pages/orderManagement/OrderAddEditPage")
);
const OrderPreviewPage = lazy(() =>
  import("../pages/orderManagement/OrderPreview")
);
//  Order End

// PopUp Start
const PopUpIndexPage = lazy(() => import("../pages/popup/PopUpIndex"));
const PopUpAddEditPage = lazy(() => import("../pages/popup/PopUpAddEdit"));

// Fulfillment Start
const FulfillmentIndexPage = lazy(() =>
  import("../pages/fulfillment/FulfillmentIndex")
);
const FulfillmentAddEditPage = lazy(() =>
  import("../pages/fulfillment/FulfillmentAddEdit")
);

// PopUp End

// const BlogCategoriesPage = () => <div>Blog Categories Page</div>;
const BlogCommentsPage = () => <div>Blog Comments Page</div>;

// Marketing
const MarketingCampaignsPage = () => <div>Marketing Campaigns Page</div>;
const MarketingPromotionsPage = () => <div>Marketing Promotions Page</div>;
const MarketingCouponsPage = () => <div>Marketing Coupons Page</div>;

// Other Pages
const SupportPage = () => <div>Support Page</div>;
const HomepageSetupPage = () => <div>Homepage Setup Page</div>;
const StaffAdminPage = () => <div>Staff/Admin Manager Page</div>;
const DeliveryPage = () => <div>Delivery Page</div>;
const LoyaltyPage = () => <div>Loyalty and Reward Points Page</div>;
const SettingsPage = () => <div>Settings Page</div>;

// Support
const SupportCategoryList = lazy(() =>
  import("../pages/support/category/SupportCategoryList")
);
const SupportTopicList = lazy(() =>
  import("../pages/support/topic/SupportTopicList")
);
const SupportReasonList = lazy(() =>
  import("../pages/support/reason/SupportReasonList")
);
const SupportTicketList = lazy(() =>
  import("../pages/support/ticket/SupportTicketList")
);
const SupportTicketAddEdit = lazy(() =>
  import("../pages/support/ticket/SupportTicketAddEdit")
);
const SupportTicketView = lazy(() =>
  import("../pages/support/ticket/SupportTicketView")
);

/**
 * Private routes configuration
 * These routes require authentication to access
 */
const privateRoutes = [
  // Dashboard
  {
    path: "/dashboard",
    component: Dashboard,
    permissions: [
      permissions.BROWSE_ORDERS,
      permissions.BROWSE_PRODUCTS,
      permissions.BROWSE_USERS,
      permissions.BROWSE_VENDORS,
    ],
  },
  // Order
  {
    path: "/order/list",
    component: OrderIndexPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/order/preview/:id",
    component: OrderPreviewPage,
    permissions: [permissions.BROWSE_VENDORS],
  },
  {
    path: "/order/add",
    component: OrderAddEditPage,
    permissions: [permissions.BROWSE_VENDORS],
  },
  {
    path: "/order/edit/:id",
    component: OrderAddEditPage,
    permissions: [permissions.BROWSE_VENDORS],
  },

  // PopUp
  {
    path: "/popup/list",
    component: PopUpIndexPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/popup/add",
    component: PopUpAddEditPage,
    permissions: [permissions.BROWSE_VENDORS],
  },
  {
    path: "/popup/edit/:id",
    component: PopUpAddEditPage,
    permissions: [permissions.BROWSE_VENDORS],
  },
  // PopUp
  {
    path: "/popup/list",
    component: PopUpIndexPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/popup/add",
    component: PopUpAddEditPage,
    permissions: [permissions.BROWSE_VENDORS],
  },
  {
    path: "/popup/edit/:id",
    component: PopUpAddEditPage,
    permissions: [permissions.BROWSE_VENDORS],
  },
  // Fulfillment
  {
    path: "/fulfillment/list",
    component: FulfillmentIndexPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/fulfillment/add",
    component: FulfillmentAddEditPage,
    permissions: [permissions.BROWSE_VENDORS],
  },
  {
    path: "/fulfillment/edit/:id",
    component: FulfillmentAddEditPage,
    permissions: [permissions.BROWSE_VENDORS],
  },

  // Products
  {
    path: "/products/all",
    component: ProductListPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/products/add",
    component: ProductWizardPage,
    permissions: [permissions.CREATE_PRODUCT],
  },
  {
    path: "/products/edit/:id",
    component: ProductWizardPage,
    permissions: [permissions.EDIT_PRODUCT],
  },
  //product circle
  // {
  //   path: "/products/list",
  //   component: ProductCircleIndexPage,
  //   permissions: [permissions.BROWSE_PRODUCTS],
  // },
  // {
  //   path: "/products/list/add",
  //   component: ProductCircleAddEditPage,
  //   permissions: [permissions.CREATE_PRODUCT],
  // },
  // {
  //   path: "/products/list/edit/:id",
  //   component: ProductCircleAddEditPage,
  //   permissions: [permissions.CREATE_PRODUCT],
  // },

  {
    path: "/products/categories",
    component: ProductCategoriesPage,
    permissions: [permissions.BROWSE_CATEGORIES],
  },
  {
    path: "/products/categories/add",
    component: ProductCategoriesAddEditPage,
    permissions: [permissions.BROWSE_CATEGORIES],
  },
  {
    path: "/products/categories/edit/:id",
    component: ProductCategoriesAddEditPage,
    permissions: [permissions.BROWSE_CATEGORIES],
  },
  {
    path: "/products/class",
    component: ProductClassPage,
    permissions: [permissions.BROWSE_CATEGORIES],
  },
  // Brand
  {
    path: "/products/brands",
    component: BrandListPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/products/brands/add",
    component: BrandAddEditPage,
    permissions: [permissions.CREATE_PRODUCT],
  },
  {
    path: "/products/brands/edit/:id",
    component: BrandAddEditPage,
    permissions: [permissions.EDIT_PRODUCT],
  },
  {
    path: "/products/brands/preview/:id",
    component: BrandPreviewPage,
    permissions: [permissions.CREATE_PRODUCT],
  },

  {
    path: "/products/reviews",
    component: ProductReviewsPage,
    permissions: [permissions.BROWSE_REVIEWS],
  },
  {
    path: "/products/catalog/digital",
    component: DigitalProductsPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/products/catalog/physical",
    component: PhysicalProductsPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  // Attribute
  {
    path: "/products/attribute",
    component: ProductAttributePage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/products/attribute/add",
    component: ProductAttributeAddEditPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/products/attribute/edit/:id",
    component: ProductAttributeAddEditPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },

  // DropDown Setting
  {
    path: "/setting/menu/dropdown",
    component: DropDownOptionPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/setting/menu/dropdown/add",
    component: DropDownAddEditPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/setting/menu/dropdown/edit/:id",
    component: DropDownAddEditPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },

  // Brand Management
  // {
  //   path: "/brandManagement/brand",
  //   component: BrandPage,
  //   permissions: [permissions.BROWSE_PRODUCTS],
  // },

  // Vendor Management
  {
    path: "/vendor/eoi",
    component: VendorEOIPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/vendor/eoi/preview/:id",
    component: VendorEOIPreviewPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/vendor/list",
    component: VendorIndexPage,
    permissions: [permissions.BROWSE_VENDORS],
  },
  {
    path: "/vendor/preview/:id",
    component: VendorPreviewPage,
    permissions: [permissions.BROWSE_VENDORS],
  },

  // Customers and Vendors
  {
    path: "/customers/list",
    component: CustomersPage,
    permissions: [permissions.BROWSE_USERS],
  },
  {
    path: "/customers/add",
    component: CustomersAddEditPage,
    permissions: [permissions.BROWSE_USERS],
  },
  {
    path: "/customers/edit/:id",
    component: CustomersAddEditPage,
    permissions: [permissions.BROWSE_USERS],
  },

  {
    path: "/vendors",
    component: VendorsPage,
    permissions: [permissions.BROWSE_VENDORS],
  },

  // Configuration
  {
    path: "/configuration/banners",
    component: BannerPage,
    permissions: [permissions.BROWSE_USERS],
  },
  {
    path: "/configuration/banner/items",
    component: BannerItemPage,
    permissions: [permissions.BROWSE_USERS],
  },

  // Reports
  {
    path: "/reports/sales",
    component: SalesReportsPage,
    permissions: [permissions.VIEW_SALES_REPORTS],
  },
  {
    path: "/reports/inventory",
    component: InventoryReportsPage,
    permissions: [permissions.VIEW_SALES_REPORTS],
  },
  {
    path: "/reports/customers",
    component: CustomerReportsPage,
    permissions: [permissions.ANALYZE_BUYER_BEHAVIOR],
  },
  {
    path: "/reports/vendors",
    component: VendorReportsPage,
    permissions: [permissions.ANALYZE_PRODUCT_PERFORMANCE],
  },

  // Blog
  {
    path: "/blog/posts",
    component: BlogPostsPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  {
    path: "/blog/posts/add",
    component: BlogAddEditPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  {
    path: "/blog/posts/edit/:id",
    component: BlogAddEditPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  {
    path: "/blog/categories",
    component: BlogCategoriesPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  {
    path: "/blog/comments",
    component: BlogCommentsPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },

  // Pages
  {
    path: "/pages/list",
    component: PageIndex,
    // permissions: [permissions.PAGE_VIEW],
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  {
    path: "/pages/add",
    component: PageAddEditPage,
    // permissions: [permissions.PAGE_VIEW],
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  {
    path: "/pages/edit/:id",
    component: PageAddEditPage,
    // permissions: [permissions.PAGE_VIEW],
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },

  // Marketing
  {
    path: "/marketing/campaigns",
    component: MarketingCampaignsPage,
    permissions: [permissions.MANAGE_DISCOUNT_CODES_AND_CAMPAIGNS],
  },
  {
    path: "/marketing/promotions",
    component: MarketingPromotionsPage,
    permissions: [permissions.MANAGE_DISCOUNT_CODES_AND_CAMPAIGNS],
  },
  {
    path: "/marketing/coupons",
    component: MarketingCouponsPage,
    permissions: [permissions.BROWSE_COUPONS],
  },

  // Other Pages
  {
    path: "/support",
    component: SupportPage,
    permissions: [permissions.BROWSE_SUPPORT_TICKETS],
  },
  {
    path: "/support/category",
    component: SupportCategoryList,
    permissions: [permissions.BROWSE_SUPPORT_TICKETS],
  },
  {
    path: "/support/topic",
    component: SupportTopicList,
    permissions: [permissions.BROWSE_SUPPORT_TICKETS],
  },
  {
    path: "/support/reason",
    component: SupportReasonList,
    permissions: [permissions.BROWSE_SUPPORT_TICKETS],
  },
  {
    path: "/support/ticket",
    component: SupportTicketList,
    permissions: [permissions.BROWSE_SUPPORT_TICKETS],
  },
  {
    path: "/support/ticket/add",
    component: SupportTicketAddEdit,
    permissions: [permissions.BROWSE_SUPPORT_TICKETS],
  },
  {
    path: "/support/ticket/edit/:id",
    component: SupportTicketAddEdit,
    permissions: [permissions.BROWSE_SUPPORT_TICKETS],
  },
  {
    path: "/support/ticket/view/:id",
    component: SupportTicketView,
    permissions: [permissions.BROWSE_SUPPORT_TICKETS],
  },
  {
    path: "/homepage-setup",
    component: HomepageSetupPage,
    permissions: [permissions.MANAGE_HOMEPAGE_BANNERS],
  },
  // {
  //   path: "/staff",
  //   component: StaffAdminPage,
  //   permissions: [permissions.STAFF_VIEW],
  // },

  // User Management routes start
  {
    path: "/staff/users",
    component: UserIndex,
    permissions: [permissions.STAFF_VIEW],
  },
  {
    path: "/staff/roles",
    component: RoleIndex,
    permissions: [permissions.STAFF_VIEW],
  },
  {
    path: "/staff/permissions",
    component: PermissionIndex,
    permissions: [permissions.STAFF_VIEW],
  },
  // User Management routes end

  {
    path: "/delivery",
    component: DeliveryPage,
    permissions: [permissions.BROWSE_SHIPMENTS],
  },
  {
    path: "/loyalty",
    component: LoyaltyPage,
    permissions: [permissions.MANAGE_AFFILIATE_AND_REFERRAL_PROGRAMS],
  },
  {
    path: "/settings",
    component: SettingsPage,
    permissions: [permissions.MANAGE_SETTINGS],
  },
  //Banner
  {
    path: "/setting/banner",
    component: BannerIndexPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  //Banner Items
  {
    path: "/setting/banner/items/list/:id",
    component: BannerItemsIndexPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  //WareHouse
  {
    path: "/warehouse/list",
    component: WarehouseIndexPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  {
    path: "/warehouse/add",
    component: WareHouseAddEditPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  {
    path: "/warehouse/edit/:id",
    component: WareHouseAddEditPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  //Coupon
  {
    path: "/coupon/list",
    component: CouponIndexPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  {
    path: "/coupon/add",
    component: CouponAddEditPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  {
    path: "/coupon/edit/:id",
    component: CouponAddEditPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },

  // Offer and Deal.
  {
    path: "/OfferDeal/list",
    component: OfferDealIndexPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  {
    path: "/OfferDeal/add",
    component: OfferDealAddEditPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  {
    path: "/OfferDeal/edit/:id",
    component: OfferDealAddEditPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },

  // Email Templates
  {
    path: "/email-templates",
    component: EmailTemplateIndex,
    permissions: [permissions.BROWSE_EMAIL_TEMPLATES],
  },
  {
    path: "/email-templates/create",
    component: EmailTemplateCreateEdit,
    permissions: [permissions.CREATE_EMAIL_TEMPLATE],
  },
  {
    path: "/email-templates/edit/:uuid",
    component: EmailTemplateCreateEdit,
    permissions: [permissions.EDIT_EMAIL_TEMPLATE],
  },
  {
    path: "/email-templates/history/:uuid",
    component: TemplateHistory,
    permissions: [permissions.VIEW_EMAIL_TEMPLATE],
  },
  {
    path: "/email-templates/categories",
    component: CategoryIndex,
    permissions: [permissions.MANAGE_EMAIL_TEMPLATE_CATEGORIES],
  },
  {
    path: "/email-templates/categories/:slug",
    component: CategoryDetail,
    permissions: [permissions.MANAGE_EMAIL_TEMPLATE_CATEGORIES],
  },
  {
    path: "/email-templates/variables",
    component: VariableIndex,
    permissions: [permissions.MANAGE_EMAIL_TEMPLATE_VARIABLES],
  },

  // Demo Form.
  {
    path: "/demo/form",
    component: FormDemo,
    permissions: [permissions.MANAGE_SETTINGS],
  },

  // Demo List
  {
    path: "/demo/list",
    component: ListDemo,
    permissions: [permissions.MANAGE_SETTINGS],
  },
];

export default privateRoutes;
