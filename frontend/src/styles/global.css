/* Reusable layout and component classes */

/* Table Action styling */
.action-button-group {
  @apply flex gap-2;
}

.page-container {
  @apply mx-auto relative p-5;
}

.title-icon {
  @apply text-indigo-600 me-1;
}

.title-add-edit {
  @apply text-lg font-medium text-gray-800;
}

.title-add-edit-card {
  @apply p-4 border-b border-gray-200 flex items-center justify-between;
}
.title-add-edit-div {
  @apply flex items-center gap-2;
}

.pagination-info {
  @apply mt-4;
}
.card-style {
  @apply bg-white rounded-lg shadow-xl border border-gray-300;
}
.back-arrow-icon {
  @apply text-gray-500 hover:text-gray-700;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.loading-error {
  @apply flex justify-center items-center h-screen;
}

.loading-error-view {
  @apply flex-1 flex items-center justify-center py-12;
}

/* Icon colors */
.icon-primary {
  @apply text-indigo-600;
}

.icon-view {
  @apply text-teal-600;
}
.icon-edit {
  @apply text-indigo-600;
}
.icon-danger {
  @apply text-red-600;
}

/* Form input styling */
.input-field {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500;
}
